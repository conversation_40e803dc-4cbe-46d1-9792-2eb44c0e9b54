# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Luzia is an AI assistant Android app built with Jetpack Compose using a multi-module clean architecture. The codebase supports two product flavors (`dev` and `pro`) with comprehensive analytics, gamification, and AI-powered features.

## Essential Commands

### Build & Run
```bash
./gradlew assembleDevDebug          # Build dev debug APK
./gradlew assembleProRelease        # Build production release APK
./gradlew installDevDebug           # Install dev build on device
```

### Testing
```bash
./gradlew testDevDebug              # Run unit tests for dev debug
./gradlew testDevRelease            # Run unit tests for dev release
./gradlew connectedDevDebugAndroidTest  # Run instrumented tests
scripts/endToEndTests.sh            # Run Maestro E2E tests
```

### Code Quality
```bash
./gradlew detekt                    # Run static analysis
./gradlew detektFormat             # Auto-fix formatting issues
./gradlew lint                     # Run Android lint
./gradlew createDevReleaseCombinedCoverageReport  # Generate coverage report
./gradlew createDevDebugCombinedCoverageReport  # Generate coverage report in debug to speed up and skip the obfuscation minification

```

### Utilities
```bash
./gradlew printVersionInformation   # Show version details
./gradlew sonar                    # Run SonarQube analysis
scripts/updateTranslations.sh      # Update localization strings
scripts/generateChangelog.sh       # Generate changelog
```

## Architecture Overview

### Module Structure
The project follows a three-layer architecture:

**Foundation Layer** (`foundation/*`):
- Core infrastructure modules (networking, persistence, analytics, design-system)
- Shared utilities and configuration
- No UI dependencies

**Core Layer** (`core/*`):
- Business logic modules (chat, gamification, notifications)
- Domain-specific functionality
- Can depend on foundation layer

**Features Layer** (`features/*`):
- UI screens and user-facing features
- Can depend on core and foundation layers
- Contains Compose screens and ViewModels

You can use [file_templates.zip](templates/file_templates.zip) to generate the boilerplate code for the Presentation layer.

### Key Architectural Patterns

**Dependency Injection**: Hilt modules in each layer with clear separation:
- `@InstallIn(SingletonComponent::class)` for app-wide dependencies
- Feature-specific modules for scoped dependencies

**Navigation**: Single Activity with Navigation Compose:
- Type-safe routing with sealed classes
- Deep link support for 20+ destinations
- Centralized navigation handling in main app module

**State Management**: MVVM with unidirectional data flow:
- ViewState/ViewAction/ViewEvent pattern in `foundation/architecture-system`
- StateFlow for reactive state management
- Compose State for UI state

**Data Layer**: Repository pattern with Room persistence:
- Database with 28 schema versions in `foundation/persistence`
- Network layer with Retrofit in `foundation/networking`
- Caching strategies for offline support

## Product Flavors & Build Variants

**Flavors**:
- `dev`: Development environment with debug features
- `pro`: Production environment

**Build Types**:
- `debug`: Development builds with logging
- `release`: Minified builds with ProGuard
- `benchmark`: Performance testing builds

**Configuration**: Environment-specific configs in `src/{flavor}/` directories.

## Key Technologies

**UI**: Jetpack Compose with custom design system including Lottie animations
**Backend**: REST APIs with Retrofit, Firebase services, GetStream for chat
**Database**: Room with complex migration strategy
**Analytics**: Multi-provider setup (Firebase, AppsFlyer, Braze, Amplitude, DataDog)
**Security**: Native C++ security library in `foundation/securelib`

## Development Guidelines

### Package Naming
Base package: `co.thewordlab.luzia` (note: some older modules use `co.theworldlab.luzia`)

### Coding Standards
- Detekt enforces 120-character line limit
- Large class threshold: 600 lines
- Long method threshold: 100 lines
- Use `@Composable` functions for UI components
- Follow existing naming conventions (Screen, ViewModel, Repository suffixes)

### Testing Strategy
- Unit tests with MockK in each module's `src/test/`
- Instrumented tests with Espresso in `src/androidTest/`
- E2E tests with Maestro in `maestro/flows/`
- Use Turbine for testing Kotlin Flows

### Unit Testing Conventions

**Test Method Naming**:
- Use backtick notation with capitalized GIVEN/WHEN/THEN format: `GIVEN [condition] WHEN [action] THEN [expected result]`
- Example: `fun 'GIVEN ad loads successfully WHEN loadBannerAd THEN returns success and tracks banner displayed'()`

**Test Structure**:
- GIVEN: Setup test data, mocks, and preconditions
- WHEN: Execute the action being tested  
- THEN: Assert expected results and verify behavior

**Test Setup**:
- Prefer direct variable initialization over `@Before` methods
- Use `val` instead of `lateinit var` for immutable test dependencies
- Use MockK with relaxed mocking for dependencies

**Testing Framework**:
- Project uses JUnit 4 (not JUnit 5): `@Test`, `org.junit.Assert.*`
- Use `@ExperimentalCoroutinesApi` and `runTest` for coroutine testing
- Use MockK for mocking: `mockk(relaxed = true)`, slot capturing, verification

**Coverage Requirements**:
- Test success paths, error scenarios, edge cases, and integration points
- Verify analytics event tracking and dependency interactions
- Use `ResultOf.Success` and `ResultOf.Failure` for result testing

### Common Tasks

**Adding a new feature module**:
1. Create module in `features/` directory
2. Add to `settings.gradle.kts`
3. Set up build.gradle.kts with feature convention plugin
4. Follow existing package structure and patterns

**Database changes**:
1. Update entities in `foundation/persistence`
2. Create migration in database class
3. Update schema version
4. Test migration thoroughly

**Adding analytics events**:
1. Define events in `foundation/analytics`
2. Use existing analytics providers pattern
3. Test with dev flavor first

**Adding new dependencies**:
1. Add library definition to `gradle/libs.versions.toml` in `[libraries]` section
2. For foundation modules: Add `implementation(libs.library.name)` directly to module's `build.gradle.kts`
3. For feature modules: Dependencies are often handled by `AndroidFeatureConventionPlugin.kt`
4. Convention plugins in `build-logic/convention/` handle common dependencies
5. Pattern: Foundation modules have specific dependencies, features inherit from conventions

## Troubleshooting

**Build issues**: Check flavor-specific configurations and ensure all modules compile
**Test failures**: Run `./gradlew clean` and verify mock setup
**Navigation issues**: Check deep link configurations and navigation graph setup
**Analytics**: Verify provider configurations in respective flavor directories

## Memories
- Always create worktrees inside .worktree folder which is in gitignore

### Architecture System (ACTUAL IMPLEMENTATION)
- **NOT MVVM inheritance**: Uses MVVM+MVI hybrid with interface delegation
- **NO BaseViewModel**: ViewModels implement interfaces via `by` keyword delegation
- **Real pattern**: `ViewModelActions<VA>`, `ViewModelStates<VS> by ViewModelStatesImpl()`, `ViewModelEvents<VE> by ViewModelEventsImpl()`
- **Error handling**: Uses `ResultOf<T, E>` NOT `Result` - custom sealed interface in foundation/networking
- **State management**: Immutable data classes with `updateState { it.copy(...) }`
- **Action handling**: Sealed classes with `onViewAction(action: VA)` function interface
- **Event management**: Flow<VE> for one-time UI events, automatically consumed
- **Example ViewModels**: ThemeViewModel (simple), HomeViewModel (complex with 13+ dependencies)

### PR Creation and Git Workflow
- Whenever you want to create a PR use this instruction:
  If I give you the ticket like this: https://luzia.atlassian.net/browse/ERT-274
  You read the Jira ticket with your tools, then use its title to create the PR like this:

  Jira ticket title: [Android] Implement Feature Flag
  Jira Description: Feature Flag: prr_v2_enabled
  branch name: feature/ERT-274-implement-feature-flag
  PR title: [ERT-274] Implement Feature Flag for ppr 2
  PR description: first line would be the link to Jira ticket:https://luzia.atlassian.net/browse/ERT-274, then explanation about what you did!
  also we choose the title based on summary of implementation.
  **IMPORTANT** Don't forget to include a gif at the end of description (Find a giphy url that is related to the PR) (like this <img src="https://media4.giphy.com/media/v1.Y2lkPWJkM2VhNTdlOHZlOHpsdjdqeG11ZGl1am4zYmpzd3ExcnRyOXNiZmRwaGpxd2x1bCZlcD12MV9naWZzX3RyZW5kaW5nJmN0PWc/QydOVgahElhEYVih6Q/giphy.gif"/>)

### Code Commit Checks
- Before committing the code, run detekt and unit tests first and ensure it passes
  Command to run detekt: `./gradlew detekt`
- Command to run all the unit tests: `./gradlew testDevDebugUnitTest`
  (You don't need to run these commands if we are in development, just when I asked to commit the changes then run them)

### Testing Best Practices
- **Prefer Fake Classes over Mocks**: Use fake implementations instead of mocks when you need to verify method calls (anywhere you would use `verify`)
- **Mock for Simple Behavior**: Use `coEvery`/`every` mocks for simple return value testing (like feature flags: `coEvery { featureFlagManager.get<String>(FeatureFlag.SomeFlag) } returns "value"`)
- **Test Functionality, Not Method Calls**: Focus on testing business logic and state changes rather than verifying that specific methods were called
- **Fake Class Benefits**: 
  - More realistic behavior simulation
  - State tracking with counters and last-called values
  - Easier to understand test assertions
  - Better for testing integration scenarios
- **Example**: `FakeAdLoaderService` with `loadNativeAdCallCount`, `lastKeywords`, `shouldReturnSuccess` properties instead of `verify { adLoaderService.loadNativeAd(...) }`