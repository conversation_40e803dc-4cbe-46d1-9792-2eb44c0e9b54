#!/bin/bash

# Define file to store the last commit hash
LAST_COMMIT_FILE=".last_commit"

# Get the short form of the current HEAD commit hash (first 7 characters)
CURRENT_HEAD=$(git rev-parse --short HEAD)

# Save the short commit hash to the file
echo "$CURRENT_HEAD" > "$LAST_COMMIT_FILE"

# Fetch the latest changes from the remote
git fetch origin

# Rebase the local branch onto the latest changes
git rebase origin/latest

# Add the .last_commit file to staging
git add "$LAST_COMMIT_FILE"

# Commit the changes with a message
git commit -m "CI: update .last_commit with current HEAD commit hash"

# Push the changes to the remote repository
git push origin latest

# Confirm the action
echo "Current HEAD commit (short version) saved to '$LAST_COMMIT_FILE': $CURRENT_HEAD"
echo "Commit and push complete."
