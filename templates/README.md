### Overview

File templates can be used to boost productivity in daily dev tasks. In this directory, we have file templates for `Presentation Layer`.

### Installation

Presentation layer template asks for a name, which is used to create following MVI structures in the selected package.
1. View state
2. View actions
3. View events
4. ViewModel
5. ViewScreen

To import file templates, click on `File > Manage IDE Settings > Import Settings` to import `file_templates.zip` file.

![Alternate image text](screenshot.png)

### Usage

Left click on any package and tap on `Presentation Layer` and input a name for prefixing all classes.
