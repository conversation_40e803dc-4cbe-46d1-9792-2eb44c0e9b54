# LuzIA-android-app

Android app for https://soyluzia.com/

## What's happening

Look at the [project board](https://github.com/orgs/The-Wordlab/projects/4).

## Designs

- [User app](https://www.figma.com/file/ZFggNEKWRPImxetca9DJ6J/Luzia-AND?type=design&node-id=0-1&mode=design&t=8IkeMPgE89Aiguo1-0)
- [Design System](https://www.figma.com/file/TGX83R4jNbB03hg00eHbXZ/DS-%2F-Luzia-%2F-Android?type=design&node-id=2-30&mode=design&t=4NSbK37V53WiMZhi-0)

If you get a 404 or access error, ask for access.

## Ideas

- [Notion](https://www.notion.so/soyluzia/2d583c1fed7640d49a206fb71de2bf5a?v=5c742ba2f8ca4fa1aa5eedd6acbaad78)

## Backend API specification project

https://github.com/The-Wordlab/luzia-openapi

## Generate AAB/APK

To generate an aab or apk file , there is an action on github that allows you generate this file
choosing the environment, dev or PRO.
***IMPORTANT***: the version code will be update automatically, just change the version name
For this:

1. Select "Build AAB/APK" action
2. Click "Run workflow"
3. Choose branch, environment and type file
4. When the process finishes, the aab/apk will be included in the execution.

<img width="1636" alt="actionGenerateBundle" src="https://github.com/The-Wordlab/luzia-android-app/assets/141713090/4507f481-9fd7-4d51-8e39-5ba74fd96c1a">

## Translations

Install phrase cli tool
`brew install phrase-cli`

Run update translations script

```
cd scripts
./updateTranslations.sh
```

## Github Packages Access
| Step                              | Image                                                                                                             | Description                                                                                                                                                                                                                                                                                                                                                    |
|-----------------------------------|-------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 1. Access Settings                | <img width="309" alt="1" src="https://github.com/user-attachments/assets/a1c6632a-35f1-413c-8d31-b17c8867f514" /> | From your GitHub profile dropdown, select "Settings".                                                                                                                                                                                                                                                                                                          |
| 2. Navigate to Developer Settings | <img width="371" alt="2" src="https://github.com/user-attachments/assets/516a333a-e5aa-4d2b-b7d5-7c0b5a992eca" /> | On the left sidebar, scroll down and click on "Developer settings" under the "Archives" section.                                                                                                                                                                                                                                                               |
| 3. Select Personal Access Tokens  | <img width="325" alt="3" src="https://github.com/user-attachments/assets/63e78718-8a21-4717-a49b-4642cfa8b554" /> | In the Developer settings, expand "Personal access tokens" and then select "Tokens (classic)".                                                                                                                                                                                                                                                                 | 
| 4. Generate New Token (classic)   | <img width="872" alt="4" src="https://github.com/user-attachments/assets/5da73591-c198-44d8-9d64-a9aa89491360" /> | On the Personal access tokens (classic) page, click on the "Generate new token" dropdown and choose "Generate new token (classic)".                                                                                                                                                                                                                            |
| 5. Configure New Token            | <img width="825" alt="5" src="https://github.com/user-attachments/assets/afd2562b-e6d6-4c9b-8d71-3705a00a97a2" /> | Fill in the details for your new token: &lt;br> - Note: Provide a descriptive name for your token (e.g., "Luzia Packages Access"). &lt;br> - Expiration: Set to expire Never to not have issues later. &lt;br> - Select scopes: Choose the necessary permissions. For package access, ensure "write:packages" and "read:packages" are selected.                |
| 6. Copy Your Token                | <img width="464" alt="6" src="https://github.com/user-attachments/assets/5ea577b4-c77f-4eb4-b34e-fbbb00f69347" /> | After generation, your token will be displayed. Copy it immediately as you will not be able to see it again. This token is what you will use as GITHUB_TOKEN in your environment or configuration. Go to `/Users/<USER>/.gradle` and create `gradle.properties` file if it doesn't exist and input the token as shown in the image as well as your username |    

## Supported Deeplinks

| Section                        | Link                                           |
|--------------------------------|------------------------------------------------|
| Chat Detail Screen             | luzia://chats/{personalityId}                  |
| Landing Page Home              | luzia://landing/home                           |
| Landing Page Chats             | luzia://landing/chats                          |
| Landing Page Tools             | luzia://landing/tools                          |
| Landing Page Friends           | luzia://landing/friends                        |
| Landing Page Custom Characters | luzia://landing/custom_characters              |
| Tools Detail                   | luzia://tools/{toolId}                         |
| Dynamic Tools Detail           | luzia://dynamic_tools/{toolId}                 |
| Bestie Points                  | luzia://bp                                     |
| Signup                         | luzia://signup                                 |
| Profile                        | luzia://profile/detail                         |
| Profile wizard                 | luzia://profile/wizard                         |
| Profile referral               | luzia://profile/referral                       |
| Profile schoolmates            | luzia://profile/schoolmates                    |
| Show Proactive Shared Messages | luzia://proactive/share-message/{masterUserId} |
| Get Stream Group Detail        | luzia://groups/{groupId}                       |
| Google In-App Reviews          | luzia://app_review                             |
| New Group Chat                 | luzia://new-group-chat                         |
| School selection               | luzia://profile/student-school                 |
| Student Status                 | luzia://profile/student-status                 |
| Contact-Us                     | luzia://settings/contact-us                    |
| Custom Bestie                  | luzia://custom-bestie/{id}                     |



## Sonarqube
1. Install Docker and Sonarqube image.
2. Run: docker run -d --name sonarqube -p 9000:9000 sonarqube
3. Open http://localhost:9000/ and use admin/admin as user/pass.
4. Create a local project with the following name and generate a token for the project.
    - Luzia Android App
5. Change sonar.token in build.gradle.kts and sync.
6. Run: ./gradlew testDevRelease
7. Run: ./gradlew createDevReleaseCombinedCoverageReport
8. Run: ./gradlew detekt
9. Run: ./gradlew sonar
