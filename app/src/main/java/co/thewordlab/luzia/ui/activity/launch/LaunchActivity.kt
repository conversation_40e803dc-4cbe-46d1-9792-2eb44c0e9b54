package co.thewordlab.luzia.ui.activity.launch

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.buildFrom
import co.thewordlab.luzia.ui.activity.auth.AuthActivity
import co.thewordlab.luzia.ui.activity.launch.presentation.LaunchViewActions
import co.thewordlab.luzia.ui.activity.launch.presentation.LaunchViewEvents
import co.thewordlab.luzia.ui.activity.launch.presentation.LaunchViewModel
import co.thewordlab.luzia.ui.activity.session.UserSessionActivity
import dagger.hilt.android.AndroidEntryPoint

@SuppressLint("CustomSplashScreen")
@AndroidEntryPoint
class LaunchActivity : AppCompatActivity() {

    private val viewModel: LaunchViewModel by viewModels()

    @SuppressLint("InlinedApi")
    override fun onCreate(savedInstanceState: Bundle?) {
        installSplashScreen().setKeepOnScreenCondition { true }
        enableEdgeToEdge()
        super.onCreate(savedInstanceState)
        setContent {
            viewModel.onViewAction(LaunchViewActions.OnCreate(intent))
            ViewModelEventEffect(viewModel) {
                when (it) {
                    is LaunchViewEvents.NavigateToFullUserLogin -> navigateToAuth(it.excludeDeeplink)
                    is LaunchViewEvents.NavigateToHome -> navigateToHome(it.excludeDeeplink)
                }
            }
        }
    }

    private fun navigateToHome(excludeDeeplink: Boolean) {
        val newIntent = Intent(this, UserSessionActivity::class.java)
            .buildFrom(intent, !excludeDeeplink)
        startActivity(newIntent)
        finish()
    }

    private fun navigateToAuth(excludeDeeplink: Boolean) {
        val newIntent = Intent(this, AuthActivity::class.java)
            .buildFrom(intent, !excludeDeeplink)
        startActivity(newIntent)
        finish()
    }
}
