package co.thewordlab.luzia.ui.app.landing

import co.thewordlab.luzia.foundation.architecture.system.ViewState
import co.theworldlab.luzia.foundation.design.system.components.bottombar.BottomTabs

data class LandingScreenViewState(
    val tabs: List<BottomTabs> = listOf(
        BottomTabs.HOME,
        BottomTabs.CHATS,
        BottomTabs.TOOLS
    ),
    val selectedTab: BottomTabs = BottomTabs.HOME,
    val badges: Set<BottomTabs> = setOf()
) : ViewState
