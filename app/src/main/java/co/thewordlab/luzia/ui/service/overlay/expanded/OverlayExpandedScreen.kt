package co.thewordlab.luzia.ui.service.overlay.expanded

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.input.clearText
import androidx.compose.foundation.text.input.rememberTextFieldState
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.paging.PagingData
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.paging.compose.itemContentType
import androidx.paging.compose.itemKey
import co.thewordlab.luzia.app.R
import co.thewordlab.luzia.core.chat.presentation.list.model.MessageRenderModel
import co.thewordlab.luzia.core.chat.presentation.message.ChatMessageView
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.ButtonSize
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.ButtonState
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.CircleButtonType
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.LuziaCircleButton
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.LuziaSecondaryButton
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import kotlinx.coroutines.flow.Flow
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun OverlayExpandedScreen(viewModel: OverlayExpandedViewModel, onBack: () -> Unit) {
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    OnCreate("OverlayExpandedScreen") {
        viewModel.onViewAction(OverlayExpandedViewActions.OnCreate)
    }
    ViewModelEventEffect(viewModel) {
        when (it) {
            OverlayExpandedViewEvents.NavigateBack -> onBack()
        }
    }
    OverlayExpandedContent(
        messages = viewModel.messages,
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@Composable
private fun OverlayExpandedContent(
    viewState: OverlayExpandedViewState,
    onViewActions: (OverlayExpandedViewActions) -> Unit,
    messages: Flow<PagingData<MessageRenderModel>>
) {
    val items = messages.collectAsLazyPagingItems()
    Column(
        modifier = Modifier
            .fillMaxSize()
            .systemBarsPadding()
            .imePadding()
            .padding(Spacing.X16.dp),
        verticalArrangement = Arrangement.spacedBy(Spacing.X16.dp)
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Image(
                modifier = Modifier.size(dimensionResource(R.dimen.collapsed_overlay_size)),
                painter = painterResource(designR.drawable.luzia_with_background),
                contentDescription = stringResource(localizationR.string.luzia_overlay_toggle)
            )
            Spacer(Modifier.weight(1f))
            Icon(
                modifier = Modifier
                    .size(Spacing.X32.dp)
                    .clip(CircleShape)
                    .background(LuziaTheme.palette.surface.content)
                    .clickable { onViewActions(OverlayExpandedViewActions.OnBack) }
                    .padding(Spacing.X6.dp)
                    .semantics { role = Role.Button },
                painter = painterResource(designR.drawable.ic_close),
                contentDescription = stringResource(localizationR.string.luzia_overlay_toggle),
                tint = LuziaTheme.palette.interactive.primary
            )
        }
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .clip(RoundedCornerShape(Corners.X5.dp))
                .background(LuziaTheme.palette.surface.background)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(Spacing.X12.dp)
            ) {
                MessagesContent(items)
                OverlayComposerView(viewState, onViewActions)
            }
        }
    }
}

@Composable
private fun OverlayComposerView(
    viewState: OverlayExpandedViewState,
    onViewActions: (OverlayExpandedViewActions) -> Unit
) {
    val textState = rememberTextFieldState("")
    BasicTextField(
        modifier = Modifier
            .fillMaxWidth()
            .navigationBarsPadding()
            .clip(RoundedCornerShape(Corners.X5.dp))
            .background(LuziaTheme.palette.surface.content)
            .padding(Spacing.X12.dp)
            .wrapContentHeight(),
        state = textState,
        decorator = { innerTextField ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
            ) {
                viewState.webSearch?.let { webSearch ->
                    LuziaSecondaryButton(
                        modifier = Modifier.height(Spacing.X32.dp),
                        onClick = {
                            onViewActions(OverlayExpandedViewActions.OnWebSearchClicked)
                        },
                        text = stringResource(id = localizationR.string.common_web),
                        icon = painterResource(id = designR.drawable.ic_web_20),
                        size = ButtonSize.SMALL,
                        state = when {
                            !viewState.actionsEnabled -> ButtonState.DISABLED
                            webSearch.isActive -> ButtonState.TOGGLED
                            else -> ButtonState.ENABLED
                        }
                    )
                }
                Box(modifier = Modifier.weight(1f)) {
                    if (textState.text.isEmpty()) {
                        LuziaText(
                            text = stringResource(localizationR.string.overlay_hint_text),
                            style = LuziaTheme.typography.body.regular.small,
                            color = LuziaTheme.palette.text.secondary
                        )
                    }
                    innerTextField()
                }
                LuziaCircleButton(
                    onClick = {
                        onViewActions(OverlayExpandedViewActions.OnSendMessage(textState.text))
                        textState.clearText()
                    },
                    type = CircleButtonType.Primary(painterResource(designR.drawable.ic_send)),
                    isEnabled = viewState.isSendEnabled
                )
            }
        }
    )
}

@Composable
private fun ColumnScope.MessagesContent(
    lazyMessageItems: LazyPagingItems<MessageRenderModel>,
    messageRenderer: @Composable (MessageRenderModel) -> Unit = {
        ChatMessageView(model = it)
    }
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxWidth()
            .weight(1f, false)
            .animateContentSize(),
        reverseLayout = true,
        verticalArrangement = Arrangement.spacedBy(Spacing.X12.dp, Alignment.Top),
    ) {
        items(
            count = lazyMessageItems.itemCount,
            key = lazyMessageItems.itemKey { it.message.messageId },
            contentType = lazyMessageItems.itemContentType { it.message.messageType }
        ) { index ->
            val item = lazyMessageItems[index]
            item?.let { messageRenderer(it) }
        }
    }
}
