package co.thewordlab.luzia.ui.app

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.activity.compose.LocalActivity
import androidx.compose.runtime.Composable
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.navigation.NavHostController
import co.thewordlab.luzia.core.bestiepoints.presentation.streaksBestiePoints
import co.thewordlab.luzia.core.camera.advanced.camera
import co.thewordlab.luzia.core.chat.presentation.sources.messageSources
import co.thewordlab.luzia.core.navigation.common.DeeplinkHandler
import co.thewordlab.luzia.core.navigation.common.RouteNavHost
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.core.notifications.notificationPermission
import co.thewordlab.luzia.core.sharing.presentation.chat.newChat
import co.thewordlab.luzia.core.sharing.presentation.chatdetail.proactiveSharedMessage
import co.thewordlab.luzia.core.sharing.presentation.group.newGroup
import co.thewordlab.luzia.core.tools.presentation.webview
import co.thewordlab.luzia.features.chat.presentation.details.chatDetail
import co.thewordlab.luzia.features.chat.presentation.favorites.favorites
import co.thewordlab.luzia.features.chat.presentation.responses.responseStyles
import co.thewordlab.luzia.features.personality.presentation.custombestie.customBestie
import co.thewordlab.luzia.features.personality.presentation.personalityprofile.personality
import co.thewordlab.luzia.features.profile.presentation.profile.external.externalUser
import co.thewordlab.luzia.features.profile.presentation.profile.profile
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.profileFill
import co.thewordlab.luzia.features.profile.presentation.referral.referralScreen
import co.thewordlab.luzia.features.profile.presentation.school.mates.schoolMates
import co.thewordlab.luzia.features.stream.chatdetail.getStreamChatDetail
import co.thewordlab.luzia.features.stream.chatfriends.addNewMembersToStreamGroup
import co.thewordlab.luzia.features.stream.chatimage.changeImageOfStreamGroup
import co.thewordlab.luzia.features.stream.chatname.renameStreamGroup
import co.thewordlab.luzia.features.stream.invite.invitation
import co.thewordlab.luzia.features.tools.presentation.tools.documents.toolDocuments
import co.thewordlab.luzia.features.tools.presentation.tools.dynamic.navigation.dynamicTools
import co.thewordlab.luzia.features.tools.presentation.tools.math.toolMath
import co.thewordlab.luzia.foundation.common.extensions.animateActivityTransition
import co.thewordlab.luzia.ui.activity.auth.signup
import co.thewordlab.luzia.ui.activity.session.UserSessionActivity
import co.thewordlab.luzia.ui.app.animations.NavHostAnimations
import co.thewordlab.luzia.ui.app.landing.landingScreen
import co.thewordlab.luzia.ui.service.overlay.info.overlayLuziaInfo
import co.theworldlab.luzia.features.gamification.presentation.majorreward.majorReward
import co.theworldlab.luzia.features.gamification.presentation.yourscore.gamification
import co.theworldlab.luzia.features.imagine.presentation.gallery.imagineGallery
import co.theworldlab.luzia.features.imagine.presentation.gallery.imagineGalleryDetail
import co.theworldlab.luzia.features.imagine.presentation.imagineTool
import co.theworldlab.luzia.features.imagine.presentation.result.imagineResult
import co.theworldlab.luzia.features.settings.presentation.account.account
import co.theworldlab.luzia.features.settings.presentation.language.languageSelection
import co.theworldlab.luzia.features.settings.presentation.privacypolicy.privacyPolicy
import co.theworldlab.luzia.features.settings.presentation.privacypolicy.privacyPolicyAndTerms
import co.theworldlab.luzia.features.settings.presentation.privacypolicy.termsOfUser
import co.theworldlab.luzia.features.settings.presentation.settings.settings
import co.theworldlab.luzia.features.settings.presentation.theme.themeSelection
import co.theworldlab.luzia.features.vision.screens.vision
import co.theworldlab.luzia.foundation.design.system.components.bottombar.LandingDestinations
import co.theworldlab.luzia.foundation.design.system.events.AppEvent
import co.theworldlab.luzia.foundation.design.system.events.AppEventActions
import co.theworldlab.luzia.foundation.design.system.events.EventsHandler
import co.theworldlab.luzia.foundation.design.system.events.EventsHandlerView

@OptIn(ExperimentalComposeUiApi::class)
@Suppress("LongMethod", "CyclomaticComplexMethod")
@Composable
fun AppNavGraph(
    navController: NavHostController,
    eventsHandler: EventsHandler<AppEvent>,
    onNotificationPermissionGranted: () -> Unit,
    deeplinkHandler: DeeplinkHandler,
    navigateToChat: Boolean
) {
    val context = LocalContext.current
    val activity = LocalActivity.current
    val startDestination = if (navigateToChat) {
        UserSessionRoutes.OnboardingChatDetail
    } else {
        UserSessionRoutes.Landing(LandingDestinations.HOME)
    }

    RouteNavHost(
        modifier = Modifier.semantics { testTagsAsResourceId = true },
        navController = navController,
        startRoute = startDestination,
        enterTransition = NavHostAnimations.enterTransition,
        popExitTransition = NavHostAnimations.popExitTransition,
        popEnterTransition = NavHostAnimations.popEnterTransition,
        deeplinkHandler = deeplinkHandler
    ) {
        landingScreen()
        settings()
        themeSelection()
        languageSelection()
        chatDetail(navController) { restartUserSessionActivity(context, activity) }
        imagineTool()
        imagineResult()
        privacyPolicy()
        termsOfUser()
        privacyPolicyAndTerms()
        account()
        imagineGallery()
        imagineGalleryDetail()
        webview()
        vision(navController)
        notificationPermission(navController)
        toolDocuments()
        toolMath()
        camera(navController)
        streaksBestiePoints()
        profile()
        profileFill()
        schoolMates(navController)
        getStreamChatDetail()
        proactiveSharedMessage()
        referralScreen()
        personality()
        favorites()
        signup()
        newChat()
        newGroup()
        dynamicTools()
        externalUser()
        invitation()
        renameStreamGroup()
        changeImageOfStreamGroup()
        addNewMembersToStreamGroup()
        responseStyles()
        customBestie()
        gamification()
        majorReward()
        overlayLuziaInfo()
        messageSources()
    }
    EventsHandlerView(eventsHandler = eventsHandler) {
        when (it) {
            AppEventActions.NavigateToSignup -> navController.navigate(
                UserSessionRoutes.Signup(
                    false
                )
            )

            AppEventActions.NotificationPermissionGranted -> onNotificationPermissionGranted.invoke()
        }
    }
}

private fun restartUserSessionActivity(context: Context, activity: Activity?) {
    val intent = UserSessionActivity.buildIntent(context, null, false)
        .apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
    activity?.finish()
    context.startActivity(intent)
    activity?.animateActivityTransition(
        android.R.anim.slide_in_left,
        android.R.anim.slide_out_right
    )
}
