package co.thewordlab.luzia.ui.activity.session.di

import co.thewordlab.fouundation.persistence.LuziaDataStore
import co.thewordlab.fouundation.persistence.chat.ChatDataSource
import co.thewordlab.luzia.foundation.messages.data.PrrManager
import co.thewordlab.luzia.foundation.messages.di.Persistent
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
object SessionModule {

    @Provides
    fun providePrrManager(
        @Persistent chatDataSource: ChatDataSource,
        luziaDataStore: LuziaDataStore
    ) = PrrManager(chatDataSource, luziaDataStore)
}
