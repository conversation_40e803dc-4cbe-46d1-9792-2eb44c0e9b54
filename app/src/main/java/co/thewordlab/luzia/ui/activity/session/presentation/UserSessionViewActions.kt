package co.thewordlab.luzia.ui.activity.session.presentation

import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class UserSessionViewActions : ViewAction {
    data class OnCreate(val notificationsEnabled: Boolean) : UserSessionViewActions()
    data class OnDismissReward(val id: String) : UserSessionViewActions()
    data object OnNotificationPermissionGranted : UserSessionViewActions()
}
