package co.thewordlab.luzia.ui.activity.auth.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.profile.domain.GetUserProfileUseCase
import co.thewordlab.luzia.core.profile.domain.model.UserType
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.ui.activity.launch.domain.LaunchRepository
import co.theworldlab.luzia.features.settings.domain.models.ModeTheme
import co.theworldlab.luzia.features.settings.domain.repository.SettingsRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AuthViewModel @Inject constructor(
    private val settingsRepository: SettingsRepository,
    private val profileUseCase: GetUserProfileUseCase,
    private val launchRepository: LaunchRepository
) : ViewModel(),
    ViewModelActions<AuthViewActions>,
    ViewModelEvents<AuthViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<AuthViewState> by ViewModelStatesImpl(AuthViewState()) {

    override fun onViewAction(action: AuthViewActions) {
        when (action) {
            is AuthViewActions.OnCreate -> {
                checkFullUserProfile()
                listenToThemeChanges()
                listenToAppUpdates()
            }

            is AuthViewActions.OnDismiss -> sendEvent(AuthViewEvents.NavigateBack)
            AuthViewActions.OnChat -> sendEvent(AuthViewEvents.NavigateChat)
        }
    }

    private fun listenToAppUpdates() {
        launchRepository.isForceUpdateNeeded()
            .distinctUntilChanged()
            .filter { it }
            .onEach { theme -> updateState { it.copy(forceUpdateNeeded = true) } }
            .launchIn(viewModelScope)
    }

    private fun checkFullUserProfile() = viewModelScope.launch {
        val profile = profileUseCase().firstOrNull()
        // Close auth screen in case full screen. Safe guard for deep links
        // normally user should not be navigating to this screen manually.
        if (profile?.userType == UserType.FULL_USER) {
            sendEvent(AuthViewEvents.NavigateBack)
        }
    }

    private fun listenToThemeChanges() {
        settingsRepository.themeModeFlow
            .onEach { theme -> updateState { it.copy(theme = ModeTheme.findTheme(theme)) } }
            .launchIn(viewModelScope)
    }
}
