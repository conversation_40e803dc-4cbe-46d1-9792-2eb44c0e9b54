package co.thewordlab.luzia.ui.app.landing

import androidx.activity.compose.LocalActivity
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.features.chat.domain.ChatsNavigator
import co.thewordlab.luzia.features.chat.presentation.allchats.AllChatsScreen
import co.thewordlab.luzia.features.home.presentation.HomeScreenNavigation
import co.thewordlab.luzia.features.home.presentation.HomeScreenView
import co.thewordlab.luzia.features.stream.chatstab.StreamChatsInTabScreen
import co.thewordlab.luzia.features.tools.presentation.screen.ToolsScreen
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.reviews.requestPlayStoreReview
import co.theworldlab.luzia.foundation.design.system.components.bottombar.BottomTabs
import co.theworldlab.luzia.foundation.design.system.components.bottombar.LuziaBottomBar
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme

@Suppress("LongMethod")
@Composable
internal fun LandingScreenView(
    navigation: LandingScreenNavigation,
) {
    val viewModel: LandingScreenViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val tabClicked = rememberSaveable { mutableStateOf<BottomTabs?>(null) }
    val activity = LocalActivity.current

    ViewModelEventEffect(viewModel) { event ->
        when (event) {
            LandingScreenViewEvents.LaunchAppReviewProcess ->
                activity?.requestPlayStoreReview()
        }
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(LuziaTheme.palette.surface.background)
    ) {
        Crossfade(
            modifier = Modifier.weight(1f),
            targetState = viewState.selectedTab,
            label = "Tabs"
        ) { tab ->
            when (tab) {
                BottomTabs.HOME -> {
                    HomeScreenView(
                        comingFromBottomBar = tabClicked.value == BottomTabs.HOME,
                        navigation = HomeScreenNavigation(
                            onNavigateToSettings = navigation.onNavigateToSettings,
                            onNavigateToChat = { chatDestination ->
                                viewModel.onViewAction(
                                    LandingScreenViewActions.OnTabSelected(
                                        BottomTabs.CHATS
                                    )
                                )
                                ChatsNavigator.setInitialTab(chatDestination)
                            },
                            onNavigateToTools = {
                                viewModel.onViewAction(
                                    LandingScreenViewActions.OnTabSelected(
                                        BottomTabs.TOOLS
                                    )
                                )
                            },
                            onNavigateToImagine = navigation.onNavigateToImagine,
                            onNavigateToVision = navigation.onNavigateToVision,
                            onNavigateToDocumentTool = navigation.onNavigateToDocumentTool,
                            onNavigateToMathTool = navigation.onNavigateToMathTool,
                            onNavigateToWeb = navigation.onNavigateToWeb,
                            onNavigateToBestiePoints = navigation.onNavigateToBestiePoints,
                            onNavigateToSignup = navigation.onNavigateToSignup,
                            onNavigateToProfile = navigation.onNavigateToProfile,
                            onLinkClicked = navigation.onLinkClicked,
                            onNavigateToProfileWizard = navigation.onNavigateToProfileWizard,
                            onNavigateToReferral = navigation.onNavigateToReferral,
                            onNavigateToDynamicTools = navigation.onNavigateToDynamicTools
                        )
                    )
                    if (tabClicked.value == BottomTabs.HOME) {
                        tabClicked.value = null
                    }
                }

                BottomTabs.CHATS -> {
                    AllChatsScreen(
                        comingFromBottomBar = tabClicked.value == BottomTabs.CHATS,
                        onBestiePointsClicked = navigation.onNavigateToBestiePoints,
                        onNavigateToSignup = navigation.onNavigateToSignup,
                        onNavigateToProfileFill = navigation.onNavigateToProfileWizard,
                        onNavigateToReferral = navigation.onNavigateToReferral
                    )
                    if (tabClicked.value == BottomTabs.CHATS) {
                        tabClicked.value = null
                    }
                }

                BottomTabs.TOOLS -> {
                    ToolsScreen(
                        comingFromBottomBar = tabClicked.value == BottomTabs.TOOLS,
                        onNavigateToImagine = navigation.onNavigateToImagine,
                        onNavigateToVision = navigation.onNavigateToVision,
                        onNavigateToDocumentTool = navigation.onNavigateToDocumentTool,
                        onNavigateToMathTool = navigation.onNavigateToMathTool,
                        onNavigateToWeb = navigation.onNavigateToWeb,
                        onBestiePointsClicked = navigation.onNavigateToBestiePoints,
                        onNavigateToSignup = navigation.onNavigateToSignup,
                        onNavigateToDynamicTools = navigation.onNavigateToDynamicTools
                    )
                    if (tabClicked.value == BottomTabs.TOOLS) {
                        tabClicked.value = null
                    }
                }

                BottomTabs.FRIENDS -> StreamChatsInTabScreen()
            }
        }
        LuziaBottomBar(
            selectedTab = { viewState.selectedTab },
            tabs = viewState.tabs,
            badges = viewState.badges,
            onTabSelected = {
                tabClicked.value = it
                viewModel.onViewAction(LandingScreenViewActions.OnTabSelected(it))
            }
        )
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        LandingScreenView(
            navigation = LandingScreenNavigation(),
        )
    }
}
