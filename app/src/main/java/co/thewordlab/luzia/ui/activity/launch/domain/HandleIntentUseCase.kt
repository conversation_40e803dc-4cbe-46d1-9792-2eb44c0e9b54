package co.thewordlab.luzia.ui.activity.launch.domain

import android.content.Intent
import android.content.Intent.EXTRA_SHORTCUT_ID
import android.net.Uri
import android.os.Build
import android.os.Parcelable
import androidx.core.net.toUri
import co.thewordlab.luzia.app.BuildConfig
import co.thewordlab.luzia.core.notifications.firebase.PushNotificationService.Companion.EXTRA_FROM_NOTIFICATION
import co.thewordlab.luzia.core.notifications.firebase.PushNotificationService.Companion.GROUP_ID
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Event
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.common.navigation.DeepLinks
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.foundation.files.importing.FileImporter
import co.thewordlab.luzia.foundation.files.importing.TextImporter
import co.thewordlab.luzia.foundation.messages.domain.repository.ChatRepository
import javax.inject.Inject

class HandleIntentUseCase @Inject constructor(
    private val chatRepository: ChatRepository,
    private val fileImporter: FileImporter,
    private val textImporter: TextImporter,
    private val analytics: Analytics,
    private val featureFlagManager: FeatureFlagManager,
    private val canNavigateToDeepLinkUseCase: CanNavigateToDeepLinkUseCase,
) {

    suspend operator fun invoke(intent: Intent): IntentResult {
        runCatching {
            intent.extras?.let { featureFlagManager.overWriteConfigValue(it) }
            val intentType = intent.type.orEmpty()
            when {
                intent.action == "${BuildConfig.APPLICATION_ID}.send_message" -> {
                    val extraPersonalityId = intent.extras?.getString("personality_id")
                    val text = intent.extras?.getString("text")
                    if (!extraPersonalityId.isNullOrEmpty() && !text.isNullOrEmpty()) {
                        chatRepository.saveRemoteMessage(extraPersonalityId, text)
                    }
                }

                intent.action == Intent.ACTION_SEND && fileImporter.supports(intentType) -> {
                    val uri = intent.getParcelableExtra<Parcelable>(Intent.EXTRA_STREAM) as? Uri
                    if (uri != null) {
                        val shortcutId =
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                                intent.getStringExtra(EXTRA_SHORTCUT_ID)
                            } else {
                                null
                            }
                        if (intent.data == null) {
                            intent.data = DeepLinks.LUZIA_CHAT.toUri()
                        }
                        fileImporter.importFile(uri, shortcutId)
                    }
                }

                intent.action == Intent.ACTION_SEND && intentType == "text/plain" -> {
                    val sharedText = intent.getStringExtra(Intent.EXTRA_TEXT)
                    println("HandleIntentUseCase: Processing text intent with type='$intentType', text='$sharedText'")
                    if (!sharedText.isNullOrEmpty()) {
                        if (intent.data == null) {
                            intent.data = DeepLinks.LUZIA_CHAT.toUri()
                        }
                        println("HandleIntentUseCase: Calling textImporter.importText with text='$sharedText'")
                        textImporter.importText(sharedText)
                    }
                }
            }
            if (intent.hasExtra(EXTRA_FROM_NOTIFICATION)) {
                val groupId = intent.getStringExtra(GROUP_ID).orEmpty()
                analytics.logEvent(Event.NotificationOpen, mapOf(Parameter.GroupId to groupId))
            }
        }

        val deeplink = intent.dataString ?: return IntentResult.OK
        return if (canNavigateToDeepLinkUseCase(deeplink)) {
            IntentResult.OK
        } else {
            IntentResult.DEEPLINK_DISABLED
        }
    }
}
