package co.thewordlab.luzia.ui.activity.auth

import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import co.thewordlab.luzia.ui.activity.auth.presentation.AuthScreen
import co.thewordlab.luzia.ui.activity.session.UserSessionActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AuthActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        enableEdgeToEdge()
        super.onCreate(savedInstanceState)
        setContent {
            AuthScreen(
                onClose = { navigate(launchChat = false) },
                onChat = { navigate(launchChat = true) }
            )
        }
    }

    private fun navigate(launchChat: <PERSON>olean) {
        val intent = UserSessionActivity.buildIntent(
            context = this,
            intent = intent,
            launchChatDirectly = launchChat
        ).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        finish()
        startActivity(intent)
    }
}
