package co.thewordlab.luzia.ui.app.landing

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import co.thewordlab.luzia.core.chat.analytics.ChatAnalytics
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.core.sharing.domain.repository.StreamRepository
import co.thewordlab.luzia.core.sharing.domain.usecase.GetUnreadMessageCountUseCase
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.theworldlab.luzia.foundation.design.system.components.bottombar.BottomTabs
import co.theworldlab.luzia.foundation.design.system.components.bottombar.LandingDestinations
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class LandingScreenViewModel @Inject constructor(
    private val featureFlagManager: FeatureFlagManager,
    private val getUnreadMessageCountUseCase: GetUnreadMessageCountUseCase,
    private val dispatcher: CoroutineDispatcher,
    private val analytics: Analytics,
    private val streamRepository: StreamRepository,
    private val savedStateHandle: SavedStateHandle
) :
    ViewModel(),
    ViewModelActions<LandingScreenViewActions>,
    ViewModelStates<LandingScreenViewState> by ViewModelStatesImpl(LandingScreenViewState()),
    ViewModelEvents<LandingScreenViewEvents> by ViewModelEventsImpl() {

    init {
        runCatching {
            val route = savedStateHandle.toRoute<UserSessionRoutes.Landing>()
            onCreate(route.destinations, route.showAInAppReview)
        }
    }

    override fun onViewAction(action: LandingScreenViewActions) {
        when (action) {
            is LandingScreenViewActions.OnTabSelected -> onTabSelected(action.tab)
        }
    }

    private fun onTabSelected(tab: BottomTabs) = viewModelScope.launch {
        if (tab == BottomTabs.FRIENDS) {
            streamRepository.setFriendsTabSelected()
            val hasBadge = viewState.value.badges.contains(BottomTabs.FRIENDS)
            val props = mapOf(Parameter.BadgeVisible to hasBadge)
            analytics.logScreen(ChatAnalytics.GroupsChatsScreen, props)
        }
        updateState { it.copy(selectedTab = tab) }
    }

    private fun onCreate(
        landingDestinations: LandingDestinations,
        launchAppReviewFlow: Boolean
    ) = viewModelScope.launch {
        val showGroupBottomBarItem = featureFlagManager.get<Boolean>(FeatureFlag.GroupChatsEnabled)
        if (showGroupBottomBarItem) {
            setupBottomBarWithFriendsBottomBarItem(landingDestinations)
        } else {
            setupBottomBarWithoutFriendsBottomBarItem(landingDestinations)
        }

        if (launchAppReviewFlow) {
            sendEvent(LandingScreenViewEvents.LaunchAppReviewProcess)
        }
    }

    private fun setupBottomBarWithFriendsBottomBarItem(
        landingDestinations: LandingDestinations
    ) {
        updateState {
            it.copy(
                tabs = listOf(
                    BottomTabs.HOME,
                    BottomTabs.CHATS,
                    BottomTabs.FRIENDS,
                    BottomTabs.TOOLS
                ),
                selectedTab = when (landingDestinations) {
                    LandingDestinations.HOME -> BottomTabs.HOME
                    LandingDestinations.FRIENDS -> BottomTabs.FRIENDS
                    LandingDestinations.TOOLS -> BottomTabs.TOOLS
                    else -> BottomTabs.CHATS
                }
            )
        }
        listenToFriendsTabBadge()
    }

    private fun setupBottomBarWithoutFriendsBottomBarItem(
        landingDestinations: LandingDestinations
    ) {
        updateState {
            it.copy(
                tabs = listOf(
                    BottomTabs.HOME,
                    BottomTabs.CHATS,
                    BottomTabs.TOOLS
                ),
                selectedTab = when (landingDestinations) {
                    LandingDestinations.HOME -> BottomTabs.HOME
                    LandingDestinations.TOOLS -> BottomTabs.TOOLS
                    else -> BottomTabs.CHATS
                }
            )
        }
    }

    private fun listenToFriendsTabBadge() {
        combine(
            streamRepository.isFriendsTabSelectedBefore(),
            getUnreadMessageCountUseCase()
        ) { hasVisitedFriendsTabBefore, unreadMessageCount ->
            val hasBadge = unreadMessageCount > 0
            updateState { state ->
                state.copy(
                    badges = if (hasBadge || !hasVisitedFriendsTabBefore) {
                        state.badges.plus(BottomTabs.FRIENDS)
                    } else {
                        state.badges.minus(BottomTabs.FRIENDS)
                    }
                )
            }
        }
            .flowOn(dispatcher)
            .launchIn(viewModelScope)
    }
}
