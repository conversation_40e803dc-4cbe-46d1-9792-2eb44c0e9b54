package co.thewordlab.luzia.ui.service.overlay

import android.app.Application
import android.provider.Settings
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import co.theworldlab.luzia.features.settings.domain.repository.SettingsRepository
import com.google.android.gms.common.api.internal.BackgroundDetector
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class OverlayController @Inject constructor(
    val application: Application,
    private val userSessionManager: UserSessionManager,
    private val settingsRepository: SettingsRepository,
    private val coroutineScope: CoroutineScope,
    private val featureFlagManager: FeatureFlagManager
) : BackgroundDetector.BackgroundStateChangeListener {

    fun initialize() {
        if (featureFlagManager.getImmediate(FeatureFlag.OverlayEnabled)) {
            BackgroundDetector.initialize(application)
            BackgroundDetector.getInstance().addListener(this)
        }
    }

    override fun onBackgroundStateChanged(isBackgrounded: Boolean) {
        if (!Settings.canDrawOverlays(application)) return
        if (userSessionManager.getUserSessionImmediate() == null) return
        coroutineScope.launch {
            val isEnabled = settingsRepository.overlayChoice.firstOrNull() ?: false
            if (isEnabled) {
                if (isBackgrounded) {
                    OverlayService.showOverlay(application)
                } else {
                    OverlayService.hideOverlay(application)
                }
            } else {
                OverlayService.stopOverlay(application)
            }
        }
    }
}
