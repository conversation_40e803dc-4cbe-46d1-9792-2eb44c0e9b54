package co.thewordlab.luzia.ui.service.overlay.info

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.ui.service.overlay.data.ShouldPromoteOverlayUseCase
import co.theworldlab.luzia.features.settings.domain.repository.SettingsRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class OverlayInfoViewModel @Inject constructor(
    private val shouldPromoteOverlayUseCase: ShouldPromoteOverlayUseCase,
    private val settingsRepository: SettingsRepository,
) :
    ViewModel(),
    ViewModelActions<OverlayInfoViewActions>,
    ViewModelEvents<OverlayInfoViewEvents> by ViewModelEventsImpl() {

    override fun onViewAction(action: OverlayInfoViewActions) {
        when (action) {
            OverlayInfoViewActions.OnCtaClicked -> onCtaClicked()
            OverlayInfoViewActions.OnDismiss -> onDismiss()
        }
    }

    private fun onCtaClicked() = viewModelScope.launch {
        shouldPromoteOverlayUseCase.setPromoted()
        settingsRepository.updateOverlayChoice(true)
        sendEvent(OverlayInfoViewEvents.NavigateToOverlaySettings)
        sendEvent(OverlayInfoViewEvents.NavigateBack)
    }

    private fun onDismiss() = viewModelScope.launch {
        shouldPromoteOverlayUseCase.setPromoted()
        sendEvent(OverlayInfoViewEvents.NavigateBack)
    }
}
