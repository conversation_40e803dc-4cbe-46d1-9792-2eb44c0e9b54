package co.thewordlab.luzia.ui.service.overlay

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.provider.Settings
import android.view.View
import android.view.animation.DecelerateInterpolator
import co.thewordlab.luzia.app.R
import co.thewordlab.luzia.foundation.common.service.OverlayMotionHandler
import co.thewordlab.luzia.foundation.common.service.OverlayMotionHandlerImpl
import co.thewordlab.luzia.foundation.common.service.OverlayMotionListener
import co.thewordlab.luzia.foundation.common.service.OverlayViewService
import co.thewordlab.luzia.ui.service.overlay.expanded.OverlayExpandedScreen
import co.thewordlab.luzia.ui.service.overlay.expanded.OverlayExpandedViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class OverlayService :
    OverlayViewService(),
    OverlayMotionHandler by OverlayMotionHandlerImpl(),
    OverlayMotionListener {

    @Inject
    lateinit var overlayExpandedViewModel: OverlayExpandedViewModel

    override fun onCreate() {
        super.onCreate()
        if (!Settings.canDrawOverlays(this)) {
            stopSelf()
            return
        }
        showCollapsedView()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (intent?.hasExtra(INTENT_EXTRA_COMMAND_SHOW_OVERLAY) == true) {
            showCollapsedView()
        }
        if (intent?.hasExtra(INTENT_EXTRA_COMMAND_HIDE_OVERLAY) == true) {
            removeOverlay()
        }
        return START_NOT_STICKY
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun showCollapsedView() {
        val view = layoutInflater.inflate(R.layout.overlay_collapsed, null)
        val size = resources.getDimensionPixelSize(R.dimen.collapsed_overlay_size)
        showLegacyView(view, size, size)
        enableDragging(view, params, this)
    }

    private fun showExpandedView() {
        showComposeView(
            content = {
                OverlayExpandedScreen(overlayExpandedViewModel, onBack = { showCollapsedView() })
            },
            backgroundColor = SCRIM_COLOR
        )
    }

    override fun onUpdateLayout() {
        updateLayout()
    }

    override fun onViewClick(view: View) {
        val margin = resources.getDimensionPixelSize(R.dimen.overlay_margin)
        val startX = params.x
        val startY = params.y
        val endX = margin
        val endY = margin

        val animatorX = ValueAnimator.ofInt(startX, endX)
        val animatorY = ValueAnimator.ofInt(startY, endY)
        val interpolator = DecelerateInterpolator()

        animatorX.duration = ANIM_DURATION
        animatorX.interpolator = interpolator
        animatorX.addUpdateListener { animation ->
            params.x = animation.animatedValue as Int
            updateLayout()
        }

        animatorY.duration = ANIM_DURATION
        animatorY.interpolator = interpolator
        animatorY.addUpdateListener { animation ->
            params.y = animation.animatedValue as Int
            updateLayout()
        }

        animatorX.start()
        animatorY.start()

        animatorX.addListener(object : android.animation.AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: android.animation.Animator) {
                showExpandedView()
            }
        })
    }

    companion object {
        private const val ANIM_DURATION = 300L
        private const val SCRIM_COLOR = 0x74777580
        private const val INTENT_EXTRA_COMMAND_SHOW_OVERLAY = "INTENT_EXTRA_COMMAND_SHOW_OVERLAY"
        private const val INTENT_EXTRA_COMMAND_HIDE_OVERLAY = "INTENT_EXTRA_COMMAND_HIDE_OVERLAY"

        fun showOverlay(context: Context) {
            val intent = Intent(context, OverlayService::class.java)
            intent.putExtra(INTENT_EXTRA_COMMAND_SHOW_OVERLAY, true)
            context.startService(intent)
        }

        fun hideOverlay(context: Context) {
            val intent = Intent(context, OverlayService::class.java)
            intent.putExtra(INTENT_EXTRA_COMMAND_HIDE_OVERLAY, true)
            context.startService(intent)
        }

        fun stopOverlay(context: Context) {
            val intent = Intent(context, OverlayService::class.java)
            context.stopService(intent)
        }
    }
}
