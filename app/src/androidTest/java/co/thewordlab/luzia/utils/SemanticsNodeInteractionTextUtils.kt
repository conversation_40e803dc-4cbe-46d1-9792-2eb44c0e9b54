package co.thewordlab.luzia.utils

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.SemanticsActions
import androidx.compose.ui.semantics.SemanticsProperties
import androidx.compose.ui.semantics.getOrNull
import androidx.compose.ui.test.SemanticsMatcher
import androidx.compose.ui.test.SemanticsNodeInteraction
import androidx.compose.ui.test.assert
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.unit.TextUnit

fun SemanticsNodeInteraction.assertTextColorIsEqualTo(color: Color): SemanticsNodeInteraction = assert(isOfColor(color))

private fun isOfColor(expectedColor: Color): SemanticsMatcher = SemanticsMatcher(
	"Assert ${SemanticsProperties.Text.name} is of color '$expectedColor'"
) {
	val textLayoutResults = mutableListOf<TextLayoutResult>()
	it.config.getOrNull(SemanticsActions.GetTextLayoutResult)
		?.action
		?.invoke(textLayoutResults)
	return@SemanticsMatcher if (textLayoutResults.isEmpty()) {
		false
	} else {
		val receivedColor = textLayoutResults.first().layoutInput.style.color
		if (receivedColor != expectedColor) {
			throw AssertionError("Expected font color to be $expectedColor but was $receivedColor.")
		}
		true
	}
}

fun SemanticsNodeInteraction.assertFontSizeEqualsTo(expectedFontSize: TextUnit): SemanticsNodeInteraction =
	assert(isOfFontSize(expectedFontSize))

private fun isOfFontSize(expectedFontSize: TextUnit): SemanticsMatcher = SemanticsMatcher(
	"Assert ${SemanticsProperties.Text.name} has font size $expectedFontSize"
) {
	val textLayoutResults = mutableListOf<TextLayoutResult>()
	it.config.getOrNull(SemanticsActions.GetTextLayoutResult)
		?.action
		?.invoke(textLayoutResults)
	return@SemanticsMatcher if (textLayoutResults.isEmpty()) {
		false
	} else {
		val receivedTextStyle = textLayoutResults.first().layoutInput.style
		val receivedFontSize = receivedTextStyle.fontSize
		if (receivedFontSize != expectedFontSize) {
			throw AssertionError(
				"Expected font size to be $expectedFontSize but was $receivedFontSize"
			)
		}
		true
	}
}
