package co.thewordlab.luzia.ui.activity.session.domain

import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes.Invite
import co.thewordlab.luzia.core.navigation.usersession.model.GroupJoinSource
import co.thewordlab.luzia.core.profile.domain.GetUserProfileUseCase
import co.thewordlab.luzia.core.profile.domain.model.UserProfile
import co.thewordlab.luzia.core.profile.domain.model.UserType
import co.thewordlab.luzia.foundation.analytics.domain.AppsFlyerDeepLinks
import co.thewordlab.luzia.foundation.analytics.domain.AppsFlyerInitializeUseCase
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.ui.activity.session.presentation.UserSessionViewEvents
import io.mockk.coEvery
import io.mockk.mockk
import junit.framework.TestCase.assertEquals
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertNull
import org.junit.Before
import org.junit.Test

class GetAppsFlyerDeeplinkUseCaseTest {

    private lateinit var useCase: GetAppsFlyerDeeplinkUseCase
    private lateinit var featureFlagManager: FeatureFlagManager
    private lateinit var getUserProfileUseCase: GetUserProfileUseCase
    private lateinit var appsFlyerInitializeUseCase: AppsFlyerInitializeUseCase

    @Before
    fun setup() {
        featureFlagManager = mockk()
        getUserProfileUseCase = mockk()
        appsFlyerInitializeUseCase = mockk()

        useCase = GetAppsFlyerDeeplinkUseCase(
            featureFlagManager = featureFlagManager,
            getUserProfileUseCase = getUserProfileUseCase,
            appsFlyerInitializeUseCase = appsFlyerInitializeUseCase
        )
    }

    @Test
    fun `when receiving regular deeplink, should emit OnDeeplinkReceived`() = runTest {
        // Given
        val deeplink = "https://example.com"
        setupEligibleUser()
        coEvery { appsFlyerInitializeUseCase.getDeepLink() } returns flowOf(
            AppsFlyerDeepLinks.Deeplink(deeplink)
        )

        // When
        useCase.invoke().collect { event ->
            // Then
            assertEquals(
                UserSessionViewEvents.OnDeeplinkReceived(deeplink),
                event
            )
        }
    }

    @Test
    fun `when receiving invite link and user is eligible, should emit OnNavigateToRoute`() =
        runTest {
            // Given
            val invite = AppsFlyerDeepLinks.Invite(
                cid = "123",
                name = "Test Group",
                memberCount = "5",
                image = "image.jpg"
            )
            setupEligibleUser()
            coEvery { appsFlyerInitializeUseCase.getDeepLink() } returns flowOf(invite)

            // When
            useCase.invoke().collect { event ->
                // Then
                assertEquals(
                    UserSessionViewEvents.OnNavigateToRoute(
                        Invite(
                            cid = "123",
                            name = "Test Group",
                            memberCount = "5",
                            image = "image.jpg",
                            source = GroupJoinSource.Link
                        )
                    ),
                    event
                )
            }
        }

    @Test
    fun `when receiving invite link and user is not eligible, should emit null`() = runTest {
        // Given
        val invite = AppsFlyerDeepLinks.Invite(
            cid = "123",
            name = "Test Group",
            memberCount = "5",
            image = "image.jpg"
        )
        setupIneligibleUser()
        coEvery { appsFlyerInitializeUseCase.getDeepLink() } returns flowOf(invite)

        // When
        useCase.invoke().collect { event ->
            // Then
            assertNull(event)
        }
    }

    private fun setupEligibleUser() {
        val eligibleProfile = UserProfile(
            userType = UserType.FULL_USER,
            isStudent = true
        )
        coEvery { getUserProfileUseCase.invoke() } returns flowOf(eligibleProfile)
        coEvery { featureFlagManager.get<Boolean>(FeatureFlag.GroupChatsEnabled) } returns true
    }

    private fun setupIneligibleUser() {
        val ineligibleProfile = UserProfile(
            userType = UserType.GUEST,
            isStudent = false
        )
        coEvery { getUserProfileUseCase.invoke() } returns flowOf(ineligibleProfile)
        coEvery { featureFlagManager.get<Boolean>(FeatureFlag.GroupChatsEnabled) } returns true
    }
}
