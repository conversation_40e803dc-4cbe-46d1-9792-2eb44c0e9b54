package co.thewordlab.luzia.ui.activity.launch.presentation

import android.content.Intent
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.UserIdentification
import co.thewordlab.luzia.foundation.networking.device.DeviceManagement
import co.thewordlab.luzia.foundation.networking.session.UserSession
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import co.thewordlab.luzia.foundation.networking.session.UserType
import co.thewordlab.luzia.ui.activity.launch.domain.HandleIntentUseCase
import co.thewordlab.luzia.ui.activity.launch.domain.IntentResult
import co.theworldlab.luzia.features.settings.domain.models.ModeTheme
import co.theworldlab.luzia.features.settings.domain.repository.SettingsRepository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertIs

class LaunchViewModelTest {

    private val themeMode = MutableStateFlow(0)
    private val forceUpdateFlow = MutableStateFlow(false)
    private lateinit var settingsRepository: SettingsRepository
    private lateinit var deviceManagement: DeviceManagement
    private lateinit var viewModel: LaunchViewModel

    private val userSessionManager: UserSessionManager = mockk()
    private val handleIntentUseCase: HandleIntentUseCase = mockk()
    private val analytics: Analytics = mockk(relaxUnitFun = true)
    private val testDispatcher = StandardTestDispatcher()

    @Before
    fun setup() {
        settingsRepository = mockk { every { themeModeFlow } returns themeMode }
        deviceManagement = mockk { every { getAdvertisingId() } returns DEVICE_ID }
        viewModel = LaunchViewModel(
            dispatcher = testDispatcher,
            settingsRepository = settingsRepository,
            userSessionManager = userSessionManager,
            handleIntentUseCase = handleIntentUseCase,
            analytics = analytics,
            deviceManagement = deviceManagement
        )
    }

    @Test
    fun `onCreate with existing session should navigate to home`() = runTest {
        val intent = mockk<Intent>()
        coEvery { handleIntentUseCase(intent) } returns IntentResult.OK
        coEvery { userSessionManager.userSession } returns flowOf(session)

        viewModel.onViewAction(LaunchViewActions.OnCreate(intent))
        testDispatcher.scheduler.advanceUntilIdle()

        coVerify { handleIntentUseCase(intent) }
        coVerify { userSessionManager.userSession }
        assertEquals(
            LaunchViewEvents.NavigateToHome(excludeDeeplink = false),
            viewModel.viewEvent.first()
        )
        verify { analytics.setUserIdentification(identification) }
    }

    @Test
    fun `onCreate with no session should navigate to login`() = runTest {
        val intent = mockk<Intent>()
        coEvery { handleIntentUseCase(intent) } returns IntentResult.OK
        coEvery { userSessionManager.userSession } returns flowOf(null)

        viewModel.onViewAction(LaunchViewActions.OnCreate(intent))
        testDispatcher.scheduler.advanceUntilIdle()

        assertEquals(
            LaunchViewEvents.NavigateToFullUserLogin(excludeDeeplink = false),
            viewModel.viewEvent.first()
        )
    }

    @Test
    fun `force update should update state when needed`() = runTest {
        val intent = mockk<Intent>()
        coEvery { handleIntentUseCase(intent) } returns IntentResult.OK
        coEvery { userSessionManager.userSession } returns flowOf(session)

        viewModel.onViewAction(LaunchViewActions.OnCreate(intent))
        testDispatcher.scheduler.advanceUntilIdle()
        forceUpdateFlow.value = true
        testDispatcher.scheduler.advanceUntilIdle()

        verify { analytics.setUserIdentification(identification) }
    }

    @Test
    fun `theme changes should update state`() = runTest {
        val intent = mockk<Intent>()
        coEvery { handleIntentUseCase(intent) } returns IntentResult.OK
        coEvery { userSessionManager.userSession } returns flowOf(session)

        viewModel.onViewAction(LaunchViewActions.OnCreate(intent))
        testDispatcher.scheduler.advanceUntilIdle()
        themeMode.value = ModeTheme.DarkMode().id
        testDispatcher.scheduler.advanceUntilIdle()

        assertIs<ModeTheme.DarkMode>(viewModel.viewState.value.theme)
        verify { analytics.setUserIdentification(identification) }
    }

    private companion object {
        const val DEVICE_ID = "device-id"
        val session = UserSession(
            "access-token",
            "refresh-token",
            "device-key",
            "session-id",
            "master-user-id",
            UserType.GUEST
        )
        val identification = UserIdentification(
            userAddId = DEVICE_ID,
            userSessionId = session.sessionId,
            externalDeviceId = session.deviceKey,
        )
    }
}
