package co.thewordlab.luzia.ui.service.overlay.data

import androidx.datastore.preferences.core.Preferences
import co.thewordlab.fouundation.persistence.LuziaDataStore
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.slot
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

@ExperimentalCoroutinesApi
class ShouldPromoteOverlayUseCaseTest {

    private lateinit var shouldPromoteOverlayUseCase: ShouldPromoteOverlayUseCase
    private val dataStore = mockk<LuziaDataStore>(relaxed = true)
    private val featureFlagManager = mockk<FeatureFlagManager>()

    @Before
    fun setup() {
        shouldPromoteOverlayUseCase = ShouldPromoteOverlayUseCase(
            dataStore = dataStore,
            featureFlagManager = featureFlagManager
        )
    }

    @Test
    fun `Given feature flag enabled and preference true When invoke Then returns true`() = runTest {
        // Given
        coEvery { featureFlagManager.getImmediate<Boolean>(FeatureFlag.OverlayEnabled) } returns true
        coEvery { dataStore.getData<Boolean>(match { it.name == "promoteOverlayLuzia" }) } returns true

        // When
        val result = shouldPromoteOverlayUseCase.invoke()

        // Then
        assertEquals(true, result)
        coVerify { dataStore.getData<Boolean>(match { it.name == "promoteOverlayLuzia" }) }
        coVerify { featureFlagManager.getImmediate<Boolean>(FeatureFlag.OverlayEnabled) }
    }

    @Test
    fun `Given feature flag enabled and preference false When invoke Then returns false`() = runTest {
        // Given
        coEvery { featureFlagManager.getImmediate<Boolean>(FeatureFlag.OverlayEnabled) } returns true
        coEvery { dataStore.getData<Boolean>(match { it.name == "promoteOverlayLuzia" }) } returns false

        // When
        val result = shouldPromoteOverlayUseCase.invoke()

        // Then
        assertEquals(false, result)
        coVerify { dataStore.getData<Boolean>(match { it.name == "promoteOverlayLuzia" }) }
        coVerify { featureFlagManager.getImmediate<Boolean>(FeatureFlag.OverlayEnabled) }
    }

    @Test
    fun `Given feature flag disabled and preference true When invoke Then returns false`() = runTest {
        // Given
        coEvery { featureFlagManager.getImmediate<Boolean>(FeatureFlag.OverlayEnabled) } returns false
        coEvery { dataStore.getData<Boolean>(match { it.name == "promoteOverlayLuzia" }) } returns true

        // When
        val result = shouldPromoteOverlayUseCase.invoke()

        // Then
        assertEquals(false, result)
        coVerify(exactly = 0) { dataStore.getData<Boolean>(match { it.name == "promoteOverlayLuzia" }) }
        coVerify { featureFlagManager.getImmediate<Boolean>(FeatureFlag.OverlayEnabled) }
    }

    @Test
    fun `Given feature flag disabled and preference false When invoke Then returns false`() = runTest {
        // Given
        coEvery { featureFlagManager.getImmediate<Boolean>(FeatureFlag.OverlayEnabled) } returns false
        coEvery { dataStore.getData<Boolean>(match { it.name == "promoteOverlayLuzia" }) } returns false

        // When
        val result = shouldPromoteOverlayUseCase.invoke()

        // Then
        assertEquals(false, result)
        coVerify(exactly = 0) { dataStore.getData<Boolean>(match { it.name == "promoteOverlayLuzia" }) }
        coVerify { featureFlagManager.getImmediate<Boolean>(FeatureFlag.OverlayEnabled) }
    }

    @Test
    fun `Given preference not set Then uses default value true and returns based on feature flag`() = runTest {
        // Given
        coEvery { featureFlagManager.getImmediate<Boolean>(FeatureFlag.OverlayEnabled) } returns true
        coEvery { dataStore.getData<Boolean>(match { it.name == "promoteOverlayLuzia" }) } returns null

        // When
        val result = shouldPromoteOverlayUseCase.invoke()

        // Then
        assertEquals(true, result)
        coVerify { dataStore.getData<Boolean>(match { it.name == "promoteOverlayLuzia" }) }
        coVerify { featureFlagManager.getImmediate<Boolean>(FeatureFlag.OverlayEnabled) }
    }

    @Test
    fun `Given dataStore When setPromoted Then saves false to dataStore`() = runTest {
        // Given
        val keySlot = slot<Preferences.Key<Boolean>>()
        val valueSlot = slot<Boolean>()
        coEvery { dataStore.saveData(capture(keySlot), capture(valueSlot)) } returns Unit

        // When
        shouldPromoteOverlayUseCase.setPromoted()

        // Then
        assertEquals("promoteOverlayLuzia", keySlot.captured.name)
        assertEquals(false, valueSlot.captured)
    }
}
