package co.thewordlab.luzia.ui.service.overlay.info

import app.cash.turbine.test
import co.thewordlab.luzia.ui.service.overlay.data.ShouldPromoteOverlayUseCase
import co.theworldlab.luzia.features.settings.domain.repository.SettingsRepository
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Test
import kotlin.test.assertEquals

@ExperimentalCoroutinesApi
class OverlayInfoViewModelTest {

    private val shouldPromoteOverlayUseCase: ShouldPromoteOverlayUseCase =
        mockk(relaxUnitFun = true)
    private val settingsRepository: SettingsRepository = mockk(relaxUnitFun = true)
    val viewModel = OverlayInfoViewModel(shouldPromoteOverlayUseCase, settingsRepository)

    @Test
    fun `Given viewModel When OnCtaClicked Then sends NavigateToOverlaySettings and NavigateBack events`() =
        runTest {
            viewModel.onViewAction(OverlayInfoViewActions.OnCtaClicked)
            coVerify {
                shouldPromoteOverlayUseCase.setPromoted()
                settingsRepository.updateOverlayChoice(true)
            }
        }

    @Test
    fun `Given viewModel When OnDismiss Then sends NavigateBack event`() = runTest {
        viewModel.viewEvent.test {
            viewModel.onViewAction(OverlayInfoViewActions.OnDismiss)
            assertEquals(OverlayInfoViewEvents.NavigateBack, awaitItem())
            cancelAndIgnoreRemainingEvents()
        }
        coVerify { shouldPromoteOverlayUseCase.setPromoted() }
    }
}
