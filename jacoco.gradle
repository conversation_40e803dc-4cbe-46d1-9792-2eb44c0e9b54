apply from: '../jacoco-exclusions.gradle'

project.afterEvaluate { project ->
    setupAndroidReporting()
}

def setupAndroidReporting() {
    tasks.withType(Test) {
        // Whether or not classes without source location should be instrumented
        jacoco.includeNoLocationClasses = true
        jacoco.excludes = ['jdk.internal.*']
    }

    // Grab all build types and product flavors
    def buildTypes = android.buildTypes.collect { type ->
        type.name
    }
    def productFlavors = android.productFlavors.collect { flavor ->
        flavor.name
    }
    // When no product flavors defined, use empty
    if (!productFlavors) productFlavors.add('')
    productFlavors.each { productFlavorName ->
        buildTypes.each { buildTypeName ->
            def sourceName, sourcePath
            if (!productFlavorName) {
                sourceName = sourcePath = "${buildTypeName}"
            } else {
                sourceName = "${productFlavorName}${buildTypeName.capitalize()}"
                sourcePath = "${productFlavorName}/${buildTypeName}"
            }
            def testTaskName = "test${sourceName.capitalize()}UnitTest"
            System.out.println("Task -> $testTaskName")
            System.out.println("Task -> $testTaskName")

            // Create coverage task of form 'testFlavorTypeCoverage' depending on 'testFlavorTypeUnitTest'
            tasks.create(name: "${testTaskName}Coverage", type: JacocoReport, dependsOn: [
                    "$testTaskName",
                    "api:testDebugUnitTests",
                    "app:testDebugUnitTest",
                    "data:testDebugUnitTest",
                    "viewmodel:testDebugUnitTest",
            ]) {
                group = "Reporting"
                description = "Generate Jacoco coverage reports on the ${sourceName.capitalize()} build."

                def javaTree = fileTree(dir: "${project.buildDir}/intermediates/javac/$sourceName/classes", excludes: excludes)
                def kotlinTree = fileTree(dir: "${project.buildDir}/tmp/kotlin-classes/$sourceName", excludes: excludes)
                classDirectories.from = files([javaTree], [kotlinTree])
                executionData.from = files("${project.buildDir}/jacoco/${testTaskName}.exec")
                def coverageSourceDirs = ["src/main/java",
                                          "src/$productFlavorName/java",
                                          "src/$buildTypeName/java"]

                sourceDirectories.setFrom(files(coverageSourceDirs))
                additionalSourceDirs.setFrom(files(coverageSourceDirs))

                reports {
                    csv {
                        enabled true
                    }
                    xml {
                        enabled true
                        destination file("${buildDir}/coverage-report/report_xml.xml")
                    }
                    html {
                        enabled true
                        destination file("${buildDir}/coverage-report")
                    }
                }
            }
        }
    }
}
