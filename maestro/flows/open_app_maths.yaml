appId: co.thewordlab.luzia.dev
---
- runFlow:
      file: subflows/guest_login.yaml

- tapOn:
    id: "buttonTools"

- assertVisible:
    id: containerTools

- scrollUntilVisible:
    element:
      id: "item/math"
    centerElement: true

- tapOn:
    id: "item/math"

- assertVisible:
    id: containerMaths

- tapOn:
    id: "buttonUploadPicture"

- assertVisible:
    id: containerCaptureImage

- tapOn:
    id: "buttonCameraCapture"

- assertVisible:
    id: containerPreviewImage

- tapOn:
    id: "buttonOk"

- tapOn:
    id: buttonNavBack

- tapOn:
    text: "Exit"

- assertVisible:
    id: containerTools

- tapOn:
    id: "item/math"

- tapOn:
    id: "inputBar"
- inputText: "what is 2x+10 = 40"
- tapOn:
    id: "buttonSend"

- hideKeyboard

- assertVisible:
    id: messageView.+
    index: 2

- tapOn:
    id: buttonNavBack

- tapOn:
    text: "Exit"

- tapOn:
    id: "bottomBarTools"

- assertVisible:
    id: containerTools

- scrollUntilVisible:
    element:
      id: "item/math"
    centerElement: true

- tapOn:
    id: "item/math"

- assertVisible:
    id: containerMaths

- tapOn:
    id: "inputBar"
- inputText: "what is 2x+10 = 40"
- tapOn:
    id: "buttonSend"

- hideKeyboard

- assertVisible:
    id: messageView.+
    index: 2