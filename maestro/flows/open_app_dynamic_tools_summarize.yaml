appId: co.thewordlab.luzia.dev
---
- stopApp
- clearState
- launchApp:
      arguments:
          dynamic_tools_treatment: "a"

- tapOn:
      id: "button_close"

- tapOn:
    id: "bottomBarTools"

- assertVisible:
    id: containerTools

- scrollUntilVisible:
    element:
      id: "item/summarize"

- tapOn:
    id: "item/summarize"

- assertVisible:
    id: containerDynamicTools

- assertVisible:
    id: titleContent

- assertVisible:
    id: inputContent

- assertVisible:
    id: selector2

- assertVisible:
    id: selector3

- assertVisible:
    id: selector4

- tapOn:
    id: "inputField"

- inputText: "what is 2x2"

- tapOn:
    id: "selector2"

- assertVisible:
    id: dynamicToolsSelectorContent

- tapOn:
    id: "buttonSelectorClose"

- tapOn:
    id: "buttonContinue"

- assertVisible:
    id: containerDynamicToolsResult

- assertVisible:
    id: titleContent

- assertVisible:
    id: htmlContent


