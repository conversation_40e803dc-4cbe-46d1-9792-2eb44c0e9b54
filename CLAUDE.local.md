- Whenever you want to create a PR use this instruction:
  If I give you the ticket like this: https://luzia.atlassian.net/browse/ERT-274
  You read the Jira ticket with your tools, then use its title to create the PR like this:

  Jira ticket title: [Android] Implement Feature Flag
  Jira Description: Feature Flag: prr_v2_enabled
  branch name: feature/ERT-274-implement-feature-flag
  PR title: [ERT-274] Implement Feature Flag for ppr 2
  PR description: first line would be the link to Jira ticket:https://luzia.atlassian.net/browse/ERT-274, then explanation about what you did!
  also we choose the title based on summary of implementation.
  **IMPORTANT** Don't forget to include a gif at the end of description (Find a giphy url that is related to the PR) (like this <img src="https://media4.giphy.com/media/v1.Y2lkPWJkM2VhNTdlOHZlOHpsdjdqeG11ZGl1am4zYmpzd3ExcnRyOXNiZmRwaGpxd2x1bCZlcD12MV9naWZzX3RyZW5kaW5nJmN0PWc/QydOVgahElhEYVih6Q/giphy.gif"/>)

- Before committing the code, run detekt and unit tests first and ensure it passes
  Command to run detekt: `./gradlew detekt`
- Command to run all the unit tests: `./gradlew testDevDebugUnitTest`
  (You don't need to run these commands if we are in development, just when I asked to commit the changes then run them)

**IMPORTANT**
Check the file names inside ai/experience/ md files and load relevant data to your current task to know better about our past experiences
Whenever you learn something about project, if we developed something and if I told you that you are doing wrong and you learned something from me, save it as a experience md file inside ai/experience/

Always consider reading /docs to find relative information about the module you need!

**Testing Guidelines**
- Don't use try-catch blocks in test files