/*
 * Copyright 2022 The Android Open Source Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       https://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

import co.thewordlab.luzia.configureDetekt
import com.android.build.api.dsl.ApplicationExtension
import com.android.build.api.dsl.LibraryExtension
import com.android.build.api.dsl.Lint
import io.gitlab.arturbosch.detekt.extensions.DetektExtension
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.getByType

class AndroidLintConventionPlugin : Plugin<Project> {
	override fun apply(target: Project) {
		with(target) {
			with(pluginManager) {
				apply("io.gitlab.arturbosch.detekt")
			}
			when {
				pluginManager.hasPlugin("com.android.application") ->
					configure<ApplicationExtension> { lint(Lint::configure) }

				pluginManager.hasPlugin("com.android.library") ->
					configure<LibraryExtension> { lint(Lint::configure) }

				else -> {
					pluginManager.apply("com.android.lint")
					configure<Lint>(Lint::configure)
				}
			}
			val extension = extensions.getByType<DetektExtension>()
			configureDetekt(extension)
		}
	}
}

private fun Lint.configure() {
	xmlReport = true
	checkDependencies = true
}
