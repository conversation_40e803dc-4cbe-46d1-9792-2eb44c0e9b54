/*
 * Copyright 2022 The Android Open Source Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       https://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

import com.android.build.gradle.LibraryExtension
import co.thewordlab.luzia.configureGradleManagedDevices
import co.thewordlab.luzia.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies

class AndroidFeatureConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            pluginManager.apply {
                apply("luzia.android.library")
                apply("luzia.android.library.compose")
                apply("luzia.android.library.jacoco")
                apply("luzia.android.hilt")
            }
            extensions.configure<LibraryExtension> {
                defaultConfig {
                    testInstrumentationRunner =
                        "co.thewordlab.luzia.core.testing.NiaTestRunner"
                }
                testOptions.animationsDisabled = true
                configureGradleManagedDevices(this)
            }

            dependencies {
                add("implementation", project(":foundation:design-system"))
                add("implementation", project(":foundation:localization"))
                add("implementation", project(":foundation:persistence"))
                add("implementation", project(":foundation:networking"))
                add("implementation", project(":foundation:common"))
                add("implementation", project(":foundation:config"))
                add("implementation", project(":foundation:architecture-system"))

                add("implementation", libs.findLibrary("androidx.hilt.navigation.compose").get())
                add("implementation", libs.findLibrary("androidx.lifecycle.runtimeCompose").get())
                add("implementation", libs.findLibrary("androidx.lifecycle.viewModelCompose").get())
                add("implementation", libs.findLibrary("androidx.tracing.ktx").get())
                add("implementation", libs.findLibrary("retrofit-core").get())
                add("implementation", libs.findLibrary("slack-eithernet").get())
                add("implementation", libs.findLibrary("moshi-core").get())
                add("implementation", libs.findLibrary("moshi-kotlin").get())
                add("implementation", libs.findLibrary("moshi-adapters").get())
                add("ksp", libs.findLibrary("moshi-codegen").get())
                add("androidTestImplementation", libs.findLibrary("androidx.lifecycle.runtimeTesting").get())
            }
        }
    }
}
