# Module Creation Guide

This guide provides step-by-step instructions for creating new modules in the Luzia Android application, with verified examples based on actual codebase implementations.

## Module Architecture Overview

```mermaid
graph TD
    A[Features Layer] --> B[Core Layer]
    B --> C[Foundation Layer]
    D[App Module] --> A
    
    subgraph "Features Layer"
        F1[features/home]
        F2[features/chat]
        F3[features/profile]
        F4[features/tools]
    end
    
    subgraph "Core Layer"
        C1[core/chat]
        C2[core/profile]
        C3[core/gamification]
        C4[core/navigation]
    end
    
    subgraph "Foundation Layer"
        FN1[foundation/networking]
        FN2[foundation/persistence]
        FN3[foundation/design-system]
        FN4[foundation/architecture-system]
    end
    
    F1 --> C1
    F1 --> C2
    C1 --> FN1
    C1 --> FN2
    C2 --> FN1
    C3 --> FN2
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
```

## Module Types and Conventions

### Foundation Layer (`foundation/`)
- **Purpose**: Core infrastructure and shared utilities
- **Plugin**: `alias(libs.plugins.luzia.android.library)`
- **Package**: `co.thewordlab.luzia.foundation.{module-name}`
- **Dependencies**: Android framework, third-party libraries only
- **Examples**: `networking`, `persistence`, `design-system`, `analytics`

### Core Layer (`core/`)
- **Purpose**: Business logic and domain functionality  
- **Plugin**: `alias(libs.plugins.luzia.android.feature)` (includes Hilt, networking, testing)
- **Package**: `co.thewordlab.luzia.core.{module-name}`
- **Dependencies**: Foundation modules only, NO UI dependencies
- **Examples**: `chat`, `gamification`, `profile`, `tools`

### Features Layer (`features/`)
- **Purpose**: User-facing screens and UI logic
- **Plugin**: `alias(libs.plugins.luzia.android.feature)`  
- **Package**: `co.thewordlab.luzia.features.{feature-name}`
- **Dependencies**: Core and foundation modules, Compose UI
- **Examples**: `home`, `chat`, `profile`, `settings`

## Convention Plugin System

The build system uses convention plugins located in `build-logic/convention/src/main/kotlin/`:

### Available Convention Plugins

// File: build-logic/convention/src/main/kotlin/AndroidFeatureConventionPlugin.kt:25-66
```kotlin
class AndroidFeatureConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            pluginManager.apply {
                apply("luzia.android.library")
                apply("luzia.android.library.compose")
                apply("luzia.android.library.jacoco")
                apply("luzia.android.hilt")
            }
            
            dependencies {
                add("implementation", project(":foundation:design-system"))
                add("implementation", project(":foundation:localization"))
                add("implementation", project(":foundation:persistence"))
                add("implementation", project(":foundation:networking"))
                add("implementation", project(":foundation:common"))
                add("implementation", project(":foundation:config"))
                add("implementation", project(":foundation:architecture-system"))
                // ... additional dependencies
            }
        }
    }
}
```

### Dependency Management

Libraries are managed in `gradle/libs.versions.toml`:

// File: gradle/libs.versions.toml:264-278
```toml
# Plugins defined by this project
luzia-android-application = { id = "luzia.android.application", version = "unspecified" }
luzia-android-application-compose = { id = "luzia.android.application.compose", version = "unspecified" }
luzia-android-feature = { id = "luzia.android.feature", version = "unspecified" }
luzia-android-hilt = { id = "luzia.android.hilt", version = "unspecified" }
luzia-android-library = { id = "luzia.android.library", version = "unspecified" }
luzia-android-library-compose = { id = "luzia.android.library.compose", version = "unspecified" }
luzia-android-library-jacoco = { id = "luzia.android.library.jacoco", version = "unspecified" }
luzia-android-room = { id = "luzia.android.room", version = "unspecified" }
```

## Creating Foundation Modules

Foundation modules provide infrastructure services used across the application.

### Step 1: Create Module Directory Structure

```bash
# Foundation module structure
mkdir -p foundation/new-module/src/main/java/co/thewordlab/luzia/foundation/newmodule
mkdir -p foundation/new-module/src/test/java/co/thewordlab/luzia/foundation/newmodule
```

### Step 2: Foundation Module build.gradle.kts

// File: foundation/analytics/build.gradle.kts:1-28
```kotlin
plugins {
    alias(libs.plugins.luzia.android.library)
    alias(libs.plugins.luzia.android.library.compose)
    alias(libs.plugins.luzia.android.hilt)
    alias(libs.plugins.luzia.android.library.jacoco)
}

android {
    namespace = "co.thewordlab.luzia.foundation.newmodule"
}

dependencies {
    implementation(projects.foundation.securelib)
    implementation(libs.amplitude.analytics)
    // Foundation-specific dependencies only
    
    testImplementation(projects.foundation.testing)
}
```

### Step 3: Foundation Module Implementation

```kotlin
// foundation/new-module/src/main/java/co/thewordlab/luzia/foundation/newmodule/NewModuleService.kt

package co.thewordlab.luzia.foundation.newmodule

/**
 * Foundation service for [specific functionality]
 * Used across multiple features in the Luzia app.
 */
interface NewModuleService {
    suspend fun performOperation(): String
    fun getConfiguration(): ServiceConfig
}

internal class NewModuleServiceImpl @Inject constructor(
    private val secureStorage: SecureStorage
) : NewModuleService {
    
    override suspend fun performOperation(): String {
        return try {
            // Foundation-level implementation
            "Operation completed"
        } catch (e: Exception) {
            throw NewModuleException("Operation failed", e)
        }
    }
    
    override fun getConfiguration(): ServiceConfig {
        return ServiceConfig(
            apiKey = secureStorage.getKey(SecureKey.NEW_MODULE_API_KEY),
            enabled = true
        )
    }
}

data class ServiceConfig(
    val apiKey: String,
    val enabled: Boolean
)

class NewModuleException(message: String, cause: Throwable) : Exception(message, cause)
```

### Step 4: Hilt Module for Foundation

```kotlin
// foundation/new-module/src/main/java/co/thewordlab/luzia/foundation/newmodule/di/NewModuleModule.kt

@Module
@InstallIn(SingletonComponent::class)
abstract class NewModuleModule {
    
    @Binds
    abstract fun bindNewModuleService(
        impl: NewModuleServiceImpl
    ): NewModuleService
    
    companion object {
        @Provides
        @Singleton
        fun provideNewModuleConfig(): ServiceConfig {
            return ServiceConfig(
                apiKey = "default_key",
                enabled = true
            )
        }
    }
}
```

### Step 5: Add Module to settings.gradle.kts

```kotlin
// settings.gradle.kts
include(":foundation:new-module")
```

## Creating Core Modules

Core modules contain business logic and domain functionality following clean architecture.

### Step 1: Core Module Structure

```mermaid
graph TD
    A[Core Module] --> B[Domain Layer]
    A --> C[Data Layer]
    A --> D[DI Layer]
    
    B --> E[Models]
    B --> F[Repository Interfaces]
    B --> G[Use Cases]
    
    C --> H[Repository Implementation]
    C --> I[API Services]
    C --> J[Local Data Sources]
    C --> K[Mappers]
```

### Step 2: Core Module build.gradle.kts

// File: core/chat/build.gradle.kts:1-29
```kotlin
plugins {
    alias(libs.plugins.luzia.android.feature)
}

android {
    namespace = "co.thewordlab.luzia.core.newmodule"
    
    testOptions {
        unitTests {
            isIncludeAndroidResources = true
        }
    }
}

dependencies {
    implementation(libs.androidx.dataStore.core)
    implementation(libs.androidx.dataStore.preferences)
    implementation(projects.core.navigation)
    implementation(projects.foundation.analytics)
    implementation(projects.foundation.networking)
    implementation(projects.foundation.messages)
    
    testImplementation(projects.foundation.testing)
}
```

### Step 3: Domain Models

```kotlin
// core/new-module/src/main/java/co/thewordlab/luzia/core/newmodule/domain/model/DomainModel.kt

package co.thewordlab.luzia.core.newmodule.domain.model

/**
 * Core domain model for [business functionality]
 * Represents the main entity in this module's domain.
 */
data class DomainModel(
    val id: String,
    val name: String,
    val status: ModelStatus,
    val createdAt: Long,
    val updatedAt: Long
)

enum class ModelStatus {
    ACTIVE,
    INACTIVE,
    PENDING;
    
    companion object {
        fun fromString(value: String): ModelStatus {
            return entries.find { it.name.equals(value, ignoreCase = true) } ?: PENDING
        }
    }
}

sealed class DomainError : Exception() {
    object NetworkError : DomainError()
    object NotFound : DomainError()
    data class ValidationError(val field: String) : DomainError()
}
```

### Step 4: Repository Pattern

```kotlin
// core/new-module/src/main/java/co/thewordlab/luzia/core/newmodule/domain/repository/DomainRepository.kt

package co.thewordlab.luzia.core.newmodule.domain.repository

import co.thewordlab.luzia.core.newmodule.domain.model.DomainModel
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import kotlinx.coroutines.flow.Flow

interface DomainRepository {
    fun getAllModels(): Flow<List<DomainModel>>
    suspend fun getModel(id: String): ResultOf<DomainModel?, DomainError>
    suspend fun saveModel(model: DomainModel): ResultOf<Unit, DomainError>
    suspend fun deleteModel(id: String): ResultOf<Unit, DomainError>
    suspend fun syncWithRemote(): ResultOf<Unit, DomainError>
}
```

### Step 5: Data Implementation

```kotlin
// core/new-module/src/main/java/co/thewordlab/luzia/core/newmodule/data/DomainRepositoryImpl.kt

package co.thewordlab.luzia.core.newmodule.data

import co.thewordlab.luzia.core.newmodule.data.local.DomainDao
import co.thewordlab.luzia.core.newmodule.data.mapper.DomainMapper
import co.thewordlab.luzia.core.newmodule.data.remote.DomainApi
import co.thewordlab.luzia.core.newmodule.domain.model.DomainModel
import co.thewordlab.luzia.core.newmodule.domain.repository.DomainRepository
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class DomainRepositoryImpl @Inject constructor(
    private val api: DomainApi,
    private val dao: DomainDao,
    private val mapper: DomainMapper
) : DomainRepository {
    
    override fun getAllModels(): Flow<List<DomainModel>> =
        dao.getAllModels().map { entities ->
            entities.map(mapper::toDomain)
        }
    
    override suspend fun getModel(id: String): ResultOf<DomainModel?, DomainError> =
        try {
            val entity = dao.getModel(id)
            ResultOf.Success(entity?.let(mapper::toDomain))
        } catch (e: Exception) {
            ResultOf.Failure(DomainError.NetworkError)
        }
    
    override suspend fun saveModel(model: DomainModel): ResultOf<Unit, DomainError> =
        try {
            dao.insertModel(mapper.toEntity(model))
            ResultOf.Success(Unit)
        } catch (e: Exception) {
            ResultOf.Failure(DomainError.ValidationError("save"))
        }
    
    override suspend fun deleteModel(id: String): ResultOf<Unit, DomainError> =
        try {
            dao.deleteModel(id)
            ResultOf.Success(Unit)
        } catch (e: Exception) {
            ResultOf.Failure(DomainError.NotFound)
        }
    
    override suspend fun syncWithRemote(): ResultOf<Unit, DomainError> =
        try {
            val remoteModels = api.getModels()
            val entities = remoteModels.map(mapper::toEntity)
            dao.insertModels(entities)
            ResultOf.Success(Unit)
        } catch (e: Exception) {
            ResultOf.Failure(DomainError.NetworkError)
        }
}
```

## Creating Feature Modules

Feature modules contain UI screens using the MVVM+MVI pattern with interface delegation.

### Step 1: Feature Module Structure

```mermaid
graph TD
    A[Feature Module] --> B[Presentation Layer]
    B --> C[ViewModels with Delegation]
    B --> D[Compose Screens]
    B --> E[Navigation]
    
    C --> F[ViewModelActions Interface]
    C --> G[ViewModelStates Interface]
    C --> H[ViewModelEvents Interface]
    
    style C fill:#e1f5fe
    style F fill:#f3e5f5
    style G fill:#f3e5f5
    style H fill:#f3e5f5
```

### Step 2: Feature Module build.gradle.kts

// File: features/home/<USER>
```kotlin
plugins {
    alias(libs.plugins.luzia.android.feature)
}

android {
    namespace = "co.thewordlab.luzia.features.newfeature"
}

dependencies {
    implementation(libs.androidx.dataStore.preferences)
    implementation(projects.core.navigation)
    implementation(projects.core.profile)
    implementation(projects.core.newModule) // The core module we created
    implementation(projects.foundation.analytics)
    implementation(projects.foundation.files)
    implementation(projects.foundation.architectureSystem)
    
    testImplementation(projects.foundation.testing)
}
```

### Step 3: MVVM+MVI Contract

```kotlin
// features/new-feature/src/main/java/co/thewordlab/luzia/features/newfeature/NewFeatureContract.kt

package co.thewordlab.luzia.features.newfeature

import co.thewordlab.luzia.core.newmodule.domain.model.DomainModel
import co.thewordlab.luzia.foundation.architecture.system.ViewAction
import co.thewordlab.luzia.foundation.architecture.system.ViewEvent
import co.thewordlab.luzia.foundation.architecture.system.ViewState

data class NewFeatureViewState(
    val models: List<DomainModel> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null,
    val selectedModel: DomainModel? = null,
    val showDialog: Boolean = false
) : ViewState

sealed class NewFeatureViewAction : ViewAction {
    object LoadModels : NewFeatureViewAction()
    data class SelectModel(val model: DomainModel) : NewFeatureViewAction()
    data class SaveModel(val model: DomainModel) : NewFeatureViewAction()
    data class DeleteModel(val id: String) : NewFeatureViewAction()
    object RetryLoading : NewFeatureViewAction()
    object DismissError : NewFeatureViewAction()
    object DismissDialog : NewFeatureViewAction()
}

sealed class NewFeatureViewEvent : ViewEvent {
    object ModelSaved : NewFeatureViewEvent()
    object ModelDeleted : NewFeatureViewEvent()
    data class ShowError(val message: String) : NewFeatureViewEvent()
    data class NavigateToDetail(val modelId: String) : NewFeatureViewEvent()
}
```

### Step 4: ViewModel with Interface Delegation

// File: features/home/<USER>/main/java/co/thewordlab/luzia/features/home/<USER>/HomeViewModel.kt:93-96
```kotlin
@HiltViewModel
class NewFeatureViewModel @Inject constructor(
    private val domainRepository: DomainRepository,
    private val analytics: Analytics
) : ViewModel(),
    ViewModelActions<NewFeatureViewAction>,
    ViewModelEvents<NewFeatureViewEvent> by ViewModelEventsImpl(),
    ViewModelStates<NewFeatureViewState> by ViewModelStatesImpl(NewFeatureViewState()) {
    
    init {
        loadModels()
    }
    
    override fun onViewAction(action: NewFeatureViewAction) {
        when (action) {
            NewFeatureViewAction.LoadModels -> loadModels()
            is NewFeatureViewAction.SelectModel -> selectModel(action.model)
            is NewFeatureViewAction.SaveModel -> saveModel(action.model)
            is NewFeatureViewAction.DeleteModel -> deleteModel(action.id)
            NewFeatureViewAction.RetryLoading -> loadModels()
            NewFeatureViewAction.DismissError -> dismissError()
            NewFeatureViewAction.DismissDialog -> dismissDialog()
        }
    }
    
    private fun loadModels() {
        viewModelScope.launch {
            updateState { it.copy(isLoading = true, error = null) }
            
            domainRepository.getAllModels()
                .catch { exception ->
                    updateState { 
                        it.copy(
                            isLoading = false, 
                            error = exception.message ?: "Unknown error"
                        ) 
                    }
                }
                .collect { models ->
                    updateState { 
                        it.copy(
                            models = models, 
                            isLoading = false, 
                            error = null
                        ) 
                    }
                }
        }
    }
    
    private fun selectModel(model: DomainModel) {
        analytics.trackAction(AnalyticsAction.ModelSelected(model.id))
        updateState { it.copy(selectedModel = model, showDialog = true) }
    }
    
    private fun saveModel(model: DomainModel) {
        viewModelScope.launch {
            when (val result = domainRepository.saveModel(model)) {
                is ResultOf.Success -> {
                    sendEvent(NewFeatureViewEvent.ModelSaved)
                    analytics.trackAction(AnalyticsAction.ModelSaved(model.id))
                }
                is ResultOf.Failure -> {
                    sendEvent(NewFeatureViewEvent.ShowError(
                        "Failed to save model: ${result.error}"
                    ))
                }
            }
        }
    }
    
    private fun deleteModel(id: String) {
        viewModelScope.launch {
            when (val result = domainRepository.deleteModel(id)) {
                is ResultOf.Success -> {
                    sendEvent(NewFeatureViewEvent.ModelDeleted)
                    analytics.trackAction(AnalyticsAction.ModelDeleted(id))
                }
                is ResultOf.Failure -> {
                    sendEvent(NewFeatureViewEvent.ShowError("Failed to delete model"))
                }
            }
        }
    }
    
    private fun dismissError() {
        updateState { it.copy(error = null) }
    }
    
    private fun dismissDialog() {
        updateState { it.copy(showDialog = false, selectedModel = null) }
    }
}
```

### Step 5: Compose Screen Implementation

```kotlin
// features/new-feature/src/main/java/co/thewordlab/luzia/features/newfeature/NewFeatureScreen.kt

package co.thewordlab.luzia.features.newfeature

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsState
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme

@Composable
fun NewFeatureScreen(
    onNavigateToDetail: (String) -> Unit,
    viewModel: NewFeatureViewModel = hiltViewModel()
) {
    val viewState by viewModel.viewState.collectAsState()
    
    LaunchedEffect(Unit) {
        viewModel.onViewAction(NewFeatureViewAction.LoadModels)
    }
    
    LaunchedEffect(Unit) {
        viewModel.viewEvent.collect { event ->
            when (event) {
                is NewFeatureViewEvent.NavigateToDetail -> {
                    viewModel.consumeEvent(event) {
                        onNavigateToDetail(it.modelId)
                    }
                }
                is NewFeatureViewEvent.ShowError -> {
                    viewModel.consumeEvent(event) {
                        // Handle error (show snackbar, etc.)
                    }
                }
                NewFeatureViewEvent.ModelSaved -> {
                    viewModel.consumeEvent(event) {
                        // Handle success
                    }
                }
                NewFeatureViewEvent.ModelDeleted -> {
                    viewModel.consumeEvent(event) {
                        // Handle deletion
                    }
                }
            }
        }
    }
    
    NewFeatureContent(
        state = viewState,
        onAction = viewModel::onViewAction
    )
}

@Composable
internal fun NewFeatureContent(
    state: NewFeatureViewState,
    onAction: (NewFeatureViewAction) -> Unit
) {
    LuziaTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            when {
                state.isLoading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                
                state.error != null -> {
                    ErrorContent(
                        error = state.error,
                        onRetry = { onAction(NewFeatureViewAction.RetryLoading) },
                        onDismiss = { onAction(NewFeatureViewAction.DismissError) }
                    )
                }
                
                else -> {
                    ModelsList(
                        models = state.models,
                        onModelClick = { model ->
                            onAction(NewFeatureViewAction.SelectModel(model))
                        }
                    )
                }
            }
            
            if (state.showDialog && state.selectedModel != null) {
                ModelDialog(
                    model = state.selectedModel,
                    onSave = { onAction(NewFeatureViewAction.SaveModel(it)) },
                    onDismiss = { onAction(NewFeatureViewAction.DismissDialog) }
                )
            }
        }
    }
}
```

### Step 6: Navigation Setup

```kotlin
// features/new-feature/src/main/java/co/thewordlab/luzia/features/newfeature/navigation/NewFeatureNavigation.kt

package co.thewordlab.luzia.features.newfeature.navigation

import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import co.thewordlab.luzia.features.newfeature.NewFeatureScreen

const val NEW_FEATURE_ROUTE = "new_feature"

fun NavController.navigateToNewFeature() {
    navigate(NEW_FEATURE_ROUTE)
}

fun NavGraphBuilder.newFeatureScreen(
    onNavigateToDetail: (String) -> Unit
) {
    composable(route = NEW_FEATURE_ROUTE) {
        NewFeatureScreen(
            onNavigateToDetail = onNavigateToDetail
        )
    }
}
```

## Package Naming Conventions

### Verified Package Structure

Based on actual codebase analysis:

**Foundation Modules**:
- `co.thewordlab.luzia.foundation.{module-name}`
- **Exception**: `co.theworldlab.luzia.foundation.design` (note: `theworldlab` vs `thewordlab`)
- **Exception**: `co.thewordlab.fouundation.persistence` (note: `fouundation` vs `foundation`)

**Core Modules**:
- `co.thewordlab.luzia.core.{module-name}`
- **Exception**: `co.thewordlab.luzia.dynamicflow` (core/dynamic-flow uses different structure)

**Features Modules**:
- `co.thewordlab.luzia.features.{feature-name}`
- **Exception**: `co.theworldlab.luzia.features.{feature-name}` for some modules

### Resource Prefixes

// File: build-logic/convention/src/main/kotlin/AndroidLibraryConventionPlugin.kt:51
```kotlin
// Auto-generated resource prefixes to avoid conflicts
resourcePrefix = path.split("""\W""".toRegex()).drop(1).distinct().joinToString(separator = "_").lowercase() + "_"
```

Examples:
- `core/chat` → `core_chat_`
- `foundation/design-system` → `foundation_design_system_`
- `features/home` → `features_home_`

## Testing Strategy

### Foundation Module Tests

```kotlin
// foundation/new-module/src/test/java/co/thewordlab/luzia/foundation/newmodule/NewModuleServiceTest.kt

package co.thewordlab.luzia.foundation.newmodule

import co.thewordlab.luzia.foundation.testing.MainDispatcherRule
import com.google.common.truth.Truth.assertThat
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.Test

class NewModuleServiceTest {
    
    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()
    
    @Test
    fun `service should perform operation successfully`() = runTest {
        // Given
        val service = NewModuleServiceImpl(mockSecureStorage)
        
        // When
        val result = service.performOperation()
        
        // Then
        assertThat(result).isEqualTo("Operation completed")
    }
}
```

### Core Module Tests

```kotlin
// core/new-module/src/test/java/co/thewordlab/luzia/core/newmodule/DomainRepositoryTest.kt

package co.thewordlab.luzia.core.newmodule

import app.cash.turbine.test
import co.thewordlab.luzia.foundation.testing.MainDispatcherRule
import com.google.common.truth.Truth.assertThat
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.Test

class DomainRepositoryTest {
    
    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()
    
    @Test
    fun `repository should emit models from local database`() = runTest {
        // Given
        val repository = DomainRepositoryImpl(mockApi, mockDao, mockMapper)
        
        // When & Then
        repository.getAllModels().test {
            val models = awaitItem()
            assertThat(models).hasSize(2)
            assertThat(models.first().name).isEqualTo("Test Model")
        }
    }
}
```

### Feature Module Tests

```kotlin
// features/new-feature/src/test/java/co/thewordlab/luzia/features/newfeature/NewFeatureViewModelTest.kt

package co.thewordlab.luzia.features.newfeature

import app.cash.turbine.test
import co.thewordlab.luzia.foundation.testing.MainDispatcherRule
import com.google.common.truth.Truth.assertThat
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.Test

class NewFeatureViewModelTest {
    
    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()
    
    @Test
    fun `viewModel should load models on init`() = runTest {
        // Given
        val viewModel = NewFeatureViewModel(mockRepository, mockAnalytics)
        
        // When & Then
        viewModel.viewState.test {
            val initialState = awaitItem()
            assertThat(initialState.isLoading).isTrue()
            
            val loadedState = awaitItem()
            assertThat(loadedState.isLoading).isFalse()
            assertThat(loadedState.models).hasSize(2)
        }
    }
}
```

## Module Creation Workflow

```mermaid
flowchart TD
    A[Start Module Creation] --> B{Choose Module Type}
    
    B -->|Foundation| C[Create Foundation Module]
    B -->|Core| D[Create Core Module] 
    B -->|Feature| E[Create Feature Module]
    
    C --> F[Apply library plugin]
    C --> G[Add minimal dependencies]
    C --> H[Implement service interface]
    C --> I[Create Hilt module]
    
    D --> J[Apply feature plugin]
    D --> K[Add foundation dependencies]
    D --> L[Implement domain layer]
    D --> M[Implement data layer]
    D --> N[Create repository]
    
    E --> O[Apply feature plugin]
    E --> P[Add core dependencies]
    E --> Q[Create MVVM+MVI contract]
    E --> R[Implement ViewModel with delegation]
    E --> S[Create Compose screens]
    E --> T[Setup navigation]
    
    F --> U[Add to settings.gradle.kts]
    J --> U
    O --> U
    
    U --> V[Write tests]
    V --> W[Run quality checks]
    W --> X[Module Complete]
    
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
```

## Best Practices

### 1. Module Naming
- Use **kebab-case** for directories: `new-feature`, `user-profile`
- Use **camelCase** for packages: `newfeature`, `userprofile`  
- Use **PascalCase** for classes: `NewFeature`, `UserProfile`

### 2. Dependency Direction
```mermaid
graph TD
    A[Features] --> B[Core]
    B --> C[Foundation]
    D[App Module] --> A
    
    A -.->|❌ NO| C
    B -.->|❌ NO| A
    C -.->|❌ NO| B
    C -.->|❌ NO| A
```

### 3. Interface Delegation Pattern
Always use the MVVM+MVI pattern with interface delegation:

```kotlin
class YourViewModel : ViewModel(),
    ViewModelActions<YourViewAction>,
    ViewModelEvents<YourViewEvent> by ViewModelEventsImpl(),
    ViewModelStates<YourViewState> by ViewModelStatesImpl(YourViewState())
```

### 4. Error Handling
Use `ResultOf<T, E>` pattern from `foundation/networking`:

```kotlin
when (val result = repository.getData()) {
    is ResultOf.Success -> handleSuccess(result.data)
    is ResultOf.Failure -> handleError(result.error)
}
```

### 5. Analytics Integration
Include analytics in all user-facing actions:

```kotlin
override fun onViewAction(action: ViewAction) {
    when (action) {
        is ViewAction.ButtonClicked -> {
            analytics.trackAction(AnalyticsAction.ButtonClicked(action.buttonId))
            // Handle action
        }
    }
}
```

This guide provides a complete reference for creating modules in the Luzia Android app based on actual implementations and verified patterns from the codebase.