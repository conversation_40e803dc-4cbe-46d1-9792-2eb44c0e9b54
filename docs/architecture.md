# Architecture Overview

This document provides a comprehensive overview of the Luzia Android application architecture, including design patterns, module organization, and key architectural decisions.

## High-Level Architecture

Luzia follows a **multi-module clean architecture** with clear separation of concerns across three main layers:

```mermaid
graph TB
    subgraph "App Module"
        AM[Main Activity<br/>Navigation & DI Setup]
    end
    
    subgraph "Features Layer (UI/UX)"
        F1[Chat<br/>Compose Screens]
        F2[Home<br/>Dashboard UI]
        F3[Settings<br/>Preferences UI]
        F4[Profile<br/>User Management UI]
        F5[Tools<br/>AI Tools UI]
        F6[Imagine<br/>Image Generation UI]
    end
    
    subgraph "Core Layer (Business Logic)"
        C1[Chat<br/>GetStream Integration]
        C2[Gamification<br/>Rewards & Points]
        C3[Notifications<br/>Braze Integration]
        C4[Profile<br/>User Management]
        C5[Tools<br/>AI Services]
        C6[Sharing<br/>Content Sharing]
    end
    
    subgraph "Foundation Layer (Infrastructure)"
        FD1[Networking<br/>HTTP Client & APIs]
        FD2[Persistence<br/>Room Database]
        FD3[Design System<br/>Compose Components]
        FD4[Analytics<br/>Multi-provider Tracking]
        FD5[Architecture System<br/>MVVM+MVI Patterns]
        FD6[Config<br/>Feature Flags]
    end
    
    AM --> F1
    AM --> F2
    AM --> F3
    AM --> F4
    AM --> F5
    AM --> F6
    
    F1 --> C1
    F2 --> C2
    F3 --> C4
    F4 --> C4
    F5 --> C5
    F6 --> C5
    
    C1 --> FD1
    C1 --> FD2
    C2 --> FD2
    C2 --> FD4
    C3 --> FD1
    C4 --> FD1
    C4 --> FD2
    C5 --> FD1
    C6 --> FD1
    
    F1 --> FD3
    F2 --> FD3
    F3 --> FD3
    F4 --> FD3
    F5 --> FD3
    F6 --> FD3
    
    F1 --> FD5
    F2 --> FD5
    F3 --> FD5
    F4 --> FD5
    F5 --> FD5
    F6 --> FD5
```

### Dependency Rules

```mermaid
graph LR
    subgraph "Allowed Dependencies"
        A[App] --> B[Features]
        B --> C[Core]
        C --> D[Foundation]
        B --> D
    end
    
    subgraph "Forbidden Dependencies" 
        E[Foundation] -.->|❌| F[Core]
        G[Core] -.->|❌| H[Features]
        I[Foundation] -.->|❌| J[Features]
    end
```

## Layer Definitions

### Foundation Layer (`foundation/`)
**Purpose**: Core infrastructure and shared utilities that don't contain business logic.

**Characteristics**:
- No UI dependencies
- Reusable across multiple features
- Contains framework integrations
- Minimal external dependencies

**Modules**:
- `analytics` - Multi-provider analytics system
- `architecture-system` - MVVM patterns and base classes
- `common` - Shared utilities and extensions
- `config` - Configuration and feature flags
- `data-cache` - Caching strategies and implementations
- `design-system` - UI components, themes, and design tokens
- `files` - File management utilities
- `localization` - Multi-language support
- `messages` - Message handling and formatting
- `networking` - HTTP client and API management
- `persistence` - Database layer and Room setup
- `securelib` - Security utilities and encryption
- `testing` - Testing utilities and helpers

### Core Layer (`core/`)
**Purpose**: Business logic modules that implement domain-specific functionality.

**Characteristics**:
- Contains business rules and use cases
- Can depend on foundation layer
- No direct UI dependencies
- Domain-focused modules

**Modules**:
- `ads` - Advertisement integration and management
- `bestiepoints` - Gamification points system
- `chat` - Chat business logic and GetStream integration
- `connectivity` - Network connectivity monitoring
- `dynamic-flow` - Dynamic content flows
- `feedback` - User feedback collection and processing
- `gamification` - Rewards, achievements, and progression
- `navigation` - App navigation logic
- `notifications` - Push notifications and Braze integration
- `profile` - User profile management
- `sharing` - Content sharing functionality
- `signup` - User registration and onboarding
- `tools` - AI tools and utilities
- `web` - WebView integration and management

### Features Layer (`features/`)
**Purpose**: User-facing features and UI screens built with Jetpack Compose.

**Characteristics**:
- Contains UI/UX implementation
- Can depend on core and foundation layers
- Compose screens and ViewModels
- User interaction handling

**Modules**:
- `camera` - Camera functionality and UI
- `chat` - Chat screens and conversation UI
- `gamification` - Gamification UI and animations
- `home` - Home screen and dashboard
- `imagine` - AI image generation interface
- `personality` - AI personality selection
- `proactive-messaging` - Proactive messaging features
- `profile` - Profile screens and editing
- `settings` - Settings and preferences UI
- `shortcuts` - App shortcuts and quick actions
- `signup` - Registration and onboarding UI
- `stream` - Streaming features and live content
- `tools` - Tools interface and interactions
- `vision` - Computer vision features and camera integration

## Key Architectural Patterns

### MVVM + MVI with Interface Delegation

The app uses a **sophisticated hybrid architecture** combining MVVM state management with MVI unidirectional data flow, implemented through **interface delegation** instead of inheritance:

```mermaid
graph TB
    subgraph "User Interface (Compose)"
        UI[Compose Screen]
        COMP[UI Components]
    end
    
    subgraph "ViewModel Layer"
        VM[ViewModel<br/>with Delegation]
        STATES[ViewModelStatesImpl<br/>StateFlow Management]
        EVENTS[ViewModelEventsImpl<br/>Event Management]
        ACTIONS[ViewModelActions<br/>Action Handler]
    end
    
    subgraph "Business Layer"
        REPO[Repository<br/>Data Source]
        UC[Use Cases<br/>Business Logic]
    end
    
    subgraph "Data Layer"
        API[Network API]
        DB[Local Database]
        CACHE[Cache Layer]
    end
    
    UI -->|User Interaction| ACTIONS
    ACTIONS -->|onViewAction| VM
    VM -->|Business Logic| UC
    UC -->|Data Operations| REPO
    REPO --> API
    REPO --> DB
    REPO --> CACHE
    
    REPO -->|Flow<Data>| VM
    VM -->|updateState| STATES
    VM -->|sendEvent| EVENTS
    
    STATES -->|StateFlow<ViewState>| UI
    EVENTS -->|Flow<ViewEvent>| UI
    UI --> COMP
```

#### Delegation Pattern Detail

```mermaid
classDiagram
    class ViewModel {
        +ViewModel()
    }
    
    class ViewModelActions~VA~ {
        <<interface>>
        +onViewAction(action: VA)
    }
    
    class ViewModelStates~VS~ {
        <<interface>>
        +viewState: StateFlow~VS~
        +updateState(newState: VS)
        +updateState(function: VS → VS)
    }
    
    class ViewModelEvents~VE~ {
        <<interface>>
        +viewEvent: Flow~VE~
        +sendEvent(event: VE)
        +consumeEvent(event: VE, callback)
    }
    
    class ViewModelStatesImpl~VS~ {
        -mutableViewState: MutableStateFlow~VS~
        +viewState: StateFlow~VS~
        +updateState(newState: VS)
        +updateState(function: VS → VS)
    }
    
    class ViewModelEventsImpl~VE~ {
        -mutableViewEvents: MutableStateFlow~List~VE~~
        +viewEvent: Flow~VE~
        +sendEvent(event: VE)
        +consumeEvent(event: VE, callback)
    }
    
    class ThemeViewModel {
        -settingsRepository: SettingsRepository
        -analytics: Analytics
        +onViewAction(action: ThemeViewActions)
        -updateTheme(mode: ModeTheme)
        -getThemeStatus()
    }
    
    ViewModel <|-- ThemeViewModel
    ViewModelActions <|.. ThemeViewModel
    ViewModelStates <|.. ThemeViewModel : "by ViewModelStatesImpl"
    ViewModelStates <|-- ViewModelStatesImpl
    ViewModelEvents <|-- ViewModelEventsImpl
    
    note for ThemeViewModel "Uses delegation pattern:\n'by ViewModelStatesImpl(ThemeViewState())'"
```

#### Core Architecture Interfaces

```kotlin
// Foundation interfaces for state management
interface ViewState
interface ViewAction  
interface ViewEvent

// Delegation interfaces
interface ViewModelStates<VS : ViewState> {
    val viewState: StateFlow<VS>
    fun updateState(newState: VS)
    fun updateState(function: (VS) -> VS)
}

interface ViewModelActions<VA : ViewAction> {
    fun onViewAction(action: VA)
}

interface ViewModelEvents<VE : ViewEvent> {
    val viewEvent: Flow<VE>
    suspend fun consumeEvent(event: VE, eventToConsumeCallback: suspend (VE) -> Unit)
    fun sendEvent(event: VE)
}
```

#### ViewModel Implementation Pattern

```kotlin
// Real implementation using delegation pattern
@HiltViewModel
class ThemeViewModel @Inject constructor(
    private val settingsRepository: SettingsRepository,
    private val analytics: Analytics
) : ViewModel(),
    ViewModelActions<ThemeViewActions>,
    ViewModelStates<ThemeViewState> by ViewModelStatesImpl(ThemeViewState()) {

    override fun onViewAction(action: ThemeViewActions) {
        when (action) {
            ThemeViewActions.OnStart -> {
                analytics.logScreen(Theme)
                getThemeStatus()
            }
            is ThemeViewActions.OnThemeChanged -> updateTheme(action.mode)
        }
    }

    private fun updateTheme(mode: ModeTheme) = viewModelScope.launch {
        analytics.logEvent(Event.Theme, mapOf(Parameter.Theme to mode.label))
        settingsRepository.updateThemeMode(mode.id)
        updateState { it.copy(themes = listOf(/* updated themes */)) }
    }
}
```

#### Complex ViewModel Example

```kotlin
// Complex ViewModels use multiple delegation interfaces
@HiltViewModel
class HomeViewModel @Inject constructor(
    // 13 dependencies for complex business logic
) : ViewModel(),
    ViewModelActions<HomeViewActions>,
    ViewModelEvents<HomeViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<HomeViewState> by ViewModelStatesImpl(HomeViewState()) {

    override fun onViewAction(action: HomeViewActions) {
        when (action) {
            HomeViewActions.OnStart -> onStart()
            is HomeViewActions.OnCreate -> onCreate(action)
            HomeViewActions.OnReferralClicked -> onReferralClicked()
            // ... 15+ more actions
        }
    }

    private fun onReferralClicked() {
        analytics.logAction(OpenReferralCode, mapOf(Parameter.Source to REFERRAL_SOURCE_HOME))
        sendEvent(HomeViewEvents.NavigateToReferral)
    }
}
```

#### State Management Pattern

```kotlin
// ViewState - Immutable data classes
data class ThemeViewState(
    val themes: List<ModeTheme> = emptyList()
) : ViewState

data class HomeViewState(
    val isFullUser: Boolean = false,
    val avatarState: AvatarState? = null,
    val showProfileBadge: Boolean = false,
    val userName: String = "",
    val isReferralEnabled: Boolean = false,
    val personalitiesViewState: PersonalitiesViewState = PersonalitiesViewState(),
    val gamificationModel: GamificationPillUiModel? = null,
    val showNewLastMessageView: Boolean = false,
    val pendingReward: Reward? = null,
    val showRewardDismissPopup: Boolean = false,
    val brazeCardsEnabled: Boolean = false
) : ViewState

// ViewActions - Sealed classes for type safety
sealed class ThemeViewActions : ViewAction {
    data object OnStart : ThemeViewActions()
    data class OnThemeChanged(val mode: ModeTheme) : ThemeViewActions()
}

sealed class HomeViewActions : ViewAction {
    data class OnCreate(val comingFromBottomBar: Boolean) : HomeViewActions()
    data object OnStart : HomeViewActions()
    data object OnProactiveMessageActionClicked : HomeViewActions()
    data object OnDismissProactiveMessage : HomeViewActions()
    data object OnReferralClicked : HomeViewActions()
    data object OnNavigateToSignup : HomeViewActions()
    data object OnAddCustomBestieClicked : HomeViewActions()
    data object OnNewChatClicked : HomeViewActions()
    data object OnClaimReward : HomeViewActions()
    data object OnAttemptCloseReward : HomeViewActions()
    data object OnCloseReward : HomeViewActions()
    data object OnGamificationPillTapped : HomeViewActions()
}
```

### Dependency Injection with Hilt

Each layer has its own Hilt modules with clear scoping:

```kotlin
// Foundation layer - Singleton scope
@Module
@InstallIn(SingletonComponent::class)
object NetworkingModule {
    @Provides
    @Singleton
    fun provideHttpClient(): OkHttpClient = // ...
}

// Feature layer - ViewModelComponent scope
@Module
@InstallIn(ViewModelComponent::class)
object ChatModule {
    @Provides
    fun provideChatRepository(/* ... */): ChatRepository = // ...
}
```

### Repository Pattern

Data access is centralized through repositories:

```kotlin
interface ChatRepository {
    suspend fun getMessages(): Flow<List<Message>>
    suspend fun sendMessage(message: Message): Result<Unit>
}

class ChatRepositoryImpl @Inject constructor(
    private val chatApi: ChatApi,
    private val chatDao: ChatDao,
    private val mapper: MessageMapper
) : ChatRepository {
    
    override suspend fun getMessages(): Flow<List<Message>> = 
        chatDao.getAllMessages().map { entities ->
            entities.map(mapper::toDomain)
        }
    
    override suspend fun sendMessage(message: Message): Result<Unit> =
        try {
            val response = chatApi.sendMessage(mapper.toApi(message))
            chatDao.insertMessage(mapper.toEntity(response))
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
}
```

### Navigation Architecture

Single Activity architecture with Navigation Compose:

```kotlin
// Type-safe routing
@Serializable
sealed class UserSessionRoutes : Route {
    @Serializable
    data class ChatDetail(
        val personalityId: String,
        val highlightId: Long? = null,
        val isCustomBestie: Boolean,
        val openKeyboardOnLaunch: Boolean = false
    ) : UserSessionRoutes()
    
    @Serializable
    data class ImagineTool(val imagineId: String? = null) : UserSessionRoutes()
    
    @Serializable
    data object Home : UserSessionRoutes()
}

// Navigation setup with type-safe routes
@Composable
fun LuziaNavigation(navController: NavHostController) {
    NavHost(navController = navController, startDestination = UserSessionRoutes.Home) {
        composable<UserSessionRoutes.Home> { 
            HomeScreen() 
        }
        composable<UserSessionRoutes.ChatDetail> { backStackEntry ->
            val route = backStackEntry.toRoute<UserSessionRoutes.ChatDetail>()
            ChatScreen(
                personalityId = route.personalityId,
                highlightId = route.highlightId,
                isCustomBestie = route.isCustomBestie,
                openKeyboardOnLaunch = route.openKeyboardOnLaunch
            )
        }
        composable<UserSessionRoutes.ImagineTool> { backStackEntry ->
            val route = backStackEntry.toRoute<UserSessionRoutes.ImagineTool>()
            ImagineToolScreen(imagineId = route.imagineId)
        }
    }
}
// File: core/navigation/src/main/java/co/thewordlab/luzia/core/navigation/usersession/UserSessionRoutes.kt
```

## State Management

### Interface Delegation Architecture

The app uses interface delegation to separate concerns in ViewModels:

```kotlin
// ViewModel with multiple delegated responsibilities
class ChatDetailViewModel @Inject constructor(
    private val chatRepository: ChatRepository,
    sendMessageEventHelper: SendMessageEventHelper
) : ViewModel(),
    ViewModelStates<ChatDetailViewState> by ViewModelStatesImpl(ChatDetailViewState()),
    ViewModelActions<ChatDetailViewActions>,
    ViewModelEvents<ChatDetailViewEvents> by ViewModelEventsImpl(),
    SendMessageEventHelper by sendMessageEventHelper {
    
    // ViewModel focuses on business logic
    override fun onViewAction(action: ChatDetailViewActions) {
        when (action) {
            is ChatDetailViewActions.SendMessage -> {
                sendMessage(action.content)
                sendEvent(ChatDetailViewEvents.MessageSent)
            }
        }
    }
    
    private fun sendMessage(content: String) {
        viewModelScope.launch {
            updateState { it.copy(isLoading = true) }
            // Business logic implementation
        }
    }
}
// File: features/chat/src/main/java/co/thewordlab/luzia/features/chat/presentation/details/ChatDetailViewModel.kt
```

### Compose State Management

```kotlin
@Composable
fun ChatScreen(viewModel: ChatDetailViewModel = hiltViewModel()) {
    val viewState by viewModel.viewState.collectAsState()
    val context = LocalContext.current
    val appState = LocalAppState.current
    
    // ViewModelEventEffect handles lifecycle-aware event collection
    ViewModelEventEffect(events = viewModel) { event ->
        when (event) {
            is ChatDetailViewEvents.ShowError -> {
                appState.showSnackBar(message = event.message)
            }
            ChatDetailViewEvents.MessageSent -> {
                appState.showSnackBar(message = context.getString(R.string.message_sent))
            }
            ChatDetailViewEvents.NavigateBack -> {
                // Handle navigation
            }
        }
    }
    
    ChatContent(
        state = viewState,
        onAction = viewModel::onViewAction
    )
}
```

## Data Flow

### Typical Data Flow Pattern

1. **User Interaction** → ViewAction is dispatched
2. **ViewModel** → Processes action and calls Repository
3. **Repository** → Coordinates between API and local storage
4. **Database/Network** → Data source operations
5. **Repository** → Maps data and returns Flow/Result
6. **ViewModel** → Updates ViewState
7. **Compose UI** → Recomposes with new state

```
User Tap → ViewAction → ViewModel → Repository → API/DB
    ↑                                                ↓
Compose UI ← ViewState ← ViewModel ← Repository ← Data
```

## Error Handling

### ResultOf Pattern for Type-Safe Error Handling

The app uses `ResultOf<T, E>` instead of Kotlin's `Result` for comprehensive error handling:

```kotlin
// Core error handling types
interface Error

sealed interface ResultOf<out T, out E : Error> {
    data class Success<out T, out E : Error>(val data: T) : ResultOf<T, E>
    data class Failure<out T, out E : Error>(
        val error: E,
        val extraData: ExtraData? = null,
        val throwable: Throwable? = null
    ) : ResultOf<T, E>
}

// Application-specific errors
sealed class AppErrors : Error {
    object NoNetwork : AppErrors()
    object AuthRequired : AppErrors()
    object LimitReached : AppErrors()
    object FileTooBig : AppErrors()
    object MediaNotFound : AppErrors()
    object Unknown : AppErrors()
}

// Extension functions for convenience
fun <T, E : Error> T.asSuccess() = ResultOf.Success<T, E>(this)
fun <T, E : Error> E.asFailure() = ResultOf.Failure<T, E>(this)
fun <T, E : Error> ResultOf<T, E>.isSuccess() = this is ResultOf.Success
fun <T, E : Error> ResultOf<T, E>.isFailure() = this is ResultOf.Failure
fun <T, E : Error> ResultOf<T, E>.getDataOrNull(): T? = when (this) {
    is ResultOf.Failure -> null
    is ResultOf.Success -> data
}
```

### Usage in Repository Layer

```kotlin
class ChatRepositoryImpl @Inject constructor(
    private val chatApi: ChatApi,
    private val chatDao: ChatDao
) : ChatRepository {
    
    override suspend fun sendMessage(content: String): ResultOf<Message, AppErrors> = try {
        val response = chatApi.sendMessage(content).asResult()
        when (response) {
            is ResultOf.Success -> {
                chatDao.insertMessage(response.data.toEntity())
                response.data.asSuccess()
            }
            is ResultOf.Failure -> response
        }
    } catch (e: Exception) {
        AppErrors.Unknown.asFailure()
    }
}
```

### Usage in ViewModel

```kotlin
private fun sendMessage(content: String) = viewModelScope.launch {
    val result = chatRepository.sendMessage(content)
    
    when (result) {
        is ResultOf.Success -> {
            sendEvent(ChatViewEvents.MessageSent)
            analytics.trackEvent(Event.MessageSent)
        }
        is ResultOf.Failure -> {
            val errorMessage = when (result.error) {
                AppErrors.NoNetwork -> "No internet connection"
                AppErrors.LimitReached -> "Rate limit exceeded"
                AppErrors.Unknown -> "Something went wrong"
                else -> "Failed to send message"
            }
            sendEvent(ChatViewEvents.ShowError(errorMessage))
        }
    }
}
```

## Testing Architecture

### Testing Pyramid

```
    E2E Tests (Maestro)
         ↗      ↖
  Integration    UI Tests
      ↗            ↖
Unit Tests      Component Tests
```

### Module Testing Strategy

- **Foundation**: Focus on utilities and framework integrations
- **Core**: Test business logic and use cases
- **Features**: Test UI logic and user interactions

## Security Considerations

- **Native Security**: C++ security library for sensitive operations
- **Network Security**: Certificate pinning and encrypted communication
- **Data Protection**: Encrypted local storage for sensitive data
- **API Security**: Token-based authentication with refresh mechanisms

## Performance Considerations

- **Module Loading**: Lazy initialization of feature modules
- **Memory Management**: Efficient Compose state management
- **Network**: Request caching and debouncing
- **Database**: Optimized queries and indexing

## Scalability

The architecture supports scalability through:

- **Horizontal Scaling**: Easy addition of new feature modules
- **Vertical Scaling**: Layer-based separation allows independent scaling
- **Team Scaling**: Module ownership enables parallel development
- **Code Reuse**: Foundation layer provides reusable components

## Module Dependencies

```
app → features → core → foundation
```

**Dependency Rules**:
- Foundation modules cannot depend on core or features
- Core modules can only depend on foundation
- Features can depend on core and foundation
- Cross-dependencies within the same layer should be minimized