# Luzia Android App Documentation

Welcome to the comprehensive documentation for the Luzia Android application. This documentation covers all aspects of the application architecture, development guidelines, and module-specific details.

## Table of Contents

### Core Documentation
- [Architecture Overview](./architecture.md) - High-level system design and MVVM+MVI patterns
- [Coding Style Guide](./coding-style.md) - Standards, conventions, and best practices  
- [Build System](./build-system.md) - Gradle setup, variants, and build process
- [Database Guide](./database.md) - Room database architecture and migrations
- [Dependency Management](./dependency-management.md) - How to manage dependencies and plugins
- [Module Creation Guide](./module-creation.md) - Step-by-step guide to create new modules

### Module Documentation

#### Foundation Layer (Infrastructure)
> **Package**: `co.thewordlab.luzia.foundation.*` or `co.theworldlab.luzia.foundation.*`

- [Analytics](./modules/foundation/analytics.md) - Multi-provider analytics system
- [Architecture System](./modules/foundation/architecture-system.md) - MVVM+MVI patterns and interface delegation
- [Common](./modules/foundation/common.md) - Shared utilities and extensions
- [Config](./modules/foundation/config.md) - Feature flag and configuration management
- [Data Cache](./modules/foundation/data-cache.md) - Store-based caching strategies
- [Design System](./modules/foundation/design-system.md) - Custom UI components and theming
- [Files](./modules/foundation/files.md) - File management utilities for AI features
- [Localization](./modules/foundation/localization.md) - Multi-language support (6 languages)
- [Messages](./modules/foundation/messages.md) - AI chat infrastructure and completion API
- [Networking](./modules/foundation/networking.md) - HTTP client, ResultOf pattern, API management
- [Persistence](./modules/foundation/persistence.md) - Room database layer (28 schema versions)
- [Secure Library](./modules/foundation/securelib.md) - Native C++ API key storage
- [Testing](./modules/foundation/testing.md) - Testing utilities and MainDispatcherRule

#### Core Layer (Business Logic) 
> **Package**: `co.thewordlab.luzia.core.*`

- [Ads](./modules/core/ads.md) - Google AdMob native ads for chat
- [Bestie Points](./modules/core/bestiepoints.md) - Streak rewards system
- [Chat](./modules/core/chat.md) - AI chat business logic (NOT GetStream)
- [Connectivity](./modules/core/connectivity.md) - Network connectivity UI components
- [Dynamic Flow](./modules/core/dynamic-flow.md) - Server-driven UI configuration
- [Feedback](./modules/core/feedback.md) - Thumbs up/down feedback system
- [Gamification](./modules/core/gamification.md) - Level and rewards system
- [Navigation](./modules/core/navigation.md) - Type-safe navigation with deep links
- [Notifications](./modules/core/notifications.md) - Push notification handling (Firebase/Braze/GetStream)
- [Profile](./modules/core/profile.md) - User profile management with avatar system
- [Sharing](./modules/core/sharing.md) - GetStream chat integration and AI message sharing
- [Signup](./modules/core/signup.md) - Google OAuth authentication only
- [Tools](./modules/core/tools.md) - AI tools and utilities
- [Web](./modules/core/web.md) - WebView wrapper with JavaScript bridge

#### Features Layer (UI Implementation)
> **Package**: `co.thewordlab.luzia.features.*` or `co.theworldlab.luzia.features.*`

**Documented Feature Modules**:
- [Home](./modules/features/home.md) - Home screen and dashboard with complex integrations
- [Chat](./modules/features/chat.md) - Chat UI screens with AI conversations, file attachments, search
- [Profile](./modules/features/profile.md) - Profile UI screens with avatar management, school integration
- [Tools](./modules/features/tools.md) - Tools UI screens with dynamic AI-powered utilities

**Additional Feature Modules** (no documentation yet):
- `features/camera` - Camera functionality
- `features/gamification` - Gamification UI
- `features/imagine` - AI image generation UI
- `features/personality` - AI personality selection UI
- `features/proactive-messaging` - Proactive messaging features UI
- `features/settings` - Settings screens
- `features/shortcuts` - App shortcuts
- `features/signup` - Registration UI
- `features/stream` - Streaming features
- `features/vision` - Computer vision features

### Guides
> **Status**: Directory exists but no files created yet

**Planned Guides** (directory: `docs/guides/`):
- Testing Strategy - Unit, integration, and E2E testing
- Performance Optimization - Performance best practices  
- Security Guidelines - Security considerations
- Release Process - Build and release workflow
- Troubleshooting - Common issues and solutions

## Project Architecture Overview

```mermaid
graph TD
    subgraph "App Layer"
        A["app/\n(Main Application)"]
    end
    
    subgraph "Features Layer"
        F1["features/chat"]
        F2["features/home"]
        F3["features/profile"]
        F4["features/tools"]
        F5["features/..."]
    end
    
    subgraph "Core Layer\n(Business Logic)"
        C1["core/chat"]
        C2["core/gamification"]
        C3["core/profile"] 
        C4["core/navigation"]
        C5["core/..."]
    end
    
    subgraph "Foundation Layer\n(Infrastructure)"
        FN1["foundation/networking"]
        FN2["foundation/persistence"]
        FN3["foundation/design-system"]
        FN4["foundation/analytics"]
        FN5["foundation/..."]
    end
    
    subgraph "External Systems"
        EXT1["Android SDK"]
        EXT2["Jetpack Compose"]
        EXT3["Room Database"]
        EXT4["Firebase Services"]
        EXT5["GetStream Chat"]
    end
    
    A --> F1
    A --> F2
    A --> F3
    
    F1 --> C1
    F2 --> C2
    F3 --> C3
    F4 --> C4
    
    C1 --> FN1
    C2 --> FN2
    C3 --> FN3
    C4 --> FN4
    
    FN1 --> EXT1
    FN2 --> EXT3
    FN3 --> EXT2
    FN4 --> EXT4
    C1 --> EXT5
    
    classDef appLayer fill:#e1f5fe
    classDef featureLayer fill:#f3e5f5
    classDef coreLayer fill:#e8f5e8
    classDef foundationLayer fill:#fff3e0
    classDef externalLayer fill:#fafafa
    
    class A appLayer
    class F1,F2,F3,F4,F5 featureLayer
    class C1,C2,C3,C4,C5 coreLayer
    class FN1,FN2,FN3,FN4,FN5 foundationLayer
    class EXT1,EXT2,EXT3,EXT4,EXT5 externalLayer
```

### 🎵 Audio Overview
https://github.com/user-attachments/assets/11673c05-9720-494c-81de-01e8a55086b0

## Quick Start

For new developers joining the project:

1. **Architecture**: Read the [Architecture Overview](./architecture.md) to understand the MVVM+MVI delegation patterns
2. **Standards**: Review the [Coding Style Guide](./coding-style.md) for development standards
3. **Environment**: Follow the [Build System](./build-system.md) guide to set up your development environment  
4. **Development**: Check the [Module Creation Guide](./module-creation.md) when adding new features
5. **Commands**: Refer to [`CLAUDE.md`](../CLAUDE.md) for essential build, test, and quality commands

## Verified Project Structure

```
// File: Project root structure verified against actual codebase
luzia-android-app/
├── app/                    # Main application module with LuziaApplication.kt
├── foundation/             # Core infrastructure (13 modules)
│   ├── analytics/          # Multi-provider analytics system
│   ├── architecture-system/ # MVVM+MVI patterns with interface delegation
│   ├── common/             # Shared utilities and extensions
│   ├── config/             # Feature flags and configuration
│   ├── data-cache/         # Store-based caching with reactive flows
│   ├── design-system/      # Custom UI components (NOT Material Design 3)
│   ├── files/              # File utilities for AI features
│   ├── localization/       # Multi-language support (6 languages)
│   ├── messages/           # AI chat infrastructure
│   ├── networking/         # ResultOf pattern and HTTP client
│   ├── persistence/        # Room database (28 schema versions)
│   ├── securelib/          # Native C++ API key storage
│   └── testing/            # Testing utilities with MainDispatcherRule
├── core/                   # Business logic modules (14 modules)
│   ├── ads/                # Google AdMob native ads
│   ├── bestiepoints/       # Streak rewards system
│   ├── chat/               # AI chat business logic
│   ├── connectivity/       # Network connectivity monitoring
│   ├── dynamic-flow/       # Server-driven UI configuration
│   ├── feedback/           # User feedback collection
│   ├── gamification/       # Level and rewards system
│   ├── navigation/         # Type-safe navigation with deep links
│   ├── notifications/      # Push notification handling
│   ├── profile/            # User profile management
│   ├── sharing/            # GetStream integration and AI message sharing
│   ├── signup/             # Google OAuth authentication
│   ├── tools/              # AI tools and utilities
│   └── web/                # WebView wrapper with JavaScript bridge
├── features/               # UI and feature modules (14 modules)
│   ├── camera/             # Camera functionality UI
│   ├── chat/               # Chat UI screens
│   ├── gamification/       # Gamification UI
│   ├── home/               # Home screen and dashboard
│   ├── imagine/            # AI image generation UI
│   ├── personality/        # AI personality selection UI
│   ├── proactive-messaging/# Proactive messaging features UI
│   ├── profile/            # Profile UI screens
│   ├── settings/           # Settings screens
│   ├── shortcuts/          # App shortcuts
│   ├── signup/             # Registration UI
│   ├── stream/             # Streaming features
│   ├── tools/              # Tools UI screens
│   └── vision/             # Computer vision features
├── build-logic/            # Build convention plugins
├── docs/                   # This documentation
├── scripts/                # Utility scripts (endToEndTests.sh, etc.)
├── maestro/                # E2E test flows
├── benchmark/              # Performance benchmarking
└── dev-tools/              # Development tools module
```

## Package Naming Patterns

```text
// File: app/src/main/java/co/thewordlab/luzia/LuziaApplication.kt:1
Main package: co.thewordlab.luzia

// Foundation modules (mixed package naming):
// - Most use: co.thewordlab.luzia.foundation.*
// - Some use: co.theworldlab.luzia.foundation.*

// Core modules:
// - Standard: co.thewordlab.luzia.core.*

// Feature modules (mixed package naming):
// - Most use: co.thewordlab.luzia.features.*  
// - Some use: co.theworldlab.luzia.features.*
```

## Key Technologies & Patterns

```mermaid
graph LR
    subgraph "UI Layer"
        A["Jetpack Compose"]
        B["Custom Design System"]
        C["Lottie Animations"]
    end
    
    subgraph "Architecture"
        D["MVVM+MVI with Interface Delegation"]
        E["Hilt Dependency Injection"]
        F["Type-safe Navigation"]
    end
    
    subgraph "Data Layer"
        G["Room Database (28 versions)"]
        H["ResultOf Error Handling"]
        I["Store-based Caching"]
    end
    
    subgraph "External Services"
        J["Firebase Analytics"]
        K["GetStream Chat"]
        L["Google AdMob"]
        M["Native C++ Security"]
    end
    
    A --> D
    D --> G
    D --> E
    E --> J
    G --> H
    H --> I
    K --> D
    L --> D
    M --> D
```

## Contributing

When contributing to this project:

1. **Standards**: Follow the [coding style guide](./coding-style.md) with MVVM+MVI delegation patterns
2. **Documentation**: Update relevant documentation for any changes  
3. **Testing**: Add tests for new functionality using MockK and Turbine
4. **Quality**: Run code quality checks (`./gradlew detekt lint`) before submitting PRs
5. **Architecture**: Follow the three-layer architecture (Foundation → Core → Features)

## Essential Commands

> **Reference**: See [`CLAUDE.md`](../CLAUDE.md) for complete command reference

```bash
# Build & Test
./gradlew assembleDevDebug              # Build development APK
./gradlew testDevDebug                  # Run unit tests
scripts/endToEndTests.sh                # Run Maestro E2E tests

# Code Quality  
./gradlew detekt                        # Static analysis
./gradlew lint                          # Android lint
./gradlew createDevReleaseCombinedCoverageReport  # Coverage report

# Utilities
scripts/updateTranslations.sh           # Update localization strings
./gradlew printVersionInformation       # Show version details
```

## Getting Help

- **Architecture**: Review [Architecture Overview](./architecture.md) for system design
- **Modules**: Consult module-specific documentation for detailed implementation details
