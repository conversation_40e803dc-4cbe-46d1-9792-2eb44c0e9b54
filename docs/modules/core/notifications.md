# Core: Notifications Module

## Overview

The Notifications module provides a simple notification permission request UI and push notification handling for the Luzia Android app. This core module focuses on Firebase Cloud Messaging (FCM) integration, Braze push notifications, GetStream integration, and basic notification permission management.

```mermaid
graph TD
    A[PushNotificationService] --> B[Firebase Messaging]
    A --> C[Braze Integration]
    A --> D[GetStream Integration]
    A --> E[LuziaPushNotificationManager]
    
    F[NotificationPermissionScreen] --> G[Permission Request UI]
    
    H[NotificationsRepository] --> I[Backend API]
    
    E --> J[Chat Messages]
    E --> K[Proactive Messages]
    
    subgraph "Push Message Routing"
        B --> L[Firebase Delegate]
        C --> M[Braze Delegate]
        L --> N[Chat Handler]
        M --> N
    end
    
    subgraph "API Integration"
        I --> O[Update FCM Token]
        O --> P[Backend Sync]
    end
```

## Module Dependencies

**File**: `core/notifications/build.gradle.kts`

```kotlin
// File: core/notifications/build.gradle.kts
dependencies {
    implementation(libs.braze.ui)
    implementation(libs.firebase.cloud.messaging)
    implementation(libs.moshi.core)
    implementation(libs.retrofit.core)
    implementation(projects.core.navigation)
    implementation(projects.features.proactiveMessaging)
    implementation(projects.foundation.analytics)
    implementation(projects.foundation.designSystem)
    implementation(projects.foundation.messages)
    implementation(projects.foundation.networking)
    implementation(projects.foundation.securelib)
    implementation(libs.stream.chat.android.push)
    implementation(libs.stream.chat.android.client)
}
```

## Core Architecture

### Push Notification Service

The main service handles Firebase Cloud Messaging and routes messages to appropriate handlers.

**File**: `core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/firebase/PushNotificationService.kt`

```kotlin
// File: core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/firebase/PushNotificationService.kt:34-96
@AndroidEntryPoint
class PushNotificationService : FirebaseMessagingService() {

    @Inject
    lateinit var luziaPushNotificationManager: LuziaPushNotificationManager

    @Inject
    lateinit var brazeManager: BrazeManager

    @Inject
    lateinit var analytics: Analytics

    @Inject
    lateinit var secureStorage: SecureStorage

    private val notificationManager by lazy {
        NotificationManagerCompat.from(this)
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            getString(R.string.channel_default),
            getString(R.string.channel_default_name),
            NotificationManager.IMPORTANCE_HIGH
        )
        notificationManager.createNotificationChannel(channel)
    }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        updateGetStreamToken(token)
        if (checkSelfPermission(this, POST_NOTIFICATIONS) == PERMISSION_GRANTED) {
            luziaPushNotificationManager.updateNotificationToken(token)
        }
    }

    override fun onMessageReceived(message: RemoteMessage) {
        super.onMessageReceived(message)
        try {
            if (FirebaseMessagingDelegate.handleRemoteMessage(message)) return
            if (brazeManager.handleBrazeMessage(this, message)) return

            // Fallback handling if no handler processes the message
            if (luziaPushNotificationManager.handleDataMessage(message, packageName)) {
                createNotificationMessage(message)
            }
        } catch (ex: IllegalStateException) {
            analytics.reportException("Push message failed", ex)
        }
    }
}
```

### Push Message Flow

```mermaid
sequenceDiagram
    participant Backend
    participant FCM
    participant App
    participant GetStream
    participant Braze
    participant Luzia
    
    Backend->>FCM: Send Push Message
    FCM->>App: onMessageReceived
    
    App->>GetStream: FirebaseMessagingDelegate.handle
    alt GetStream Message
        GetStream-->>App: return true (handled)
    else Not GetStream
        App->>Braze: BrazeManager.handle
        alt Braze Message
            Braze-->>App: return true (handled)
        else Not Braze
            App->>Luzia: LuziaPushNotificationManager.handle
            Luzia->>Luzia: createNotificationMessage
        end
    end
```

### Luzia Push Notification Manager

Handles Luzia-specific push notifications including chat messages and proactive messaging.

**File**: `core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/firebase/LuziaPushNotificationManager.kt`

```kotlin
// File: core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/firebase/LuziaPushNotificationManager.kt:15-61
class LuziaPushNotificationManager @Inject constructor(
    private val analytics: Analytics,
    private val chatRepository: ChatRepository,
    private val notificationsRepository: NotificationsRepository,
    private val handleProactiveMessagesUseCase: HandleProactiveChatMessagesUseCase
) {

    fun handleDataMessage(message: RemoteMessage, packageName: String): Boolean {
        val clickAction = message.data[DATA_CLICK_ACTION]
        val sendAction = "$packageName.$ACTION_SEND_MESSAGE"
        val type = message.data[DATA_SOURCE].orEmpty()
        
        if (type == DATA_SOURCE_PROACTIVE) {
            handleProactiveMessages()
        } else if (message.data.containsKey(DATA_PERSONALITY) &&
            message.data.containsKey(DATA_TEXT) &&
            clickAction == sendAction
        ) {
            handleChatMessages(message.data[DATA_PERSONALITY], message.data[DATA_TEXT])
        }
        return true
    }

    fun updateNotificationToken(token: String) = coroutineScope.launch {
        notificationsRepository.updateNotificationToken(token)
    }

    private fun handleChatMessages(personalityId: String?, text: String?) {
        if (!personalityId.isNullOrEmpty() && !text.isNullOrEmpty()) {
            coroutineScope.launch { chatRepository.saveRemoteMessage(personalityId, text) }
        }
    }

    private fun handleProactiveMessages() {
        coroutineScope.launch { handleProactiveMessagesUseCase() }
    }

    companion object {
        const val DATA_PERSONALITY = "personality_id"
        const val DATA_TEXT = "text"
        const val ACTION_SEND_MESSAGE = "send_message"
        const val DATA_CLICK_ACTION = "click_action"
        const val DATA_SOURCE = "service_source"
        const val DATA_SOURCE_PROACTIVE = "proactive"
    }
}
```

## Braze Integration

Simple Braze manager for in-app messages and push notification routing.

**File**: `core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/braze/BrazeManager.kt`

```kotlin
// File: core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/braze/BrazeManager.kt:17-62
class BrazeManager @Inject constructor(
    private val brazeConfig: BrazeConfig
) {

    private var inAppRunning = false
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    fun initializeBraze(app: Application) {
        if (brazeConfig.isEnabled()) {
            coroutineScope.launch {
                app.registerActivityLifecycleCallbacks(BrazeActivityLifecycleCallbackListener())
                BrazeInAppMessageManager.getInstance().ensureSubscribedToInAppMessageEvents(app)
            }
        }
    }

    /**
     * Should be called in [Activity.onResume]. Remember to call this method in the compose screens
     * where we need to display the In-App messages.
     */
    fun registerInAppMessages(activity: Activity) {
        if (brazeConfig.isEnabled()) {
            inAppRunning = true
            coroutineScope.launch {
                BrazeInAppMessageManager.getInstance().registerInAppMessageManager(activity)
            }
        }
    }

    /**
     * Should be called in [Activity.onPause]
     */
    fun unregisterInAppMessages(activity: Activity) {
        if (inAppRunning) {
            inAppRunning = false
            BrazeInAppMessageManager.getInstance().unregisterInAppMessageManager(activity)
        }
    }

    /**
     * Handle push notification from Braze. If the push notification is not from Braze, return false
     * and the push notification will be handled in PushNotificationService.
     */
    fun handleBrazeMessage(context: Context, remoteMessage: RemoteMessage): Boolean =
        BrazeFirebaseMessagingService.handleBrazeRemoteMessage(context, remoteMessage)
}
```

### Braze Configuration

Braze is configured per build flavor with different API keys and settings.

**File**: `core/notifications/src/dev/res/values/braze.xml`

```xml
<!-- File: core/notifications/src/dev/res/values/braze.xml -->
<resources>
    <string name="com_braze_api_key" translatable="false">00edfb69-7d50-4bc8-9505-ce830db13f3b</string>
    <string name="com_braze_custom_endpoint" translatable="false">sdk.iad-07.braze.com</string>
    <integer name="com_braze_logger_initial_log_level">5</integer>
    <bool name="com_braze_firebase_cloud_messaging_registration_enabled" translatable="false">true</bool>
    <string name="com_braze_firebase_cloud_messaging_sender_id" translatable="false">902703594582</string>
    <bool name="com_braze_fallback_firebase_cloud_messaging_service_enabled">true</bool>
    <string name="com_braze_fallback_firebase_cloud_messaging_service_classpath" translatable="false">co.thewordlab.luzia.core.notifications.firebase.PushNotificationService</string>
    <drawable name="com_braze_push_small_notification_icon">@drawable/luzia_logo_letter</drawable>
    <drawable name="com_braze_push_large_notification_icon">@drawable/luzia_logo_letter</drawable>
    <integer name="com_braze_default_notification_accent_color">0xFF121344</integer>
    <bool name="com_braze_handle_push_deep_links_automatically">true</bool>
    <string name="com_braze_default_notification_channel_name" translatable="false">@string/channel_default</string>
    <string name="com_braze_default_notification_channel_description" translatable="false">@string/channel_default_name</string>
</resources>
```

## API Integration

### Notifications Repository

Simple repository for updating FCM tokens with the backend.

**File**: `core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/core/data/repository/NotificationsRepository.kt`

```kotlin
// File: core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/core/data/repository/NotificationsRepository.kt:7-14
class NotificationsRepository @Inject constructor(
    private val notificationsApi: NotificationsApi
) {
    suspend fun updateNotificationToken(token: String?) {
        val request = UpdatePushTokenRequest(fcmSubscriptionToken = token)
        notificationsApi.updateTokenPush(request)
    }
}
```

### Notifications API

**File**: `core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/core/data/api/NotificationsApi.kt`

```kotlin
// File: core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/core/data/api/NotificationsApi.kt:7-10
interface NotificationsApi {
    @PUT("user/messaging-tokens")
    suspend fun updateTokenPush(@Body updatePushTokenRequest: UpdatePushTokenRequest): Response<Unit>
}
```

**File**: `core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/core/data/api/UpdatePushTokenRequest.kt`

```kotlin
// File: core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/core/data/api/UpdatePushTokenRequest.kt:9-14
@JsonClass(generateAdapter = true)
data class UpdatePushTokenRequest(
    @Json(name = "platform")
    val platform: String = "android",
    @Json(name = "fcmSubscriptionToken")
    val fcmSubscriptionToken: String? = null
)
```

## Permission Management

### Notification Helper

Simple utility for checking notification permissions.

**File**: `core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/NotificationHelper.kt`

```kotlin
// File: core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/NotificationHelper.kt:11-16
class NotificationHelper @Inject constructor(@ApplicationContext private val context: Context) {

    @SuppressLint("InlinedApi")
    fun checkNotificationPermissionEnabled(): Boolean =
        ContextCompat.checkSelfPermission(context, POST_NOTIFICATIONS) == PERMISSION_GRANTED
}
```

### Update Firebase Token Use Case

Handles FCM token updates with permission checks and GetStream integration.

**File**: `core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/firebase/domain/UpdateFirebasePushTokenUseCase.kt`

```kotlin
// File: core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/firebase/domain/UpdateFirebasePushTokenUseCase.kt:14-42
class UpdateFirebasePushTokenUseCase @Inject constructor(
    private val notificationHelper: NotificationHelper,
    private val repo: NotificationsRepository,
    private val secureStorage: SecureStorage,
) {

    suspend operator fun invoke(notificationEnabled: Boolean) {
        val token = if (notificationEnabled) {
            Firebase.messaging.token
                .addOnFailureListener { Log.d("LuziaApp", "Message token failed") }
                .await()
        } else {
            null
        }
        if (token != null) {
            val provider = secureStorage.get(SecureKey.KEY_GET_STREAM_NOTIFICATION)
            FirebaseMessagingDelegate.registerFirebaseToken(token, provider)
        }
        repo.updateNotificationToken(token)
    }

    suspend fun checkPermissionAndUpdate() {
        try {
            invoke(notificationHelper.checkNotificationPermissionEnabled())
        } catch (expected: Exception) {
            Log.d("LuziaApp", "Message token failed due: ${expected.message}")
        }
    }
}
```

## Notification Permission UI

### MVVM+MVI Pattern Implementation

The notification permission screen follows the standard MVVM+MVI pattern used throughout the app.

**File**: `core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/presentation/NotificationPermissionViewModel.kt`

```kotlin
// File: core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/presentation/NotificationPermissionViewModel.kt:16-43
@HiltViewModel
class NotificationPermissionViewModel @Inject constructor(
    private val analytics: Analytics
) :
    ViewModel(),
    ViewModelActions<NotificationPermissionViewActions>,
    ViewModelEvents<NotificationPermissionViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<NotificationPermissionViewState> by ViewModelStatesImpl(
        NotificationPermissionViewState()
    ) {

    override fun onViewAction(action: NotificationPermissionViewActions) {
        when (action) {
            NotificationPermissionViewActions.OnAllow -> {
                analytics.logEvent(Event.FakeNotificationAllow)
                sendEvent(NotificationPermissionViewEvents.AskPermission)
            }

            NotificationPermissionViewActions.OnDismiss -> {
                analytics.logEvent(Event.FakeNotificationDeny)
                sendEvent(NotificationPermissionViewEvents.NavigateBack)
            }

            NotificationPermissionViewActions.OnCreate -> {
                analytics.logScreen(NotificationPermissionAnalytics.FakeNotificationScreen)
            }
        }
    }
}
```

**File**: `core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/presentation/NotificationPermissionViewState.kt`

```kotlin
// File: core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/presentation/NotificationPermissionViewState.kt:5-7
data class NotificationPermissionViewState(
    val id: String = "",
) : ViewState
```

### Navigation Integration

**File**: `core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/Navigation.kt`

```kotlin
// File: core/notifications/src/main/java/co/thewordlab/luzia/core/notifications/Navigation.kt:13-36
fun NavGraphBuilder.notificationPermission(navController: NavController) {
    composable<UserSessionRoutes.NotificationPermission> {
        val route = it.toRoute<UserSessionRoutes.NotificationPermission>()
        NotificationPermissionScreen(
            onDismiss = {
                if (route.openChat) {
                    val popupTo = UserSessionRoutes.Landing(LandingDestinations.HOME)
                    val options = NavOptions.Builder()
                        .setPopUpTo(popupTo, false)
                        .build()
                    navController.navigate(
                        route = UserSessionRoutes.ChatDetail(
                            personalityId = LUZIA_ID_PERSONALITY,
                            isCustomBestie = false
                        ),
                        navOptions = options
                    )
                } else {
                    navController.popBackStack()
                }
            }
        )
    }
}
```

## Notification Channels

Simple notification channel configuration for Android O+.

**File**: `core/notifications/src/main/res/values/channel_names.xml`

```xml
<!-- File: core/notifications/src/main/res/values/channel_names.xml -->
<resources>
    <string name="channel_default" translatable="false">id_channel_luzia_notification</string>
    <string name="channel_default_name" translatable="false">Luzia notifications</string>
</resources>
```

## Architecture Summary

```mermaid
classDiagram
    class PushNotificationService {
        +onMessageReceived(RemoteMessage)
        +onNewToken(String)
        +createNotificationMessage(RemoteMessage)
    }
    
    class LuziaPushNotificationManager {
        +handleDataMessage(RemoteMessage, String) Boolean
        +updateNotificationToken(String)
        -handleChatMessages(String, String)
        -handleProactiveMessages()
    }
    
    class BrazeManager {
        +initializeBraze(Application)
        +registerInAppMessages(Activity)
        +unregisterInAppMessages(Activity)
        +handleBrazeMessage(Context, RemoteMessage) Boolean
    }
    
    class NotificationsRepository {
        +updateNotificationToken(String)
    }
    
    class NotificationHelper {
        +checkNotificationPermissionEnabled() Boolean
    }
    
    class UpdateFirebasePushTokenUseCase {
        +invoke(Boolean)
        +checkPermissionAndUpdate()
    }
    
    class NotificationPermissionViewModel {
        +onViewAction(NotificationPermissionViewActions)
    }
    
    PushNotificationService --> LuziaPushNotificationManager
    PushNotificationService --> BrazeManager
    LuziaPushNotificationManager --> NotificationsRepository
    UpdateFirebasePushTokenUseCase --> NotificationHelper
    UpdateFirebasePushTokenUseCase --> NotificationsRepository
    NotificationPermissionViewModel --> Analytics
```

## Module Integration

The Notifications module integrates with:

- **Foundation Analytics**: For event tracking and error reporting
- **Foundation Messages**: For chat message handling through `ChatRepository`
- **Foundation Networking**: For API communication
- **Foundation SecureLib**: For GetStream API key access
- **Core Navigation**: For notification permission screen routing
- **Features Proactive Messaging**: For handling proactive chat messages

## Key Features

1. **Multi-Provider Push Handling**: Routes messages between GetStream, Braze, and Luzia systems
2. **FCM Token Management**: Automatic token updates with backend synchronization
3. **Braze Integration**: In-app messaging and push notification support
4. **GetStream Integration**: Chat-specific push notifications
5. **Permission Management**: Android 13+ notification permission handling
6. **Analytics Integration**: Comprehensive tracking of notification events
7. **Deep Link Support**: Automatic deep link handling for push notifications

This core module provides essential notification infrastructure while maintaining simplicity and focusing on the specific needs of the Luzia AI assistant app.