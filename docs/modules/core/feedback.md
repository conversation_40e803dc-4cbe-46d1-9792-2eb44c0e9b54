# Core: Feedback Module

## Overview

The Feedback module provides a simple thumbs up/down rating system for AI-generated responses in the Luzia app. Users can provide binary feedback (👍/👎) on chat messages, AI tools responses, math solutions, document summaries, and image generation results.

## Architecture

```mermaid
graph TD
    A[User Interaction] --> B[FeedbackViewActions]
    B --> C[FeedbackRepository]
    C --> D[FeedbackApi]
    D --> E[Backend Server]
    
    subgraph "Feedback Sources"
        F[Chat Messages] --> A
        G[Math Tool] --> A
        H[Document Tool] --> A
        I[Image Generation] --> A
    end
    
    subgraph "Analytics Integration"
        C --> J[Analytics]
        J --> K[FeedbackDisplayed Event]
        J --> L[FeedbackBravo Action]
        J --> M[FeedbackNegative Action]
    end
```

## Core Implementation

### Domain Models

**File**: `core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/domain/Feedback.kt`

```kotlin
// File: core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/domain/Feedback.kt
data class Feedback(
    val buttonId: ButtonFeedback,
    val feedbackId: String,
    val feedbackType: FeedbackType,
    val feedbackSource: FeedbackSource,
    val userFeedback: UserFeedback,
    val group: GroupFeedback
)

enum class FeedbackType(val value: String) {
    PRR("prr")  // Performance, Relevance, Reliability rating
}

sealed class FeedbackSource(val value: String) {
    data class Chat(val personalityId: String) : FeedbackSource(personalityId)
    data object Math : FeedbackSource("maths")
    data object Document : FeedbackSource("documents")
    data object ImageCreation : FeedbackSource("image_creation")
}

enum class UserFeedback(val value: String) {
    OK("👍"),    // Thumbs up emoji
    KO("👎")     // Thumbs down emoji
}

enum class ButtonFeedback(val value: String) {
    THUMBS_UP("thumbs_up"),
    THUMBS_DOWN("thumbs_down")
}

enum class GroupFeedback(val value: String) {
    GENERIC_PRR("generic_prr"),
    IMAGE_GENERATION_PRR("image_generation_prr")
}
```

### Repository Interface

**File**: `core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/domain/FeedbackRepository.kt`

```kotlin
// File: core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/domain/FeedbackRepository.kt
interface FeedbackRepository {
    suspend fun sendFeedback(feedback: Feedback): ResultOf<Unit, FeedbackErrors>
}
```

### Error Handling

**File**: `core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/domain/FeedbackErrors.kt`

```kotlin
// File: core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/domain/FeedbackErrors.kt
sealed class FeedbackErrors(val description: String) : Error {
    data object UserFeedbackError : FeedbackErrors("Error sending the user feedback")
    data class CommonError(val error: CommonErrors) : FeedbackErrors(error.description)
}
```

## Data Layer Implementation

### API Interface

**File**: `core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/data/FeedbackApi.kt`

```kotlin
// File: core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/data/FeedbackApi.kt
interface FeedbackApi {
    @DecodeErrorBody
    @POST("rating-feedback")
    suspend fun sendFeedback(@Body request: FeedbackRequest): ApiResult<EmptyResponse, ErrorDto>
}
```

### Repository Implementation

**File**: `core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/data/FeedbackRepositoryImp.kt`

```kotlin
// File: core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/data/FeedbackRepositoryImp.kt:17-47
class FeedbackRepositoryImp @Inject constructor(
    private val apiDataSource: FeedbackApi,
    private val analytics: Analytics
) : FeedbackRepository {

    override suspend fun sendFeedback(feedback: Feedback): ResultOf<Unit, FeedbackErrors> {
        val result = apiDataSource.sendFeedback(
            FeedbackRequest(
                buttonId = feedback.buttonId.value,
                messageId = feedback.feedbackId,
                requestType = feedback.feedbackType.value,
                source = feedback.feedbackSource.value,
                value = feedback.userFeedback.value,
                groupId = feedback.group.value
            )
        ).asResult { error ->
            val feedbackError = error.mapToFeedbackError()
            analytics.reportException("Sending feedback failed: ${feedbackError.description}")
            feedbackError.asFailure()
        }

        return result.map { DO_NOTHING }
    }

    private fun ApiResult.Failure<*>.mapToFeedbackError(): FeedbackErrors = when (this) {
        is ApiResult.Failure.NetworkFailure -> FeedbackErrors.CommonError(CommonErrors.NetworkError)
        is ApiResult.Failure.ApiFailure,
        is ApiResult.Failure.HttpFailure,
        is ApiResult.Failure.UnknownFailure -> FeedbackErrors.UserFeedbackError
    }
}
```

### Request Model

**File**: `core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/data/models/FeedbackRequest.kt`

```kotlin
// File: core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/data/models/FeedbackRequest.kt
@JsonClass(generateAdapter = true)
data class FeedbackRequest(
    @Json(name = "buttonId")
    val buttonId: String,
    @Json(name = "messageId")
    val messageId: String,
    @Json(name = "requestType")
    val requestType: String,
    @Json(name = "source")
    val source: String,
    @Json(name = "value")
    val value: String,
    @Json(name = "groupId")
    val groupId: String? = null
)
```

## Feedback Data Flow

```mermaid
sequenceDiagram
    participant UI as User Interface
    participant VA as FeedbackViewActions
    participant Repo as FeedbackRepository
    participant API as FeedbackApi
    participant Server as Backend Server
    participant Analytics as Analytics

    UI->>VA: User clicks thumbs up/down
    Note over VA: OnLiked(feedbackId) or OnDisliked(feedbackId)
    
    VA->>Repo: sendFeedback(feedback)
    Note over Repo: Create FeedbackRequest from Feedback
    
    Repo->>API: POST /rating-feedback
    API->>Server: Send feedback data
    
    alt Success Response
        Server-->>API: EmptyResponse
        API-->>Repo: ApiResult.Success
        Repo-->>VA: ResultOf.Success(Unit)
    else Error Response
        Server-->>API: ErrorDto
        API-->>Repo: ApiResult.Failure
        Repo->>Analytics: reportException("Sending feedback failed")
        Repo-->>VA: ResultOf.Failure(FeedbackErrors)
    end
    
    VA-->>UI: Update UI state
```

## Presentation Layer

### View Actions

**File**: `core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/presentation/FeedbackViewActions.kt`

```kotlin
// File: core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/presentation/FeedbackViewActions.kt
sealed class FeedbackViewActions : ViewAction {
    data class OnDismiss(val feedbackId: String) : FeedbackViewActions()
    data class OnLiked(val feedbackId: String) : FeedbackViewActions()
    data class OnDisliked(val feedbackId: String) : FeedbackViewActions()
}
```

### Analytics Integration

**File**: `core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/presentation/FeedbackAnalytics.kt`

```kotlin
// File: core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/presentation/FeedbackAnalytics.kt
data object FeedbackDisplayed : AnalyticsEvents("feedback_displayed")

data object FeedbackDismiss : AnalyticsActions("feedback_closed")
data object FeedbackBravo : AnalyticsActions("feedback_bravo")
data object FeedbackNegative : AnalyticsActions("feedback_negative")
```

## Dependency Injection

**File**: `core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/di/CoreFeedbackModule.kt`

```kotlin
// File: core/feedback/src/main/java/co/thewordlab/luzia/core/feedback/di/CoreFeedbackModule.kt
@Module
@InstallIn(SingletonComponent::class)
object CoreFeedbackModule {

    @Provides
    fun provideFeedbackApi(@BaseHost retrofit: Retrofit): FeedbackApi =
        retrofit.create(FeedbackApi::class.java)

    @Provides
    fun provideFeedbackRepository(impl: FeedbackRepositoryImp): FeedbackRepository = impl
}
```

## Module Dependencies

```mermaid
graph TD
    A[core/feedback] --> B[foundation/analytics]
    A --> C[foundation/networking]
    A --> D[foundation/architecture-system]
    A --> E[foundation/common]
    
    subgraph "Inherited Dependencies"
        F[AndroidFeatureConventionPlugin]
        F --> G[Hilt DI]
        F --> H[Retrofit]
        F --> I[Moshi JSON]
        F --> J[Coroutines]
    end
    
    A -.-> F
```

**File**: `core/feedback/build.gradle.kts`

```kotlin
// File: core/feedback/build.gradle.kts
plugins {
    alias(libs.plugins.luzia.android.feature)
}

android {
    namespace = "co.thewordlab.luzia.core.feedback"
}

dependencies {
    implementation(projects.foundation.analytics)
}
```

## Usage Patterns

### Creating Feedback for Chat Messages

```kotlin
// Example usage in chat features
val chatFeedback = Feedback(
    buttonId = ButtonFeedback.THUMBS_UP,
    feedbackId = messageId,
    feedbackType = FeedbackType.PRR,
    feedbackSource = FeedbackSource.Chat(personalityId = "luzia"),
    userFeedback = UserFeedback.OK,
    group = GroupFeedback.GENERIC_PRR
)

// Send feedback
viewModel.handleAction(FeedbackViewActions.OnLiked(messageId))
```

### Creating Feedback for AI Tools

```kotlin
// Math tool feedback
val mathFeedback = Feedback(
    buttonId = ButtonFeedback.THUMBS_DOWN,
    feedbackId = solutionId,
    feedbackType = FeedbackType.PRR,
    feedbackSource = FeedbackSource.Math,
    userFeedback = UserFeedback.KO,
    group = GroupFeedback.GENERIC_PRR
)

// Image generation feedback
val imageFeedback = Feedback(
    buttonId = ButtonFeedback.THUMBS_UP,
    feedbackId = imageId,
    feedbackType = FeedbackType.PRR,
    feedbackSource = FeedbackSource.ImageCreation,
    userFeedback = UserFeedback.OK,
    group = GroupFeedback.IMAGE_GENERATION_PRR
)
```

## Module Integration

The Feedback module provides simple rating functionality for:

- **Features Chat**: Thumbs up/down for AI chat responses
- **Features Tools**: Rating for math solutions and document summaries  
- **Features Imagine**: Feedback for AI-generated images
- **Foundation Analytics**: Tracking user satisfaction patterns

This core module focuses specifically on binary user feedback collection with minimal complexity, designed to gather performance, relevance, and reliability ratings (PRR) across all AI-powered features in the Luzia app.