# Core: Tools Module

**Location**: `core/tools`  
**Package**: `co.thewordlab.luzia.core.tools`  
**Purpose**: Core business logic for Luzia's AI tools including dynamic tools, document processing, math solving, and audio transcription

## Overview

The Tools module provides the core business logic for Luzia's AI-powered tools functionality. It manages tool discovery, presentation, execution, and analytics tracking. The module supports both static tools (predefined) and dynamic tools (configurable via backend), with specialized handling for document processing and mathematical problem solving.

```mermaid
graph TD
    A[Tools Module] --> B[Domain Layer]
    A --> C[Data Layer]
    A --> D[Presentation Layer]
    
    B --> B1[Tool Models]
    B --> B2[Repositories]
    B --> B3[Use Cases]
    B --> B4[Error Handling]
    
    C --> C1[Tools API]
    C --> C2[Local Storage with Store Pattern]
    C --> C3[Audio Processing]
    C --> C4[File Handling]
    
    D --> D1[Tools in Chat ViewModel]
    D --> D2[Analytics Events]
    D --> D3[Navigation Events]
```

## Dependencies

**File**: `core/tools/build.gradle.kts`

```kotlin
// File: core/tools/build.gradle.kts:9-16
dependencies {
    implementation(libs.androidx.dataStore.core)
    implementation(libs.androidx.dataStore.preferences)
    implementation(projects.core.bestiepoints)
    implementation(projects.core.feedback)
    implementation(projects.foundation.analytics)
    implementation(projects.foundation.dataCache)
    implementation(projects.foundation.messages)
}
```

## Core Domain Models

### Tool Entities and Items

**File**: `core/tools/src/main/java/co/thewordlab/luzia/core/tools/domain/model/ToolInChatItem.kt`

```kotlin
// File: core/tools/src/main/java/co/thewordlab/luzia/core/tools/domain/model/ToolInChatItem.kt:5-8
sealed class ToolInChatItem {
    data object Camera : ToolInChatItem()
    data class Tool(val tool: ToolEntity) : ToolInChatItem()
}
```

### Dynamic Tool Components

**File**: `core/tools/src/main/java/co/thewordlab/luzia/core/tools/domain/model/DynamicTool.kt`

The dynamic tool system allows for backend-configurable tool interfaces:

```kotlin
// File: core/tools/src/main/java/co/thewordlab/luzia/core/tools/domain/model/DynamicTool.kt:6-12
data class DynamicTool(
    val id: String,
    val name: String,
    val description: String,
    val footer: List<DynamicToolComponent>,
    val body: List<DynamicToolComponent>
)
```

#### Component Architecture

```mermaid
classDiagram
    class DynamicTool {
        +String id
        +String name
        +String description
        +List~DynamicToolComponent~ footer
        +List~DynamicToolComponent~ body
    }
    
    class DynamicToolComponent {
        +String id
        +DynamicToolComponentType type
        +String title
        +String subtitle
        +List~DynamicToolOptions~ options
        +Int maxLength
        +String placeholder
        +String content
        +DynamicToolButtonType actionType
        +String action
    }
    
    class DynamicToolComponentType {
        <<enumeration>>
        MEDIA_INPUT
        SELECTOR
        BUTTON
        SUBTITLE
        HTML
        TITLE
        NONE
    }
    
    class DynamicToolButtonType {
        <<enumeration>>
        DEEPLINK
        NAVIGATE
        COPY
        WEB_URL
        NONE
    }
    
    DynamicTool --> DynamicToolComponent
    DynamicToolComponent --> DynamicToolComponentType
    DynamicToolComponent --> DynamicToolButtonType
```

#### Component Types

**File**: `core/tools/src/main/java/co/thewordlab/luzia/core/tools/domain/model/DynamicTool.kt:57-82`

```kotlin
// File: core/tools/src/main/java/co/thewordlab/luzia/core/tools/domain/model/DynamicTool.kt:57-82
@JsonClass(generateAdapter = false)
enum class DynamicToolComponentType(val value: String) {
    @Json(name = "media_input")
    MEDIA_INPUT("media_input"),

    @Json(name = "selector")
    SELECTOR("selector"),

    @Json(name = "button")
    BUTTON("button"),

    @Json(name = "subtitle")
    SUBTITLE("subtitle"),

    @Json(name = "html")
    HTML("html"),

    @Json(name = "title")
    TITLE("title"),

    @Json(name = "none")
    NONE("none");
    
    companion object {
        fun fromValue(value: String?): DynamicToolComponentType = entries.find { it.value == value } ?: NONE
    }
}
```

### Error Handling

**File**: `core/tools/src/main/java/co/thewordlab/luzia/core/tools/domain/model/ToolsErrors.kt`

```kotlin
// File: core/tools/src/main/java/co/thewordlab/luzia/core/tools/domain/model/ToolsErrors.kt:6-10
sealed class ToolsErrors(val description: String) : Error {

    data class CommonError(val error: CommonErrors) : ToolsErrors(error.description)
    data class DynamicToolError(val error: String) : ToolsErrors(error)
}
```

## Repository Layer

### ToolsRepository Interface

**File**: `core/tools/src/main/java/co/thewordlab/luzia/core/tools/domain/repository/ToolsRepository.kt`

```kotlin
// File: core/tools/src/main/java/co/thewordlab/luzia/core/tools/domain/repository/ToolsRepository.kt:13-37
interface ToolsRepository {

    fun getTools(): Flow<List<ToolEntity>>

    fun getAvailableTools(): Flow<List<ToolEntity>>

    suspend fun getDynamicTool(toolId: String): ResultOf<DynamicTool, ToolsErrors>

    suspend fun getDynamicToolResult(
        toolId: String,
        file: File?,
        fileInfo: ToolSupportedFile,
        config: List<DynamicToolConfig>
    ): ResultOf<DynamicTool, ToolsErrors>

    suspend fun shouldShowBestiePointsBanner(): Boolean

    suspend fun markBestiePointsBannerAsShown()

    suspend fun sendAudioRecorder(
        audio: File,
        personalityId: String?,
        isForwarded: Boolean?
    ): ResultOf<ContentText, ToolsErrors>
}
```

### Implementation with Store Pattern

**File**: `core/tools/src/main/java/co/thewordlab/luzia/core/tools/data/repository/ToolsRepositoryImpl.kt`

The repository uses the foundation data-cache Store pattern for reactive caching:

```kotlin
// File: core/tools/src/main/java/co/thewordlab/luzia/core/tools/data/repository/ToolsRepositoryImpl.kt:48-77
private val store = inMemoryStore(
    fetcher = {
        val result = toolsApi.getTools()
        if (result is ApiResult.Success) {
            val featuredId = result.value.popular.firstOrNull()?.id
            val remoteTools = result.value.popular + result.value.tools
            remoteTools.mapIndexed { index, item ->
                item.asEntity(
                    index = index,
                    isPopular = item.upcoming == null || item.id == featuredId,
                    featuredId = featuredId
                )
            }
        } else {
            emptyList()
        }
    },
    diskCache = object : DiskCache<List<ToolEntity>> {
        override fun getFromCache(): Flow<List<ToolEntity>> {
            return toolDao.getAllTools()
        }

        override suspend fun saveIntoCache(item: List<ToolEntity>) {
            if (item.isNotEmpty()) {
                toolDao.removeAll()
                toolDao.insertTools(item)
            }
        }
    }
).withExpiration(TOOLS_CACHE_EXPIRY).build()
```

### Tools API Integration

**File**: `core/tools/src/main/java/co/thewordlab/luzia/core/tools/data/api/ToolsApi.kt`

```kotlin
// File: core/tools/src/main/java/co/thewordlab/luzia/core/tools/data/api/ToolsApi.kt:23-78
interface ToolsApi {

    @CacheEnabled(CacheExpiry.HOUR_12)
    @DecodeErrorBody
    @GET("tools")
    suspend fun getTools(): ApiResult<ToolsDTO, ErrorDto>

    @DecodeErrorBody
    @POST("audio-transcriptions")
    suspend fun postAudioTranscriptions(
        @Body body: RequestBody? = null,
        @Header("Accept-Language") acceptLanguage: String = Locale.getDefault().toLanguageTag(),
        @Header("Content-Type") contentType: String? = AUDIO_MP4,
        @Query("personalityId") personalityId: String? = null,
        @Query("forwarded") isForwarded: Boolean?
    ): ApiResult<ContentTextDto, ErrorDto>

    @DecodeErrorBody
    @GET("dynamic-tools/{toolId}")
    suspend fun getDynamicTools(
        @Path("toolId") toolId: String
    ): ApiResult<DynamicToolDto, ErrorDto>

    @DecodeErrorBody
    @POST("dynamic-tools/{toolId}/process")
    suspend fun getDynamicToolResult(
        @Path("toolId") toolId: String,
        @Body body: MultipartBody
    ): ApiResult<DynamicToolDto, ErrorDto>

    @DecodeErrorBody
    @POST("attachments/upload")
    suspend fun sendAttachedFiles(@Body body: MultipartBody): ApiResult<ContentTextDto, ErrorDto>

    @DecodeErrorBody
    @POST("tools/documents/chat")
    suspend fun sendChatWithDocuments(@Body body: CompletionsRequest): ApiResult<ContentTextDto, ErrorDto>

    @DecodeErrorBody
    @GET("tools/documents")
    suspend fun getDocumentsDetails(): ApiResult<ToolDetailDto, ErrorDto>

    companion object {
        const val AUDIO_MP4 = "audio/mp4"
    }
}
```

## Specialized Tool Repositories

### Document Tool Repository

**File**: `core/tools/src/main/java/co/thewordlab/luzia/core/tools/domain/repository/DocumentToolRepository.kt`

Handles document processing tools with chat-like interface:

```kotlin
// File: core/tools/src/main/java/co/thewordlab/luzia/core/tools/domain/repository/DocumentToolRepository.kt:12-42
interface DocumentToolRepository {

    suspend fun getMessages(personalityId: String): Flow<PagingData<MessageEntity>>
    suspend fun resendMessage(
        attachedFileId: String?,
        message: MessageEntity
    ): ResultOf<ContentTextDto, AppErrors>

    suspend fun sendFileForDocumentTool(
        personalityId: String,
        file: File,
        insertDocumentInChatAfterNextMessage: Boolean
    ): ResultOf<ContentTextDto, AppErrors>

    suspend fun sendQuestionForDocumentTool(
        personalityId: String,
        attachedFileId: String?,
        text: String,
    ): ResultOf<ContentTextDto, AppErrors>

    suspend fun sendAudioRecordingForDocument(
        personalityId: String,
        attachedFileId: String?,
        file: File,
    ): ResultOf<ContentTextDto, AppErrors>

    suspend fun getIceBreakers(): ResultOf<ToolDetailDto, AppErrors>
    suspend fun dismissFeedback(feedbackId: String)
    fun setContextWindowSize(numberOfMessages: Int)
    fun clearDocuments()
}
```

### Math Tool Repository

**File**: `core/tools/src/main/java/co/thewordlab/luzia/core/tools/domain/repository/MathToolRepository.kt`

Handles mathematical problem solving with image recognition:

```kotlin
// File: core/tools/src/main/java/co/thewordlab/luzia/core/tools/domain/repository/MathToolRepository.kt:13-23
interface MathToolRepository {

    fun getMessages(): Flow<PagingData<MessageEntity>>
    suspend fun resendMessage(message: MessageEntity): ResultOf<ContentTextDto, AppErrors>
    suspend fun sendImage(file: File): ResultOf<MathImageDto, AppErrors>
    suspend fun chatCompletion(text: String): ResultOf<ContentTextDto, AppErrors>
    suspend fun sendAudioRecording(file: File): ResultOf<ContentTextDto, AppErrors>
    suspend fun getIceBreakers(): ResultOf<ToolDetailDto, AppErrors>
    suspend fun clearMessages()
    suspend fun dismissFeedback(feedbackId: String)
}
```

## Use Cases and Business Logic

### Get Tools in Chat Use Case

**File**: `core/tools/src/main/java/co/thewordlab/luzia/core/tools/domain/usecases/GetToolsInChatUseCase.kt`

```kotlin
// File: core/tools/src/main/java/co/thewordlab/luzia/core/tools/domain/usecases/GetToolsInChatUseCase.kt:10-18
class GetToolsInChatUseCase @Inject constructor(
    private val toolsRepository: ToolsRepository
) {
    operator fun invoke(): Flow<List<ToolInChatItem>> =
        toolsRepository.getAvailableTools().map { tools ->
            tools.map { ToolInChatItem.Tool(it) }
                .filter { it.tool.id != SupportedTools.VISION_TOOL_ID.key }
        }
}
```

## Presentation Layer

### Tools in Chat ViewModel

**File**: `core/tools/src/main/java/co/thewordlab/luzia/core/tools/presentation/toolsinchat/ToolsInChatViewModel.kt`

Uses the MVVM+MVI delegation pattern:

```kotlin
// File: core/tools/src/main/java/co/thewordlab/luzia/core/tools/presentation/toolsinchat/ToolsInChatViewModel.kt:28-35
@HiltViewModel
class ToolsInChatViewModel @Inject constructor(
    private val getToolsInChatUseCase: GetToolsInChatUseCase,
    private val analytics: Analytics,
) : ViewModel(),
    ViewModelStates<ToolsInChatViewState> by ViewModelStatesImpl(ToolsInChatViewState()),
    ViewModelActions<ToolsInChatViewActions>,
    ViewModelEvents<ToolsInChatViewEvents> by ViewModelEventsImpl() {
```

### Action Handling

**File**: `core/tools/src/main/java/co/thewordlab/luzia/core/tools/presentation/toolsinchat/ToolsInChatViewModel.kt:47-67`

```kotlin
// File: core/tools/src/main/java/co/thewordlab/luzia/core/tools/presentation/toolsinchat/ToolsInChatViewModel.kt:47-67
override fun onViewAction(action: ToolsInChatViewActions) = when (action) {
    is ToolsInChatViewActions.OnClick -> onItemClicked(action.item)
    is ToolsInChatViewActions.OnCameraClick -> {
        trackToolClick(TOOL_ID_CAMERA)
        analytics.logEvent(Event.PlusItem, mapOf(Parameter.Camera to true))
        sendEvent(ToolsInChatViewEvents.NavigateToCamera(action.openGallery))
    }
}

private fun onItemClicked(item: ToolInChatItem) = when (item) {
    ToolInChatItem.Camera -> DO_NOTHING
    is ToolInChatItem.Tool -> {
        trackToolClick(item.tool.id)
        analytics.logEvent(Event.PlusItem, mapOf(Parameter.Tool to item.tool.id))
        if (item.tool.id == SupportedTools.VISION_TOOL_ID.key) {
            sendEvent(ToolsInChatViewEvents.NavigateToCamera(openGallery = false))
        } else {
            sendEvent(ToolsInChatViewEvents.NavigateToTool(item.tool.id, item.tool.isDynamic))
        }
    }
}
```

## Data Flow Architecture

```mermaid
sequenceDiagram
    participant UI as Tools UI
    participant VM as ToolsInChatViewModel
    participant UC as GetToolsInChatUseCase
    participant Repo as ToolsRepository
    participant Store as InMemoryStore
    participant API as ToolsApi
    participant DB as ToolDao
    
    UI->>VM: Initialize
    VM->>UC: invoke()
    UC->>Repo: getAvailableTools()
    Repo->>DB: getAllTools()
    DB-->>Repo: Flow<List<ToolEntity>>
    Repo-->>UC: Filtered tools (max 7)
    UC-->>VM: Flow<List<ToolInChatItem>>
    VM-->>UI: State update
    
    Note over Store: Background refresh every 5 minutes
    Store->>API: getTools()
    API-->>Store: ToolsDTO
    Store->>DB: saveIntoCache()
```

## Analytics Integration

**File**: `core/tools/src/main/java/co/thewordlab/luzia/core/tools/Analytics.kt`

Comprehensive analytics tracking for tool usage:

```kotlin
// File: core/tools/src/main/java/co/thewordlab/luzia/core/tools/Analytics.kt:9-21
data object Tools : AnalyticsScreens("tools")
data object DynamicTools : AnalyticsScreens("presentation_tool")
data object DynamicToolsResult : AnalyticsScreens("tool_response")

data object DynamicToolInputButtonClicked : AnalyticsActions("tool_input_tap")
data object DynamicToolAttachmentClicked : AnalyticsActions("tool_attachment_selection")
data object DynamicToolSubmitClicked : AnalyticsActions("tool_submit")
data object DynamicToolCopyClicked : AnalyticsActions("tool_response_copy")
data object DynamicToolShareClicked : AnalyticsActions("tool_response_share")
data object DynamicToolNewClicked : AnalyticsActions("tool_new")
data object ChatComposerToolClicked : AnalyticsActions("chat_composer_tool_tap")
data object ToolClicked : AnalyticsActions("tool_tap")
```

### Analytics Parameters

**File**: `core/tools/src/main/java/co/thewordlab/luzia/core/tools/Analytics.kt:34-41`

```kotlin
// File: core/tools/src/main/java/co/thewordlab/luzia/core/tools/Analytics.kt:34-41
fun buildParamsForLaunchAction(toolId: String, origin: String, options: List<DynamicToolConfig>): Map<String, Any> {
    val params = mutableMapOf(
        PARAMETER_TOOL to toolId,
        PARAMETER_ORIGIN to origin
    )
    options.forEach { params[it.componentId] = it.selectedOption.orEmpty() }
    return params.toMap()
}

// Constants defined in the same file:
const val INPUT_TYPE_ATTACHMENT = "attachment"
const val INPUT_TYPE_PASTE = "paste"
const val INPUT_TYPE_CAMERA = "camera"
const val INPUT_TYPE_GALLERY = "gallery"
const val INPUT_TYPE_FILES = "files"
const val PARAMETER_TOOL = "tool"
const val PARAMETER_ORIGIN = "origin"
const val PARAMETER_INPUT = "input"
const val PARAMETER_TYPE = "type"
const val ORIGIN_CHAT = "chat"
const val TOOL_ID_CAMERA = "camera"
```

## Key Features

### 1. **Dynamic Tool System**
- Backend-configurable tool interfaces
- Component-based UI building (media input, selectors, buttons, HTML)
- Flexible form configuration with validation

### 2. **Store-Based Caching**
- Uses foundation data-cache Store pattern
- 5-minute cache expiration for tool lists
- Automatic background refresh with Room persistence

### 3. **Specialized Tool Types**
- **Document Tools**: PDF/file processing with chat interface
- **Math Tools**: Image recognition for mathematical problems
- **Audio Tools**: Transcription and voice processing

### 4. **Analytics Integration**
- Comprehensive tracking of tool interactions
- Origin tracking (chat vs standalone)
- Dynamic parameter collection

### 5. **MVVM+MVI Architecture**
- Interface delegation pattern from foundation architecture-system
- Reactive state management with Flow
- Type-safe action and event handling

## Error Handling

```mermaid
flowchart TD
    A[API Request] --> B{Request Result}
    B -->|Success| C[Map to Domain Model]
    B -->|Network Error| D[CommonErrors.NetworkError]
    B -->|HTTP Error| E[Parse ErrorDto]
    
    E --> F{Can Parse?}
    F -->|Yes| G[DynamicToolError]
    F -->|No| H[UnspecifiedError]
    
    C --> I[ResultOf.Success]
    D --> J[ToolsErrors.CommonError]
    G --> K[ToolsErrors.DynamicToolError]
    H --> L[ToolsErrors.CommonError]
    
    J --> M[UI Error Handling]
    K --> M
    L --> M
```

## Usage Patterns

### Tool Selection and Navigation

```kotlin
// File: Example usage based on ToolsInChatViewModel.kt implementation
class ToolNavigationHandler {
    
    fun handleToolSelection(tool: ToolEntity) {
        when {
            tool.isDynamic -> {
                // Navigate to dynamic tool screen
                navigator.navigateToTool(tool.id, isDynamic = true)
            }
            tool.id == "math" -> {
                // Navigate to specialized math tool
                navigator.navigateToMathTool()
            }
            tool.id == "document" -> {
                // Navigate to document tool
                navigator.navigateToDocumentTool()
            }
            else -> {
                // Handle legacy tools
                navigator.navigateToLegacyTool(tool.id)
            }
        }
    }
}
```

### Dynamic Tool Processing

```kotlin
// File: Example usage based on ToolsRepository.kt implementation
suspend fun processDynamicTool(
    toolId: String,
    selectedFile: File?,
    userConfiguration: List<DynamicToolConfig>
) {
    val result = toolsRepository.getDynamicToolResult(
        toolId = toolId,
        file = selectedFile,
        fileInfo = ToolSupportedFile(
            fileName = selectedFile?.name ?: "input.txt",
            mimeType = "application/pdf"
        ),
        config = userConfiguration
    )
    
    when (result) {
        is ResultOf.Success -> {
            // Display tool result with components
            displayToolResult(result.data)
        }
        is ResultOf.Failure -> {
            // Handle error
            handleToolError(result.error)
        }
    }
}
```

### Audio Transcription

```kotlin
// File: Example usage based on ToolsRepository.kt sendAudioRecorder
suspend fun transcribeAudio(audioFile: File, personalityId: String?) {
    val result = toolsRepository.sendAudioRecorder(
        audio = audioFile,
        personalityId = personalityId,
        isForwarded = false
    )
    
    when (result) {
        is ResultOf.Success -> {
            val transcription = result.data.content
            // Use transcription in chat or tool
        }
        is ResultOf.Failure -> {
            // Handle transcription error
        }
    }
}
```

## Integration Points

- **Foundation Data Cache**: Store pattern for reactive caching
- **Foundation Analytics**: Comprehensive usage tracking
- **Foundation Messages**: Shared message models and API integration
- **Core Bestie Points**: Gamification banner management
- **Core Feedback**: Tool response feedback system
- **Features**: UI screens consume this business logic

This Tools module provides focused business logic for Luzia's AI tool ecosystem, emphasizing dynamic configuration, specialized tool types, and comprehensive analytics tracking while maintaining clean architecture principles.