# Core: Navigation Module

## Overview

The Navigation module provides a lightweight, type-safe navigation system for the Luzia Android app using Navigation Compose with Kotlin serialization. The module focuses on deep link handling, route definitions, and navigation utilities rather than complex state management or guards.

## Module Dependencies

```mermaid
graph TD
    A[core/navigation] --> B[foundation/analytics]
    A --> C[foundation/common]
    A --> D[foundation/design-system]
    A --> E[Navigation Compose]
    A --> F[Kotlin Serialization]
```

## Core Architecture

The navigation system is built around several key components:

```mermaid
graph LR
    A[Navigation Interface] --> B[RouteNavHost]
    A --> C[DeeplinkHandler]
    A --> D[UserSessionRoutes]
    B --> E[LocalNavigation]
    C --> F[Deep Link Processing]
    D --> G[Serializable Routes]
```

## Navigation Interface

The core navigation contract is simple and lightweight:

```kotlin
// File: core/navigation/src/main/java/co/thewordlab/luzia/core/navigation/common/Navigation.kt
interface Navigation {
    fun navigate(route: Route, options: NavOptions? = null)
    fun goBack()
    fun goBackTo(route: Route, inclusive: Boolean = false)
    fun handleDeepLink(uri: Uri, options: NavOptions? = null)
}
```

The `Route` interface is a marker interface for type safety:

```kotlin
// File: core/navigation/src/main/java/co/thewordlab/luzia/core/navigation/common/Route.kt
interface Route
```

## Route Definitions

All navigation routes are defined as serializable sealed classes implementing the `Route` interface:

```kotlin
// File: core/navigation/src/main/java/co/thewordlab/luzia/core/navigation/usersession/UserSessionRoutes.kt:14-30
@Serializable
sealed class UserSessionRoutes : Route {

    @Serializable
    data class Landing(
        val destinations: LandingDestinations,
        val showAInAppReview: Boolean = false
    ) : UserSessionRoutes()

    @Serializable
    data class ChatDetail(
        val personalityId: String,
        val highlightId: Long? = null,
        val isCustomBestie: Boolean,
        val openKeyboardOnLaunch: Boolean = false
    ) : UserSessionRoutes()
```

### Key Route Categories

**Main App Navigation:**
- `Landing` - Bottom tab navigation with destinations
- `ChatDetail` - Individual chat screens with personality context
- `Profile` - User profile management
- `Settings` - App configuration

**AI Tools:**
- `ImagineTool` - Image generation tool
- `DocumentTool` - Document processing
- `MathTool` - Mathematical problem solving
- `Vision` - Image recognition tool
- `DynamicTool` - Server-driven tool configuration

**Social Features:**
- `GetStreamChatDetail` - Group chat functionality
- `NewChat`/`NewGroup` - Chat creation flows
- `Invite` - Group invitation handling

**Profile & Authentication:**
- `Signup` - User registration flow
- `ProfileFill` - Profile completion wizard
- `ExternalProfile` - Other user profiles

## Deep Link System

The deep link system processes `luzia://` scheme URLs and routes them to appropriate screens:

```mermaid
flowchart TD
    A[Deep Link Received] --> B[DeeplinkHandler.navigate]
    B --> C[Extract Path Segments]
    C --> D{Match Deeplink Type}
    
    D -->|landing/home| E[Landing Screen]
    D -->|chats/id| F[Chat Detail]
    D -->|tools/type| G[Tool Screen]
    D -->|profile/action| H[Profile Screen]
    D -->|groups/id| I[Group Chat]
    
    E --> J[Navigate with Options]
    F --> J
    G --> J
    H --> J
    I --> J
```

### Deep Link Handler Implementation

```kotlin
// File: core/navigation/src/main/java/co/thewordlab/luzia/core/navigation/common/DeeplinkHandler.kt:20-45
class DeeplinkHandler @Inject constructor(
    private val analytics: Analytics
) {

    @SuppressLint("RestrictedApi")
    fun navigate(navController: NavHostController, intentUri: Uri?, navOptions: NavOptions? = null) {
        if (intentUri != null && intentUri.toString().startsWith(LUZIA_SCHEME)) {
            try {
                val navigation = resolve(intentUri.toString(), navController)
                navigation?.let {
                    val options = navOptions ?: navOptions {
                        val singleTop = it.shouldLaunchSingleTop()
                        launchSingleTop = singleTop
                        if (it.clearNavigationStack()) {
                            popUpTo(it::class) { inclusive = true }
                        }
                    }
                    navController.navigate(it, options)
                }
            } catch (expected: Exception) {
                analytics.reportException("DeeplinkHandler Exception", expected)
            }
        }
    }
```

### Deep Link Route Resolution

The resolver parses deep link paths and maps them to routes:

```kotlin
// File: core/navigation/src/main/java/co/thewordlab/luzia/core/navigation/common/DeeplinkHandler.kt:45-64
fun resolve(link: String, navController: NavHostController?): UserSessionRoutes? {
    val deepLink = link.removePrefix(LUZIA_SCHEME)
    val split = deepLink.split("/")
    val navigation: UserSessionRoutes? = when (Deeplink.fromRoute(split.first())) {
        Deeplink.Landing -> landingNavigation(split[1])
        Deeplink.Chats -> UserSessionRoutes.ChatDetail(personalityId = split[1], isCustomBestie = false)
        Deeplink.BestiePoints -> UserSessionRoutes.BestiePoints
        Deeplink.Groups -> UserSessionRoutes.GetStreamChatDetail(channelId = split[1])
        Deeplink.Proactive -> UserSessionRoutes.ProactiveSharedMessage(masterUserId = split[2])
        Deeplink.Profile -> profileNavigation(split[1])
        Deeplink.Signup -> UserSessionRoutes.Signup()
        Deeplink.Tools -> toolsNavigation(split[1], navController?.currentBackStackEntry)
        Deeplink.NewGroupChat -> UserSessionRoutes.NewChat(NewChatPurpose.MESSAGE)
        Deeplink.AppReview -> UserSessionRoutes.Landing(LandingDestinations.HOME, true)
        Deeplink.Settings -> settingsNavigation(split[1])
        Deeplink.CustomBestie -> bestieNavigation(split)
        Deeplink.GroupJoin -> groupJoinNavigation(split[1])
    }
    return navigation
}
```

### Supported Deep Link Types

```kotlin
// File: core/navigation/src/main/java/co/thewordlab/luzia/core/navigation/common/DeeplinkHandler.kt:130-164
sealed class Deeplink(val id: String) {
    data object Landing : Deeplink("landing")
    data object Chats : Deeplink("chats")
    data object Tools : Deeplink("tools")
    data object BestiePoints : Deeplink("bp")
    data object Signup : Deeplink("signup")
    data object Profile : Deeplink("profile")
    data object Proactive : Deeplink("proactive")
    data object Groups : Deeplink("groups")
    data object NewGroupChat : Deeplink("new-group-chat")
    data object AppReview : Deeplink("app_review")
    data object Settings : Deeplink("settings")
    data object CustomBestie : Deeplink("custom-bestie")
    data object GroupJoin : Deeplink("groups-invite-join")
```

## Navigation Host Implementation

The `RouteNavHost` provides a Compose-based navigation setup with local navigation context:

```kotlin
// File: core/navigation/src/main/java/co/thewordlab/luzia/core/navigation/common/RouteNavHost.kt:28-46
val navigation = remember {
    object : Navigation {
        override fun navigate(route: Route, options: NavOptions?) {
            navController.navigate(route, options)
        }

        override fun goBack() {
            navController.popBackStack()
        }

        override fun goBackTo(route: Route, inclusive: Boolean) {
            navController.popBackStack(route, inclusive)
        }

        override fun handleDeepLink(uri: Uri, options: NavOptions?) {
            deeplinkHandler.navigate(navController, uri, options)
        }
    }
}
```

### Composition Local Navigation

Navigation is provided through Compose's CompositionLocal pattern:

```kotlin
// File: core/navigation/src/main/java/co/thewordlab/luzia/core/navigation/common/LocalNavigation.kt:8-26
val LocalNavigation = staticCompositionLocalOf<Navigation> {
    object : Navigation {
        override fun navigate(route: Route, options: NavOptions?) {
            DO_NOTHING
        }

        override fun goBack() {
            DO_NOTHING
        }

        override fun goBackTo(route: Route, inclusive: Boolean) {
            DO_NOTHING
        }

        override fun handleDeepLink(uri: Uri, options: NavOptions?) {
            DO_NOTHING
        }
    }
}
```

## Navigation Utilities

### Link Handling Extensions

The module provides utilities for handling various link types:

```kotlin
// File: core/navigation/src/main/java/co/thewordlab/luzia/core/navigation/common/Extensions.kt:15-36
fun Context.onLinkClicked(navigation: Navigation, url: String) {
    val isNetworkUrl = URLUtil.isNetworkUrl(url)
    val isLuziaDeepLink = url.startsWith("luzia://")
    when {
        isLuziaDeepLink ->
            navigation.handleDeepLink(url.toUri())

        isNetworkUrl -> {
            val route = UserSessionRoutes.Web("", url)
            navigation.navigate(route)
        }

        else ->
            try {
                val uri = Uri.parse(removeQueryParameter(url, QUERY_STRING_OPEN_IN_BROWSER))
                val intent = Intent(Intent.ACTION_VIEW, uri)
                startActivity(intent)
            } catch (ex: ActivityNotFoundException) {
                Log.d("LuziaApp", "Intent action not found: ${ex.message}")
            }
    }
}
```

### Bottom Sheet Navigation

Support for modal bottom sheet navigation:

```kotlin
// File: core/navigation/src/main/java/co/thewordlab/luzia/core/navigation/common/NavigationBottomSheetState.kt:23-36
@OptIn(ExperimentalMaterial3Api::class)
data class NavigationBottomSheetState(
    val backstackEntry: NavBackStackEntry,
    private val coroutineScope: CoroutineScope,
    private val bottomSheetState: SheetState,
    private val navigation: Navigation,
) {
    @OptIn(ExperimentalMaterial3Api::class)
    fun close() {
        coroutineScope.launch {
            bottomSheetState.hide()
            navigation.goBack()
        }
    }
}
```

## Model Classes

### New Chat Purpose

```kotlin
// File: core/navigation/src/main/java/co/thewordlab/luzia/core/navigation/usersession/model/NewChatPurpose.kt:8-11
@Keep
enum class NewChatPurpose(@StringRes val titleRes: Int, val source: String) {
    MESSAGE(R.string.new_chat, "chat_list"),
    SHARE(R.string.share_content_title, "share_list"),
}
```

### Settings Destinations

```kotlin
// File: core/navigation/src/main/java/co/thewordlab/luzia/core/navigation/usersession/model/SettingDestinations.kt:8-11
@Keep
@Stable
enum class SettingDestinations(val value: String) {
    CONTACT_US("contact_us"),
    NONE("none")
}
```

## Dependencies

```kotlin
// File: core/navigation/build.gradle.kts:10-15
dependencies {
    implementation(libs.kotlinx.serialization.json)
    implementation(projects.foundation.analytics)
    implementation(projects.foundation.common)
    implementation(projects.foundation.designSystem)
}
```

The module uses:
- **Kotlin Serialization** for type-safe route parameters
- **Navigation Compose** (inherited from feature plugin)
- **Foundation Analytics** for error reporting
- **Foundation Common** for utility extensions
- **Foundation Design System** for theme integration

## Key Design Principles

1. **Type Safety**: All routes are strongly typed with Kotlin serialization
2. **Simplicity**: Minimal abstraction over Navigation Compose
3. **Deep Link Focus**: Comprehensive deep link handling for app-to-app navigation
4. **Composition**: Uses CompositionLocal for navigation context
5. **Error Handling**: Analytics integration for deep link processing errors

## Module Integration

The Navigation module provides routing infrastructure for:
- **Main App**: Route definitions and navigation graph setup
- **All Feature Modules**: Type-safe navigation through LocalNavigation
- **Deep Link Handling**: App-wide deep link processing and routing
- **Analytics**: Navigation error reporting and tracking

This core module ensures consistent, type-safe navigation throughout the Luzia app while maintaining simplicity and focusing on the specific needs of deep link handling and route management.