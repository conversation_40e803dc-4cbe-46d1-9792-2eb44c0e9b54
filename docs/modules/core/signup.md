# Core: Signup Module

## Overview

The Signup module provides **Google OAuth authentication only** for Luzia Android app. This focused core module handles Google Sign-In using Android Credential Manager API with fallback to Custom Tabs for web authentication. It does NOT handle general user registration, onboarding flows, or email/password authentication.

## Architecture

```mermaid
graph TD
    A[GoogleWebAuthManager] --> B[Android Credential Manager]
    A --> C[Custom Tabs Fallback]
    A --> D[Google OAuth API]
    
    subgraph "Authentication Flow"
        B --> E[Native Google Sign-In]
        E --> F[Authorized Accounts]
        F --> G[All Accounts]
        G --> C
    end
    
    subgraph "Web Authentication"
        C --> H[OpenID Connect]
        H --> I[Authorization Code]
        I --> J[Token Exchange]
        J --> K[ID Token + Profile]
    end
    
    subgraph "Profile Extraction"
        D --> L[Google UserInfo API]
        L --> M[Name, Email, Picture]
    end
```

## Google Authentication Flow

```mermaid
sequenceDiagram
    participant UI as Auth UI
    participant AS as AuthState
    participant CM as CredentialManager
    participant <PERSON><PERSON> as GoogleWebAuthManager
    participant CT as CustomTabs
    participant GA as GoogleApi
    
    UI->>AS: loginWithCredentials()
    AS->>GWA: getNativeSignInRequest(true)
    AS->>CM: getCredential(authorized=true)
    
    alt Native Auth Success
        CM-->>AS: GoogleIdTokenCredential
        AS-->>UI: AuthResult(idToken, name, picture)
    else Native Auth Fails
        AS->>GWA: getNativeSignInRequest(false)
        AS->>CM: getCredential(authorized=false)
        
        alt All Accounts Fails
            AS->>GWA: createCustomTabsIntent()
            GWA->>CT: OAuth Authorization Flow
            CT-->>GWA: Authorization Code
            GWA->>GWA: Token Exchange
            GWA->>GA: getUserInfo(accessToken)
            GA-->>GWA: GoogleUserDto
            GWA-->>AS: AuthResult
            AS-->>UI: AuthResult
        end
    end
```

## Core Models

```kotlin
// File: core/signup/src/main/java/co/thewordlab/luzia/core/signup/handler/AuthResult.kt
data class AuthResult(
    val idToken: String,
    val name: String?,
    val picture: String?,
    val email: String?
)

// File: core/signup/src/main/java/co/thewordlab/luzia/core/signup/data/model/GoogleUserDto.kt
@JsonClass(generateAdapter = true)
data class GoogleUserDto(
    @Json(name = "name")
    val name: String? = null,
    @Json(name = "picture")
    val picture: String? = null,
    @Json(name = "email")
    val email: String? = null,
)

// File: core/signup/src/main/java/co/thewordlab/luzia/core/signup/web/GoogleWebErrors.kt
sealed class GoogleWebErrors(
    val title: String,
    @StringRes val errorResId: Int
) : Throwable(title) {
    data class ConfigFetchFailed(val innerException: Exception?) : GoogleWebErrors(...)
    data class EmptyResult(val innerException: Exception?) : GoogleWebErrors(...)
    data class TokenFailed(val innerException: Exception?) : GoogleWebErrors(...)
    data class UserCancelled(val innerException: Exception?) : GoogleWebErrors(...)
    data class ServerFailed(val innerException: Exception?) : GoogleWebErrors(...)
    data class BrowserNotFound(val innerException: Exception?) : GoogleWebErrors(...)
}
```

## GoogleWebAuthManager

```kotlin
// File: core/signup/src/main/java/co/thewordlab/luzia/core/signup/web/GoogleWebAuthManager.kt
class GoogleWebAuthManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val secureStorage: SecureStorage,
    private val googleApi: GoogleApi,
    private val analytics: Analytics
) {
    private val authService = AuthorizationService(context)
    
    fun getNativeSignInRequest(filterByAuthorizedAccounts: Boolean): GetCredentialRequest {
        val googleIdOption: GetGoogleIdOption = GetGoogleIdOption.Builder()
            .setFilterByAuthorizedAccounts(filterByAuthorizedAccounts)
            .setServerClientId(secureStorage.get(SecureKey.KEY_GOOGLE_WEB_CLIENT_ID))
            .build()
        return GetCredentialRequest.Builder()
            .addCredentialOption(googleIdOption)
            .build()
    }
    
    suspend fun createCustomTabsIntent(): Intent {
        return suspendCancellableCoroutine { continuation ->
            AuthorizationServiceConfiguration.fetchFromUrl(Uri.parse(AUTH_CONFIG_URL)) { config, ex ->
                if (config != null) {
                    val request = AuthorizationRequest.Builder(
                        config,
                        getClientId(),
                        ResponseTypeValues.CODE,
                        getRedirectUrl()
                    )
                        .setNonce(null)
                        .setScope(SCOPES)
                        .setCodeVerifier(null, null, null)
                        .build()
                    val authIntent = authService.getAuthorizationRequestIntent(request)
                    if (continuation.isActive) {
                        continuation.resume(authIntent)
                    }
                } else {
                    continuation.resumeWithException(GoogleWebErrors.ConfigFetchFailed(ex))
                }
            }
        }
    }
    
    suspend fun processData(data: Intent): AuthResult {
        val authState = obtainAuthState(data)
        val profile: GoogleUserDto? = extractProfileInformation(authState)
        return AuthResult(authState.idToken!!, profile?.name, profile?.picture, profile?.email)
    }
    
    private suspend fun extractProfileInformation(authState: AuthState): GoogleUserDto? {
        val accessToken = authState.accessToken ?: return null
        val result = googleApi.getUserInfo("Bearer $accessToken")
        return (result as? ApiResult.Success)?.value
    }
    
    private fun getClientId(): String {
        val prefix = getPrefix()
        return "$prefix.apps.googleusercontent.com"
    }
    
    private fun getPrefix(): String {
        val key = if (BuildConfig.DEBUG) {
            SecureKey.KEY_GOOGLE_CLIENT_PREFIX_DEBUG
        } else {
            SecureKey.KEY_GOOGLE_CLIENT_PREFIX
        }
        return secureStorage.get(key)
    }
    
    companion object {
        const val AUTH_CONFIG_URL = "https://accounts.google.com/.well-known/openid-configuration"
        const val SCOPES = "openid profile email"
    }
}
```

## AuthState (Compose Helper)

```mermaid
graph TD
    A[rememberAuthState] --> B[AuthState Instance]
    B --> C[loginWithCredentials]
    B --> D[logout]
    
    subgraph "Native Authentication"
        C --> E[CredentialManager.getCredential]
        E --> F[filterByAuthorizedAccounts=true]
        F --> G[Success: GoogleIdTokenCredential]
        F --> H[Failure: Try All Accounts]
        H --> I[filterByAuthorizedAccounts=false]
        I --> J[Success: AuthResult]
        I --> K[Failure: Web Fallback]
    end
    
    subgraph "Web Authentication"
        K --> L[createCustomTabsIntent]
        L --> M[Launch Custom Tabs]
        M --> N[onActivityResult]
        N --> O[processData]
        O --> P[AuthResult]
    end
```

```kotlin
// File: core/signup/src/main/java/co/thewordlab/luzia/core/signup/handler/AuthState.kt
class AuthState(
    private val coroutineScope: CoroutineScope,
    private val context: Context,
    private val googleWebAuthManager: GoogleWebAuthManager,
    private val credentialManager: CredentialManager,
    private val onWebIntentLaunch: suspend (Intent) -> Unit,
    private val onSuccess: (AuthResult) -> Unit
) {
    
    fun loginWithCredentials() {
        coroutineScope.launch {
            val nativeRequest = googleWebAuthManager.getNativeSignInRequest(true)
            credentialManager.login(
                activity = context.findActivity(),
                request = nativeRequest,
                onSuccess = onSuccess,
                onFailure = {
                    loginWithCredentialsWithoutAuthorizedAccounts()
                }
            )
        }
    }
    
    fun logout() {
        coroutineScope.launch {
            runCatching {
                credentialManager.clearCredentialState(ClearCredentialStateRequest())
            }
        }
    }
    
    private fun loginWithWebIntents() {
        coroutineScope.launch {
            val intent = googleWebAuthManager.createCustomTabsIntent()
            onWebIntentLaunch(intent)
        }
    }
    
    private suspend fun CredentialManager.login(
        activity: Activity,
        request: GetCredentialRequest,
        onSuccess: (AuthResult) -> Unit,
        onFailure: (Exception) -> Unit
    ) {
        runCatching {
            val result = getCredential(request = request, context = activity)
            val credential = result.credential
            if (credential is CustomCredential) {
                if (credential.type == GoogleIdTokenCredential.TYPE_GOOGLE_ID_TOKEN_CREDENTIAL) {
                    val googleIdTokenCredential = GoogleIdTokenCredential.createFrom(credential.data)
                    onSuccess(
                        AuthResult(
                            idToken = googleIdTokenCredential.idToken,
                            name = googleIdTokenCredential.displayName,
                            picture = googleIdTokenCredential.profilePictureUri?.toString().orEmpty(),
                            email = ""
                        )
                    )
                }
            }
        }.onFailure {
            onFailure(Exception(it))
        }
    }
}

@Composable
fun rememberAuthState(
    googleWebAuthManager: GoogleWebAuthManager,
    onFailure: (GoogleWebErrors) -> Unit = {},
    onSuccess: (AuthResult) -> Unit = {}
): AuthState {
    val context = LocalContext.current
    val appState = LocalAppState.current
    val coroutineScope = rememberCoroutineScope()
    val credentialManager = remember { CredentialManager.create(context) }
    
    DisposableEffect(appState) {
        appState.setOnActivityResult { intent ->
            if (intent != null) {
                coroutineScope.launch {
                    val result = googleWebAuthManager.processData(intent)
                    onSuccess(result)
                }
            } else {
                val error = GoogleWebErrors.EmptyResult(null)
                onFailure(error)
            }
        }
        onDispose { appState.setOnActivityResult(null) }
    }
    
    return remember {
        AuthState(
            coroutineScope = coroutineScope,
            context = context,
            googleWebAuthManager = googleWebAuthManager,
            credentialManager = credentialManager,
            onWebIntentLaunch = { appState.launchActivityResult(it) },
            onSuccess = onSuccess
        )
    }
}
```

## Google API Integration

```mermaid
classDiagram
    class GoogleApi {
        <<interface>>
        +getUserInfo(token: String): ApiResult~GoogleUserDto, Unit~
    }
    
    class GoogleUserDto {
        +name: String?
        +picture: String?
        +email: String?
    }
    
    class SignupModule {
        +provideGoogleApi(retrofit: Retrofit): GoogleApi
    }
    
    GoogleApi --> GoogleUserDto : returns
    SignupModule --> GoogleApi : provides
```

```kotlin
// File: core/signup/src/main/java/co/thewordlab/luzia/core/signup/data/api/GoogleApi.kt
interface GoogleApi {
    @DecodeErrorBody
    @GET("userinfo")
    suspend fun getUserInfo(
        @Header("Authorization") token: String
    ): ApiResult<GoogleUserDto, Unit>
}

// File: core/signup/src/main/java/co/thewordlab/luzia/core/signup/di/SignupModule.kt
@Module
@InstallIn(SingletonComponent::class)
object SignupModule {
    @Provides
    fun provideGoogleApi(
        @GoogleHost retrofit: Retrofit
    ): GoogleApi {
        return retrofit.create(GoogleApi::class.java)
    }
}
```

## Usage in Features

The core signup module is used by the `features/signup` module, which provides the actual UI screens:

```kotlin
// File: features/signup/src/main/kotlin/co/thewordlab/luzia/features/signup/presentation/auth/AuthLandingViewModel.kt
class AuthLandingViewModel @Inject constructor(
    private val googleWebAuthManager: GoogleWebAuthManager,
    // ... other dependencies
) : ViewModel(), 
    ViewModelActions<AuthLandingViewActions>,
    ViewModelStates<AuthLandingViewState> by ViewModelStatesImpl(),
    ViewModelEvents<AuthLandingViewEvents> by ViewModelEventsImpl() {
    
    // AuthResult from core/signup is used here
    private fun handleGoogleAuthSuccess(authResult: AuthResult) {
        // Process Google sign-in result
    }
}
```

## Module Dependencies

```mermaid
graph TD
    A[core/signup] --> B[foundation/analytics]
    A --> C[foundation/securelib]
    A --> D[foundation/networking via @GoogleHost]
    A --> E[foundation/common]
    A --> F[foundation/design-system for LocalAppState]
    A --> G[foundation/localization for error strings]
    
    subgraph "External Dependencies"
        H[androidx.credentials]
        I[google.identity]
        J[appauth]
        K[jwtdecode]
    end
    
    A --> H
    A --> I
    A --> J
    A --> K
```

## Real Testing Strategy

The core/signup module currently has **no test files**. Testing is done at the features/signup level:

```kotlin
// File: features/signup/src/test/java/co/thewordlab/luzia/features/signup/presentation/auth/AuthLandingViewModelTest.kt
class AuthLandingViewModelTest {
    // Tests Google auth integration using AuthResult from core/signup
    // Tests are focused on UI logic, not core authentication mechanics
}
```

The actual authentication testing happens through:
1. **Integration tests** in features/signup that mock GoogleWebAuthManager
2. **E2E tests** using real Google OAuth flows
3. **Manual testing** with different browsers and auth states

## Actual Dependencies

```kotlin
// File: core/signup/build.gradle.kts
plugins {
    alias(libs.plugins.luzia.android.feature)
}

android {
    namespace = "co.thewordlab.luzia.core.signup"
}

dependencies {
    // Foundation modules (actual dependencies)
    implementation(projects.foundation.analytics)
    implementation(projects.foundation.securelib)
    
    // Authentication libraries
    implementation(libs.androidx.credentials)
    implementation(libs.androidx.credentials.play.services)
    implementation(libs.jwtdecode)
    implementation(libs.google.identity)
    implementation(libs.play.services.base)
    implementation(libs.appauth)
    
    // Note: Other dependencies come from android.feature convention plugin:
    // - foundation.networking (via @GoogleHost)
    // - foundation.common (for Context.findActivity)
    // - foundation.design-system (for LocalAppState)
    // - foundation.localization (for error string resources)
    // - Hilt, Coroutines, Compose, etc.
}
```

## Module Integration

The core/signup module provides **Google OAuth authentication only** for:
- **features/signup**: UI screens for authentication landing and Google sign-in flow
- **features/settings**: Account logout functionality via AuthState.logout()
- **features/profile**: Profile picture selection after Google authentication

This focused core module provides:
1. **Google OAuth authentication** with Android Credential Manager + Custom Tabs fallback
2. **AuthResult model** for passing authentication data to feature modules
3. **GoogleWebErrors** for standardized error handling
4. **Compose integration** via rememberAuthState() for UI state management
5. **Analytics tracking** for authentication funnel and browser identification

**NOT PROVIDED**: Email/password registration, onboarding flows, user account creation, email verification, or general signup business logic. These are handled by other modules or the backend API.