# Core: Profile Module

## Overview

The Profile module provides user profile management functionality for the Luzia Android app. This core module handles user profile data, avatar management, school integration, and social features like finding schoolmates. It includes both local caching and API integration for comprehensive profile management.

```mermaid
graph TD
    A[ProfileRepository] --> B[ProfileApi]
    A --> C[ProfileDao]
    A --> D[Store Cache]
    
    E[AvatarRepository] --> F[AI Avatar Generation]
    E --> G[Profile Image Storage]
    
    H[GetUserProfileUseCase] --> A
    H --> E
    H --> I[Combined Profile + Avatar]
    
    subgraph "API Endpoints"
        B --> J[GET /user/profile]
        B --> K[PUT /user/profile]
        B --> L[GET /schools/{country}]
        B --> M[GET /user/friends]
        B --> N[DELETE /user]
    end
    
    subgraph "Local Storage"
        C --> O[Room Database]
        D --> P[In-Memory Cache]
        G --> Q[DataStore Images]
    end
```

## Module Dependencies

**File**: `core/profile/build.gradle.kts`

```kotlin
// File: core/profile/build.gradle.kts
dependencies {
    implementation(projects.foundation.analytics)
    implementation(projects.foundation.files)
    implementation(projects.foundation.dataCache)
    implementation(projects.core.signup)
    implementation(projects.core.navigation)
    implementation(libs.kotlinx.coroutines.play)
    implementation(libs.androidx.dataStore.core)
    implementation(libs.androidx.dataStore.preferences)
}
```

## Core Domain Models

### UserProfile

The main profile data model with comprehensive user information.

**File**: `core/profile/src/main/java/co/thewordlab/luzia/core/profile/domain/model/UserProfile.kt`

```kotlin
// File: core/profile/src/main/java/co/thewordlab/luzia/core/profile/domain/model/UserProfile.kt:5-31
data class UserProfile(
    val displayName: String? = null,
    val username: String? = null,
    val avatarURL: String? = null,
    val backgroundURL: String? = null,
    val language: String? = null,
    val utcOffset: Int? = null,
    val email: String? = null,
    val phone: String? = null,
    val birthdate: String? = null,
    val pronouns: UserPronouns? = null,
    val privacy: UserProfilePrivacy? = null,
    val schoolName: String? = null,
    val schoolMates: Int? = null,
    val isStudent: Boolean? = null,
    val userType: UserType? = null,
    val avatarState: AvatarState? = null,
    val bestiePoints: Int? = null,
    val referralCode: String? = null,
    val referralLink: String? = null,
    val streamToken: String? = null,
    val streamUserId: String? = null,
    val countryName: String? = null,
    val blockedByMe: Boolean = false,
    val onboardingIntentId: String? = null,
    val selfDescription: String? = null
)
```

### Profile Completion Check

**File**: `core/profile/src/main/java/co/thewordlab/luzia/core/profile/domain/model/UserProfile.kt:33-39`

```kotlin
// File: core/profile/src/main/java/co/thewordlab/luzia/core/profile/domain/model/UserProfile.kt:33-39
fun UserProfile?.isProfileCompleted(): Boolean =
    this?.let {
        !it.username.isNullOrEmpty() &&
            !it.birthdate.isNullOrEmpty() &&
            it.privacy != null &&
            (it.isStudent != null && if (it.isStudent) !it.schoolName.isNullOrEmpty() else true)
    } ?: false
```

## Repository Architecture

### ProfileRepository Interface

Comprehensive profile management interface with caching and API operations.

**File**: `core/profile/src/main/java/co/thewordlab/luzia/core/profile/domain/ProfileRepository.kt`

```kotlin
// File: core/profile/src/main/java/co/thewordlab/luzia/core/profile/domain/ProfileRepository.kt:14-30
@Suppress("TooManyFunctions")
interface ProfileRepository {
    fun getUserProfileAsFlow(): Flow<UserProfile?>
    suspend fun getUserProfile(forceRefresh: Boolean = false): UserProfile?

    suspend fun getUserProfileById(masterUserId: String): ResultOf<UserProfile, Error>
    suspend fun updateUserProfile(request: UserProfileUpdateRequest): ResultOf<UserProfile, Error>
    suspend fun checkUsernameAvailable(username: String): ResultOf<Boolean, Error>
    suspend fun getSchools(country: String, query: String): ResultOf<List<School>, Error>
    suspend fun getSchoolMates(): ResultOf<SchoolMates, Error>
    suspend fun deleteProfileImage(): ResultOf<Unit, Error>
    suspend fun isSessionLocked(): Boolean
    suspend fun deleteUser(): ResultOf<Unit, Error>
    suspend fun deleteUserToken(): ResultOf<Unit, Error>
    suspend fun searchUsername(username: String): ResultOf<List<UserMateUiModel>, Error>
    suspend fun clearData(masterUserId: String)
    suspend fun getUserIntents(): ResultOf<List<UserIntent>, Error>
}
```

### ProfileRepositoryImpl

**File**: `core/profile/src/main/java/co/thewordlab/luzia/core/profile/data/ProfileRepositoryImpl.kt:40-50`

```kotlin
// File: core/profile/src/main/java/co/thewordlab/luzia/core/profile/data/ProfileRepositoryImpl.kt:40-50
@Singleton
class ProfileRepositoryImpl @Inject constructor(
    private val profileApi: ProfileApi,
    private val userSessionManager: UserSessionManager,
    private val profileDao: ProfileDao,
    private val mapper: ProfileMapper
) : ProfileRepository {

    private val store = inMemoryStore(
        fetcher = { profileApi.getProfile().asResult().getDataOrNull() },
        diskCache = object : DiskCache<UserProfileDto?> {
            // ... disk cache implementation
        }
    )
}
```

## API Integration

### ProfileApi

Comprehensive API interface for profile operations with 12+ endpoints.

**File**: `core/profile/src/main/java/co/thewordlab/luzia/core/profile/data/api/ProfileApi.kt`

```kotlin
// File: core/profile/src/main/java/co/thewordlab/luzia/core/profile/data/api/ProfileApi.kt:26-86
@Suppress("TooManyFunctions")
interface ProfileApi {

    @FullUserNeeded
    @DecodeErrorBody
    @GET("user/profile")
    suspend fun getProfile(): ApiResult<UserProfileDto, ErrorDto>

    @FullUserNeeded
    @DecodeErrorBody
    @GET("user/{masterUserId}/profile")
    suspend fun getProfileById(@Path("masterUserId") masterUserId: String): ApiResult<UserProfileDto, ErrorDto>

    @FullUserNeeded
    @DecodeErrorBody
    @PUT("user/profile")
    suspend fun updateProfile(@Body request: UserProfileUpdateRequest): ApiResult<UserProfileDto, ErrorDto>

    @DecodeErrorBody
    @GET("user/check-username")
    suspend fun checkUsername(@Query("username") username: String): ApiResult<EmptyResponse, ErrorDto>

    @DecodeErrorBody
    @GET("schools/{country}")
    suspend fun getSchools(
        @Path("country") country: String,
        @Query("q") query: String
    ): ApiResult<SchoolsResponseDto, ErrorDto>

    @DecodeErrorBody
    @GET("user/friends")
    suspend fun getSchoolMates(): ApiResult<SchoolsMatesResponseDto, ErrorDto>

    @DecodeErrorBody
    @DELETE("user/profile/avatar")
    suspend fun deleteProfileImage(): Response<Unit>

    @DecodeErrorBody
    @POST("user/profile/avatar-generation")
    suspend fun generateAvatarImage(@Body body: MultipartBody): ApiResult<AvatarResponseDto, ErrorDto>

    @DecodeErrorBody
    @DELETE("user")
    suspend fun deleteUser(): Response<Unit>

    @DecodeErrorBody
    @DELETE("user/token")
    suspend fun deleteUserTokens(): Response<Unit>

    @DecodeErrorBody
    @GET("user/social-profiles")
    suspend fun searchUsername(@Query("username") username: String): ApiResult<SearchUsernameResponseDto, ErrorDto>

    @FullUserNeeded
    @DecodeErrorBody
    @GET("user/onboarding/intents")
    suspend fun getUserIntents(): ApiResult<UserIntentsResponseDto, ErrorDto>
}
```

## Avatar Management

### AvatarRepository

Simple avatar repository interface for AI avatar generation and profile image storage.

**File**: `core/profile/src/main/java/co/thewordlab/luzia/core/profile/domain/AvatarRepository.kt`

```kotlin
// File: core/profile/src/main/java/co/thewordlab/luzia/core/profile/domain/AvatarRepository.kt:7-13
interface AvatarRepository {
    val profileImage: Flow<String?>
    val avatarImage: Flow<String?>
    suspend fun generateAvatar(url: String): ResultOf<String, Error>
    suspend fun clearSession()
    suspend fun saveProfileImage(imageUrl: String?)
}
```

## Use Cases

### GetUserProfileUseCase

Combines profile data with avatar state using reactive flows.

**File**: `core/profile/src/main/java/co/thewordlab/luzia/core/profile/domain/GetUserProfileUseCase.kt`

```kotlin
// File: core/profile/src/main/java/co/thewordlab/luzia/core/profile/domain/GetUserProfileUseCase.kt:12-42
class GetUserProfileUseCase @Inject constructor(
    profileRepository: ProfileRepository,
    avatarRepository: AvatarRepository,
    private val dispatcher: CoroutineDispatcher
) {

    private val userProfile = profileRepository.getUserProfileAsFlow()
        .distinctUntilChanged()

    private val imageAvatarFromLocal = avatarRepository.avatarImage
        .distinctUntilChanged()

    private val imageProfileFromLocal = avatarRepository.profileImage
        .distinctUntilChanged()

    operator fun invoke(): Flow<UserProfile?> = combine(
        userProfile,
        imageAvatarFromLocal,
        imageProfileFromLocal
    ) { profile, imageAvatarFromLocal, imageProfileFromLocal ->
        val image = profile?.avatarURL
        profile?.copy(
            avatarState = when (image) {
                null -> AvatarState.Initials(profile.displayName.orEmpty().take(1))
                imageAvatarFromLocal -> AvatarState.AiAvatar(imageAvatarFromLocal)
                imageProfileFromLocal -> AvatarState.ProfileImage(imageProfileFromLocal)
                else -> AvatarState.ProfileImage(image)
            }
        )
    }.flowOn(dispatcher)
}
```

## Data Flow Architecture

```mermaid
sequenceDiagram
    participant UI as UI Layer
    participant UC as GetUserProfileUseCase
    participant Repo as ProfileRepository
    participant Store as In-Memory Store
    participant API as ProfileApi
    participant Cache as Room Cache
    
    UI->>UC: getUserProfile()
    UC->>Repo: getUserProfileAsFlow()
    
    alt Cache Hit
        Repo->>Store: get cached data
        Store-->>Repo: UserProfileDto
    else Cache Miss
        Repo->>API: getProfile()
        API-->>Repo: UserProfileDto
        Repo->>Store: store(UserProfileDto)
        Repo->>Cache: save to Room
    end
    
    Repo-->>UC: Flow<UserProfile?>
    UC->>UC: combine with avatar state
    UC-->>UI: Flow<UserProfile?>
```

## School Integration

### School Search and SchoolMates

**Key Features**:
1. **School Search**: Country-based school search with query support
2. **SchoolMates**: Find other users from the same school
3. **Student Status**: Track student status and school affiliation

**Files**:
- `core/profile/src/main/java/co/thewordlab/luzia/core/profile/domain/model/School.kt`
- `core/profile/src/main/java/co/thewordlab/luzia/core/profile/domain/model/SchoolMates.kt`

## Profile UI Components

### Profile Entry Points

**File**: `core/profile/src/main/java/co/thewordlab/luzia/core/profile/presentation/entrypoints/chat/ProfileChatEntryPointViewModel.kt`

Simple MVVM+MVI pattern for profile UI components integrated with chat functionality.

## Architecture Summary

```mermaid
classDiagram
    class ProfileRepository {
        +getUserProfileAsFlow() Flow~UserProfile?~
        +updateUserProfile(request) ResultOf~UserProfile, Error~
        +getSchools(country, query) ResultOf~List~School~, Error~
        +getSchoolMates() ResultOf~SchoolMates, Error~
        +deleteUser() ResultOf~Unit, Error~
    }
    
    class AvatarRepository {
        +profileImage Flow~String?~
        +avatarImage Flow~String?~
        +generateAvatar(url) ResultOf~String, Error~
        +saveProfileImage(imageUrl)
    }
    
    class GetUserProfileUseCase {
        +invoke() Flow~UserProfile?~
        -combine(profile, avatars)
    }
    
    class ProfileApi {
        +getProfile() ApiResult~UserProfileDto, ErrorDto~
        +updateProfile(request) ApiResult~UserProfileDto, ErrorDto~
        +getSchools(country, query) ApiResult~SchoolsResponseDto, ErrorDto~
        +generateAvatarImage(body) ApiResult~AvatarResponseDto, ErrorDto~
    }
    
    class UserProfile {
        +displayName String?
        +username String?
        +avatarURL String?
        +schoolName String?
        +isStudent Boolean?
        +avatarState AvatarState?
        +bestiePoints Int?
    }
    
    ProfileRepository --> ProfileApi
    ProfileRepository --> UserProfile
    AvatarRepository --> UserProfile
    GetUserProfileUseCase --> ProfileRepository
    GetUserProfileUseCase --> AvatarRepository
    GetUserProfileUseCase --> UserProfile
```

## Module Integration

The Profile module integrates with:

- **Foundation Analytics**: For profile interaction tracking
- **Foundation Files**: For avatar image processing
- **Foundation Data-Cache**: For Store-based caching pattern
- **Core Signup**: For user registration integration
- **Core Navigation**: For profile screen navigation
- **Foundation Design System**: For AvatarState and UI components

## Key Features

1. **Comprehensive Profile Management**: Display name, username, avatar, school info
2. **Avatar System**: AI avatar generation + profile image support with state management
3. **School Integration**: School search, schoolmate discovery, student status
4. **Social Features**: Username search, friend discovery, blocking
5. **Store-based Caching**: In-memory store with disk cache fallback
6. **API Integration**: 12+ endpoints for complete profile management
7. **Reactive Data Flow**: Flow-based architecture with combined profile+avatar state
8. **Profile Completion**: Validation logic for onboarding completion

This core module provides essential user profile infrastructure while integrating smoothly with Luzia's social and educational features through school integration and friend discovery.