# Core: Sharing Module

## Overview

The Sharing module manages AI chat message sharing and GetStream chat integration in the Luzia Android app. This core module provides business logic for sharing AI conversations between users, managing group chats, and generating shareable links for AI-generated content.

**Key Focus**: 
- GetStream chat platform integration for group messaging
- AI conversation sharing within the Luzia ecosystem  
- Group chat creation and moderation tools
- Message forwarding between users

## Architecture

```mermaid
graph TD
    A[SharingRepository] --> B[AI Message Sharing]
    A --> C[Link Generation]
    D[StreamRepository] --> E[GetStream Client]
    D --> F[Group Chat Management]
    D --> G[Channel Operations]
    
    subgraph "AI Content Sharing"
        B --> H[User Messages]
        B --> I[AI Responses]
        B --> J[Base64 Images]
    end
    
    subgraph "GetStream Integration"
        E --> K[Chat Client]
        F --> L[Create Channels]
        F --> M[Add Members]
        G --> N[Block Users]
        G --> O[Report Content]
    end
    
    subgraph "Share Types"
        C --> P[Message Links]
        C --> Q[Group Invites]
        C --> R[AppsFlyer Links]
    end
```

## Core Data Models

```mermaid
classDiagram
    class SharedMessageItem {
        <<sealed>>
        +String value
    }
    
    class UserMessage {
        +String message
    }
    
    class AIMessage {
        +String message
    }
    
    class Image {
        +String content
    }
    
    SharedMessageItem <|-- UserMessage
    SharedMessageItem <|-- AIMessage
    SharedMessageItem <|-- Image
    
    class SharingErrors {
        <<sealed>>
    }
    
    class CommonError {
        +CommonErrors error
    }
    
    class SharingLimitReached
    
    class GenerateLink {
        +String message
    }
    
    SharingErrors <|-- CommonError
    SharingErrors <|-- SharingLimitReached
    SharingErrors <|-- GenerateLink
```

```kotlin
// File: core/sharing/src/main/java/co/thewordlab/luzia/core/sharing/domain/model/SharedMessageItem.kt
sealed class SharedMessageItem(val value: String) {
    data class UserMessage(val message: String) : SharedMessageItem(message)
    data class AIMessage(val message: String) : SharedMessageItem(message)
    data class Image(val content: String) : SharedMessageItem(content) // Base64 encoded image
}
```

```kotlin
// File: core/sharing/src/main/java/co/thewordlab/luzia/core/sharing/domain/model/SharingErrors.kt
sealed class SharingErrors {
    data class CommonError(val error: CommonErrors) : SharingErrors()
    object SharingLimitReached : SharingErrors()
    data class GenerateLink(val message: String) : SharingErrors()
}
```

## Sharing Repository

**Primary interface for AI message sharing within Luzia ecosystem.**

```kotlin
// File: core/sharing/src/main/java/co/thewordlab/luzia/core/sharing/domain/repository/SharingRepository.kt
interface SharingRepository {

    suspend fun shareMessages(
        users: List<String>,
        messages: List<SharedMessageItem>
    ): ResultOf<Unit, SharingErrors>

    suspend fun shareMessagesWithLink(
        personalityId: String,
        messages: List<SharedMessageItem>,
        personalityName: String? = null
    ): ResultOf<String, SharingErrors>

    suspend fun generateInviteLink(
        channel: Channel
    ): ResultOf<String, SharingErrors.GenerateLink>
}
```

### Implementation Details

**File**: `core/sharing/src/main/java/co/thewordlab/luzia/core/sharing/data/repository/SharingRepositoryImp.kt`

```kotlin
class SharingRepositoryImp(
    private val apiDataSource: SharingApi,
    private val textProvider: TextProvider,
    private val dispatcher: CoroutineDispatcher,
    private val appsFlyerAnalyticsProvider: AppsFlyerAnalyticsProvider
) : SharingRepository {

    override suspend fun shareMessages(
        users: List<String>,
        messages: List<SharedMessageItem>,
    ): ResultOf<Unit, SharingErrors> =
        if (messages.isNotEmpty() && users.isNotEmpty()) {
            val result = apiDataSource.sendMessageToShare(
                ShareMessageRequest(
                    masterUserIds = users,
                    messages = messages.mapToRequestDto()
                )
            ).asResult { error ->
                val shareError = error.mapToSharingError()
                shareError.asFailure()
            }
            result.map { DO_NOTHING }
        } else {
            Unit.asSuccess()
        }

    override suspend fun shareMessagesWithLink(
        personalityId: String,
        messages: List<SharedMessageItem>,
        personalityName: String?
    ): ResultOf<String, SharingErrors> =
        if (messages.isNotEmpty()) {
            val request = buildShareRequest(personalityId, messages)
            apiDataSource.shareMessageLink(request).asResult { error ->
                val shareError = error.mapToSharingError()
                shareError.asFailure()
            }.map {
                StringBuilder().apply {
                    append(
                        personalityName?.let {
                            textProvider.getString(
                                localizationR.string.share_custom_character_conversation,
                                personalityName
                            )
                        } ?: textProvider.getString(localizationR.string.external_share_message)
                    )
                    appendLine()
                    append(link)
                }.toString()
            }
        } else {
            "".asSuccess()
        }

    override suspend fun generateInviteLink(channel: Channel): ResultOf<String, GenerateLink> =
        withContext(dispatcher) {
            try {
                val link = appsFlyerAnalyticsProvider.generateLink(
                    cid = channel.cid,
                    name = channel.name,
                    memberCount = channel.memberCount,
                    image = channel.image
                )
                ResultOf.Success(link)
            } catch (ex: Exception) {
                GenerateLink(ex.message.toString()).asFailure()
            }
        }
}
```

## GetStream Repository

**Manages GetStream chat client and group chat functionality.**

```mermaid
flowchart TD
    A[StreamRepository] --> B[Chat Client Management]
    A --> C[Channel Operations]
    A --> D[User Management]
    A --> E[School Features]
    
    B --> F[Initialize Client]
    B --> G[Connect/Disconnect]
    
    C --> H[Create Channels]
    C --> I[Join Channels]
    C --> J[Add Members]
    
    D --> K[Block Users]
    D --> L[Report Users]
    D --> M[Report Messages]
    
    E --> N[School Channels]
    E --> O[School Information]
```

```kotlin
// File: core/sharing/src/main/java/co/thewordlab/luzia/core/sharing/domain/repository/StreamRepository.kt
@Suppress("TooManyFunctions")
interface StreamRepository {
    suspend fun initialize(): ChatClient

    suspend fun createChannel(
        isDm: Boolean,
        recipients: List<String>
    ): ResultOf<String, GroupChatErrors>

    suspend fun joinChannel(channelId: String): ResultOf<String, GroupChatErrors>

    suspend fun logout()

    suspend fun isUserAcceptedOrRejected(masterUserId: String): Boolean

    suspend fun setUserAcceptedOrRejected(masterUserId: String)

    fun isFriendsTabSelectedBefore(): Flow<Boolean>

    suspend fun setFriendsTabSelected()

    suspend fun blockUser(isBlocked: Boolean, masterId: String)

    suspend fun reportUser(masterId: String, cid: String?)

    suspend fun reportMessage(masterId: String, messageId: String, cid: String)

    suspend fun addMembers(
        channelId: String,
        recipients: List<String>
    ): ResultOf<String, GroupChatErrors>

    suspend fun fetchAndSaveSchoolInformation()

    suspend fun getSchoolChannelToJoin(): SchoolChannel?

    suspend fun removeSchoolChannelToJoin()
}
```

## Message Actions ViewModel

**Handles message sharing, forwarding, and deletion actions with MVVM+MVI pattern.**

```mermaid
sequenceDiagram
    participant UI as Message UI
    participant VM as MessageActionsViewModel
    participant SR as SharingRepository
    participant CR as ChatRepository
    
    UI->>VM: OnShareClicked(messages)
    VM->>SR: shareMessagesWithLink(personalityId, messages)
    SR-->>VM: ResultOf.Success(shareUrl)
    VM->>UI: NativeShare(textToShare)
    
    UI->>VM: OnForwardClicked(messages)
    VM->>UI: StreamShare(personalityId, messages)
    
    UI->>VM: OnDeleteClicked(messages)
    VM->>CR: setMessagesAsPendingToDelete(personalityId, messageIds)
    VM->>UI: ShowDeleteConfirmation(count)
```

```kotlin
// File: core/sharing/src/main/java/co/thewordlab/luzia/core/sharing/presentation/container/MessageActionsViewModel.kt:46-50
@HiltViewModel
class MessageActionsViewModel @Inject constructor(
    private val analytics: Analytics,
    private val getUserProfileUseCase: GetUserProfileUseCase,
    private val featureFlagManager: FeatureFlagManager,
    private val sharingRepository: SharingRepository,
    private val chatRepository: ChatRepository,
) : ViewModel(),
    ViewModelActions<MessageActionsViewActions>,
    ViewModelEvents<MessageActionsViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<MessageActionsViewState> by ViewModelStatesImpl(MessageActionsViewState())
```

### Key Functionality

**Native Sharing**: Generates shareable text/links for external apps
```kotlin
// File: core/sharing/src/main/java/co/thewordlab/luzia/core/sharing/presentation/container/MessageActionsViewModel.kt:89-107
private fun notifyStringToShare(messages: List<MessageModel>) = viewModelScope.launch {
    updateState { it.copy(isLoading = true) }
    val textToShare = when (
        val result = sharingRepository.shareMessagesWithLink(
            personalityId = messages.firstOrNull()?.messagePersonalityId.orEmpty(),
            messages = messages.mapTo()
        )
    ) {
        is ResultOf.Failure -> buildStringToShare(
            messages.mapTo(),
            viewState.value.profile?.username.orEmpty()
        )

        is ResultOf.Success -> result.data
    }

    updateState { it.copy(isLoading = false) }
    sendEvent(MessageActionsViewEvents.NativeShare(textToShare))
}
```

**Stream Forwarding**: Forwards messages within GetStream chat ecosystem
```kotlin
// File: core/sharing/src/main/java/co/thewordlab/luzia/core/sharing/presentation/container/MessageActionsViewModel.kt:109-117
private fun onForwardClicked(messages: List<MessageModel>) {
    if (messages.isEmpty()) return
    sendEvent(
        MessageActionsViewEvents.StreamShare(
            personalityId = messages.firstOrNull()?.messagePersonalityId.orEmpty(),
            messages = messages.mapTo()
        )
    )
}
```

## Sharing API

**Backend API interface for message sharing and link generation.**

```kotlin
// File: core/sharing/src/main/java/co/thewordlab/luzia/core/sharing/data/api/SharingApi.kt
interface SharingApi {

    @DecodeErrorBody
    @POST("user/share-content")
    suspend fun sendMessageToShare(
        @Body request: ShareMessageRequest
    ): ApiResult<EmptyResponse, ErrorDto>

    @DecodeErrorBody
    @POST("user/share-link")
    suspend fun shareMessageLink(@Body request: ShareContentRequest): ApiResult<ShareContentResponse, ErrorDto>
}
```

### Request/Response Models

```kotlin
// File: core/sharing/src/main/java/co/thewordlab/luzia/core/sharing/data/model/ShareMessageRequest.kt
data class ShareMessageRequest(
    val masterUserIds: List<String>,
    val messages: List<SharedMessageItemRequest>
)

// File: core/sharing/src/main/java/co/thewordlab/luzia/core/sharing/data/model/ShareContentRequest.kt
data class ShareContentRequest(
    val type: ShareContentWithLinkItemType,
    val content: SharedMessageContentRequest
)

data class SharedMessageContentRequest(
    val personalityId: String? = null,
    val messages: List<SharedMessageItemRequest>? = null,
    val image: String? = null // Base64 with prefix
)

enum class ShareContentWithLinkItemType {
    TEXT,
    IMAGE
}
```

### Share Content Processing

```mermaid
flowchart TD
    A[Messages Input] --> B{Content Type?}
    B -->|Has PersonalityId| C[Text Share]
    B -->|Image Only| D[Image Share]
    
    C --> E[ShareContentRequest with TEXT type]
    C --> F[Include personalityId + messages]
    
    D --> G[ShareContentRequest with IMAGE type]
    D --> H[Base64 encode image with prefix]
    
    E --> I[POST /user/share-link]
    G --> I
    
    I --> J[ShareContentResponse]
    J --> K[Generate formatted share text]
```

## Dependency Injection

**Hilt modules providing sharing and GetStream dependencies.**

```kotlin
// File: core/sharing/src/main/java/co/thewordlab/luzia/core/sharing/di/SharingModule.kt
@Module
@InstallIn(SingletonComponent::class)
object SharingModule {

    @Provides
    fun provideApi(@BaseHost retrofit: Retrofit): SharingApi =
        retrofit.create(SharingApi::class.java)

    @Provides
    fun provideRepository(
        api: SharingApi,
        textProvider: TextProvider,
        dispatcher: CoroutineDispatcher,
        appsFlyerAnalyticsProvider: AppsFlyerAnalyticsProvider
    ): SharingRepository =
        SharingRepositoryImp(api, textProvider, dispatcher, appsFlyerAnalyticsProvider)

    @Provides
    fun provideExternalUserSendMessageHandler(
        useCase: CreateChannelUseCase,
    ): ExternalUserSendMessageHandler = useCase
}
```

```kotlin
// File: core/sharing/src/main/java/co/thewordlab/luzia/core/sharing/di/StreamModule.kt
@Module
@InstallIn(SingletonComponent::class)
object StreamModule {

    @Provides
    fun provideStreamApi(@BaseHost retrofit: Retrofit): StreamApi =
        retrofit.create(StreamApi::class.java)

    @Provides
    fun provideStreamRepository(
        streamApi: StreamApi,
        // ... other dependencies
    ): StreamRepository = StreamRepositoryImpl(
        streamApi,
        // ... implementation details
    )
}
```

## Testing Strategy

**Unit tests focus on actual sharing functionality with MockK.**

```kotlin
// Example test structure based on actual implementation
class MessageActionsViewModelTest {
    
    @Mock private lateinit var sharingRepository: SharingRepository
    @Mock private lateinit var chatRepository: ChatRepository
    @Mock private lateinit var analytics: Analytics
    
    @Test
    fun `share messages creates external link successfully`() = runTest {
        // Given
        val messages = listOf(createTestMessage())
        val expectedLink = "https://luzia.com/share/12345"
        
        coEvery { 
            sharingRepository.shareMessagesWithLink(any(), any(), any())
        } returns ResultOf.Success(expectedLink)
        
        // When
        viewModel.onViewAction(MessageActionsViewActions.OnShareClicked(messages))
        
        // Then
        verify { analytics.logAction(NativeShareClicked, any()) }
        coVerify { sharingRepository.shareMessagesWithLink(any(), any(), any()) }
    }
}
```

## Dependencies

**Actual dependencies from build.gradle.kts focusing on GetStream integration.**

```kotlin
// File: core/sharing/build.gradle.kts
dependencies {
    // Core modules for business logic
    implementation(projects.core.notifications)
    implementation(projects.core.profile)
    implementation(projects.core.navigation)
    implementation(projects.core.chat)
    implementation(projects.features.proactiveMessaging)
    
    // Foundation infrastructure
    implementation(projects.foundation.analytics)
    implementation(projects.foundation.architectureSystem)
    implementation(projects.foundation.designSystem)
    implementation(projects.foundation.localization)
    implementation(projects.foundation.messages)
    implementation(projects.foundation.securelib)
    
    // GetStream Chat SDK - CORE DEPENDENCY
    implementation(libs.stream.chat.android.compose)
    implementation(libs.stream.chat.android.offline)
    implementation(libs.stream.chat.android.push)
    
    // Data persistence
    implementation(libs.androidx.dataStore.core)
    implementation(libs.androidx.dataStore.preferences)
}
```

**Key External Dependencies**:
- **GetStream Chat SDK**: Primary chat platform integration
- **DataStore**: Local preferences for user state
- **Foundation modules**: Analytics, networking, architecture patterns
- **Core modules**: Profile management, navigation, notifications

## Module Integration

**The Sharing module provides GetStream chat integration and AI message sharing for:**

### **Primary Use Cases**
- **Message Actions**: Share/forward AI conversations within Luzia ecosystem
- **Group Chats**: Create and manage GetStream channels for user communication  
- **School Features**: Special channels for educational institutions
- **Content Moderation**: Report users/messages, block functionality

### **Integration Points**
```mermaid
graph LR
    A[Features/Chat] --> B[Sharing Module]
    C[Features/Profile] --> B
    D[Core/Chat] --> B
    E[Foundation/Messages] --> B
    
    B --> F[GetStream SDK]
    B --> G[Backend APIs]
    B --> H[Analytics]
    
    F --> I[Chat Channels]
    G --> J[Share Links]
    H --> K[User Behavior Tracking]
```

### **NOT General Content Sharing**
This module does **NOT** provide:
- ❌ Social media platform integration
- ❌ QR code generation  
- ❌ File system exports
- ❌ Email/SMS sharing
- ❌ Complex permission systems

**Purpose**: Specialized GetStream chat integration focused on AI conversation sharing within the Luzia app ecosystem, not general content sharing functionality.