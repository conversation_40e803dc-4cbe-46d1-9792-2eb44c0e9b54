# Core: Bestie Points Module

## Overview

The Bestie Points module provides a read-only display system for user points and streak rewards in the Luzia Android app. This core module fetches user score data and streak-based rewards from backend APIs and presents them through a gamified UI, focusing specifically on daily usage streak tracking and milestone rewards.

**Path**: `core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/`

## Architecture

```mermaid
graph TD
    A[StreakBestiePointsViewModel] --> B[BestiePointsRepository]
    A --> C[GetUserProfileUseCase]
    A --> D[UserSessionManager]
    
    B --> E[BestiePointsApi]
    E --> F[Backend API]
    
    G[StreakBestiePointsScreen] --> A
    G --> H[UI Components]
    
    subgraph "API Endpoints"
        I[GET /user/score]
        J[GET /user/rewards/day-streak]
    end
    
    E --> I
    E --> J
    
    subgraph "Data Models"
        K[UserScore]
        L[UserRewards]
        M[RewardType]
        N[RewardStatus]
    end
    
    B --> K
    B --> L
    
    subgraph "UI Models"
        O[StreakChallengeUiModel]
        P[RewardUiModel]
        Q[StreakAchievedUiModel]
    end
    
    A --> O
    A --> P
    A --> Q
    
    subgraph "Streak Milestones"
        R[3 Days]
        S[7 Days]
        T[14 Days]
        U[30 Days]
    end
```

## Core Domain Models

### UserScore - Main Data Structure

**File**: `core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/domain/models/UserScore.kt`

```kotlin
// File: core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/domain/models/UserScore.kt:3-17
data class UserScore(
    val points: Int,
    val streak: Int,
    val rewards: List<UserRewards> = emptyList()
)

data class UserRewards(
    val id: String,
    val type: RewardType,
    val category: RewardCategory,
    val points: Int,
    val status: RewardStatus,
    val title: String,
    val message: String
)
```

**Purpose**: Simple container for user's current points, streak count, and available/claimed rewards.

### RewardType - Streak Milestones

**File**: `core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/domain/models/RewardType.kt`

```kotlin
// File: core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/domain/models/RewardType.kt:3-22
enum class RewardType(val value: String) {
    DAILY_USAGE("daily-usage-app"),
    DAY_3("day-streak-3"),
    DAY_7("day-streak-7"),
    DAY_14("day-streak-14"),
    DAY_30("day-streak-30"),
    NOT_SET("not-set");

    companion object {
        fun fromValue(value: String): RewardType = when {
            value.contains(DAILY_USAGE.value) -> DAILY_USAGE
            value.contains(DAY_3.value) -> DAY_3
            value.contains(DAY_7.value) -> DAY_7
            value.contains(DAY_14.value) -> DAY_14
            value.contains(DAY_30.value) -> DAY_30
            else -> NOT_SET
        }

        fun getDayStreakRewards(): List<RewardType> = listOf(DAY_3, DAY_7, DAY_14, DAY_30)
    }
}
```

**Reward Structure**:
- **DAILY_USAGE**: General daily engagement reward
- **DAY_3, DAY_7, DAY_14, DAY_30**: Specific streak milestone rewards
- **Backend String Mapping**: Converts API response strings to typed enums

## Data Layer Implementation

### BestiePointsRepository - API Interface

**File**: `core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/domain/BestiePointsRepository.kt`

```kotlin
// File: core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/domain/BestiePointsRepository.kt:7-10
interface BestiePointsRepository {
    suspend fun getUserScoreAndRewards(): ResultOf<UserScore, BestiePointsErrors>
    suspend fun getUserAlreadyClaimedRewards(): ResultOf<List<UserRewards>, BestiePointsErrors>
}
```

### Repository Implementation with API Integration

**File**: `core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/data/BestiePointsRepositoryImp.kt`

```kotlin
// File: core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/data/BestiePointsRepositoryImp.kt:20-42
class BestiePointsRepositoryImp @Inject constructor(
    private val apiDataSource: BestiePointsApi,
    private val analytics: Analytics
) : BestiePointsRepository {

    override suspend fun getUserScoreAndRewards(): ResultOf<UserScore, BestiePointsErrors> =
        when (val result = apiDataSource.getUserScoreAndRewards().asResult()) {
            is ResultOf.Failure -> {
                val mappedError = result.error.mapToBestiePointsError()
                analytics.reportException("Requesting user bestie points failed: ${mappedError.description}")
                mappedError.asFailure()
            }

            is ResultOf.Success -> {
                with(result.data) {
                    UserScore(
                        points = points,
                        streak = streak,
                        rewards = rewards.mapToDomain()
                    ).asSuccess()
                }
            }
        }
}
```

**Implementation Details**:
- **Error Handling**: Maps API errors to domain-specific error types with analytics reporting
- **Data Transformation**: Converts DTO objects to domain models
- **ResultOf Pattern**: Uses foundation networking pattern for consistent error handling

### API Definition with Caching

**File**: `core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/data/BestiePointsApi.kt`

```kotlin
// File: core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/data/BestiePointsApi.kt:13-26
interface BestiePointsApi {

    @FullUserNeeded
    @CacheEnabled(CacheExpiry.HOUR_12)
    @DecodeErrorBody
    @GET("user/score")
    suspend fun getUserScoreAndRewards(): ApiResult<UserScoreDTO, ErrorDto>

    @FullUserNeeded
    @CacheEnabled(CacheExpiry.HOUR_12)
    @DecodeErrorBody
    @GET("user/rewards/day-streak")
    suspend fun getUserAlreadyClaimedRewards(): ApiResult<UserRewardsDTO, ErrorDto>
}
```

**API Configuration**:
- **Authentication**: `@FullUserNeeded` requires authenticated full user
- **Caching**: 12-hour cache for reduced backend load
- **Error Handling**: `@DecodeErrorBody` for proper error response parsing
- **Two Endpoints**: Current score/rewards and historical claimed rewards

## Presentation Layer with MVVM+MVI

### ViewModel Implementation

**File**: `core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/presentation/StreakBestiePointsViewModel.kt`

```kotlin
// File: core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/presentation/StreakBestiePointsViewModel.kt:31-63
@HiltViewModel
class StreakBestiePointsViewModel @Inject constructor(
    private val getUserProfileUseCase: GetUserProfileUseCase,
    private val bestiePointsRepository: BestiePointsRepository,
    private val userSessionManager: UserSessionManager,
    private val rewardsMapper: RewardsMapper,
    private val analytics: Analytics
) :
    ViewModel(),
    ViewModelStates<StreakBestiePointsViewState> by ViewModelStatesImpl(StreakBestiePointsViewState()),
    ViewModelEvents<StreakBestiePointsViewEvents> by ViewModelEventsImpl(),
    ViewModelActions<StreakBestiePointsViewActions> {

    override fun onViewAction(action: StreakBestiePointsViewActions) {
        when (action) {
            StreakBestiePointsViewActions.OnStart -> fetchBestiePointsAndStreaks()
            is StreakBestiePointsViewActions.OnBannerInfoClicked -> {
                sendEventForInfoBannerClicked(action.page)
                sendEvent(StreakBestiePointsViewEvents.ShowInfo(action.page))
            }
            is StreakBestiePointsViewActions.OpenBestiePoints -> attemptOpenBestiePoints(action)
            StreakBestiePointsViewActions.OnBackClicked ->
                sendEvent(StreakBestiePointsViewEvents.DoBack)
            StreakBestiePointsViewActions.OnScreenView -> analytics.logScreen(BestiePointsScreen)
            StreakBestiePointsViewActions.OnScreenClose -> analytics.logAction(CloseBestiePointsScreen)
        }
    }
}
```

**MVVM+MVI Pattern**:
- **Interface Delegation**: Uses foundation architecture system patterns
- **ViewModelStates**: Manages reactive UI state
- **ViewModelEvents**: Handles one-time UI events (navigation, popups)
- **ViewModelActions**: Processes user interactions

### User Session Management

**File**: `core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/presentation/StreakBestiePointsViewModel.kt:84-136`

```kotlin
// File: core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/presentation/StreakBestiePointsViewModel.kt:84-136
private fun fetchBestiePointsAndStreaks() = viewModelScope.launch {
    userSessionManager.userSession
        .map { it?.userType == UserType.FULL_USER }
        .collect { isFullUser ->
            if (!isFullUser) {
                updateState {
                    it.copy(
                        streaks = 0,
                        points = 0,
                        isFullUser = false,
                        challenges = buildStreakChallengeUiModel(0),
                        newRewards = emptyList()
                    )
                }
            } else {
                val result = bestiePointsRepository.getUserScoreAndRewards()
                val claimedRewards =
                    bestiePointsRepository.getUserAlreadyClaimedRewards().getDataOrNull()
                if (result is ResultOf.Success) {
                    updateState {
                        val newRewards = result.data.rewards
                            .filter { reward -> reward.category == RewardCategory.DAY_STREAK }
                            .map { reward ->
                                RewardUiModel(
                                    id = reward.id,
                                    title = reward.title,
                                    desc = reward.message,
                                    icon = rewardsMapper.getRewardIcon(reward.type, true),
                                    achieved = true
                                )
                            }
                        if (it.newRewards != newRewards && newRewards.isNotEmpty()) {
                            val reward = newRewards.last()
                            analytics.logScreen(
                                BestiePointsRewardScreen,
                                mapOf(Parameter.Streak to result.data.streak)
                            )
                            sendEvent(StreakBestiePointsViewEvents.ShowReward(reward))
                        }
                        updateUserProperties(result.data)
                        it.copy(
                            streaks = result.data.streak,
                            points = result.data.points,
                            isFullUser = true,
                            challenges = buildStreakChallengeUiModel(result.data.streak),
                            newRewards = newRewards,
                            rewards = rewardsMapper.buildRewardsList(claimedRewards)
                        )
                    }
                }
            }
        }
}
```

**Business Logic**:
- **User Type Check**: Only full users can access bestie points
- **Reactive Updates**: Observes user session changes with Flow
- **Reward Detection**: Shows popup for new DAY_STREAK rewards
- **Analytics Integration**: Updates user properties and tracks reward views

### Streak Challenge Logic

**File**: `core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/presentation/StreakBestiePointsViewModel.kt:147-174`

```kotlin
// File: core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/presentation/StreakBestiePointsViewModel.kt:147-174
private fun buildStreakChallengeUiModel(streak: Int): StreakChallengeUiModel {
    val currentChallenge = when (streak) {
        in STREAK_3_LOWER_RANGE..STREAK_3 -> STREAK_3
        in STREAK_7_LOWER_RANGE..STREAK_7 -> STREAK_7
        in STREAK_14_LOWER_RANGE..STREAK_14 -> STREAK_14
        else -> STREAK_30
    }

    val challenges = mutableListOf(
        StreakAchievedUiModel(STREAK_3, false),
        StreakAchievedUiModel(STREAK_7, false),
        StreakAchievedUiModel(STREAK_14, false),
        StreakAchievedUiModel(STREAK_30, false)
    )

    challenges.forEachIndexed { index, challenge ->
        if (streak >= challenge.streak) {
            challenges[index].achieved = true
        }
    }

    return StreakChallengeUiModel(
        currentChallenge = currentChallenge,
        dayOfCurrentChallenge = streak,
        endDayOfChallenge = currentChallenge,
        challengesAchieved = challenges
    )
}
```

**Milestone Progression**:
- **Days 1-3**: Working toward first milestone
- **Days 4-7**: Working toward weekly milestone  
- **Days 8-14**: Working toward bi-weekly milestone
- **Days 15+**: Working toward monthly milestone
- **Achievement Tracking**: Marks completed milestones as achieved

## Analytics Integration

### Event Tracking

**File**: `core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/presentation/BestiePointsAnalytics.kt`

```kotlin
// File: core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/presentation/BestiePointsAnalytics.kt:6-13
data object BestiePointsScreen : AnalyticsScreens("bp")
data object BestiePointsRewardScreen : AnalyticsScreens("reward_info")

data object OpenBestiePointsScreen : AnalyticsActions("open_bp")
data object CloseBestiePointsScreen : AnalyticsActions("close_bp")
data object OpenInfoForBestiePoints : AnalyticsActions("open_bp_info")
data object OpenInfoForStreaks : AnalyticsActions("open_streaks_info")
data object OpenSignUpFromBanner : AnalyticsActions("open_signup_from_bp_banner")
```

**Tracked Events**:
- **Screen Views**: Main bestie points screen and reward popup screen
- **User Actions**: Opening/closing screens, info button clicks
- **User Journey**: Signup prompts for non-authenticated users

### User Properties Analytics

**File**: `core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/presentation/StreakBestiePointsViewModel.kt:138-145`

```kotlin
// File: core/bestiepoints/src/main/java/co/thewordlab/luzia/core/bestiepoints/presentation/StreakBestiePointsViewModel.kt:138-145
private fun updateUserProperties(data: UserScore) {
    analytics.setUserProperties(
        mapOf(
            Parameter.UserStreak to data.streak,
            Parameter.UserBestiePoints to data.points
        )
    )
}
```

**Business Intelligence**: Updates user analytics properties for segmentation and engagement analysis.

## Data Flow Architecture

```mermaid
sequenceDiagram
    participant U as User
    participant VM as ViewModel
    participant R as Repository
    participant API as BestiePointsApi
    participant B as Backend

    U->>VM: OnStart action
    VM->>VM: Check user session
    VM->>R: getUserScoreAndRewards()
    R->>API: GET /user/score
    API->>B: HTTP request with auth
    B-->>API: UserScoreDTO
    API-->>R: ApiResult<UserScoreDTO>
    R-->>VM: ResultOf<UserScore>
    
    VM->>R: getUserAlreadyClaimedRewards()
    R->>API: GET /user/rewards/day-streak
    API->>B: HTTP request with auth
    B-->>API: UserRewardsDTO
    API-->>R: ApiResult<UserRewardsDTO>
    R-->>VM: ResultOf<List<UserRewards>>
    
    VM->>VM: Build UI models
    VM->>VM: Check for new rewards
    VM->>+U: Show reward popup (if new)
    VM->>VM: Update state
    VM-->>U: Render UI
```

## Dependencies

**File**: `core/bestiepoints/build.gradle.kts`

```kotlin
// File: core/bestiepoints/build.gradle.kts:9-16
dependencies {
    implementation(libs.lottie)
    implementation(projects.core.navigation)
    implementation(projects.core.profile)
    implementation(projects.core.connectivity)
    implementation(projects.foundation.analytics)
    implementation(projects.foundation.designSystem)
}
```

**Key Dependencies**:
- **Lottie**: For animated reward graphics and visual feedback
- **Core Profile**: User profile access for authentication checks
- **Foundation Analytics**: Event tracking and user property management
- **Design System**: UI components and theming

## Module Integration

```mermaid
graph LR
    A[Bestie Points] --> B[Core Profile]
    A --> C[Foundation Analytics]
    A --> D[Foundation Networking]
    A --> E[Foundation Design System]
    
    F[Features Home] --> A
    G[Features Profile] --> A
    H[Features Gamification] --> A
    
    subgraph "User Journey"
        I[Anonymous User] --> J[Signup Prompt]
        K[Full User] --> L[Points Display]
        L --> M[Streak Tracking]
        M --> N[Reward Popups]
    end
```

**Usage Context**:
- **Home Feature**: Displays points/streak summary in navigation
- **Profile Feature**: Shows detailed points history and achievements
- **Gamification**: Integrates with broader reward and engagement systems

**Key Characteristics**:
- **Read-Only**: No local point earning/spending logic - all backend-driven
- **Streak-Focused**: Emphasizes daily usage habits with milestone rewards
- **Lightweight**: Minimal local state - fetches fresh data on each view
- **User-Gated**: Only functions for authenticated full users
- **Analytics-Rich**: Comprehensive tracking for engagement optimization

This focused bestie points module prioritizes simplicity and backend flexibility over complex local economy management, ensuring consistent cross-platform behavior while maintaining rich analytics for business intelligence.