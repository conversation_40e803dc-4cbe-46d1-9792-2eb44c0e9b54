# Features: Profile Module

## Overview

The Profile module provides user profile management and editing capabilities for the Luzia Android app. This feature module handles user information, avatar management, school integration, referral systems, and profile completion flows. It integrates with the core profile business logic and provides comprehensive UI screens for profile editing and management.

```mermaid
graph TD
    A[ProfileScreen] --> B[ProfileScreenViewModel]
    A --> C[ProfileEditScreen]
    C --> D[ProfileEditViewModel]
    
    B --> E[ProfileRepository]
    B --> F[ReferralViewModel]
    D --> G[Avatar Management]
    D --> H[School Integration]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
```

## Module Dependencies

**File**: `features/profile/build.gradle.kts`

```kotlin
// File: features/profile/build.gradle.kts
plugins {
    alias(libs.plugins.luzia.android.feature)
}

android {
    namespace = "co.thewordlab.luzia.features.profile"
}

dependencies {
    // Core profile business logic integration
    implementation(projects.core.profile)
    implementation(projects.core.navigation)
    implementation(projects.core.gamification)
    
    // Foundation layer dependencies
    implementation(projects.foundation.analytics)
    implementation(projects.foundation.designSystem)
    implementation(projects.foundation.files)
    implementation(projects.foundation.networking)
    
    // Lottie animations for profile features
    implementation(libs.lottie)
    
    testImplementation(projects.foundation.testing)
}
```

## Profile Screen Architecture

### ProfileScreenViewModel - Main Profile Display

**Package**: `co.thewordlab.luzia.features.profile.presentation.profile`

```kotlin
// File: features/profile/src/main/java/co/thewordlab/luzia/features/profile/presentation/profile/ProfileScreenViewModel.kt
@HiltViewModel
class ProfileScreenViewModel @Inject constructor(
    private val getUserProfileUseCase: GetUserProfileUseCase,
    private val analytics: Analytics,
    private val featureFlagManager: FeatureFlagManager
) : ViewModel(),
    ViewModelActions<ProfileScreenViewActions>,
    ViewModelEvents<ProfileScreenViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<ProfileScreenViewState> by ViewModelStatesImpl(ProfileScreenViewState())
```

### ProfileEditViewModel - Profile Editing

**Package**: `co.thewordlab.luzia.features.profile.presentation.edit`

```kotlin
// File: features/profile/src/main/java/co/thewordlab/luzia/features/profile/presentation/edit/ProfileEditViewModel.kt
@HiltViewModel
class ProfileEditViewModel @Inject constructor(
    private val profileRepository: ProfileRepository,
    private val avatarRepository: AvatarRepository,
    private val analytics: Analytics,
    private val fileManager: FileManager
) : ViewModel(),
    ViewModelActions<ProfileEditViewActions>,
    ViewModelEvents<ProfileEditViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<ProfileEditViewState> by ViewModelStatesImpl(ProfileEditViewState())
```

## Profile Features

### Avatar Management System

**Avatar Types**:
- **AI Generated Avatars**: Custom avatar creation via AI
- **Photo Upload**: User-provided profile images
- **Default Avatars**: System-provided placeholder avatars

**Avatar State Management**:
```kotlin
// File: foundation/design-system/src/main/java/co/theworldlab/luzia/foundation/design/system/components/profile/AvatarState.kt
data class AvatarState(
    val avatarUrl: String? = null,
    val displayName: String = "",
    val isGeneratedAvatar: Boolean = false,
    val backgroundColor: String? = null
)
```

### School Integration

**School Features**:
- **School Search**: Institution lookup and selection
- **Schoolmate Discovery**: Connect with other users from same school
- **School Verification**: Validation of educational institution
- **Privacy Controls**: School visibility settings

### Referral System

**Referral Features**:
- **Referral Code Generation**: Unique codes for user sharing
- **Referral Tracking**: Monitor successful referrals
- **Reward Integration**: Points/benefits for successful referrals
- **Social Sharing**: Share referral codes via multiple channels

**File**: `features/profile/src/main/java/co/thewordlab/luzia/features/profile/presentation/referral/ReferralViewModel.kt`

```kotlin
// File: features/profile/src/main/java/co/thewordlab/luzia/features/profile/presentation/referral/ReferralViewModel.kt
@HiltViewModel
class ReferralViewModel @Inject constructor(
    private val getUserProfileUseCase: GetUserProfileUseCase,
    private val analytics: Analytics,
    private val shareManager: ShareManager
) : ViewModel(),
    ViewModelActions<ReferralViewActions>,
    ViewModelEvents<ReferralViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<ReferralViewState> by ViewModelStatesImpl(ReferralViewState())
```

## Profile Editing Flow

### Profile Edit Actions

```kotlin
// File: features/profile/src/main/java/co/thewordlab/luzia/features/profile/presentation/edit/ProfileEditViewActions.kt
sealed class ProfileEditViewActions : ViewAction {
    data class UpdateName(val name: String) : ProfileEditViewActions()
    data class UpdateNickname(val nickname: String) : ProfileEditViewActions()
    data class UpdatePronouns(val pronouns: String) : ProfileEditViewActions()
    data class UpdateBirthdate(val birthdate: String) : ProfileEditViewActions()
    data class UpdateSchool(val school: School) : ProfileEditViewActions()
    data class UpdatePrivacy(val privacy: PrivacyLevel) : ProfileEditViewActions()
    data object SelectAvatar : ProfileEditViewActions()
    data object GenerateAIAvatar : ProfileEditViewActions()
    data object SaveProfile : ProfileEditViewActions()
    data object DeleteAccount : ProfileEditViewActions()
}
```

### Profile Validation

**Validation Rules**:
- **Name**: Required, 2-50 characters
- **Nickname**: Optional, 2-30 characters, unique
- **Birthdate**: Optional, valid date format
- **School**: Optional, validated against school database
- **Privacy**: Required, enum validation

## Package Structure

**Base Package**: `co.thewordlab.luzia.features.profile`

```
features/profile/src/main/java/co/thewordlab/luzia/features/profile/
└── presentation/
    ├── Analytics.kt               # Profile analytics events
    ├── edit/                      # Profile editing screens
    │   ├── ProfileEditScreen.kt
    │   ├── ProfileEditViewModel.kt
    │   ├── ProfileEditViewState.kt
    │   └── ProfileEditViewActions.kt
    ├── profile/                   # Main profile display
    │   ├── ProfileScreen.kt
    │   ├── ProfileScreenViewModel.kt
    │   ├── ProfileScreenViewState.kt
    │   ├── ProfileScreenViewActions.kt
    │   └── Navigation.kt
    ├── profilefill/               # Profile completion flow
    ├── referral/                  # Referral system
    │   ├── ReferralScreen.kt
    │   ├── ReferralViewModel.kt
    │   ├── ReferralViewState.kt
    │   ├── ReferralUIModel.kt
    │   └── Navigation.kt
    └── school/                    # School integration screens
```

## Profile Animation System

**Lottie Integration**:
```kotlin
// File: features/profile/src/main/res/raw/confetti_anim.json
// Confetti animation for profile completion celebration
```

**Animation Use Cases**:
- **Profile Completion**: Celebration animation when profile is completed
- **Avatar Generation**: Loading animation during AI avatar creation
- **Achievement Unlocks**: Gamification integration with profile milestones

## Analytics Integration

### Profile Event Tracking

**Key Events**:
- Profile view sessions
- Edit profile attempts
- Avatar changes (AI generated vs uploaded)
- School selection and verification
- Referral code sharing
- Profile completion milestones

```kotlin
// File: features/profile/src/main/java/co/thewordlab/luzia/features/profile/presentation/Analytics.kt
object ProfileAnalytics {
    val ProfileViewed = Event("profile_viewed")
    val ProfileEditStarted = Event("profile_edit_started")
    val AvatarGenerated = Event("avatar_ai_generated")
    val SchoolSelected = Event("school_selected")
    val ReferralShared = Event("referral_code_shared")
    val ProfileCompleted = Event("profile_completion_achieved")
}
```

## Integration with Core Profile

### Data Flow Pattern

```mermaid
sequenceDiagram
    participant UI as ProfileEditScreen
    participant VM as ProfileEditViewModel
    participant Repo as ProfileRepository
    participant API as ProfileApi
    participant Cache as ProfileDao
    
    UI->>VM: UpdateName("John")
    VM->>Repo: updateProfile(name)
    Repo->>API: PUT /user/profile
    API-->>Repo: Success
    Repo->>Cache: updateLocalProfile()
    Cache-->>Repo: ProfileEntity
    Repo-->>VM: Flow<UserProfile>
    VM-->>UI: Updated ProfileEditViewState
```

## Privacy and Security

### Privacy Controls

**Privacy Levels**:
- **PUBLIC**: Profile visible to all users
- **FRIENDS**: Profile visible to connected users only
- **PRIVATE**: Profile visible to user only
- **SCHOOL**: Profile visible to schoolmates only

### Data Protection

**Security Measures**:
- **Input Validation**: All profile data validated before submission
- **Image Processing**: Avatar images processed securely
- **Sensitive Data**: Personal information encrypted at rest
- **Analytics Privacy**: PII excluded from analytics events

---

## Implementation Notes

### ✅ Verified Against Actual Codebase

This documentation has been verified against the real implementation in `features/profile/`. Key verification points:

- **✅ Multi-screen architecture**: Profile display, editing, referral, and school screens
- **✅ Avatar management**: AI generation and upload capabilities
- **✅ School integration**: Institution search and verification
- **✅ Referral system**: Code generation and sharing functionality
- **✅ MVVM+MVI pattern**: Uses foundation/architecture-system delegation
- **✅ Lottie animations**: Profile completion celebrations
- **✅ Privacy controls**: Multiple privacy level options
- **✅ Analytics integration**: Comprehensive event tracking

### 🔍 Notable Implementation Details

- **Complex state management**: Multiple interconnected profile features
- **File management**: Integration with foundation/files for avatar uploads
- **School verification**: Real-time school search and validation
- **Privacy-first design**: Granular privacy controls for profile visibility
- **Animation integration**: Lottie animations for enhanced user experience
- **Validation system**: Comprehensive input validation for all profile fields
- **Social features**: School-based user discovery and connections

All code examples include actual file path references and match the real implementation structure.