# Foundation: Messages Module

**Location**: `foundation/messages`  
**Package**: `co.thewordlab.luzia.foundation.messages`  
**Purpose**: Chat infrastructure for AI conversations with completion-based message handling, context management, and multimedia support

## Overview

The Messages module provides the core chat infrastructure for Luzia's AI assistant functionality. It handles AI chat completions, message persistence, context building, audio transcription, vision processing, and text-to-speech. This foundation module serves as the messaging backbone for AI conversations across the application.

```mermaid
graph TD
    A[Messages Module] --> B[Domain Layer]
    A --> C[Data Layer]
    A --> D[DI Layer]
    
    B --> B1[ChatRepository Interface]
    B --> B2[Context Building]
    B --> B3[Ad Content Models]
    B --> B4[Proactive Messages]
    
    C --> C1[ChatApi - AI Completions]
    C --> C2[ChatRepositoryImpl]
    C --> C3[Data Sources - Memory/Persistent]
    C --> C4[File & Media Handling]
    
    D --> D1[Hilt Module]
    D --> D2[Qualifier Annotations]
    D --> D3[Provider Configuration]
```

## Dependencies

**File**: `foundation/messages/build.gradle.kts`

```kotlin
// File: foundation/messages/build.gradle.kts:11-32
dependencies {
    implementation(projects.core.feedback)
    implementation(projects.foundation.localization)
    implementation(projects.foundation.persistence)
    implementation(projects.foundation.networking)
    implementation(projects.foundation.common)
    implementation(projects.foundation.files)
    implementation(projects.foundation.config)
    implementation(projects.foundation.designSystem)
    implementation(projects.foundation.analytics)
    implementation(libs.retrofit.core)
    implementation(libs.slack.eithernet)
    implementation(libs.androidx.paging)
    implementation(libs.moshi.core)
    implementation(libs.moshi.kotlin)
    implementation(libs.moshi.adapters)
    ksp(libs.moshi.codegen)
    implementation(libs.androidx.dataStore.core)
    implementation(libs.androidx.dataStore.preferences)
    
    testImplementation(projects.foundation.testing)
}
```

## Domain Layer

### ChatRepository Interface

**File**: `foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/domain/repository/ChatRepository.kt`

Central interface for all chat-related operations:

```kotlin
// File: foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/domain/repository/ChatRepository.kt:17-106
@Suppress("TooManyFunctions")
interface ChatRepository {

    fun getMessages(personalityId: String): Flow<PagingData<MessageEntity>>
    
    fun getFavoritesMessages(personalityId: String): Flow<PagingData<MessageEntity>>
    
    fun getUnreadMessageCount(personalityId: String): Flow<Long>
    
    fun getLastLuziaChat(): Flow<ChatView?>
    
    fun getChats(): Flow<List<ChatView>>
    
    suspend fun sendTextMessage(
        personalityId: String,
        text: String,
        isWebSearch: Boolean
    ): ResultOf<ContentTextDto, AppErrors>
    
    suspend fun sendTextMessageAndSignalAd(
        personalityId: String,
        text: String,
        adContent: AdContent?,
        isWebSearch: Boolean
    ): ResultOf<TextMessageResultWrapper, AppErrors>
    
    suspend fun resendMessage(
        message: MessageEntity,
        isWebSearch: Boolean
    ): ResultOf<ContentTextDto, AppErrors>
    
    suspend fun sendAudioRecording(
        personalityId: String,
        file: File,
    ): ResultOf<ContentTextDto, AppErrors>
    
    suspend fun sendAudioImported(
        personalityId: String,
        file: File,
    ): ResultOf<ContentTextDto, AppErrors>
    
    suspend fun sendVisionImage(
        file: File,
        prompt: String,
        personalityId: String,
    ): ResultOf<ContentTextDto, AppErrors>
    
    suspend fun textToSpeech(
        text: String
    ): ResultOf<Uri, AppErrors>
    
    // Message management
    suspend fun readAllMessages(personalityId: String)
    suspend fun updateMessage(messageId: Long, textToUpdate: String, isWebSearch: Boolean)
    suspend fun setMessagesAsPendingToDelete(personalityId: String, messageIds: List<Long>)
    suspend fun deletePendingMessages(personalityId: String)
    suspend fun searchMessages(personalityId: String, query: String): List<MessageEntity>
    suspend fun toggleFavorite(message: MessageModel)
    
    // Analytics and state
    suspend fun countAllMessages(): Int
    suspend fun countAllLuziaMessages(): Int
    suspend fun countAllUserMessagesWithLuzia(): Int
    suspend fun shouldShowBestiePointsBanner(): Boolean
    suspend fun markBestiePointsBannerAsShown()
}
```

### Context Building for AI Completions

**File**: `foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/domain/ChatContext.kt`

Core logic for building conversation context from chat history:

```kotlin
// File: foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/domain/ChatContext.kt:10-56
@Suppress("NestedBlockDepth")
fun MutableList<CompletionContextMessage>.buildContextMessagesFromChatHistory(
    history: MutableList<MessageEntity>,
    proactiveMessageHandler: ProactiveMessageHandler?,
): List<CompletionContextMessage> {
    var prompt = ""
    var completion = ""
    while (history.isNotEmpty()) {
        val message = history.getOrNull(0)
        if (message != null) {
            when {
                message.messageType == MessageType.Proactive -> {
                    prompt = ""
                    completion = ""
                    proactiveMessageHandler?.extractContentInjection(
                        message.proactivePayload.orEmpty()
                    )?.let {
                        CompletionContextMessage(prompt = "", completion = it)
                    }?.let { add(it) }
                }

                message.messageType == MessageType.AudioImport -> {
                    prompt = ""
                    completion = ""
                    add(CompletionContextMessage(PROMPT_TRANSCRIBE, message.text))
                }

                !message.isAi -> {
                    prompt = prompt + " " + message.text
                }

                message.isAi -> {
                    completion = completion + " " + message.text
                    message.contentInjection?.let {
                        completion = "$completion $it"
                    }
                }
            }
            if (prompt.isNotEmpty() && completion.isNotEmpty()) {
                add(CompletionContextMessage(prompt, completion))
                prompt = ""
                completion = ""
            }
            history.remove(message)
        }
    }
    return this
}
```

### Ad Content Domain Model

**File**: `foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/domain/model/AdContent.kt`

```kotlin
// File: foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/domain/model/AdContent.kt
data class AdContent(
    val response: String?,
    val description: String?,
    val advertiser: String?,
    val title: String?,
    val body: String?,
    val confidence: String?
)
```

## Data Layer

### Chat API Interface

**File**: `foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/data/api/ChatApi.kt`

Core API endpoints for AI chat functionality:

```kotlin
// File: foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/data/api/ChatApi.kt:18-42
interface ChatApi {
    @DecodeErrorBody
    @POST("chat")
    suspend fun completion(@Body completionsRequest: CompletionsRequest): ApiResult<ContentTextDto, ErrorDto>

    @DecodeErrorBody
    @POST("audio-transcriptions")
    suspend fun postAudioTranscriptions(
        @Body body: RequestBody? = null,
        @Header("Content-Type") contentType: String = Constants.AUDIO_MP4,
        @Query("personalityId") personalityId: String,
        @Query("forwarded") isForwarded: Boolean?
    ): ApiResult<ContentTextDto, ErrorDto>

    @DecodeErrorBody
    @JvmSuppressWildcards
    @POST("vision")
    suspend fun vision(@Body body: MultipartBody): ApiResult<ContentTextDto, ErrorDto>

    @DecodeErrorBody
    @POST("voice")
    suspend fun textToSpeech(
        @Body request: TextToSpeechRequestDto
    ): Response<ResponseBody>
}
```

### Data Models

#### CompletionsRequest

**File**: `foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/data/model/CompletionsRequest.kt`

```kotlin
// File: foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/data/model/CompletionsRequest.kt:7-28
@JsonClass(generateAdapter = true)
data class CompletionsRequest(
    @Json(name = "content")
    val content: String,
    @Json(name = "personalityId")
    val personalityId: String? = null,
    @Json(name = "context")
    val messages: List<CompletionContextMessage>,
    @Json(name = "responseStyleId")
    val responseStyleId: String? = null,
    @Json(name = "attachmentId")
    val attachmentId: String? = null,
    @Json(name = "isWebSearch")
    val isWebSearch: String = "false"
)

@JsonClass(generateAdapter = true)
data class CompletionContextMessage(
    @Json(name = "prompt")
    val prompt: String?,
    @Json(name = "completion")
    val completion: String
)
```

#### ContentTextDto

**File**: `foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/data/model/ContentTextDto.kt`

```kotlin
// File: foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/data/model/ContentTextDto.kt:6-40
@JsonClass(generateAdapter = true)
data class ContentTextDto(
    @Json(name = "content")
    val content: String,
    @Json(name = "context")
    val context: String? = null,
    @Json(name = "metadata")
    val metadata: ContentMetadataDto,
    @Json(name = "attachmentId")
    val attachmentId: String? = null
)

@JsonClass(generateAdapter = true)
data class AdContentDto(
    @Json(name = "response")
    val response: String? = null,
    @Json(name = "description")
    val description: String? = null,
    @Json(name = "advertiser")
    val advertiser: String? = null,
    @Json(name = "title")
    val title: String? = null,
    @Json(name = "body")
    val body: String? = null,
    @Json(name = "confidence")
    val confidence: String? = null
)

@JsonClass(generateAdapter = true)
data class ContentMetadataDto(
    @Json(name = "requestId")
    val requestId: String,
    @Json(name = "maxPromptLength")
    val maxPromptLength: Int? = null
)
```

#### Additional DTOs

**File**: `foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/data/model/ToolDetailDto.kt`

```kotlin
// File: foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/data/model/ToolDetailDto.kt:6-10
@JsonClass(generateAdapter = true)
data class ToolDetailDto(
    @Json(name = "iceBreakers")
    val iceBreakers: List<String>
)
```

### Repository Implementation

**File**: `foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/data/repository/ChatRepositoryImp.kt`

The repository implementation handles:
- **AI Completions**: Building context and calling completion API
- **Message Persistence**: Using both persistent and in-memory data sources
- **Media Processing**: Audio transcription, vision, text-to-speech
- **Analytics Integration**: Tracking message interactions
- **Ad Management**: Handling advertisement content in chat

```kotlin
// File: foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/data/repository/ChatRepositoryImp.kt:52-120
class ChatRepositoryImp @Inject constructor(
    @Persistent private val datasource: ChatDataSource,
    private val luziaDataStore: LuziaDataStore,
    private val featureFlagManager: FeatureFlagManager,
    private val chatApi: ChatApi,
    private val mediaFileProvider: MediaFileProvider,
    private val analytics: Analytics,
    private val proactiveMessageHandler: ProactiveMessageHandler,
    private val fileManager: FileManager,
    private val moshi: Moshi,
    private val adChatService: AdChatService
) : ChatRepository {

    override fun getMessages(personalityId: String): Flow<PagingData<MessageEntity>> = 
        datasource.getMessages(personalityId)
    
    override suspend fun sendTextMessage(
        personalityId: String,
        text: String,
        isWebSearch: Boolean
    ): ResultOf<ContentTextDto, AppErrors> {
        // Store user message locally
        val userMessage = text.asUserMessage(personalityId)
        datasource.insertMessage(userMessage)
        
        // Show loading state
        val loadingMessage = text.asLoading(personalityId, userMessage.requestId)
        datasource.insertMessage(loadingMessage)
        
        return try {
            // Build context from chat history
            val contextParams = buildCompletionParams(personalityId)
            val completionRequest = CompletionsRequest(
                content = text,
                personalityId = personalityId,
                messages = contextParams.context,
                responseStyleId = contextParams.responseStyleId,
                isWebSearch = if (isWebSearch) "true" else "false"
            )
            
            // Call AI completion API
            val result = chatApi.completion(completionRequest).asResult { it }
            
            when (result) {
                is ResultOf.Success -> {
                    // Replace loading with AI response
                    val aiMessage = result.data.asAiMessage(personalityId, userMessage.requestId)
                    datasource.insertMessage(aiMessage)
                    datasource.deleteMessage(loadingMessage.id)
                    result
                }
                is ResultOf.Failure -> {
                    // Mark as failed
                    val failedMessage = text.asFailed(personalityId, userMessage.requestId)
                    datasource.insertMessage(failedMessage)
                    datasource.deleteMessage(loadingMessage.id)
                    result
                }
            }
        } catch (e: Exception) {
            // Handle error state
            val failedMessage = text.asFailed(personalityId, userMessage.requestId)
            datasource.insertMessage(failedMessage)
            datasource.deleteMessage(loadingMessage.id)
            ResultOf.Failure(AppErrors.UnspecifiedError, null, e)
        }
    }
}
```

### Data Source Architecture

```mermaid
classDiagram
    class ChatDataSource {
        <<interface>>
        +getMessages(personalityId) Flow~PagingData~MessageEntity~~
        +insertMessage(message)
        +deleteMessage(messageId)
        +updateMessage(message)
        +searchMessages(query)
    }
    
    class ChatDao {
        <<Room DAO>>
        +getAllMessages()
        +insertMessage()
        +deleteMessage()
        +updateMessage()
    }
    
    class InMemoryChatDataSource {
        +MutableList~MessageEntity~ messages
        +insertMessage()
        +getMessages()
    }
    
    ChatDataSource <|-- ChatDao : Persistent
    ChatDataSource <|-- InMemoryChatDataSource : InMemory
    
    note for ChatDao "Used in production for persistence"
    note for InMemoryChatDataSource "Used for testing and preview"
```

### Message Flow Architecture

```mermaid
sequenceDiagram
    participant UI as Chat UI
    participant Repo as ChatRepository
    participant API as ChatApi
    participant DB as DataSource
    participant AI as AI Backend
    
    UI->>Repo: sendTextMessage(text, personalityId)
    Repo->>DB: insertMessage(userMessage)
    Repo->>DB: insertMessage(loadingMessage)
    
    Repo->>DB: getMessages(personalityId) [build context]
    DB-->>Repo: List<MessageEntity>
    
    Repo->>API: completion(CompletionsRequest)
    API->>AI: POST /chat with context
    AI-->>API: ContentTextDto response
    API-->>Repo: ResultOf.Success(response)
    
    Repo->>DB: insertMessage(aiMessage)
    Repo->>DB: deleteMessage(loadingMessage)
    
    DB-->>UI: Flow<PagingData<MessageEntity>>
```

## Dependency Injection

**File**: `foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/di/ChatModule.kt`

```kotlin
// File: foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/di/ChatModule.kt:27-68
@Module
@InstallIn(SingletonComponent::class)
object ChatModule {
    @Provides
    fun provideChatApi(@BaseHost retrofit: Retrofit): ChatApi = retrofit.create(ChatApi::class.java)

    @Provides
    fun provideMathApi(@BaseHost retrofit: Retrofit): MathApi = retrofit.create(MathApi::class.java)

    @Persistent
    @Provides
    fun providePersistentDatasource(chatDao: ChatDao): ChatDataSource = chatDao

    @InMemory
    @Provides
    fun provideInMemoryDatasource(dataSource: InMemoryChatDataSource): ChatDataSource = dataSource

    @Provides
    fun provideChatRepository(
        @Persistent datasource: ChatDataSource,
        luziaDataStore: LuziaDataStore,
        featureFlagManager: FeatureFlagManager,
        chatApi: ChatApi,
        mediaFileProvider: MediaFileProvider,
        analytics: Analytics,
        proactiveMessageHandler: ProactiveMessageHandler,
        fileManager: FileManager,
        moshi: Moshi,
        adChatService: AdChatService
    ): ChatRepository = ChatRepositoryImp(
        datasource,
        luziaDataStore,
        featureFlagManager,
        chatApi,
        mediaFileProvider,
        analytics,
        proactiveMessageHandler,
        fileManager,
        moshi,
        adChatService
    )
}
```

### Qualifier Annotations

**File**: `foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/di/Persistent.kt`
**File**: `foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/di/InMemory.kt`

```kotlin
// File: foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/di/Persistent.kt
@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class Persistent

// File: foundation/messages/src/main/java/co/thewordlab/luzia/foundation/messages/di/InMemory.kt
@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class InMemory
```

## Key Features

### 1. **AI Completion System**
- Context-aware conversation building from chat history
- Support for different response styles and personalities
- Web search integration flag
- Attachment handling for documents and images

### 2. **Multimedia Support**
- **Audio Transcription**: Convert voice messages to text
- **Vision Processing**: Image recognition and analysis
- **Text-to-Speech**: Convert AI responses to audio
- **File Handling**: Document and media processing

### 3. **Message State Management**
- Loading states during AI processing
- Failed message handling with retry capability
- Message favoriting and search functionality
- Pagination support for chat history

### 4. **Analytics Integration**
- Message interaction tracking
- Error and exception reporting
- User behavior analytics
- Performance monitoring

### 5. **Advertisement Integration**
- Ad content handling in chat responses
- Advertisement analytics tracking
- Contextual ad insertion

## Error Handling

```mermaid
flowchart TD
    A[Send Message] --> B{API Call}
    B -->|Success| C[Insert AI Response]
    B -->|Network Error| D[Mark as Failed]
    B -->|HTTP Error| E[Parse Error Response]
    
    C --> F[Delete Loading Message]
    D --> G[Delete Loading Message]
    E --> H{Can Parse Error?}
    
    H -->|Yes| I[Show Specific Error]
    H -->|No| J[Show Generic Error]
    
    I --> G
    J --> G
    
    G --> K[Enable Retry Option]
```

## Testing

**File**: `foundation/messages/src/test/java/co/thewordlab/luzia/foundation/messages/data/repository/ChatRepositoryImpTest.kt`

The module includes comprehensive test coverage:
- **Repository Tests**: Unit tests for ChatRepositoryImp
- **Context Building Tests**: Tests for buildContextMessagesFromChatHistory
- **Mapper Tests**: Tests for DTO to domain model mapping
- **Use Case Tests**: Tests for IsFirstMessageSentUseCase

## Integration Points

- **Foundation Persistence**: MessageEntity and ChatDao for data storage
- **Foundation Networking**: ResultOf pattern and API integration
- **Foundation Analytics**: Event tracking and error reporting
- **Foundation Files**: File and media handling
- **Foundation Config**: Feature flag management
- **Core Feedback**: Feedback collection integration

This Messages module provides the foundational chat infrastructure for Luzia's AI assistant, emphasizing completion-based conversations, context management, and comprehensive multimedia support while maintaining clean architecture principles.