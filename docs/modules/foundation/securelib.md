# Foundation: SecureLib Module

**Location**: `foundation/securelib`  
**Package**: `co.thewordlab.luzia.foundation.securelib`  
**Purpose**: Native C++ library for secure API key storage and retrieval with flavor-based key management

## Overview

The SecureLib module provides a lightweight native C++ implementation for storing and retrieving API keys securely. It uses JNI (Java Native Interface) to access keys stored in native code, making it harder for reverse engineering tools to extract sensitive keys. The module supports flavor-based key selection (production vs development environments).

```mermaid
graph TD
    A[Kotlin Layer] --> B[JNI Bridge]
    B --> C[Native C++ Library]
    
    subgraph "Kotlin Components"
        A1[SecureStorage Class] --> A2[SecureKey Enum]
        A1 --> A3[Flavor Detection]
    end
    
    subgraph "Native Storage"
        C1[API Key Map] --> C2[Key Lookup]
        C2 --> C3[String Return]
    end
    
    A --> A1
    C --> C1
    B --> D[obtainKeyFromNative JNI Method]
```

## Dependencies

**File**: `foundation/securelib/build.gradle.kts`

```kotlin
// File: foundation/securelib/build.gradle.kts:1-16
plugins {
    alias(libs.plugins.luzia.android.library)
    alias(libs.plugins.luzia.android.library.jacoco)
    alias(libs.plugins.luzia.android.hilt)
    alias(libs.plugins.luzia.android.room)
}

android {
    namespace = "co.thewordlab.luzia.foundation.securelib"
    externalNativeBuild {
        cmake {
            path("src/main/cpp/CMakeLists.txt")
            version = "3.22.1"
        }
    }
}
```

## Native C++ Implementation

### CMakeLists.txt Configuration

**File**: `foundation/securelib/src/main/cpp/CMakeLists.txt`

```cmake
# File: foundation/securelib/src/main/cpp/CMakeLists.txt:5-37
cmake_minimum_required(VERSION 3.22.1)

project("securelib")

# Creates and names a library, sets it as either STATIC
# or SHARED, and provides the relative paths to its source code.
add_library(${CMAKE_PROJECT_NAME} SHARED
        # List C/C++ source files with relative paths to this CMakeLists.txt.
        securelib.cpp)

# Specifies libraries CMake should link to your target library.
target_link_libraries(${CMAKE_PROJECT_NAME}
        # List libraries link to the target library
        android
        log)
```

### Native Key Storage Implementation

**File**: `foundation/securelib/src/main/cpp/securelib.cpp`

The native implementation stores API keys in a C++ unordered_map and provides JNI access:

```cpp
// File: foundation/securelib/src/main/cpp/securelib.cpp:1-49
#include <jni.h>
#include <string>
#include <unordered_map>

std::unordered_map<std::string, std::string> apiKeyMap = {
        {"google_web_client_id", "880757744773-0ummpeabeiijhv3s57euu1378s9kd617.apps.googleusercontent.com"},
        {"google_web_client_id_dev", "902703594582-gnoecema89cs7otkms8hlbguj76e3ct6.apps.googleusercontent.com"},
        {"amplitude_api_key", "********************************"},
        {"amplitude_api_key_dev", "********************************"},
        {"amplitude_experiment_api_key", "client-s5NA3aiQKRKmfStWqJ4M3JKdAhwudvHg"},
        {"amplitude_experiment_api_key_dev", "client-sNXhnaWGMOhKxP8JJ5Xx0FuWnCU41MUm"},
        {"google_client_prefix", "880757744773-q1b5n0mpi0065aeo5e67qlre4viioje3"},
        {"google_client_prefix_dev", "902703594582-37r1gjdqoholm9fip4i7qcvikca5graj"},
        {"google_client_prefix_debug", "880757744773-tv7t8bdsaouo8aqu5637l9c0ns705c4s"},
        {"google_client_prefix_debug_dev", "902703594582-e5gb8d4kpn2dfcjk868u7fj81uo8qtk8"},
        {"key_get_stream_api", "7j5vrtvuppz4"},
        {"key_get_stream_api_dev", "4tjj5a6qe6nb"},
        {"android_push", "Android_Push_Prod"},
        {"android_push_dev", "Android_Push_Staging"},
        {"key_data_dog", "pub1daaa00d816f76377565e5b866280db7"},
        {"key_data_dog_dev", "pub1daaa00d816f76377565e5b866280db7"},
        {"key_data_dog_rum", "71afd497-0111-43e1-801d-e3a69e6773be"},
        {"key_data_dog_rum_dev", "71afd497-0111-43e1-801d-e3a69e6773be"},
        {"appsflyer_key", "PQSg2HdK8KRtaSLG2BwkAh"},
        {"appsflyer_key_dev", "PQSg2HdK8KRtaSLG2BwkAh"},
        {"appsflyer_link_id", "yRmB"},
        {"appsflyer_link_id_dev", "fQbL"},
        // Add more key-value pairs as needed
};

extern "C" JNIEXPORT jstring JNICALL
Java_co_thewordlab_luzia_foundation_securelib_SecureStorage_obtainKeyFromNative(JNIEnv *env,
                                                                         jobject /* this */,
                                                                         jstring key) {
    const char *nativeKey = env->GetStringUTFChars(key, 0);
    std::string keyStr(nativeKey);
    env->ReleaseStringUTFChars(key, nativeKey);

    // Find the key in the map
    auto it = apiKeyMap.find(keyStr);
    if (it != apiKeyMap.end()) {
        std::string encryptedApiKey = it->second;
        return env->NewStringUTF(encryptedApiKey.c_str());
    } else {
        // Handle case where key is not found
        return env->NewStringUTF("");
    }
}
```

## Kotlin Bridge Layer

### SecureStorage Class

**File**: `foundation/securelib/src/main/java/co/thewordlab/luzia/foundation/securelib/SecureStorage.kt`

```kotlin
// File: foundation/securelib/src/main/java/co/thewordlab/luzia/foundation/securelib/SecureStorage.kt:6-21
@Singleton
class SecureStorage @Inject constructor() {

    private external fun obtainKeyFromNative(key: String): String

    companion object {
        init {
            System.loadLibrary("securelib")
        }
    }

    fun get(secureKey: SecureKey): String {
        val key = if (BuildConfig.FLAVOR == "pro") secureKey.key else "${secureKey.key}_dev"
        return obtainKeyFromNative(key)
    }
}
```

### SecureKey Enum

**File**: `foundation/securelib/src/main/java/co/thewordlab/luzia/foundation/securelib/SecureKey.kt`

```kotlin
// File: foundation/securelib/src/main/java/co/thewordlab/luzia/foundation/securelib/SecureKey.kt:3-15
enum class SecureKey(val key: String) {
    KEY_GOOGLE_WEB_CLIENT_ID("google_web_client_id"),
    KEY_AMPLITUDE("amplitude_api_key"),
    KEY_AMPLITUDE_EXPERIMENT("amplitude_experiment_api_key"),
    KEY_GOOGLE_CLIENT_PREFIX("google_client_prefix"),
    KEY_GOOGLE_CLIENT_PREFIX_DEBUG("google_client_prefix_debug"),
    KEY_GET_STREAM_API("key_get_stream_api"),
    KEY_GET_STREAM_NOTIFICATION("android_push"),
    KEY_DATADOG("key_data_dog"),
    KEY_DATADOG_RUM("key_data_dog_rum"),
    APPSFLYER_KEY("appsflyer_key"),
    APPSFLYER_LINK_ID("appsflyer_link_id"),
}
```

## API Key Management

### Stored Keys by Service

The module securely stores keys for various third-party services:

```mermaid
graph TD
    A[SecureLib Storage] --> B[Authentication Services]
    A --> C[Analytics Services]
    A --> D[Communication Services]
    A --> E[Monitoring Services]
    
    B --> B1[Google OAuth Client IDs]
    B --> B2[Google Client Prefixes]
    
    C --> C1[Amplitude API Keys]
    C --> C2[Amplitude Experiment Keys]
    C --> C3[AppsFlyer Keys]
    
    D --> D1[GetStream API Keys]
    D --> D2[Android Push Tokens]
    
    E --> E1[DataDog API Keys]
    E --> E2[DataDog RUM Keys]
```

### Flavor-Based Key Selection

The system automatically selects the appropriate key based on the build flavor:

```kotlin
// Example usage demonstrating flavor-based key selection
class ExampleUsage @Inject constructor(
    private val secureStorage: SecureStorage
) {
    
    fun getGoogleClientId(): String {
        // In "pro" flavor: returns "google_web_client_id" key
        // In "dev" flavor: returns "google_web_client_id_dev" key
        return secureStorage.get(SecureKey.KEY_GOOGLE_WEB_CLIENT_ID)
    }
    
    fun getAmplitudeKey(): String {
        // In "pro" flavor: returns "amplitude_api_key" key
        // In "dev" flavor: returns "amplitude_api_key_dev" key
        return secureStorage.get(SecureKey.KEY_AMPLITUDE)
    }
}
```

### Key Lookup Flow

```mermaid
sequenceDiagram
    participant App as Kotlin App
    participant SS as SecureStorage
    participant JNI as JNI Bridge
    participant CPP as Native C++
    
    App->>SS: get(SecureKey.KEY_AMPLITUDE)
    SS->>SS: Check BuildConfig.FLAVOR
    SS->>SS: Determine key name (+ "_dev" if needed)
    SS->>JNI: obtainKeyFromNative(keyName)
    JNI->>CPP: Java_co_thewordlab_luzia_foundation_securelib_SecureStorage_obtainKeyFromNative
    CPP->>CPP: Lookup key in apiKeyMap
    CPP-->>JNI: Return key value or empty string
    JNI-->>SS: String result
    SS-->>App: API key
```

## Security Considerations

### 1. **Native Code Protection**
- API keys stored in compiled native C++ code
- Harder to extract via standard reverse engineering tools
- No plaintext keys in Java/Kotlin bytecode

### 2. **Flavor-Based Separation**
- Production and development keys completely separated
- Automatic selection based on build configuration
- No risk of using wrong keys in different environments

### 3. **Minimal Attack Surface**
- Simple implementation with single JNI method
- No complex encryption/decryption logic to exploit
- Direct string lookup and return

### 4. **Error Handling**
- Returns empty string for unknown keys
- No exceptions thrown that could leak information
- Graceful failure handling

## Integration Points

The SecureLib module provides API keys for:

- **Foundation Analytics**: Amplitude and AppsFlyer keys for tracking
- **Foundation Networking**: DataDog keys for monitoring and RUM
- **Authentication Services**: Google OAuth client IDs and prefixes
- **Communication Services**: GetStream API keys for chat functionality
- **Push Notifications**: Android push notification service tokens

## Usage Patterns

### Basic Key Retrieval

```kotlin
// File: Example usage based on SecureStorage implementation
@Singleton
class ApiKeyProvider @Inject constructor(
    private val secureStorage: SecureStorage
) {
    
    fun getAmplitudeApiKey(): String = 
        secureStorage.get(SecureKey.KEY_AMPLITUDE)
    
    fun getGoogleWebClientId(): String = 
        secureStorage.get(SecureKey.KEY_GOOGLE_WEB_CLIENT_ID)
    
    fun getDataDogApiKey(): String = 
        secureStorage.get(SecureKey.KEY_DATADOG)
    
    fun getAppsFlyerKey(): String = 
        secureStorage.get(SecureKey.APPSFLYER_KEY)
}
```

### Service Configuration

```kotlin
// File: Example usage in service configuration
class AnalyticsConfiguration @Inject constructor(
    private val secureStorage: SecureStorage
) {
    
    fun configureAmplitude(): AmplitudeClient {
        val apiKey = secureStorage.get(SecureKey.KEY_AMPLITUDE)
        val experimentKey = secureStorage.get(SecureKey.KEY_AMPLITUDE_EXPERIMENT)
        
        return AmplitudeClient.Builder()
            .apiKey(apiKey)
            .experimentKey(experimentKey)
            .build()
    }
    
    fun configureAppsFlyer(): AppsFlyerLib {
        val devKey = secureStorage.get(SecureKey.APPSFLYER_KEY)
        val linkId = secureStorage.get(SecureKey.APPSFLYER_LINK_ID)
        
        return AppsFlyerLib.getInstance().apply {
            init(devKey, null, context)
            setAppInviteOneLink(linkId)
        }
    }
}
```

## Key Features

### 1. **Simplicity**
- Single-purpose module focused on API key storage
- Minimal codebase with clear responsibilities
- Easy to understand and maintain

### 2. **Build Flavor Support**
- Automatic development/production key selection
- No manual configuration required
- Environment-specific key isolation

### 3. **Native Security**
- Keys compiled into native binary
- JNI bridge for secure access
- Protection against common reverse engineering

### 4. **Hilt Integration**
- `@Singleton` scope for efficient memory usage
- Dependency injection support
- Easy integration with other modules

This SecureLib module provides a lightweight but effective solution for protecting API keys in the Luzia Android application, emphasizing simplicity and practical security over complex cryptographic implementations.