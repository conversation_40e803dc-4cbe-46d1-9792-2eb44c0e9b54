# Foundation: Data Cache Module

**Location**: `foundation/data-cache`  
**Package**: `co.theworldlab.luzia.foundation.data.cache`  
**Purpose**: Simple Store-based caching layer for thread-safe data management with memory and disk persistence

## Overview

The Data Cache module provides a lightweight, reactive caching solution using a Store pattern. Unlike traditional cache implementations, this module focuses on simplicity and reactive data flows with automatic expiration and optional disk persistence.

```mermaid
graph TD
    A[Store Interface] --> B[InMemoryStore Implementation]
    B --> C[Memory Cache]
    B --> D[DiskCache Interface]
    D --> E[Disk Persistence]
    B --> F[Expiration Logic]
    B --> G[Thread Safety via Mutex]
    
    H[Client Code] --> I[Flow Pattern]
    H --> J[Get/Fetch Pattern]
    H --> K[Update Pattern]
    
    I --> A
    J --> A
    K --> A
```

## Core Architecture

### Store Interface

**File**: `foundation/data-cache/src/main/java/co/theworldlab/luzia/foundation/data/cache/store/Store.kt`

```kotlin
// File: foundation/data-cache/src/main/java/co/theworldlab/luzia/foundation/data/cache/store/Store.kt:10-50
interface Store<T> {

    /**
     * Provides a Flow of the stored data.
     * The flow will emit:
     * 1. Current value (null if empty)
     * 2. Fetched value
     * 3. All subsequent updates
     */
    fun flow(): Flow<T?>

    /**
     * Retrieves the current value or fetches a new one if none exists.
     */
    suspend fun get(): T

    /**
     * Forces a new fetch of the data, regardless of cache state.
     */
    suspend fun fetch(): T

    /**
     * Updates the stored value directly.
     */
    suspend fun update(value: T?)

    /**
     * Updates the stored value using a transformation function.
     */
    suspend fun update(function: (T?) -> T?)
}
```

### Store Configuration

**File**: `foundation/data-cache/src/main/java/co/theworldlab/luzia/foundation/data/cache/store/StoreConfig.kt`

```kotlin
// File: foundation/data-cache/src/main/java/co/theworldlab/luzia/foundation/data/cache/store/StoreConfig.kt:6-9
data class StoreConfig(
    val expiration: Duration = 10.seconds,
    val debugMode: Boolean = false
)
```

## InMemoryStore Implementation

### Core Implementation

**File**: `foundation/data-cache/src/main/java/co/theworldlab/luzia/foundation/data/cache/store/memory/InMemoryStore.kt`

```kotlin
// File: foundation/data-cache/src/main/java/co/theworldlab/luzia/foundation/data/cache/store/memory/InMemoryStore.kt:26-42
internal class InMemoryStore<T>(
    private val config: StoreConfig,
    private val fetcher: suspend () -> T,
    private val diskCache: DiskCache<T>? = null,
    private val currentTimeProvider: () -> Long = { System.currentTimeMillis() },
    private val logger: (String) -> Unit = {}
) : Store<T> {

    /** Holds the current value and notifies observers of changes */
    private val mutableState = MutableStateFlow(null as T?)

    /** Tracks when the data was last fetched for expiration checking */
    private val lastFetchedAt = AtomicLong(0L)

    /** Ensures thread-safe access to fetch operations */
    private val fetchMutex = Mutex()
    
    // ... implementation
}
```

### Thread-Safe Data Flow

```mermaid
sequenceDiagram
    participant Client
    participant Store
    participant Fetcher
    participant DiskCache
    participant StateFlow
    
    Client->>Store: flow()
    Store->>StateFlow: emit current value (null)
    Store->>Store: get()
    
    opt No cached value
        Store->>Fetcher: fetch()
        Fetcher-->>Store: data
        Store->>DiskCache: saveIntoCache(data)
        Store->>StateFlow: update value
    end
    
    Store-->>Client: emit fetched value
    
    loop Subsequent updates
        Store->>StateFlow: emit updates
        StateFlow-->>Client: new values
    end
```

### Reactive Flow Pattern

**File**: `foundation/data-cache/src/main/java/co/theworldlab/luzia/foundation/data/cache/store/memory/InMemoryStore.kt:50-55`

```kotlin
// File: foundation/data-cache/src/main/java/co/theworldlab/luzia/foundation/data/cache/store/memory/InMemoryStore.kt:50-55
override fun flow(): Flow<T?> = flow {
    emit(mutableState.value)  // 1. Current value (null if empty)
    emit(get())               // 2. Fetched value if needed
    val sources = listOfNotNull(mutableState, diskCache?.getFromCache())
    emitAll(sources.merge())  // 3. All subsequent updates
}
```

### Thread-Safe Fetch with Expiration

**File**: `foundation/data-cache/src/main/java/co/theworldlab/luzia/foundation/data/cache/store/memory/InMemoryStore.kt:73-88`

```kotlin
// File: foundation/data-cache/src/main/java/co/theworldlab/luzia/foundation/data/cache/store/memory/InMemoryStore.kt:73-88
override suspend fun fetch(): T = fetchMutex.withLock {
    logger("Acquired lock")
    mutableState.value?.let { cachedValue ->
        val isExpired = isExpired()
        logger("Cached value: $cachedValue and isExpired: $isExpired")
        if (!isExpired) {
            logger("Returning cached value")
            return cachedValue
        }
    }
    val fetchedValue = fetcher()
    mutableState.value = fetchedValue
    diskCache?.saveIntoCache(fetchedValue)
    lastFetchedAt.set(currentTimeProvider())
    return fetchedValue
}
```

## Disk Persistence Layer

### DiskCache Interface

**File**: `foundation/data-cache/src/main/java/co/theworldlab/luzia/foundation/data/cache/store/disk/DiskCache.kt`

```kotlin
// File: foundation/data-cache/src/main/java/co/theworldlab/luzia/foundation/data/cache/store/disk/DiskCache.kt:5-10
interface DiskCache<T> {

    fun getFromCache(): Flow<T?>

    suspend fun saveIntoCache(item: T)
}
```

### Architecture Pattern

```mermaid
classDiagram
    class Store~T~ {
        <<interface>>
        +flow() Flow~T?~
        +get() T
        +fetch() T
        +update(value: T?)
        +update(function: (T?) -> T?)
    }
    
    class InMemoryStore~T~ {
        -config: StoreConfig
        -fetcher: suspend () -> T
        -diskCache: DiskCache~T~?
        -mutableState: MutableStateFlow~T?~
        -lastFetchedAt: AtomicLong
        -fetchMutex: Mutex
        +flow() Flow~T?~
        +get() T
        +fetch() T
        +update(value: T?)
        -isExpired() Boolean
    }
    
    class DiskCache~T~ {
        <<interface>>
        +getFromCache() Flow~T?~
        +saveIntoCache(item: T)
    }
    
    class StoreConfig {
        +expiration: Duration
        +debugMode: Boolean
    }
    
    Store <|-- InMemoryStore
    InMemoryStore --> DiskCache : optional
    InMemoryStore --> StoreConfig
```

## Builder Pattern for Store Creation

### InMemoryStoreBuilder

**File**: `foundation/data-cache/src/main/java/co/theworldlab/luzia/foundation/data/cache/store/memory/InMemoryStoreBuilder.kt`

```kotlin
// File: foundation/data-cache/src/main/java/co/theworldlab/luzia/foundation/data/cache/store/memory/InMemoryStoreBuilder.kt:8-22
class InMemoryStoreBuilder<T>(
    private val fetcher: suspend () -> T,
    private val diskCache: DiskCache<T>? = null
) {

    private var config: StoreConfig = StoreConfig()

    fun withExpiration(expiration: Duration) = apply {
        config = config.copy(expiration = expiration)
    }

    fun build(): Store<T> {
        return InMemoryStore(config, fetcher, diskCache)
    }
}
```

### Convenience Factory Function

**File**: `foundation/data-cache/src/main/java/co/theworldlab/luzia/foundation/data/cache/store/memory/InMemoryStoreBuilder.kt:24-29`

```kotlin
// File: foundation/data-cache/src/main/java/co/theworldlab/luzia/foundation/data/cache/store/memory/InMemoryStoreBuilder.kt:24-29
fun <T> inMemoryStore(
    fetcher: suspend () -> T,
    diskCache: DiskCache<T>? = null
): InMemoryStoreBuilder<T> {
    return InMemoryStoreBuilder(fetcher, diskCache)
}
```

## Usage Patterns

### Basic Store Creation

```kotlin
// File: Example usage based on InMemoryStoreBuilder.kt implementation
val userStore = inMemoryStore(
    fetcher = { userRepository.getCurrentUser() }
)
    .withExpiration(30.minutes)
    .build()
```

### Reactive Data Access

```kotlin
// File: Example usage based on Store.kt interface
class UserViewModel : ViewModel() {
    
    private val userStore = inMemoryStore { userRepository.getCurrentUser() }
        .withExpiration(30.minutes)
        .build()
    
    val userState = userStore.flow()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = null
        )
    
    fun refreshUser() {
        viewModelScope.launch {
            userStore.fetch() // Force refresh
        }
    }
    
    fun updateUser(user: User) {
        viewModelScope.launch {
            userStore.update(user)
        }
    }
}
```

### Data Transformation Updates

```kotlin
// File: Example usage based on Store.kt update method
suspend fun updateUserName(newName: String) {
    userStore.update { currentUser ->
        currentUser?.copy(name = newName)
    }
}
```

## Testing Support

### Comprehensive Test Coverage

**File**: `foundation/data-cache/src/test/java/co/theworldlab/luzia/foundation/data/store/memory/InMemoryStoreTest.kt`

Key test scenarios include:

1. **Cache Population**: Verifies initial fetch behavior
2. **Cache Hit**: Ensures cached values are returned when not expired  
3. **Cache Expiration**: Tests automatic refetch when data expires
4. **Reactive Flow**: Validates flow emission patterns
5. **Thread Safety**: Ensures concurrent fetches are properly synchronized

```kotlin
// File: foundation/data-cache/src/test/java/co/theworldlab/luzia/foundation/data/store/memory/InMemoryStoreTest.kt:101-117
@Test
fun `concurrent fetches should be synchronized`() = runTest {
    var fetchCount = 0
    coEvery { fetcher.invoke() } answers {
        fetchCount++
        "value_$fetchCount"
    }

    // Launch multiple concurrent fetches
    val results = List(3) { store.fetch() }

    // All should have same value due to mutex
    assertEquals("value_1", results[0])
    assertEquals("value_1", results[1]) 
    assertEquals("value_1", results[2])
    coVerify(exactly = 1) { fetcher.invoke() }
}
```

## Module Dependencies

**File**: `foundation/data-cache/build.gradle.kts`

```kotlin
// File: foundation/data-cache/build.gradle.kts:11-15
dependencies {
    implementation(projects.foundation.networking)

    testImplementation(projects.foundation.testing)
}
```

The module has minimal dependencies:
- **foundation.networking**: For shared networking types and patterns
- **foundation.testing**: Test utilities and helpers

## Key Benefits

1. **Simplicity**: Clean Store interface with minimal API surface
2. **Reactive**: Built-in Flow support for reactive programming
3. **Thread-Safe**: Mutex-protected fetch operations prevent race conditions
4. **Flexible Expiration**: Configurable time-based cache invalidation
5. **Optional Persistence**: Pluggable disk cache for data persistence
6. **Testable**: Dependency injection friendly with comprehensive test coverage

## Comparison with Traditional Caching

### Store Pattern vs Traditional Cache

```mermaid
graph TB
    subgraph "Store Pattern (Actual Implementation)"
        A1[Store Interface] --> B1[Single Responsibility]
        B1 --> C1[Reactive Flow]
        C1 --> D1[Thread-Safe by Design]
        D1 --> E1[Simple Configuration]
    end
    
    subgraph "Traditional Cache (NOT Used)"
        A2[Multiple Managers] --> B2[Complex API]
        B2 --> C2[Manual Synchronization]
        C2 --> D2[Complex Configuration]
        D2 --> E2[Memory/Disk/Multi-level]
    end
```

The Luzia codebase chose the Store pattern for its simplicity and reactive nature, avoiding the complexity of traditional multi-layered cache implementations.

## Best Practices

1. **Store Creation**: Use the builder pattern for configuration
2. **Expiration**: Set appropriate expiration times based on data volatility
3. **Flow Usage**: Prefer `flow()` for reactive UI updates
4. **Updates**: Use transformation functions for safe state updates
5. **Testing**: Mock time providers for deterministic expiration testing
6. **Thread Safety**: Rely on the built-in mutex for concurrent access

This Store-based caching approach provides a clean, reactive solution for managing cached data in the Luzia Android application while maintaining simplicity and testability.