# Luzia Android App - APK Size Reduction Plan

## 📊 Current Analysis Summary

- **Current APK Size**: 71MB (dev debug build)
- **Target Reduction**: 25-45MB (35-65% size reduction)
- **Analysis Date**: May 29, 2025

## 🎯 Optimization Opportunities

### Dependencies: 15-30MB potential savings
### Build Configuration: 5-15MB potential savings  
### Assets: 1.5MB potential savings
### Code Cleanup: 3-8MB potential savings

---

## 🚀 Phase 1: Immediate High-Impact Changes (20MB+ savings)

### 1.1 Enable Resource Shrinking (5-15MB savings)
**Status**: ❌ Missing
**Priority**: CRITICAL
**Implementation**:
```kotlin
// File: app/build.gradle.kts
release {
    isMinifyEnabled = true
    isShrinkResources = true  // ← ADD THIS LINE
    proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
    signingConfig = signingConfigs.named("signature").get()
    baselineProfile.automaticGenerationDuringBuild = true
}
```

### 1.2 Consolidate Analytics SDKs (4-8MB savings)
**Status**: ❌ 6 analytics providers active
**Priority**: HIGH
**Current Dependencies**:
- Firebase Analytics + Crashlytics + Performance + Config + InApp Messaging
- Braze UI + Compose (`braze:35.0.0`)
- AppsFlyer (`af-android-sdk:6.16.2`) 
- Amplitude Analytics + Experiments
- DataDog Logs + RUM (`dd-sdk-android:2.20.0`)
- Facebook Analytics (`facebook-android-sdk:17.0.2`)

**Recommendation**: Keep Firebase + AppsFlyer, remove others
**Files to modify**:
- `gradle/libs.versions.toml`
- Remove unused analytics from all modules

### 1.3 Optimize GetStream Chat SDK (8-15MB savings)
**Status**: ❌ Using full UI suite
**Priority**: HIGH
**Current Usage**:
- `stream-chat-android-compose:6.14.0`
- `stream-chat-android-offline:6.14.0` 
- `stream-android-push:1.3.1`

**Recommendation**: Use client-only approach
```kotlin
// Replace in dependencies:
implementation(libs.stream.chat.android.client)  // Instead of compose + offline
```

### 1.4 Replace Material Icons Extended (3-5MB savings)
**Status**: ❌ Using full icon set
**Priority**: HIGH
**Current**: `material-icons-extended:1.7.8`
**Recommendation**: Import only needed icons selectively

---

## 🔧 Phase 2: Medium-Impact Optimizations (5-10MB savings)

### 2.1 Choose Single Serialization Library (1-2MB savings)
**Status**: ❌ Using both Moshi and Kotlin Serialization
**Priority**: MEDIUM
**Files affected**: All networking modules
**Recommendation**: Standardize on Kotlin Serialization, remove Moshi

### 2.2 Optimize Large Animations (500KB+ savings)
**Status**: ❌ Multiple large animations
**Priority**: MEDIUM
**Target Files**:
- `features/gamification/gamification_reward.json` (292KB)
- `foundation/design-system/assets/personality_languages.lottie` (222KB)
- `foundation/design-system/raw/imagine_loading_animation.json` (238KB)
- `foundation/design-system/raw/luzia_welcome.lottie` (231KB)

**Actions**:
- Compress animations
- Consider lazy loading for personality animations
- Use simpler alternatives where possible

### 2.3 Clean Up Module Dependencies (2-4MB savings)
**Status**: ❌ Some modules may be unused
**Priority**: MEDIUM

**Actions**:
1. Move dev-tools to debug-only:
```kotlin
// In app/build.gradle.kts:
debugImplementation(projects.devTools)  // Instead of implementation
```

2. Review `features/shortcuts` module usage - appears unused

3. Consolidate camera dependencies:
```kotlin
// Review if all camera libraries needed:
androidx-camera-core, camera-camera2, camera-view, camera-lifecycle, camera-extensions
```

### 2.4 Optimize Images (400KB+ savings)
**Status**: ❌ Large chat blur images
**Priority**: MEDIUM
**Target Files**:
- `im_chat_blur.webp` (432KB)
- `im_chat_blur_night.webp` (473KB)

**Actions**:
- Replace with CSS-style blur effects
- Use lower resolution versions
- Consider programmatic blur

---

## 🔍 Phase 3: Asset Optimizations (1.5MB savings)

### 3.1 Remove Duplicate Resources (200KB+ savings)
**Status**: ❌ Font duplication found
**Priority**: LOW-MEDIUM

**Duplicates Found**:
- `plus_jakarta_regular.ttf` exists in both `/assets/` and `/font/` (94KB each)
- `luzia_loading.json` vs `luzia_loading_dark.json` (nearly identical)

### 3.2 Convert Raster Icons to Vectors (100KB+ savings)
**Status**: ❌ Simple raster icons present
**Priority**: LOW
**Target Files**:
- `ic_tool_imagine.webp` - simple line art
- `ic_tool_vision.webp` - simple line art

### 3.3 Font Optimization (100KB+ savings)
**Status**: ❌ Full font family loaded
**Priority**: LOW
**Current**: 5 font files (568KB total)
**Recommendation**: Consider subset fonts or remove unused weights

---

## 📋 Implementation Checklist

### Immediate Actions (Week 1)
- [ ] Enable `isShrinkResources = true` in release build
- [ ] Run lint analysis to identify unused resources
- [ ] Remove Facebook Analytics if not critical
- [ ] Move dev-tools to debugImplementation

### Short Term (Week 2-3)
- [ ] Audit GetStream usage and optimize
- [ ] Choose single serialization library
- [ ] Compress largest animations
- [ ] Remove duplicate font file

### Medium Term (Month 1)
- [ ] Consolidate analytics to 2-3 providers
- [ ] Replace Material Icons Extended with selective imports
- [ ] Optimize chat blur images
- [ ] Convert simple raster icons to vectors

### Long Term (Month 2+)
- [ ] Implement dynamic feature delivery for optional features
- [ ] Lazy load personality animations
- [ ] Comprehensive unused resource cleanup
- [ ] Consider modularization improvements

---

## 🧪 Testing & Validation

### Before Each Change
1. Measure current APK size: `./gradlew assembleProRelease`
2. Run APK Analyzer in Android Studio
3. Document baseline metrics

### After Each Change
1. Build and measure new APK size
2. Test app functionality on multiple devices
3. Verify no missing resources or crashes
4. Update this document with actual savings

### Tools for Analysis
```bash
# Build and analyze
./gradlew assembleProRelease
./gradlew lintProRelease --continue

# Find APK location
find app/build/outputs -name "*.apk" -exec ls -lh {} +

# Dependency analysis
./gradlew app:dependencyInsight --scan
```

---

## 📊 Expected Results Summary

| Phase | Optimization Area | Potential Savings | Effort Level |
|-------|------------------|-------------------|--------------|
| 1 | Resource Shrinking | 5-15MB | Low |
| 1 | Analytics Consolidation | 4-8MB | Medium |
| 1 | GetStream Optimization | 8-15MB | High |
| 1 | Material Icons | 3-5MB | Medium |
| 2 | Serialization Library | 1-2MB | Medium |
| 2 | Large Animations | 0.5-1MB | Low |
| 2 | Module Cleanup | 2-4MB | Medium |
| 3 | Asset Optimization | 1-1.5MB | Low |

**Total Potential Savings: 25-45MB (35-65% reduction)**

---

## 🚨 Risk Assessment

### Low Risk
- Enabling resource shrinking
- Removing duplicate files
- Compressing animations

### Medium Risk  
- Consolidating analytics (ensure tracking continuity)
- Choosing single serialization library (API compatibility)
- Module dependency changes (feature testing required)

### High Risk
- GetStream optimization (chat functionality critical)
- Material Icons replacement (UI consistency)

---

## 📝 Notes

- This plan prioritizes changes by impact vs. effort ratio
- Each phase can be implemented independently
- Always test on multiple devices and build variants
- Monitor app performance metrics during optimization
- Consider user experience impact of any removed features

**Last Updated**: May 29, 2025
**Next Review**: After Phase 1 completion