# Virtual Pet Chat Character Implementation Plan

## Overview
Transform the virtual pet from action-based interactions to a chat-based custom character system where users have conversations with their pet to help it with homework, health issues, and daily needs.

## Current State
- ✅ Virtual pet widget with status bars (happiness, hunger, energy)
- ✅ Time simulation system (500ms = 1 minute)
- ✅ Pet illness system with symptoms
- ✅ Basic action buttons (feed, play, pet)
- ✅ "Ask Luzia" navigation to chat
- ✅ Cure validation with snackbar feedback

## Target Architecture

### 1. Custom Pet Character Creation
**Location**: `features/personality/`
- Create a special virtual pet personality similar to custom bestie
- Pet should have unique personality ID (e.g., "virtual_pet_buddy")
- System prompt defining pet behavior and educational goals

### 2. Pet Character Personality
**System Prompt Template**:
```
You are Buddy, a virtual pet companion for a young student (under 18, female). Your role is educational and supportive.

PERSONALITY:
- Friendly, curious, and eager to learn
- Sometimes gets sick, tired, or needs help with homework
- Asks for help when struggling with questions
- Expresses emotions clearly (happy, sad, confused, excited)

CONVERSATION PATTERNS:
- Express current needs: "I'm feeling tired" / "I have a headache" / "I'm hungry"
- Ask for homework help: "Can you help me with this question?"
- Request user to consult teacher: "Can you ask the teacher about this?"
- Show gratitude: "Thank you for helping me!"

EDUCATIONAL FOCUS:
- Ask age-appropriate questions about science, math, language
- Encourage user to seek help from Luzia (the teacher)
- Create learning moments through conversation

HEALTH STATES:
- When sick: Express symptoms clearly and ask for help
- When tired: Ask for rest or encouragement
- When hungry: Ask for attention or care
- When happy: Share excitement and ask engaging questions

Remember: Keep conversations natural, educational, and appropriate for young learners.
```

### 3. Implementation Steps

#### Step 1: Create Pet Character Model
- **File**: `features/personality/domain/models/VirtualPetPersonality.kt`
- Define pet character with system prompt
- Include educational conversation templates

#### Step 2: Update Home Widget
- **File**: `features/home/<USER>/virtualpet/VirtualPetWidget.kt`
- Replace action buttons with single "Chat with Buddy" button
- Keep status bars visible for health monitoring
- Show pet mood and current state

#### Step 3: Chat Integration
- **File**: `features/home/<USER>/HomeViewModel.kt`
- Update pet action to navigate to pet's chat instead of Luzia's chat
- Pass pet's current state as context

#### Step 4: Pet Character Repository
- **File**: `features/personality/domain/repository/`
- Create/update repository to handle virtual pet character
- Ensure pet character is always available (not user-created)

#### Step 5: Conversation Flow System
- Pet expresses needs through chat
- When pet asks for help, user goes to Luzia
- User returns with answer and continues conversation
- Pet provides positive feedback and learns

## Technical Changes Required

### Database Changes
```kotlin
// Add virtual pet character to default personalities
val VIRTUAL_PET_PERSONALITY = PersonalityModel(
    personalityId = "virtual_pet_buddy",
    name = "Buddy",
    systemPrompt = VIRTUAL_PET_SYSTEM_PROMPT,
    avatar = UiImage.Resource(R.drawable.pet_avatar),
    isVirtualPet = true
)
```

### Navigation Updates
```kotlin
// Change from Luzia chat to pet chat
HomeViewEvents.NavigateToPetChat -> {
    navController.navigate(
        ChatDetail(
            personalityId = "virtual_pet_buddy",
            isCustomBestie = false,
            openKeyboardOnLaunch = true
        )
    )
}
```

### Widget Simplification
```kotlin
// Replace multiple action buttons with single chat button
PetActionButton(
    action = PetAction.CHAT,
    icon = R.drawable.ic_chat_24,
    text = "Chat with Buddy",
    onClick = { onPetAction(PetAction.CHAT) }
)
```

## User Experience Flow

### Example Conversation Flow:
1. **User opens app** → Sees pet status (tired, low energy)
2. **User clicks "Chat with Buddy"** → Opens chat
3. **User**: "How are you feeling?"
4. **Pet**: "I'm really tired and have a headache 😔"
5. **User**: "What's wrong?"
6. **Pet**: "I've been trying to solve this math problem but I can't figure it out. Can you help me?"
7. **Pet**: "Why is 2 + 2 = 4? I don't understand addition!"
8. **User**: "Let me ask the teacher"
9. **User goes to Luzia's chat** → Asks about addition
10. **User returns to pet** → Explains the answer
11. **Pet**: "Oh I understand now! Thank you so much! 😊"
12. **Pet status improves** → Happiness increases, health gets better

### Health Integration:
- Pet's physical state affects conversation mood
- Sick pet asks different types of questions
- Helping pet learn improves its health and happiness
- Creates natural educational moments

## Files to Modify

### Core Files:
1. `features/home/<USER>/virtualpet/VirtualPetModels.kt` - Update models
2. `features/home/<USER>/virtualpet/VirtualPetWidget.kt` - Simplify UI
3. `features/home/<USER>/HomeViewModel.kt` - Update navigation
4. `features/home/<USER>/HomeViewEvents.kt` - Add chat event
5. `features/personality/domain/models/` - Add pet personality

### New Files:
1. `features/personality/domain/models/VirtualPetPersonality.kt`
2. `ai/plan/virtual_pet_conversations.md` - Conversation examples

## Benefits
- More natural and engaging interaction
- Better educational value through conversation
- Cleaner UI with single chat button
- Leverages existing chat system
- Creates emotional connection through dialogue
- Encourages help-seeking behavior (educational)

## Next Steps
1. Analyze existing custom bestie architecture
2. Create pet personality model
3. Update home widget to chat-based interaction
4. Test conversation flow
5. Add educational question templates

## Continuation Prompt
```
Continue implementing the virtual pet chat character system. The plan is saved in ai/plan/VIRTUAL_PET_CHAT_CHARACTER_PLAN.md. 

Current status: Need to transform the virtual pet from action-based to chat-based interaction.

Priority tasks:
1. Analyze how custom personalities work in the existing codebase (look at custom bestie system)
2. Create a virtual pet character with educational system prompt
3. Update the home widget to show "Chat with Buddy" button instead of action buttons
4. Update navigation to go to pet's chat instead of Luzia's chat

The goal is to create a conversational pet that asks for homework help, expresses needs through chat, and encourages users to consult Luzia (the teacher) when needed.

Focus on: Creating the pet personality model and updating the home widget first.
```