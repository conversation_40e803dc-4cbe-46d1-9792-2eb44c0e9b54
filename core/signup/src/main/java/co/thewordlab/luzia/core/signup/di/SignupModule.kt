package co.thewordlab.luzia.core.signup.di

import co.thewordlab.luzia.core.signup.data.api.GoogleApi
import co.thewordlab.luzia.foundation.networking.di.GoogleHost
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit

@Module
@InstallIn(SingletonComponent::class)
object SignupModule {

    @Provides
    fun provideGoogleApi(
        @GoogleHost retrofit: Retrofit
    ): GoogleApi {
        return retrofit.create(GoogleApi::class.java)
    }
}
