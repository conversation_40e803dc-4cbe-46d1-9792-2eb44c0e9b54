package co.thewordlab.luzia.core.signup.web

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import androidx.core.net.toUri
import androidx.credentials.GetCredentialRequest
import co.thewordlab.luzia.core.signup.BuildConfig
import co.thewordlab.luzia.core.signup.data.api.GoogleApi
import co.thewordlab.luzia.core.signup.data.model.GoogleUserDto
import co.thewordlab.luzia.core.signup.handler.AuthResult
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.UserIdentification
import co.thewordlab.luzia.foundation.securelib.SecureKey
import co.thewordlab.luzia.foundation.securelib.SecureStorage
import com.google.android.libraries.identity.googleid.GetGoogleIdOption
import com.slack.eithernet.ApiResult
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.suspendCancellableCoroutine
import net.openid.appauth.AuthState
import net.openid.appauth.AuthorizationException
import net.openid.appauth.AuthorizationRequest
import net.openid.appauth.AuthorizationResponse
import net.openid.appauth.AuthorizationService
import net.openid.appauth.AuthorizationServiceConfiguration
import net.openid.appauth.ResponseTypeValues
import javax.inject.Inject
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

class GoogleWebAuthManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val secureStorage: SecureStorage,
    private val googleApi: GoogleApi,
    private val analytics: Analytics
) {

    private val authService = AuthorizationService(context)

    fun getNativeSignInRequest(filterByAuthorizedAccounts: Boolean): GetCredentialRequest {
        val googleIdOption: GetGoogleIdOption = GetGoogleIdOption.Builder()
            .setFilterByAuthorizedAccounts(filterByAuthorizedAccounts)
            .setServerClientId(secureStorage.get(SecureKey.KEY_GOOGLE_WEB_CLIENT_ID))
            .build()
        return GetCredentialRequest.Builder()
            .addCredentialOption(googleIdOption)
            .build()
    }

    suspend fun createCustomTabsIntent(): Intent {
        return suspendCancellableCoroutine { continuation ->
            AuthorizationServiceConfiguration.fetchFromUrl(AUTH_CONFIG_URL.toUri()) { config, ex ->
                if (config != null) {
                    val request = AuthorizationRequest.Builder(
                        config,
                        getClientId(),
                        ResponseTypeValues.CODE,
                        getRedirectUrl()
                    )
                        .setNonce(null)
                        .setScope(SCOPES)
                        .setCodeVerifier(null, null, null)
                        .build()
                    val result = runCatching { authService.getAuthorizationRequestIntent(request) }
                    result.getOrNull()?.let {
                        if (continuation.isActive) { continuation.resume(it) }
                    } ?: run {
                        if (continuation.isActive) {
                            continuation.resumeWithException(
                                result.exceptionOrNull() ?: GoogleWebErrors.BrowserNotFound(null)
                            )
                        }
                    }
                } else {
                    if (continuation.isActive) {
                        continuation.resumeWithException(GoogleWebErrors.ConfigFetchFailed(ex))
                    }
                }
            }
        }
    }

    suspend fun processData(data: Intent): AuthResult {
        val authState = obtainAuthState(data)
        val profile: GoogleUserDto? = extractProfileInformation(authState)
        return AuthResult(authState.idToken!!, profile?.name, profile?.picture, profile?.email)
    }

    fun logBrowserInformation() {
        val defaultBrowser = getDefaultBrowserPackage()
        analytics.setUserIdentification(UserIdentification(defaultBrowser = defaultBrowser))
    }

    private suspend fun obtainAuthState(data: Intent): AuthState {
        return suspendCancellableCoroutine { continuation ->
            val response = AuthorizationResponse.fromIntent(data)
            val ex = AuthorizationException.fromIntent(data)
            val authState = AuthState(response, ex)
            if (response != null) {
                val tokenRequest = response.createTokenExchangeRequest()
                authService.performTokenRequest(tokenRequest) { tokenResponse, tokenEx ->
                    authState.update(tokenResponse, tokenEx)
                    val idToken = authState.idToken
                    if (idToken != null) {
                        if (continuation.isActive) {
                            continuation.resume(authState)
                        }
                    } else {
                        continuation.resumeWithException(GoogleWebErrors.TokenFailed(tokenEx))
                    }
                }
            } else {
                continuation.resumeWithException(GoogleWebErrors.UserCancelled(ex))
            }
        }
    }

    private suspend fun extractProfileInformation(authState: AuthState): GoogleUserDto? {
        val accessToken = authState.accessToken ?: return null
        val result = googleApi.getUserInfo("Bearer $accessToken")
        return (result as? ApiResult.Success)?.value
    }

    private fun getPrefix(): String {
        val key = if (BuildConfig.DEBUG) {
            SecureKey.KEY_GOOGLE_CLIENT_PREFIX_DEBUG
        } else {
            SecureKey.KEY_GOOGLE_CLIENT_PREFIX
        }
        return secureStorage.get(key)
    }

    private fun getClientId(): String {
        val prefix = getPrefix()
        return "$prefix.apps.googleusercontent.com"
    }

    private fun getRedirectUrl(): Uri {
        val prefix = getPrefix()
        return Uri.parse("com.googleusercontent.apps.$prefix:/oauth2redirect")
    }

    private fun getDefaultBrowserPackage(): String {
        var browser = BROWSER_NOT_FOUND
        runCatching {
            val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse("http://"))
            val packageManager = context.packageManager
            val resolveInfo =
                packageManager.resolveActivity(browserIntent, PackageManager.MATCH_DEFAULT_ONLY)
            if (resolveInfo != null) {
                val componentName = ComponentName(
                    resolveInfo.activityInfo.packageName,
                    resolveInfo.activityInfo.name
                )
                browser = componentName.packageName
            }
        }
        return browser
    }

    private companion object {
        const val AUTH_CONFIG_URL = "https://accounts.google.com/.well-known/openid-configuration"
        const val SCOPES = "openid profile email"
        const val BROWSER_NOT_FOUND = "not_found"
    }
}
