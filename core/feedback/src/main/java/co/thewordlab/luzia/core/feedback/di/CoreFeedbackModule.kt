package co.thewordlab.luzia.core.feedback.di

import co.thewordlab.luzia.core.feedback.data.FeedbackApi
import co.thewordlab.luzia.core.feedback.data.FeedbackRepositoryImp
import co.thewordlab.luzia.core.feedback.domain.FeedbackRepository
import co.thewordlab.luzia.foundation.networking.di.BaseHost
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit

@Module
@InstallIn(SingletonComponent::class)
object CoreFeedbackModule {

    @Provides
    fun provideFeedbackApi(@BaseHost retrofit: Retrofit): FeedbackApi =
        retrofit.create(FeedbackApi::class.java)

    @Provides
    fun provideFeedbackRepository(impl: FeedbackRepositoryImp): FeedbackRepository = impl
}
