package co.thewordlab.luzia.core.feedback.presentation

import co.thewordlab.luzia.foundation.analytics.AnalyticsActions
import co.thewordlab.luzia.foundation.analytics.AnalyticsEvents

data object FeedbackDisplayed : AnalyticsEvents("feedback_displayed")

data object FeedbackDismiss : AnalyticsActions("feedback_closed")
data class FeedbackBravo(val props: Map<String, Any> = emptyMap()) : AnalyticsActions("feedback_bravo", props)
data class FeedbackNegative(val props: Map<String, Any> = emptyMap()) : AnalyticsActions("feedback_negative", props)

data class ActionSelectThumbsUp(val source: String) : AnalyticsActions(
    "select_thumbs_up",
    mapOf(
        "source" to source,
        "type" to "message"
    )
)

data class ActionSelectThumbsDown(val source: String) : AnalyticsActions(
    "select_thumbs_down",
    mapOf(
        "source" to source,
        "type" to "message"
    )
)
