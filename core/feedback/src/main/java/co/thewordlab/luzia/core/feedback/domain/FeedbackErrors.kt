package co.thewordlab.luzia.core.feedback.domain

import co.thewordlab.luzia.foundation.networking.model.CommonErrors
import co.thewordlab.luzia.foundation.networking.model.Error

sealed class FeedbackErrors(val description: String) : Error {

    data object UserFeedbackError : FeedbackErrors("Error sending the user feedback")
    data class CommonError(val error: CommonErrors) : FeedbackErrors(error.description)
}
