package co.thewordlab.luzia.core.feedback.data.models

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class FeedbackRequest(
    @<PERSON>son(name = "buttonId")
    val buttonId: String,
    @<PERSON><PERSON>(name = "messageId")
    val messageId: String,
    @<PERSON><PERSON>(name = "requestType")
    val requestType: String,
    @<PERSON><PERSON>(name = "source")
    val source: String,
    @Json(name = "value")
    val value: String,
    @<PERSON><PERSON>(name = "groupId")
    val groupId: String? = null
)
