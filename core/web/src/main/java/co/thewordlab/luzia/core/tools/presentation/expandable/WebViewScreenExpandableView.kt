package co.thewordlab.luzia.core.tools.presentation.expandable

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.CircleButtonType
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.LuziaCircleButton
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR

@Composable
fun WebViewScreenExpandable(
    modifier: Modifier = Modifier,
    htmlContent: String? = null,
    url: String? = null,
    baseUrl: String? = null,
    enableScroll: Boolean = true,
    enableFullScreen: Boolean = false
) {
    var isFullScreen by remember { mutableStateOf(false) }
    val currentHtmlContent by remember(htmlContent, isFullScreen) {
        mutableStateOf(htmlContent)
    }
    val currentUrl by remember(url, isFullScreen) { mutableStateOf(url) }
    val currentBaseUrl by remember(baseUrl, isFullScreen) { mutableStateOf(baseUrl) }

    Box(modifier = modifier.background(LuziaTheme.palette.surface.content)) {
        if (isFullScreen) {
            Dialog(
                onDismissRequest = { isFullScreen = false },
                properties = DialogProperties(
                    usePlatformDefaultWidth = false,
                    dismissOnClickOutside = true
                )
            ) {
                Surface(color = LuziaTheme.palette.surface.content) {
                    Box(modifier = Modifier.fillMaxSize()) {
                        CommonWebView(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(Spacing.X24.dp),
                            htmlContent = currentHtmlContent,
                            url = currentUrl,
                            baseUrl = currentBaseUrl,
                            captureBackPresses = true,
                            enableScroll = enableScroll
                        )
                        LuziaCircleButton(
                            modifier = Modifier
                                .padding(Spacing.X16.dp)
                                .size(IconSizes.X32.dp)
                                .align(Alignment.BottomEnd)
                                .clip(CircleShape)
                                .background(LuziaTheme.palette.surface.background),
                            onClick = { isFullScreen = false },
                            type = CircleButtonType.Contrast(painterResource(designR.drawable.ic_close))
                        )
                    }
                }
            }
        } else {
            CommonWebView(
                modifier = Modifier.fillMaxSize(),
                htmlContent = currentHtmlContent,
                url = currentUrl,
                baseUrl = currentBaseUrl,
                captureBackPresses = true,
                enableScroll = enableScroll
            )
            if (enableFullScreen) {
                LuziaCircleButton(
                    modifier = Modifier
                        .size(IconSizes.X32.dp)
                        .align(Alignment.BottomEnd)
                        .clip(CircleShape)
                        .background(LuziaTheme.palette.surface.background),
                    onClick = { isFullScreen = true },
                    type = CircleButtonType.Contrast(painterResource(designR.drawable.ic_expand_content_24))
                )
            }
        }
    }
}
