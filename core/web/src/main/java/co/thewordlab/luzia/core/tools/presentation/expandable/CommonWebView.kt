package co.thewordlab.luzia.core.tools.presentation.expandable

import android.annotation.SuppressLint
import android.view.ViewGroup
import android.webkit.CookieManager
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.compose.BackHandler
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.viewinterop.AndroidView
import co.thewordlab.luzia.foundation.common.extensions.toHex
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme

private const val LUZIA_URL = "https://luzia.com"

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun CommonWebView(
    modifier: Modifier = Modifier,
    htmlContent: String? = null,
    url: String? = null,
    baseUrl: String? = null,
    onWebViewCreated: (WebView) -> Unit = {},
    webViewClient: WebViewClient = remember { WebViewClient() },
    webChromeClient: WebChromeClient = remember { WebChromeClient() },
    enableJavaScript: Boolean = true,
    enableDomStorage: Boolean = true,
    enableThirdPartyCookies: Boolean = true,
    captureBackPresses: Boolean = true,
    enableScroll: Boolean = true,
) {
    val effectiveBaseUrl = baseUrl ?: LUZIA_URL
    var webViewInstance by remember { mutableStateOf<WebView?>(null) }
    val backgroundColor = LuziaTheme.palette.surface.content.toArgb()
    val htmlContentWithBaseStyles =
        htmlContent?.let { addBaseStylesToHtmlContent(it, backgroundColor.toHex(), disableScroll = !enableScroll) }

    if (captureBackPresses) {
        BackHandler(enabled = webViewInstance?.canGoBack() == true) {
            webViewInstance?.goBack()
        }
    }

    AndroidView(
        factory = { context ->
            WebView(context).apply {
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )

                this.webViewClient = webViewClient
                this.webChromeClient = webChromeClient
                settings.javaScriptEnabled = enableJavaScript
                settings.domStorageEnabled = enableDomStorage
                settings.allowContentAccess = true
                settings.allowFileAccess = true
                isVerticalScrollBarEnabled = false
                isHorizontalScrollBarEnabled = false
                settings.useWideViewPort = true
                settings.loadWithOverviewMode = true
                settings.setSupportZoom(true)
                settings.builtInZoomControls = true
                settings.displayZoomControls = false

                if (enableThirdPartyCookies) {
                    CookieManager.getInstance().setAcceptThirdPartyCookies(this, true)
                }
                setBackgroundColor(backgroundColor)
                onWebViewCreated(this)
                webViewInstance = this
            }
        },
        update = { webView ->
            if (webView.webViewClient != webViewClient) {
                webView.webViewClient = webViewClient
            }
            if (webView.webChromeClient != webChromeClient) {
                webView.webChromeClient = webChromeClient
            }

            val currentUrlLoaded = webView.url
            if (htmlContentWithBaseStyles != null) {
                webView.loadDataWithBaseURL(
                    effectiveBaseUrl,
                    htmlContentWithBaseStyles,
                    "text/html",
                    "UTF-8",
                    null
                )
            } else if (url != null && currentUrlLoaded != url) {
                webView.loadUrl(url)
            }
            webViewInstance = webView
        },
        modifier = modifier
    )

    DisposableEffect(Unit) {
        onDispose {
            webViewInstance?.destroy()
            webViewInstance = null
        }
    }
}

@Suppress("LongMethod", "MagicNumber")
@Composable
private fun addBaseStylesToHtmlContent(
    htmlContent: String,
    background: String,
    disableScroll: Boolean = true,
    textColor: String = LuziaTheme.palette.text.primary.toArgb().toHex(),
    highlightColor: String = LuziaTheme.palette.accents.yellow.yellow30.toArgb().toHex()
) =
    """
       <!DOCTYPE html>
       <html>
       <head>
         <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no">
         <style>
           @font-face {
             font-family: 'Plus Jakarta Regular';
             src: url(file:///android_asset/plus_jakarta_regular.ttf);
             font-weight: 400;
             font-style: normal
           }

           body {
             margin: 0;
             padding: 0;
             font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
             font-size: 17px;
             color: $textColor;
             background-color: $background;
             ${if (disableScroll) "overflow: hidden;" else ""}
             line-height: 1.2;
             word-wrap: break-word;
             overflow-wrap: break-word;
           }
           
           mark {
             background-color: $highlightColor;
             color: $textColor
           }
           
           /* Markdown elements styling */
           h1, h2, h3, h4, h5, h6 {
             color: inherit;
             margin: 1em 0 0.5em 0;
             font-weight: 600;
             line-height: 1.2;
             font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
           }
            
           h1 { font-size: 1.8em; font-weight: 700; }
           h2 { font-size: 1.5em; font-weight: 700; }
           h3 { font-size: 1.3em; font-weight: 600; }
           h4 { font-size: 1.1em; font-weight: 600; }
           h5 { font-size: 1em; font-weight: 600; }
           h6 { font-size: 0.9em; font-weight: 600; }
           
           p {
             margin: 0.8em 0;
             line-height: 1.2;
             font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
           }
           
           strong, b {
             font-weight: 600;
             font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
           }
           
           em, i {
             font-style: italic;
             font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
           }
           
           /* Code blocks */
           pre {
             padding: 12px;
             border-radius: 6px;
             font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
             font-size: 14px;
             background-color: rgba(128, 128, 128, 0.1);
             overflow-x: auto;
             white-space: pre-wrap;
             margin: 1em 0;
             line-height: 1.2;
           }
           
           code {
             font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
             font-size: 0.9em;
             background-color: rgba(128, 128, 128, 0.1);
             padding: 2px 4px;
             border-radius: 3px;
           }
           
           /* Lists */
           ul, ol {
             margin: 0.8em 0;
             padding-left: 1.5em;
             font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
           }
           
           li {
             margin: 0.3em 0;
             font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
           }
           
           /* Blockquotes */
           blockquote {
             margin: 1em 0;
             padding: 0 1em;
             border-left: 3px solid rgba(128, 128, 128, 0.3);
             color: $textColor;
             font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
             font-style: italic;
           }
           
           /* Links */
           a {
             color: #007AFF;
             text-decoration: none;
             font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
           }
           
           a:hover {
             text-decoration: underline;
           }
           
           /* Tables */
           table {
             border-collapse: collapse;
             width: 100%;
             margin: 1em 0;
             font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
           }
           
           th, td {
             border: 1px solid rgba(128, 128, 128, 0.3);
             padding: 8px 12px;
             text-align: left;
             font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
           }
           
           th {
             background-color: rgba(128, 128, 128, 0.1);
             font-weight: 600;
           }
           
           /* Responsive adjustments */
           @media (max-width: 480px) {
             body {
               font-size: 16px;
               padding: 6px;
             }
             
             h1 { font-size: 1.6em; }
             h2 { font-size: 1.4em; }
             h3 { font-size: 1.2em; }
           }
         </style>
       </head>
       <body style="padding:0${if (disableScroll) "; overflow: hidden" else ""}">
         <div id="content" style="padding:0">
           <p>$htmlContent</p>
         </div>
       </body>
    </html>
    """.trimIndent()
