package co.thewordlab.luzia.core.tools.presentation

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.net.Uri
import android.util.Log
import android.view.ViewGroup
import android.webkit.ConsoleMessage
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebChromeClient.FileChooserParams.parseResult
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Toast
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.viewinterop.AndroidView
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.tools.presentation.bridge.AndroidBridgeInterface
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.theworldlab.luzia.foundation.design.system.components.navbar.NavBarMainView
import co.theworldlab.luzia.foundation.design.system.legacy.composables.Loading
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.PermissionState
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import com.google.accompanist.permissions.shouldShowRationale

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun WebViewScreen(title: String?, url: String, onClose: () -> Unit) {
    val context = LocalContext.current
    val webViewViewModel: WebViewViewModel = hiltViewModel()
    val viewState by webViewViewModel.viewState.collectAsStateWithLifecycle()
    val filePermissionState = rememberPermissionState(permission = Manifest.permission.WRITE_EXTERNAL_STORAGE)
    OnCreate("WebViewScreen") {
        webViewViewModel.onViewAction(WebViewViewActions.OnLoadUrl(url, title))
    }
    FilePermissionHandler(filePermissionState, viewState, webViewViewModel::onViewAction)
    ViewModelEventEffect(events = webViewViewModel) {
        when (it) {
            WebViewViewEvents.Close -> onClose()
            WebViewViewEvents.AskForPermission -> filePermissionState.launchPermissionRequest()
            is WebViewViewEvents.ShowMessage -> {
                val text = context.getString(it.textId)
                Toast.makeText(context, text, Toast.LENGTH_SHORT).show()
            }
        }
    }
    WebViewScreenContent(viewState, webViewViewModel::onViewAction)
}

@Composable
private fun WebViewScreenContent(viewState: WebViewViewState, onWebViewActions: (WebViewViewActions) -> Unit) {
    var fileProviderCallback: ValueCallback<Array<Uri>>? by remember {
        mutableStateOf(null)
    }
    val url = rememberUpdatedState(newValue = viewState.url)
    val fileProviderLauncher =
        rememberLauncherForActivityResult(contract = ActivityResultContracts.StartActivityForResult()) {
            if (it.resultCode == Activity.RESULT_CANCELED) {
                fileProviderCallback?.onReceiveValue(null)
            } else if (it.resultCode == Activity.RESULT_OK) {
                fileProviderCallback?.onReceiveValue(parseResult(it.resultCode, it.data))
            }
            fileProviderCallback = null
        }
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            NavBarMainView(
                title = viewState.title.orEmpty(),
                image = null,
                onLeftIconClicked = { onWebViewActions(WebViewViewActions.OnClose) }
            )
        }
    ) { paddingValues ->
        AndroidView(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            factory = {
                WebView(it).apply {
                    setupLayout()
                    setupSettings()
                    setupBridge(onWebViewActions)
                    setupChromeClient(fileProviderLauncher) { callback -> fileProviderCallback = callback }
                    setupWebClient(onWebViewActions = onWebViewActions)
                }
            },
            update = {
                it.loadUrl(url.value)
            }
        )
        if (viewState.loading) {
            Loading(
                modifier = Modifier.padding(paddingValues)
            )
        }
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
private fun FilePermissionHandler(
    filePermissionState: PermissionState,
    viewState: WebViewViewState,
    onWebViewActions: (WebViewViewActions) -> Unit
) {
    LaunchedEffect(filePermissionState.status, viewState.permissionPendingBase64) {
        viewState.permissionPendingBase64?.let { base64 ->
            when {
                filePermissionState.status.isGranted ->
                    onWebViewActions(WebViewViewActions.OnDownloadImage(base64))

                filePermissionState.status.shouldShowRationale ->
                    onWebViewActions(WebViewViewActions.OnShowPermissionRationale)
            }
        }
    }
}

private fun WebView.setupLayout() {
    layoutParams =
        ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
}

private fun WebView.setupBridge(onWebViewActions: (WebViewViewActions) -> Unit) {
    val bridge = AndroidBridgeInterface(onAction = onWebViewActions)
    addJavascriptInterface(bridge, AndroidBridgeInterface.NAME)
}

@SuppressLint("SetJavaScriptEnabled")
private fun WebView.setupSettings() {
    settings.run {
        javaScriptEnabled = true
        domStorageEnabled = true
        allowFileAccess = true
    }
}

private fun WebView.setupChromeClient(
    fileProviderLauncher: ManagedActivityResultLauncher<Intent, ActivityResult>,
    onFilePathCallback: (ValueCallback<Array<Uri>>?) -> Unit
) {
    webChromeClient = object : WebChromeClient() {
        override fun onShowFileChooser(
            webView: WebView?,
            filePathCallback: ValueCallback<Array<Uri>>?,
            fileChooserParams: FileChooserParams?
        ): Boolean {
            val intent = fileChooserParams?.createIntent()
            if (intent != null) {
                onFilePathCallback(filePathCallback)
                fileProviderLauncher.launch(intent)
            }
            return true
        }

        override fun onConsoleMessage(consoleMessage: ConsoleMessage?): Boolean {
            Log.d("WebView", consoleMessage?.message().orEmpty())
            return super.onConsoleMessage(consoleMessage)
        }
    }
}

private fun WebView.setupWebClient(onWebViewActions: (WebViewViewActions) -> Unit) {
    webViewClient =
        object : WebViewClient() {
            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                onWebViewActions(WebViewViewActions.OnLoading(true))
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                onWebViewActions(WebViewViewActions.OnLoading(false))
            }

            override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                val url = request?.url?.toString().orEmpty()
                if (handleInternalIntents(view, url, onWebViewActions)) {
                    return true
                }
                return super.shouldOverrideUrlLoading(view, request)
            }
        }
}

private fun handleInternalIntents(
    view: WebView?,
    url: String,
    onWebViewActions: (WebViewViewActions) -> Unit
): Boolean {
    val cleanUrl = url.replace(oldValue = "snssdk1233", newValue = "intent")
    if (!cleanUrl.startsWith("intent://") && !cleanUrl.startsWith("market://")) return false
    return try {
        val context: Context? = view?.context
        val intent = Intent.parseUri(cleanUrl, Intent.URI_INTENT_SCHEME)
        intent?.let {
            view?.stopLoading()
            val packageManager: PackageManager? = context?.packageManager
            val info =
                packageManager?.resolveActivity(
                    intent,
                    PackageManager.MATCH_DEFAULT_ONLY
                )
            if (info != null) {
                onWebViewActions(WebViewViewActions.OnClose)
                context.startActivity(intent)
            } else {
                val fallbackUrl = intent.getStringExtra("browser_fallback_url")
                view?.loadUrl(fallbackUrl!!)
            }
            true
        } ?: false
    } catch (expected: Exception) {
        Log.e("WebView", "Can't resolve intent: $url", expected)
        false
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        WebViewScreenContent(
            viewState = WebViewViewState(
                title = "Title",
                url = "https://www.google.com",
                loading = false
            ),
            onWebViewActions = {}
        )
    }
}
