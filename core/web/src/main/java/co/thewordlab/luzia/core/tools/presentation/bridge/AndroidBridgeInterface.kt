package co.thewordlab.luzia.core.tools.presentation.bridge

import android.webkit.JavascriptInterface
import co.thewordlab.luzia.core.tools.presentation.WebViewViewActions

class AndroidBridgeInterface(val onAction: (WebViewViewActions) -> Unit) {

    @JavascriptInterface
    fun shareImage(base64Image: String) {
        onAction(WebViewViewActions.OnShareImage(base64Image))
    }

    @JavascriptInterface
    fun downloadImage(base64Image: String) {
        onAction(WebViewViewActions.OnDownloadImage(base64Image))
    }

    companion object {
        const val NAME = "AndroidBridge"
    }
}
