plugins {
    alias(libs.plugins.luzia.android.feature)
}

android {
    namespace = "co.thewordlab.luzia.core.chat"
    
    testOptions {
        unitTests {
            isIncludeAndroidResources = true
        }
    }
}

dependencies {
    implementation(libs.androidx.dataStore.core)
    implementation(libs.androidx.dataStore.preferences)
    implementation(projects.core.feedback)
    implementation(projects.core.navigation)
    implementation(projects.core.profile)
    implementation(projects.core.tools)
    implementation(projects.core.connectivity)
    implementation(projects.foundation.analytics)
    implementation(projects.foundation.designSystem)
    implementation(projects.foundation.messages)
    implementation(projects.core.ads)

    testImplementation(projects.foundation.testing)
}