<?xml version="1.0" ?>
<SmellBaseline>
  <ManuallySuppressedIssues/>
  <CurrentIssues>
    <ID>CyclomaticComplexMethod:ChatListViewModel.kt$ChatListViewModel$override fun onViewAction(action: ChatListViewActions)</ID>
    <ID>TooManyFunctions:MessagesInChatHandler.kt$MessagesInChatHandler</ID>
    <ID>LongMethod:MessageEntityMapper.kt$MessageEntityMapper$@Suppress("LongParameterList") private fun MessageEntity.asModel( query: String, onResend: () -&gt; Unit, enableMessagingActions: Boolean, enableFavoriteActions: <PERSON>olean, enableTextToSpeech: Boolean, highlightId: Long?, actions: MessageViewActions, onViewActions: (ChatListViewActions) -&gt; Unit, ): MessageModel</ID>
  </CurrentIssues>
</SmellBaseline>
