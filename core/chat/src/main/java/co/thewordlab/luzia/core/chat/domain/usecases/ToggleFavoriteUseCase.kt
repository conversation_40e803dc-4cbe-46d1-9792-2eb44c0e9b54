package co.thewordlab.luzia.core.chat.domain.usecases

import co.thewordlab.luzia.foundation.messages.domain.repository.ChatRepository
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageModel
import javax.inject.Inject

class ToggleFavoriteUseCase @Inject constructor(
    private val chatRepository: ChatRepository,
) {

    suspend operator fun invoke(model: MessageModel) {
        chatRepository.toggleFavorite(model)
    }
}
