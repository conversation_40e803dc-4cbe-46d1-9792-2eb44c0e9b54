package co.thewordlab.luzia.core.chat.presentation.files

import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageModel
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageViewActions
import co.theworldlab.luzia.foundation.design.system.legacy.composables.BaseContextualMenuOptions
import co.theworldlab.luzia.foundation.design.system.legacy.composables.LuziaContextualMenu
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun MessageFileView(
    modifier: Modifier,
    model: MessageModel.File,
    actions: MessageViewActions,
) {
    var contextMenuPosition: DpOffset? by remember { mutableStateOf(null) }
    val density = LocalDensity.current
    val itemHeight = remember { mutableStateOf(0.dp) }

    val fileState = when {
        model.containerData.error != null -> FileViewState.Error(
            model.fileName,
            model.fileType,
            model.containerData.timestamp
        )

        model.containerData.isLoading -> FileViewState.Loading(model.fileName, model.fileType)
        else -> FileViewState.Uploaded(
            model.fileName,
            model.fileType,
            model.containerData.timestamp
        )
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = Spacing.X40.dp)
            .padding(vertical = Spacing.X14.dp)
    ) {
        FileContentView(
            modifier = modifier
                .onSizeChanged { itemHeight.value = with(density) { it.height.toDp() } }
                .pointerInput(Unit) {
                    detectTapGestures(
                        onLongPress = { contextMenuPosition = DpOffset(it.x.toDp(), it.y.toDp()) }
                    )
                },
            state = fileState
        )
    }

    contextMenuPosition?.let {
        LuziaContextualMenu(
            isVisible = true,
            pressOffset = it,
            itemHeight = itemHeight.value,
            onDismiss = { contextMenuPosition = null },
            dropDownItemList =
            BaseContextualMenuOptions.messageOnlyDeleteDropDownList(
                text = "",
                onDismiss = { contextMenuPosition = null },
                enableMessagingActions = model.capabilities.enableMessagingActions,
                onTextDeleted = { actions.onTextDeleted(model) }
            )
        )
    }
}
