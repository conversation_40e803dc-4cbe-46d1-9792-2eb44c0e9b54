package co.thewordlab.luzia.core.chat.domain.models.proactive

import co.thewordlab.luzia.core.chat.presentation.list.ChatListViewActions
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageViewActions
import co.theworldlab.luzia.foundation.design.system.components.message.proactive.ButtonsUiModel
import co.theworldlab.luzia.foundation.design.system.components.message.proactive.ProActiveMessageUiModel
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import javax.inject.Inject

class ProactiveMessageEntityMapper @Inject constructor() {

    @Suppress("ReturnCount")
    fun mapPayloadToModel(
        payload: String?,
        onViewActions: (ChatListViewActions) -> Unit,
        actions: MessageViewActions,
    ): List<ProActiveMessageUiModel> {
        val data = buildProactiveEntity(payload)
        val messages: MutableList<ProActiveMessageUiModel> = mutableListOf()
        onViewActions(ChatListViewActions.OnProactiveMessageImpression(buildDefaultParams(data)))
        data?.messages?.map { message ->
            when (message.type) {
                ProactiveMessageItemType.TEXT -> messages.add(
                    ProActiveMessageUiModel.Text(
                        title = message.text,
                        onLinkClicked = actions.onLinkClicked
                    )
                )

                ProactiveMessageItemType.MEDIA -> messages.add(
                    ProActiveMessageUiModel.Sticker(message.mediaUrl)
                )

                ProactiveMessageItemType.BUTTON ->
                    messages.add(
                        ProActiveMessageUiModel.Buttons(
                            title = message.content,
                            onLinkClicked = actions.onLinkClicked,
                            buttons = message.buttons.map {
                                ButtonsUiModel(it.title) {
                                    val param = buildButtonParams(data, it.id)
                                    onViewActions(
                                        ChatListViewActions.OnProactiveMessageActionClicked(param)
                                    )
                                    onViewActions(ChatListViewActions.SendTextMessage(it.action.getInjectedMessage()))
                                }
                            }
                        )
                    )

                ProactiveMessageItemType.BUTTON_CTA_URL -> {
                    messages.addAll(
                        message.buttons.map { button ->
                            ProActiveMessageUiModel.Link(
                                title = message.content,
                                onLinkClicked = actions.onLinkClicked,
                                buttonText = button.title,
                                buttonAction = {
                                    val param = buildButtonParams(data, button.id)
                                    onViewActions(
                                        ChatListViewActions.OnProactiveMessageActionClicked(param)
                                    )
                                    actions.onLinkClicked(button.action.getDestination())
                                }
                            )
                        }
                    )
                }

                else -> DO_NOTHING
            }
        }

        return messages
    }

    fun buildProactiveEntity(payload: String?): ProactiveMessages? {
        return payload?.let {
            val moshi: Moshi = Moshi.Builder()
                .add(KotlinJsonAdapterFactory())
                .build()
            moshi.adapter(ProactiveMessages::class.java).fromJson(it)
        }
    }

    fun buildButtonParams(message: ProactiveMessages?): Map<Parameter, String> = buildButtonParams(
        message = message,
        id = message?.messages?.firstOrNull()?.buttons?.firstOrNull()?.id.orEmpty()
    )

    fun buildDefaultParams(message: ProactiveMessages?): Map<Parameter, String> {
        val params = mutableMapOf<Parameter, String>()
        message?.let {
            params[Parameter.MessageId] = it.messageId
            params[Parameter.CampaignId] = it.groupId.orEmpty()
            params[Parameter.RequestType] = it.type.value
            params[Parameter.Component] = it.messages.firstOrNull()?.type?.value.orEmpty()
            params[Parameter.Location] = it.destination
        }
        return params
    }

    private fun buildButtonParams(message: ProactiveMessages?, id: String): Map<Parameter, String> =
        message?.let {
            val params = buildDefaultParams(it).toMutableMap()
            params[Parameter.ButtonId] = id
            params
        } ?: emptyMap()
}
