package co.thewordlab.luzia.core.chat.presentation.popup

import android.view.Gravity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BasicAlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.DialogWindowProvider
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonFilled
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun ChatPopupsView(
    viewModel: ChatPopupsViewModel,
    onNavigateToSignup: () -> Unit,
    onNavigateToProfileFill: () -> Unit
) {
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    OnCreate("ChatPopupsView") {
        viewModel.onViewAction(ChatPopupsViewActions.OnCreate)
    }
    ViewModelEventEffect(viewModel) {
        when (it) {
            ChatPopupsViewEvents.NavigateToProfileFill -> onNavigateToProfileFill()
            ChatPopupsViewEvents.NavigateToSignup -> onNavigateToSignup()
        }
    }
    ChatPopupsContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@Composable
private fun ChatPopupsContent(
    viewState: ChatPopupsViewState,
    onViewActions: (ChatPopupsViewActions) -> Unit
) {
    viewState.type?.let {
        when (it) {
            ChatPopupType.PROFILE_FILL -> {
                PopupCommonView(
                    title = stringResource(localizationR.string.connect_with_friends_title),
                    description = stringResource(localizationR.string.connect_with_friends_desc),
                    ctaLabel = stringResource(localizationR.string.profile_empty_create_cta),
                    onViewActions = onViewActions,
                    onCtaClicked = { onViewActions(ChatPopupsViewActions.OnProfileCtaTapped) }
                )
            }

            ChatPopupType.SIGN_UP -> {
                PopupCommonView(
                    title = stringResource(localizationR.string.connect_with_friends_signup_title),
                    description = stringResource(localizationR.string.connect_with_friends_signup_desc),
                    ctaLabel = stringResource(localizationR.string.profile_signup_title_cta),
                    onViewActions = onViewActions,
                    onCtaClicked = { onViewActions(ChatPopupsViewActions.OnSignupCtaTapped) }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun PopupCommonView(
    title: String,
    description: String,
    ctaLabel: String,
    onViewActions: (ChatPopupsViewActions) -> Unit,
    onCtaClicked: () -> Unit
) {
    BasicAlertDialog(
        onDismissRequest = { onViewActions(ChatPopupsViewActions.OnClosePopup) }
    ) {
        val dialogWindowProvider = LocalView.current.parent as DialogWindowProvider
        dialogWindowProvider.window.setGravity(Gravity.BOTTOM)
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(Corners.X5.dp))
                .background(LuziaTheme.palette.surface.content)
                .padding(Spacing.X16.dp),
            verticalArrangement = Arrangement.spacedBy(Spacing.X16.dp),
            horizontalAlignment = Alignment.End
        ) {
            IconButton(
                modifier = Modifier.wrapContentWidth(),
                onClick = { onViewActions(ChatPopupsViewActions.OnClosePopup) }
            ) {
                Icon(
                    painter = painterResource(id = designR.drawable.ic_close),
                    contentDescription = null,
                    tint = LuziaTheme.palette.text.primary
                )
            }
            Icon(
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .size(Spacing.X128.dp),
                painter = painterResource(designR.drawable.im_friends),
                contentDescription = null,
                tint = Color.Unspecified
            )
            LuziaText(
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                text = title,
                style = LuziaTheme.typography.body.semiBold.default,
                color = LuziaTheme.palette.text.primary
            )
            LuziaText(
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                text = description,
                style = LuziaTheme.typography.body.regular.small,
                color = LuziaTheme.palette.text.secondary
            )
            ButtonFilled(
                modifier = Modifier.fillMaxWidth(),
                onClick = onCtaClicked,
                buttonText = ctaLabel
            )
        }
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        ChatPopupsContent(
            viewState = ChatPopupsViewState(),
            onViewActions = {}
        )
    }
}
