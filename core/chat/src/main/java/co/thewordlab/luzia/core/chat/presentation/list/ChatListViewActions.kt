package co.thewordlab.luzia.core.chat.presentation.list

import co.thewordlab.luzia.core.chat.presentation.list.model.ChatListOptions
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class ChatListViewActions : ViewAction {
    data object OnCreate : ChatListViewActions()
    data object OnPerformHaptic : ChatListViewActions()
    data class OnOptionsChange(val options: ChatListOptions) : ChatListViewActions()
    data class OnIceBreakerTap(val text: String) : ChatListViewActions()
    data class SendTextMessage(val text: String) : ChatListViewActions()
    data class OnFeedbackDismiss(val feedbackId: String) : ChatListViewActions()
    data class OnProactiveMessageImpression(val parameters: Map<Parameter, String>) :
        ChatListViewActions()

    data class OnProactiveMessageActionClicked(val parameters: Map<Parameter, String>) :
        ChatListViewActions()

    data object OnDismissAudio : ChatListViewActions()
    data object OnAudioFinished : ChatListViewActions()
    data object OnAudioPlay : ChatListViewActions()
    data object OnAudioPause : ChatListViewActions()

    data class OnFeedbackDisliked(
        val feedbackId: String,
        val fromPrrFeedback: Boolean
    ) : ChatListViewActions()

    data class OnFeedbackLiked(
        val feedbackId: String,
        val fromPrrFeedback: Boolean
    ) : ChatListViewActions()

    data class OnSourcesTapped(val payload: String) : ChatListViewActions()
}
