package co.thewordlab.luzia.core.chat.analytics

import co.thewordlab.luzia.core.chat.presentation.list.model.AudioState
import co.thewordlab.luzia.foundation.analytics.AnalyticsActions
import co.thewordlab.luzia.foundation.analytics.AnalyticsEvents
import co.thewordlab.luzia.foundation.analytics.AnalyticsScreens
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageModel
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageType

private const val MESSAGE_ID = "message_id"
private const val PERSONALITY = "personality"
private const val ORIGIN = "origin"
private const val STEP = "step"
const val STEP_ICEBREAKER = "icebreaker"
const val STEP_PLUS_ICON = "plus_icon"
const val STEP_TASK_ICON = "task_icon"
const val STEP_RESPONSE_STYLE_ICON = "response_style_icon"
const val STEP_WEB_ICON = "web_icon"

object ChatAnalytics {
    object IceBreakerTap : AnalyticsActions("IceBreakerTap")
    data object OpenEditMessage : AnalyticsActions("message_edit_start")
    data object EditMessageCancelled : AnalyticsActions("message_edit_cancel")
    data object EditMessageSent : AnalyticsActions("message_edit_submitted")
    data object ProactiveMessageImpression : AnalyticsEvents("proactive_impression")
    data object ProactiveMessageActionClicked : AnalyticsActions("proactive_click")
    data object GroupsChatsScreen : AnalyticsScreens("groups_chat_list")
    data class MessageReproduceStart(val trackingProperties: Map<String, Any>) : AnalyticsActions(
        value = "message_reproduce_start",
        properties = trackingProperties
    )

    data class MessageReproducePause(val trackingProperties: Map<String, Any>) : AnalyticsActions(
        value = "message_reproduce_pause",
        properties = trackingProperties
    )

    data class MessageReproduce(val trackingProperties: Map<String, Any>) : AnalyticsEvents(
        value = "message_reproduce",
        properties = trackingProperties
    )

    data class MessageReproduceQuit(val trackingProperties: Map<String, Any>) : AnalyticsActions(
        value = "message_reproduce_quit",
        properties = trackingProperties
    )

    data class MessageReproduceFinish(val trackingProperties: Map<String, Any>) : AnalyticsActions(
        value = "message_reproduce_finish",
        properties = trackingProperties
    )

    data class ChatTooltipShown(val step: String) :
        AnalyticsEvents("onboarding_chat_tooltip_displayed", mapOf(STEP to step))
}

fun MessageModel?.buildMessageAttributes() =
    mapOf<Parameter, Any>(
        Parameter.MessageId to (this?.messageId ?: 0L),
        Parameter.Type to (this?.messageType ?: MessageType.Text),
        Parameter.Personality to this?.messagePersonalityId.orEmpty()
    )

fun AudioState.buildAudioAttributes() =
    mapOf<String, Any>(
        MESSAGE_ID to (this.messageId.orEmpty()),
        PERSONALITY to (this.personality.orEmpty()),
        ORIGIN to (this.origin.orEmpty())
    )
