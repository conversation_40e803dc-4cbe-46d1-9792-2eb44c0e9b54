package co.thewordlab.luzia.core.chat.domain.usecases

import androidx.datastore.preferences.core.booleanPreferencesKey
import co.thewordlab.fouundation.persistence.LuziaDataStore
import javax.inject.Inject

class GetResponseStyleFirstTimeUseCase @Inject constructor(private val luziaDataStore: LuziaDataStore) {

    suspend fun isFirstTime(): Boolean =
        luziaDataStore.getData(firstTimeResponseStyle) ?: true

    suspend fun markAsNotFirstTime() {
        luziaDataStore.saveData(firstTimeResponseStyle, false)
    }

    private companion object {
        const val KEY_FIRST_TIME_RESPONSE_STYLE = "firstTimeResponseStyle"
        val firstTimeResponseStyle = booleanPreferencesKey(KEY_FIRST_TIME_RESPONSE_STYLE)
    }
}
