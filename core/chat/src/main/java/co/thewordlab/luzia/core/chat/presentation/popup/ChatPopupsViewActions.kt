package co.thewordlab.luzia.core.chat.presentation.popup

import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class ChatPopupsViewActions : ViewAction {
    data object OnCreate : ChatPopupsViewActions()
    data object OnCopy : ChatPopupsViewActions()
    data object OnClosePopup : ChatPopupsViewActions()
    data object OnSignupCtaTapped : ChatPopupsViewActions()
    data object OnProfileCtaTapped : ChatPopupsViewActions()
}
