package co.thewordlab.luzia.core.chat.domain.models.message

import co.thewordlab.fouundation.persistence.chat.ChatMetadata
import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.luzia.core.ads.tracker.DateProvider
import co.thewordlab.luzia.core.chat.presentation.list.ChatListViewActions
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.theworldlab.luzia.foundation.design.system.components.message.model.FeedbackState
import co.theworldlab.luzia.foundation.design.system.components.message.model.TuTdFeedback
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

class TuTdFeedbackManager @Inject constructor(
    featureFlagManager: FeatureFlagManager,
    dateProvider: DateProvider
) {

    private val sessionCreationTime = dateProvider.currentTimestamp()
    private val isEnabled: Boolean = featureFlagManager.getImmediate(FeatureFlag.PrrV2Enabled)
    private val _feedbackState = MutableStateFlow<TuTdFeedback?>(null)
    val feedbackState: StateFlow<TuTdFeedback?> = _feedbackState.asStateFlow()

    fun mapToModel(
        message: MessageEntity,
        chatMetadata: ChatMetadata?,
        onAction: (ChatListViewActions) -> Unit
    ): TuTdFeedback? {
        val isLastMessage = chatMetadata?.lastMessageId == message.messageId
        val messageCount = chatMetadata?.messageCount ?: 0
        if (!isLastMessage || messageCount < 2) return null
        val messageServerId = message.requestId ?: ""

        return if (isEnabled &&
            message.isAi &&
            message.timeStamp > sessionCreationTime
        ) {
            val currentFeedback = _feedbackState.value
            val existingState = if (currentFeedback?.messageServerId == messageServerId) {
                currentFeedback.state
            } else {
                null
            }

            TuTdFeedback(
                messageServerId = messageServerId,
                state = existingState,
                onFeedbackProvided = if (existingState != null) {
                    { /* Already provided */ }
                } else {
                    { onFeedbackProvided(messageServerId, it, onAction) }
                }
            )
        } else {
            null
        }
    }

    private fun onFeedbackProvided(
        messageServerId: String,
        state: FeedbackState,
        onAction: (ChatListViewActions) -> Unit
    ) {
        _feedbackState.value = TuTdFeedback(
            messageServerId = messageServerId,
            state = state,
            onFeedbackProvided = { /* Already provided */ }
        )

        when (state) {
            FeedbackState.Positive -> onAction(
                ChatListViewActions.OnFeedbackLiked(
                    feedbackId = messageServerId,
                    fromPrrFeedback = false
                )
            )
            FeedbackState.Negative -> onAction(
                ChatListViewActions.OnFeedbackDisliked(
                    feedbackId = messageServerId,
                    fromPrrFeedback = false
                )
            )
        }
    }
}
