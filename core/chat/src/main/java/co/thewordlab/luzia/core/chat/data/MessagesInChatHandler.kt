package co.thewordlab.luzia.core.chat.data

import android.net.Uri
import androidx.paging.PagingData
import co.thewordlab.fouundation.persistence.chat.ChatMetadata
import co.thewordlab.fouundation.persistence.chat.ChatView
import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.luzia.core.tools.domain.repository.DocumentToolRepository
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.foundation.messages.data.model.ContentTextDto
import co.thewordlab.luzia.foundation.messages.data.repository.TextMessageResultWrapper
import co.thewordlab.luzia.foundation.messages.di.Persistent
import co.thewordlab.luzia.foundation.messages.domain.model.AdContent
import co.thewordlab.luzia.foundation.messages.domain.repository.ChatRepository
import co.thewordlab.luzia.foundation.messages.domain.usecases.IsFirstMessageSentUseCase
import co.thewordlab.luzia.foundation.networking.model.AppErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.isSuccess
import co.thewordlab.luzia.foundation.networking.model.map
import kotlinx.coroutines.flow.Flow
import java.io.File
import javax.inject.Inject

class MessagesInChatHandler @Inject constructor(
    private val featureFlagManager: FeatureFlagManager,
    private val chatRepository: ChatRepository,
    @Persistent private val documentsRepository: DocumentToolRepository,
    private val isFirstMessageSentUseCase: IsFirstMessageSentUseCase,
) {

    private var defaultContextMessagesLimit = -1
    private var currentMessagesSent = 0
    private var messagesSentByDocuments = mutableListOf<String>()
    private var attachedFileId: String? = null
    private var isFirstMessageSent = false

    suspend fun sendTextMessage(
        personalityId: String,
        text: String,
        isWebSearchEnabled: Boolean
    ): ResultOf<ContentTextDto, AppErrors> =
        executeOperation(
            isWebSearchEnabled = isWebSearchEnabled,
            documentAction = {
                documentsRepository.sendQuestionForDocumentTool(personalityId, attachedFileId, text)
                    .also {
                        if (it.isSuccess()) {
                            messagesSentByDocuments.add((it as ResultOf.Success).data.metadata.requestId)
                        }
                    }
            },
            chatAction = { chatRepository.sendTextMessage(personalityId, text, isWebSearchEnabled) }
        )

    suspend fun sendAudioRecording(
        personalityId: String,
        file: File,
    ): ResultOf<ContentTextDto, AppErrors> =
        executeOperation(
            isWebSearchEnabled = false,
            documentAction = {
                documentsRepository.sendAudioRecordingForDocument(personalityId, attachedFileId, file)
                    .also {
                        if (it.isSuccess()) {
                            messagesSentByDocuments.add((it as ResultOf.Success).data.metadata.requestId)
                        }
                    }
            },
            chatAction = { chatRepository.sendAudioRecording(personalityId, file) }
        )

    suspend fun resendMessage(
        message: MessageEntity,
        isWebSearchEnabled: Boolean
    ): ResultOf<ContentTextDto, AppErrors> =
        if (messagesSentByDocuments.contains(message.requestId)) {
            documentsRepository.resendMessage(attachedFileId, message)
        } else {
            chatRepository.resendMessage(message, isWebSearchEnabled)
        }

    suspend fun sendAudioImported(
        personalityId: String,
        file: File,
    ): ResultOf<ContentTextDto, AppErrors> {
        if (!isFirstMessageSent) {
            isFirstMessageSent = isFirstMessageSentUseCase()
        }
        return chatRepository.sendAudioImported(personalityId, file)
    }

    suspend fun textToSpeech(text: String): ResultOf<Uri, AppErrors> =
        chatRepository.textToSpeech(text)

    fun getMessages(personalityId: String): Flow<PagingData<MessageEntity>> =
        chatRepository.getMessages(personalityId)

    fun getChatMetadata(personalityId: String): Flow<ChatMetadata?> =
        chatRepository.getChatMetadata(personalityId)

    fun getMessagesAfter(personalityId: String, timestamp: Long): Flow<PagingData<MessageEntity>> =
        chatRepository.getMessagesAfter(personalityId, timestamp)

    fun getUnreadMessageCount(personalityId: String): Flow<Long> =
        chatRepository.getUnreadMessageCount(personalityId)

    fun getChats(): Flow<List<ChatView>> =
        chatRepository.getChats()

    suspend fun clearFailedUserMessages(personalityId: String) {
        chatRepository.clearFailedUserMessages(personalityId)
    }

    suspend fun resendAllSignupRequiredMessages() {
        chatRepository.resendAllSignupRequiredMessages()
    }

    suspend fun sendVisionImage(
        file: File,
        prompt: String,
        personalityId: String,
    ): ResultOf<ContentTextDto, AppErrors> {
        if (!isFirstMessageSent) {
            isFirstMessageSent = isFirstMessageSentUseCase()
        }
        return chatRepository.sendVisionImage(file, prompt, personalityId)
    }

    suspend fun readAllMessages(personalityId: String) =
        chatRepository.readAllMessages(personalityId)

    suspend fun updateMessage(messageId: Long, textToUpdate: String, isWebSearchEnabled: Boolean) =
        chatRepository.updateMessage(messageId, textToUpdate, isWebSearchEnabled)

    suspend fun searchMessages(personalityId: String, query: String): List<MessageEntity> =
        chatRepository.searchMessages(personalityId, query)

    suspend fun dismissFeedback(feedbackId: String) {
        chatRepository.dismissFeedback(feedbackId)
        documentsRepository.dismissFeedback(feedbackId)
    }

    fun getLastUserMessageMaxPromptLength(personalityId: String): Flow<Int?> =
        chatRepository.getLastUserMessageMaxPromptLength(personalityId)

    suspend fun sendDocument(
        personalityId: String,
        file: File
    ): ResultOf<ContentTextDto, AppErrors> =
        documentsRepository.sendFileForDocumentTool(
            personalityId = personalityId,
            file = file,
            insertDocumentInChatAfterNextMessage = true
        ).also {
            if (it.isSuccess()) {
                attachedFileId = (it as ResultOf.Success).data.attachmentId
                currentMessagesSent = 0
                defaultContextMessagesLimit =
                    featureFlagManager.get<Double>(FeatureFlag.DocumentsContextMessageLimit).toInt()

                if (defaultContextMessagesLimit != -1) {
                    documentsRepository.setContextWindowSize(CONTEXT_WINDOW_SIZE)
                }
            }
        }

    fun clearDocument() {
        documentsRepository.clearDocuments()
        defaultContextMessagesLimit = -1
        attachedFileId = null
        currentMessagesSent = 0
    }

    suspend fun sendTextMessageAndSignalAd(
        personalityId: String,
        text: String,
        adContent: AdContent?,
        isWebSearchEnabled: Boolean
    ): ResultOf<TextMessageResultWrapper, AppErrors> =
        executeOperation(
            isWebSearchEnabled = isWebSearchEnabled,
            documentAction = {
                documentsRepository.sendQuestionForDocumentTool(personalityId, attachedFileId, text)
                    .map { TextMessageResultWrapper(this, null) }
                    .also {
                        if (it.isSuccess()) {
                            messagesSentByDocuments.add((it as ResultOf.Success).data.baseResult.metadata.requestId)
                        }
                    }
            },
            chatAction = {
                chatRepository.sendTextMessageAndSignalAd(
                    personalityId = personalityId,
                    text = text,
                    adContent = adContent,
                    isWebSearch = isWebSearchEnabled
                )
            }
        )

    suspend fun countAllUserMessagesWithLuzia(): Int =
        chatRepository.countAllUserMessagesWithLuzia()

    private suspend fun <T> executeOperation(
        isWebSearchEnabled: Boolean,
        documentAction: suspend () -> ResultOf<T, AppErrors>,
        chatAction: suspend () -> ResultOf<T, AppErrors>
    ): ResultOf<T, AppErrors> {
        if (!isFirstMessageSent) {
            isFirstMessageSent = isFirstMessageSentUseCase()
        }
        if (isWebSearchEnabled) clearDocument()
        return if (defaultContextMessagesLimit != -1) {
            if (currentMessagesSent <= defaultContextMessagesLimit) {
                currentMessagesSent++
                documentAction()
            } else {
                currentMessagesSent = 0
                defaultContextMessagesLimit = -1
                chatAction()
            }
        } else {
            chatAction()
        }
    }

    companion object {
        const val CONTEXT_WINDOW_SIZE = 20
    }
}
