package co.thewordlab.luzia.core.chat.presentation.list.model

import androidx.compose.runtime.Immutable
import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.theworldlab.luzia.foundation.design.system.components.lds.tooltip.Tooltip

@Immutable
data class ChatListOptions(
    val enableMessagingActions: Boolean = true,
    val enableFavoriteActions: Boolean = true,
    val enableTextToSpeech: Boolean = false,
    val searchState: ChatListSearchState = ChatListSearchState(),
    val highLightMessageId: Long? = null,
    val showIcebreakers: <PERSON>olean = true,
    val icebreakerTooltip: Tooltip? = null,
    val showCustomBestieWelcome: Boolean = false,
)

@Immutable
data class ChatListSearchState(
    val position: Int = 0,
    val text: String? = null,
    val items: List<MessageEntity>? = null,
)
