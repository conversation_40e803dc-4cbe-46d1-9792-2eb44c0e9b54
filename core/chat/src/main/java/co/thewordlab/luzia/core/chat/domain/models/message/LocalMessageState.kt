package co.thewordlab.luzia.core.chat.domain.models.message

import androidx.compose.runtime.compositionLocalOf
import co.theworldlab.luzia.foundation.design.system.components.item.SelectedItemState
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageModel

val LocalSelectedMessageState = compositionLocalOf {
    SelectedItemState<MessageModel>(
        isEqual = { item, other ->
            item.messageId == other.messageId
        }
    )
}
