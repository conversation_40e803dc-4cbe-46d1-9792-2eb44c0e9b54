package co.thewordlab.luzia.core.chat.presentation.list

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.map
import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.luzia.core.chat.analytics.ChatAnalytics
import co.thewordlab.luzia.core.chat.analytics.buildAudioAttributes
import co.thewordlab.luzia.core.chat.analytics.buildMessageAttributes
import co.thewordlab.luzia.core.chat.domain.ChatController
import co.thewordlab.luzia.core.chat.domain.models.message.MessageEntityMapper
import co.thewordlab.luzia.core.chat.domain.models.message.TuTdFeedbackManager
import co.thewordlab.luzia.core.chat.presentation.list.model.AudioState
import co.thewordlab.luzia.core.chat.presentation.list.model.ChatListOptions
import co.thewordlab.luzia.core.chat.presentation.list.model.MessageRenderModel
import co.thewordlab.luzia.core.feedback.domain.ButtonFeedback
import co.thewordlab.luzia.core.feedback.domain.Feedback
import co.thewordlab.luzia.core.feedback.domain.FeedbackRepository
import co.thewordlab.luzia.core.feedback.domain.FeedbackType
import co.thewordlab.luzia.core.feedback.domain.GroupFeedback
import co.thewordlab.luzia.core.feedback.domain.UserFeedback
import co.thewordlab.luzia.core.feedback.presentation.ActionSelectThumbsDown
import co.thewordlab.luzia.core.feedback.presentation.ActionSelectThumbsUp
import co.thewordlab.luzia.core.feedback.presentation.FeedbackBravo
import co.thewordlab.luzia.core.feedback.presentation.FeedbackDismiss
import co.thewordlab.luzia.core.feedback.presentation.FeedbackNegative
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Event
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.analytics.helpers.ConnectivityObserver
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.common.flows.combine
import co.thewordlab.luzia.foundation.common.haptic.HapticFeedbackManager
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageModel
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageViewActions
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.jetbrains.annotations.VisibleForTesting

@Suppress("TooManyFunctions")
@HiltViewModel(assistedFactory = ChatListViewModelFactory::class)
class ChatListViewModel @AssistedInject constructor(
    @Assisted val options: ChatListOptions,
    @Assisted val chatController: ChatController,
    private val analytics: Analytics,
    private val hapticFeedbackManager: HapticFeedbackManager,
    private val messageEntityMapper: MessageEntityMapper,
    private val feedbackRepository: FeedbackRepository,
    private val tuTdFeedbackManager: TuTdFeedbackManager,
    private val connectivityObserver: ConnectivityObserver
) : ViewModel(),
    ViewModelActions<ChatListViewActions>,
    ViewModelEvents<ChatListViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<ChatListViewState> by ViewModelStatesImpl(ChatListViewState(options)) {

    private val searchState = chatController.searchState

    @VisibleForTesting
    internal val messageActions = MessageViewActions(
        onTextReported = { onTextReported(it) },
        onTextEdited = { onTextEdited(it) },
        onTextCopied = { onTextCopied(it) },
        onLinkClicked = { onLinkClicked(it) },
        onTextShared = { onTextShared(it) },
        onTextDeleted = { onTextDeleted(it) },
        onTextFavorite = { onTextFavorite(it) },
        onSendMessage = { sendTextMessage(it) },
        onLongClick = null,
        onTextToSpeech = { model, origin ->
            onTextToSpeech(model, origin)
        },
    )

    val messages: Flow<PagingData<MessageRenderModel>> = combine(
        chatController.getChatMessages(),
        chatController.getChatMetadata(),
        searchState,
        viewState,
        tuTdFeedbackManager.feedbackState,
        connectivityObserver.observe()
    ) { pagedData, chatMetadata, search, state, feedbackState, connectionState ->
        pagedData
            .map {
                messageEntityMapper.mapToModel(
                    entity = it,
                    query = search.query,
                    highlightId = state.highLightMessageId,
                    actions = messageActions,
                    enableMessagingActions = state.enableMessagingActions,
                    enableFavoriteActions = state.enableFavoriteActions,
                    enableTextToSpeech = state.enableTextToSpeech,
                    onResend = ::onResend,
                    onViewActions = ::onViewAction,
                    isOffline = !connectionState.isConnected,
                    tuTdFeedback = tuTdFeedbackManager.mapToModel(
                        message = it,
                        chatMetadata = chatMetadata,
                        onAction = ::onViewAction
                    )
                )
            }
    }.cachedIn(viewModelScope)

    private fun onResend(message: MessageEntity) = viewModelScope.launch {
        chatController.resendMessage(message)
    }

    override fun onViewAction(action: ChatListViewActions) {
        when (action) {
            is ChatListViewActions.OnCreate -> initializeChat()
            is ChatListViewActions.OnOptionsChange -> onOptionsChange(action.options)
            is ChatListViewActions.SendTextMessage -> sendTextMessage(action.text)
            is ChatListViewActions.OnIceBreakerTap -> onIceBreakerTap(action.text)
            ChatListViewActions.OnPerformHaptic -> onPerformHaptic()
            is ChatListViewActions.OnFeedbackDisliked -> onFeedbackDisliked(action)
            is ChatListViewActions.OnFeedbackDismiss -> onFeedbackDismiss(action)
            is ChatListViewActions.OnFeedbackLiked -> onFeedbackLiked(action)
            is ChatListViewActions.OnProactiveMessageImpression -> onProactiveMessageImpression(
                action
            )

            is ChatListViewActions.OnProactiveMessageActionClicked -> onProactiveMessageActionClicked(
                action
            )

            is ChatListViewActions.OnDismissAudio -> onAudioDismiss()
            is ChatListViewActions.OnAudioFinished -> onAudioFinished()
            is ChatListViewActions.OnAudioPlay -> onAudioPlay()
            is ChatListViewActions.OnAudioPause -> onAudioPause()
            is ChatListViewActions.OnSourcesTapped -> onSourcesTapped(action.payload)
        }
    }

    private fun onSourcesTapped(payload: String) {
        sendEvent(ChatListViewEvents.NavigateToSources(payload))
    }

    private fun onOptionsChange(options: ChatListOptions) {
        updateState {
            it.copy(
                enableMessagingActions = options.enableMessagingActions,
                enableFavoriteActions = options.enableFavoriteActions,
                enableTextToSpeech = options.enableTextToSpeech,
                searchState = options.searchState,
                highLightMessageId = options.highLightMessageId,
                showIcebreakers = options.showIcebreakers,
                icebreakerTooltip = options.icebreakerTooltip,
                showCustomBestieWelcome = options.showCustomBestieWelcome
            )
        }
    }

    private fun sendTextMessage(text: String) = viewModelScope.launch {
        chatController.sendTextMessage(text)
    }

    private fun onPerformHaptic() = viewModelScope.launch {
        hapticFeedbackManager.shot()
    }

    private fun initializeChat() {
        loadIceBreakers()
        listenToUnreadCount()
    }

    private fun listenToUnreadCount() {
        chatController.hasUnreadMessages()
            .onEach { hasUnread -> updateState { it.copy(hasUnreadMessages = hasUnread) } }
            .launchIn(viewModelScope)
    }

    private fun onIceBreakerTap(text: String) {
        analytics.logAction(ChatAnalytics.IceBreakerTap, mapOf(Parameter.IceBreaker to text))
        sendTextMessage(text)
    }

    private fun loadIceBreakers() = viewModelScope.launch {
        chatController.getIceBreakers().onEach { iceBreakers ->
            val props = iceBreakers.mapIndexed { index, text -> "item_$index" to text }.toMap()
            analytics.logEventWithProps(Event.IceBreakersShown, props)
            updateState { it.copy(iceBreakers = iceBreakers) }
        }.launchIn(viewModelScope)
    }

    private fun onTextShared(model: MessageModel) {
        analytics.logEvent(
            Event.ShareMessage,
            mapOf(Parameter.Personality to model.messagePersonalityId)
        )
        sendEvent(ChatListViewEvents.ShareText(model))
    }

    private fun onTextDeleted(model: MessageModel) {
        sendEvent(ChatListViewEvents.DeleteText(model))
    }

    private fun onTextFavorite(model: MessageModel) {
        sendEvent(ChatListViewEvents.FavoriteMessage(model))
    }

    private fun onLinkClicked(link: String) {
        sendEvent(ChatListViewEvents.ClickLink(link))
    }

    private fun onTextCopied(model: MessageModel) {
        analytics.logEvent(
            Event.CopyMessage,
            mapOf(Parameter.Personality to model.messagePersonalityId)
        )
        sendEvent(ChatListViewEvents.MessageCopied)
    }

    private fun onTextReported(model: MessageModel) {
        analytics.logEvent(
            Event.ReportMessage,
            mapOf(
                Parameter.Personality to model.messagePersonalityId,
                Parameter.Text to model.messageText,
                Parameter.MessageId to model.messageRequestId.orEmpty()
            )
        )
    }

    private fun onTextEdited(model: MessageModel) {
        analytics.logAction(
            ChatAnalytics.OpenEditMessage,
            model.buildMessageAttributes()
        )
        sendEvent(ChatListViewEvents.OpenEdit(model))
    }

    private fun onFeedbackDisliked(action: ChatListViewActions.OnFeedbackDisliked) =
        viewModelScope.launch {
            if (action.fromPrrFeedback) {
                val feedback = buildDislikedFeedback(action.feedbackId, GroupFeedback.GENERIC_PRR)
                feedbackRepository.sendFeedback(feedback)
                analytics.trackAction(FeedbackNegative())
            } else {
                val feedback = buildDislikedFeedback(action.feedbackId, GroupFeedback.GENERIC_TUTD)
                feedbackRepository.sendFeedback(feedback)
                analytics.trackAction(ActionSelectThumbsDown(chatController.getFeedbackSource().value))
            }
            chatController.dismissFeedback(action.feedbackId)
            sendEvent(ChatListViewEvents.ShowDislikedFeedbackMessage)
        }

    private fun onFeedbackLiked(action: ChatListViewActions.OnFeedbackLiked) =
        viewModelScope.launch {
            if (action.fromPrrFeedback) {
                val feedback = buildLikedFeedback(action.feedbackId, GroupFeedback.GENERIC_PRR)
                feedbackRepository.sendFeedback(feedback)
                analytics.trackAction(FeedbackBravo())
            } else {
                val feedback = buildLikedFeedback(action.feedbackId, GroupFeedback.GENERIC_TUTD)
                feedbackRepository.sendFeedback(feedback)
                analytics.trackAction(ActionSelectThumbsUp(chatController.getFeedbackSource().value))
            }
            chatController.dismissFeedback(action.feedbackId)
            sendEvent(ChatListViewEvents.ShowLikedFeedbackMessage)
        }

    private fun onFeedbackDismiss(action: ChatListViewActions.OnFeedbackDismiss) =
        viewModelScope.launch {
            chatController.dismissFeedback(action.feedbackId)
            analytics.trackAction(FeedbackDismiss)
        }

    private fun buildDislikedFeedback(feedbackId: String, groupFeedback: GroupFeedback) = Feedback(
        buttonId = ButtonFeedback.THUMBS_DOWN,
        feedbackId = feedbackId,
        feedbackType = FeedbackType.PRR,
        feedbackSource = chatController.getFeedbackSource(),
        userFeedback = UserFeedback.KO,
        group = groupFeedback
    )

    private fun buildLikedFeedback(feedbackId: String, groupFeedback: GroupFeedback) = Feedback(
        buttonId = ButtonFeedback.THUMBS_UP,
        feedbackId = feedbackId,
        feedbackType = FeedbackType.PRR,
        feedbackSource = chatController.getFeedbackSource(),
        userFeedback = UserFeedback.OK,
        group = groupFeedback
    )

    private fun onProactiveMessageActionClicked(
        action: ChatListViewActions.OnProactiveMessageActionClicked,
    ) {
        analytics.logAction(ChatAnalytics.ProactiveMessageActionClicked, action.parameters)
    }

    private fun onProactiveMessageImpression(
        action: ChatListViewActions.OnProactiveMessageImpression,
    ) {
        analytics.logEvent(ChatAnalytics.ProactiveMessageImpression, action.parameters)
    }

    private fun onTextToSpeech(model: MessageModel, origin: String) {
        viewModelScope.launch {
            updateState {
                it.copy(
                    audioState = AudioState(
                        isLoading = true,
                        messageId = model.messageId.toString(),
                        personality = model.messagePersonalityId,
                        origin = origin
                    )
                )
            }
            analytics.trackAction(
                ChatAnalytics.MessageReproduceStart(
                    viewState.value.audioState.buildAudioAttributes()
                )
            )
            when (val result = chatController.textToSpeech(model.messageText)) {
                is ResultOf.Success -> {
                    updateState {
                        it.copy(
                            audioState = it.audioState.copy(
                                uri = result.data,
                                isLoading = false
                            )
                        )
                    }
                }

                is ResultOf.Failure -> {
                    updateState {
                        it.copy(
                            audioState = it.audioState.copy(isLoading = false)
                        )
                    }
                    analytics.reportException(
                        "Text to speech failed: ${result.error.errorCode} ${result.throwable?.message}"
                    )
                }
            }
        }
    }

    private fun onAudioDismiss() {
        analytics.trackAction(
            ChatAnalytics.MessageReproduceQuit(
                viewState.value.audioState.buildAudioAttributes()
            )
        )
        updateState { it.copy(audioState = AudioState.Empty) }
    }

    private fun onAudioFinished() {
        analytics.trackAction(
            ChatAnalytics.MessageReproduceFinish(
                viewState.value.audioState.buildAudioAttributes()
            )
        )
        updateState { it.copy(audioState = AudioState.Empty) }
    }

    private fun onAudioPlay() {
        analytics.trackEvent(
            ChatAnalytics.MessageReproduce(
                viewState.value.audioState.buildAudioAttributes()
            )
        )
    }

    private fun onAudioPause() {
        analytics.trackAction(
            ChatAnalytics.MessageReproducePause(
                viewState.value.audioState.buildAudioAttributes()
            )
        )
    }
}

@AssistedFactory
interface ChatListViewModelFactory {

    fun create(options: ChatListOptions, controller: ChatController): ChatListViewModel
}
