package co.thewordlab.luzia.core.chat.di

import co.thewordlab.luzia.core.chat.analytics.SendMessageEventHelper
import co.thewordlab.luzia.core.chat.analytics.SendMessageEventHelperImp
import co.thewordlab.luzia.core.chat.data.PopupRepositoryImpl
import co.thewordlab.luzia.core.chat.domain.repository.PopupRepository
import co.thewordlab.luzia.foundation.analytics.Analytics
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@InstallIn(SingletonComponent::class)
@Module
object ChatModule {

    @Provides
    fun providePopupRepository(impl: PopupRepositoryImpl): PopupRepository = impl

    @Provides
    fun provideSendMessageEventHelper(analytics: Analytics): SendMessageEventHelper =
        SendMessageEventHelperImp(analytics)
}
