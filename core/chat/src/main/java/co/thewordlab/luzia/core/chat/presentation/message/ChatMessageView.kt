package co.thewordlab.luzia.core.chat.presentation.message

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateIntOffsetAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.offset
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.unit.IntOffset
import co.thewordlab.luzia.core.ads.components.AdMessageView
import co.thewordlab.luzia.core.ads.model.AdViewData
import co.thewordlab.luzia.core.chat.domain.models.message.LocalSelectedMessageState
import co.thewordlab.luzia.core.chat.domain.models.message.SelectedMessageContext
import co.thewordlab.luzia.core.chat.presentation.files.MessageFileView
import co.thewordlab.luzia.core.chat.presentation.list.model.MessageRenderModel
import co.theworldlab.luzia.foundation.design.system.components.latex.LatexMessageView
import co.theworldlab.luzia.foundation.design.system.components.message.MessageAudioView
import co.theworldlab.luzia.foundation.design.system.components.message.MessageImageView
import co.theworldlab.luzia.foundation.design.system.components.message.MessageTextView
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageModel
import co.theworldlab.luzia.foundation.design.system.components.message.proactive.ProactiveMessageView
import co.theworldlab.luzia.foundation.design.system.components.selection.LuziaRadioIcon
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun ChatMessageView(
    model: MessageRenderModel,
    adViewData: AdViewData? = null
) {
    val selectedMessageState = LocalSelectedMessageState.current
    val item = model.message
    val isSelected = selectedMessageState.isSelected(item)
    val isActive = selectedMessageState.isActive
    val context = selectedMessageState.context.value as? SelectedMessageContext
    val showSelection = isActive && context?.showSelectionRadioButtons == true
    val selectionActiveOffset = with(LocalDensity.current) {
        IntOffset(Spacing.X32.dp.roundToPx(), 0)
    }
    val selectionOffset by animateIntOffsetAsState(
        targetValue = if (showSelection && model.message.messageAi) {
            selectionActiveOffset
        } else {
            IntOffset.Zero
        },
        label = "Selection offset"
    )
    Box(contentAlignment = Alignment.CenterStart) {
        AnimatedVisibility(showSelection) {
            LuziaRadioIcon(
                selected = isSelected,
                onClick = {
                    when {
                        selectedMessageState.isActive && isSelected ->
                            selectedMessageState.removeItem(item)

                        selectedMessageState.isActive ->
                            selectedMessageState.addItem(selectedMessageState.context.value, item)
                    }
                }
            )
        }
        InternalMessageView(
            modifier = Modifier
                .offset { selectionOffset }
                .testTag("messageView${item.messageId}"),
            model = model,
            adViewData = adViewData
        )
    }
}

@Composable
private fun InternalMessageView(
    modifier: Modifier = Modifier,
    model: MessageRenderModel,
    adViewData: AdViewData?
) {
    when (val item = model.message) {
        is MessageModel.Text -> {
            MessageTextView(
                modifier = modifier,
                model = item,
                actions = model.actions
            )
        }

        is MessageModel.Math ->
            LatexMessageView(
                modifier = modifier,
                model = item,
                actions = model.actions
            )

        is MessageModel.Audio ->
            MessageAudioView(
                modifier = modifier,
                model = item,
                actions = model.actions
            )

        is MessageModel.Image ->
            MessageImageView(
                modifier = modifier,
                model = item,
                actions = model.actions
            )

        is MessageModel.File ->
            MessageFileView(
                modifier = modifier,
                model = item,
                actions = model.actions
            )

        is MessageModel.Proactive ->
            ProactiveMessageView(
                modifier = modifier,
                model = item,
                actions = model.actions
            )

        is MessageModel.Ad -> {
            if (adViewData != null) {
                AdMessageView(
                    modifier = modifier,
                    model = item,
                    adViewData = adViewData
                )
            }
        }
    }
}
