package co.thewordlab.luzia.core.chat.presentation.list

import co.thewordlab.luzia.core.chat.presentation.list.model.AudioState
import co.thewordlab.luzia.core.chat.presentation.list.model.ChatListOptions
import co.thewordlab.luzia.core.chat.presentation.list.model.ChatListSearchState
import co.thewordlab.luzia.foundation.architecture.system.ViewState
import co.theworldlab.luzia.foundation.design.system.components.lds.tooltip.Tooltip

data class ChatListViewState(
    val enableMessagingActions: Boolean,
    val enableFavoriteActions: <PERSON>olean,
    val enableTextToSpeech: Boolean,
    val iceBreakers: List<String> = emptyList(),
    val hasUnreadMessages: Boolean = false,
    val searchState: ChatListSearchState = ChatListSearchState(),
    val highLightMessageId: Long? = null,
    val showIcebreakers: Boolean = true,
    val icebreakerTooltip: Tooltip? = null,
    val showCustomBestieWelcome: Boolean = false,
    val audioState: AudioState = AudioState.Empty
) : ViewState {

    constructor(options: ChatListOptions) : this(
        enableMessagingActions = options.enableMessagingActions,
        enableFavoriteActions = options.enableFavoriteActions,
        enableTextToSpeech = options.enableTextToSpeech,
        searchState = options.searchState,
        highLightMessageId = options.highLightMessageId,
        showIcebreakers = options.showIcebreakers,
        icebreakerTooltip = options.icebreakerTooltip,
        showCustomBestieWelcome = options.showCustomBestieWelcome
    )
}
