package co.thewordlab.luzia.core.chat.presentation.list.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun ScrollToBottom(modifier: Modifier = Modifier, showBadge: <PERSON>olean, onClicked: () -> Unit) {
    BadgedBox(
        modifier = modifier,
        badge = {
            if (showBadge) {
                Box(
                    modifier = Modifier
                        .padding(top = Spacing.X6.dp, end = Spacing.X6.dp)
                        .clip(CircleShape)
                        .background(LuziaTheme.palette.interactive.brand)
                        .size(IconSizes.X6.dp)
                )
            }
        },
        content = {
            IconButton(
                modifier = Modifier
                    .clip(CircleShape)
                    .size(IconSizes.X24.dp)
                    .background(LuziaTheme.palette.surface.content),
                onClick = onClicked
            ) {
                Icon(
                    modifier = Modifier.size(Spacing.X16.dp),
                    painter = painterResource(id = R.drawable.ic_arrow_down),
                    tint = LuziaTheme.palette.text.primary,
                    contentDescription = null
                )
            }
        }
    )
}
