package co.thewordlab.luzia.core.chat.presentation.sources

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.common.onLinkClicked
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.theworldlab.luzia.foundation.design.system.components.message.model.SourceItem
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopCenteredNavigationBar
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.addShadow
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import coil3.compose.AsyncImage
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun MessageSourcesScreen() {
    val viewModel: MessageSourcesViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val navigation = LocalNavigation.current
    val context = LocalContext.current
    OnCreate("MessageSourcesScreen") {
        viewModel.onViewAction(MessageSourcesViewActions.OnCreate)
    }
    ViewModelEventEffect(viewModel) {
        when (it) {
            MessageSourcesViewEvents.NavigateBack -> navigation.goBack()
            is MessageSourcesViewEvents.NavigateToWeb -> {
                context.onLinkClicked(navigation, it.link)
            }
        }
    }
    MessageSourcesContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@Composable
private fun MessageSourcesContent(
    viewState: MessageSourcesViewState,
    onViewActions: (MessageSourcesViewActions) -> Unit
) {
    Column {
        TopCenteredNavigationBar(
            modifier = Modifier.padding(horizontal = Spacing.X16.dp),
            title = stringResource(localizationR.string.web_search_sources),
            titleStyle = LuziaTheme.typography.body.semiBold.small,
            navigationIconRes = designR.drawable.ic_close,
            actionBackground = LuziaTheme.palette.interactive.contrast,
            onNavigationIconClick = { onViewActions(MessageSourcesViewActions.OnDismiss) },
            endActions = emptyList()
        )
        Column(
            modifier = Modifier.padding(Spacing.X16.dp),
            verticalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
        ) {
            viewState.items.forEach { item ->
                SourceCardView(item, onViewActions)
            }
        }
    }
}

@Composable
private fun SourceCardView(item: SourceItem, onViewActions: (MessageSourcesViewActions) -> Unit) {
    Column(
        Modifier
            .addShadow()
            .fillMaxWidth()
            .clip(RoundedCornerShape(Corners.X2.dp))
            .background(LuziaTheme.palette.interactive.contrast)
            .click(action = { onViewActions(MessageSourcesViewActions.OnSelect(item)) })
            .padding(Spacing.X12.dp),
        verticalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
    ) {
        LuziaText(
            item.title,
            style = LuziaTheme.typography.body.semiBold.small,
            color = LuziaTheme.palette.text.primary
        )
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(Spacing.X8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            AsyncImage(
                modifier = Modifier
                    .size(Spacing.X16.dp)
                    .clip(CircleShape),
                contentScale = ContentScale.Crop,
                model = item.thumbnail,
                contentDescription = null
            )
            LuziaText(
                modifier = Modifier.weight(1f),
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
                text = item.link,
                style = LuziaTheme.typography.body.regular.footnote,
                color = LuziaTheme.palette.text.helper
            )
        }
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        MessageSourcesContent(
            viewState = MessageSourcesViewState(),
            onViewActions = {}
        )
    }
}
