package co.thewordlab.luzia.core.chat.presentation.favorites

import co.thewordlab.luzia.foundation.architecture.system.ViewAction
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageModel

sealed class FavoriteInChatViewActions : ViewAction {
    data object DismissSignupSheet : FavoriteInChatViewActions()
    data object OnSignup : FavoriteInChatViewActions()
    data class ToggleFavorite(val message: MessageModel) : FavoriteInChatViewActions()
}
