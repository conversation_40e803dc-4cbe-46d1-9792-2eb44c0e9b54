package co.thewordlab.luzia.core.chat.data

import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.fouundation.persistence.chat.MessageType
import co.thewordlab.luzia.foundation.messages.data.model.ContentMetadataDto
import co.thewordlab.luzia.foundation.messages.data.model.ContentTextDto
import co.thewordlab.luzia.foundation.messages.data.repository.TextMessageResultWrapper
import co.thewordlab.luzia.foundation.messages.domain.model.AdContent
import co.thewordlab.luzia.foundation.networking.model.AppErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf

object MessagesInChatMocks {

    fun mockMessage(text: String) = mockSuccess(
        ContentTextDto(
            content = text,
            metadata = ContentMetadataDto("1")
        )
    )

    fun mockMessageEntity(id: Long = 123, text: String = "Test message") = MessageEntity(
        messageId = id,
        personalityId = "LuzIA",
        text = text,
        timeStamp = 1234567890,
        isAi = false,
        isRead = true,
        isLoading = false,
        messageType = MessageType.Text,
        error = null,
        fileName = null,
        feedbackId = null,
        proactivePayload = null,
        requestId = null,
        deleted = false,
        isFavorite = false,
        maxPromptLength = null,
        contentInjection = null,
        webSearchData = null,
        webViewData = null
    )

    fun mockMessageEntityList() = listOf(
        mockMessageEntity(1, "Test message 1"),
        mockMessageEntity(2, "Test message 2")
    )

    fun <T> mockSuccess(mock: T): ResultOf<T, AppErrors> =
        ResultOf.Success(mock)

    fun mockTextMessageResultWrapper(
        text: String,
        modifiedAdDescription: String? = null
    ): ResultOf<TextMessageResultWrapper, AppErrors> {
        val contentTextDto = ContentTextDto(
            content = text,
            metadata = ContentMetadataDto("1")
        )
        return mockSuccess(
            mock = TextMessageResultWrapper(contentTextDto, modifiedAdDescription)
        )
    }

    @Suppress("LongParameterList")
    fun mockAdContent(
        headline: String? = "Test Headline",
        body: String? = "Test Body",
        callToAction: String? = "Test CTA",
        icon: String? = "test_icon.png",
        store: String? = "Test Store",
        advertiser: String? = "Test Advertiser"
    ): AdContent = AdContent(
        headline = headline,
        body = body,
        callToAction = callToAction,
        icon = icon,
        store = store,
        advertiser = advertiser
    )
}
