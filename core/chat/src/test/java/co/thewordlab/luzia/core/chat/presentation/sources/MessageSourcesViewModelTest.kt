package co.thewordlab.luzia.core.chat.presentation.sources

import androidx.lifecycle.SavedStateHandle
import app.cash.turbine.test
import co.thewordlab.luzia.core.chat.domain.models.message.MessageDecorationMapper
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.theworldlab.luzia.foundation.design.system.components.message.model.SourceItem
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

class MessageSourcesViewModelTest {

    private val savedStateHandle: SavedStateHandle = SavedStateHandle(
        mapOf(UserSessionRoutes.MessageSources::payload.name to PAYLOAD)
    )
    private val decorationMapper: MessageDecorationMapper = mockk()
    private val analytics: Analytics = mockk(relaxed = true)
    private val viewModel = MessageSourcesViewModel(
        savedStateHandle = savedStateHandle,
        analytics = analytics,
        decorationMapper = decorationMapper
    )

    @Before
    fun setup() {
        every { decorationMapper.mapToSources(PAYLOAD) } returns testSources
    }

    @Test
    fun `onCreate should map payload to sources and update state`() = runTest {
        viewModel.onViewAction(MessageSourcesViewActions.OnCreate)

        assertEquals(testSources, viewModel.viewState.value.items)
        verify { decorationMapper.mapToSources(PAYLOAD) }
        verify { analytics.trackScreen(MessageSourcesAnalytics.ScreenView) }
    }

    @Test
    fun `onCreate should not update state when mapping returns null`() = runTest {
        every { decorationMapper.mapToSources(PAYLOAD) } returns null

        viewModel.onViewAction(MessageSourcesViewActions.OnCreate)

        assertEquals(emptyList<SourceItem>(), viewModel.viewState.value.items)
        verify { decorationMapper.mapToSources(PAYLOAD) }
        verify { analytics.trackScreen(MessageSourcesAnalytics.ScreenView) }
    }

    @Test
    fun `onDismiss should send NavigateBack event`() = runTest {
        viewModel.viewEvent.test {
            viewModel.onViewAction(MessageSourcesViewActions.OnDismiss)
            assertEquals(MessageSourcesViewEvents.NavigateBack, awaitItem())
        }
    }

    @Test
    fun `onSelect should send NavigateToWeb event with correct link`() = runTest {
        val sourceItem = SourceItem(
            title = "Test Source",
            thumbnail = "https://example.com/favicon.ico",
            link = "https://example.com"
        )

        viewModel.viewEvent.test {
            viewModel.onViewAction(MessageSourcesViewActions.OnSelect(sourceItem))
            assertEquals(MessageSourcesViewEvents.NavigateToWeb(sourceItem.link), awaitItem())
        }
    }

    private companion object {
        const val PAYLOAD = "test_payload"
        val testSources = listOf(
            SourceItem(
                title = "Test Source",
                thumbnail = "https://example.com/favicon.ico",
                link = "https://example.com"
            )
        )
    }
}
