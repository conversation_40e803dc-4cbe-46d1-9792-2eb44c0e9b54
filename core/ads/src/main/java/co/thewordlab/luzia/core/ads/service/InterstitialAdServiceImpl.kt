package co.thewordlab.luzia.core.ads.service

import android.app.Activity
import co.thewordlab.luzia.core.ads.BuildConfig
import co.thewordlab.luzia.core.ads.analytics.InterstitialAdDismissed
import co.thewordlab.luzia.core.ads.analytics.InterstitialAdDisplayed
import co.thewordlab.luzia.core.ads.analytics.InterstitialAdFailedToLoad
import co.thewordlab.luzia.core.ads.analytics.InterstitialAdFailedToShow
import co.thewordlab.luzia.core.ads.analytics.InterstitialAdPreloaded
import co.thewordlab.luzia.core.ads.cache.AdCacheManager
import co.thewordlab.luzia.core.ads.model.Ad
import co.thewordlab.luzia.foundation.analytics.Analytics
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback
import dagger.hilt.android.scopes.ActivityScoped
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

private const val HOUR_IN_MS = 1000 * 60 * 60

@ActivityScoped
class InterstitialAdServiceImpl @Inject constructor(
    private val activity: Activity,
    private val adLoaderFactory: AdLoaderFactory,
    private val adCacheManager: AdCacheManager,
    private val analytics: Analytics,
) : InterstitialAdService {

    override suspend fun preLoad(): Result<Unit> = withContext(Dispatchers.Main) {
        return@withContext runCatching {
            adLoaderFactory.loadInterstitialAd(
                adUnitId = BuildConfig.INTERSTITIAL_AD_UNIT_ID,
                adListener = object : InterstitialAdLoadCallback() {
                    override fun onAdLoaded(interstitialAd: InterstitialAd) {
                        adCacheManager.add(
                            Ad.Interstitial(
                                interstitialAd = interstitialAd,
                                validUntil = System.currentTimeMillis() + HOUR_IN_MS
                            )
                        )
                        analytics.trackEvent(InterstitialAdPreloaded)
                    }

                    override fun onAdFailedToLoad(adError: com.google.android.gms.ads.LoadAdError) {
                        analytics.trackEvent(InterstitialAdFailedToLoad)
                    }
                }
            )
        }
    }

    override suspend fun show(): Result<Unit> = withContext(Dispatchers.Main) {
        return@withContext runCatching {
            with(adCacheManager.get<Ad.Interstitial>()?.interstitialAd) {
                if (this == null) return@with
                fullScreenContentCallback = object : FullScreenContentCallback() {
                    override fun onAdDismissedFullScreenContent() {
                        analytics.trackEvent(InterstitialAdDismissed)
                        // Clear callback to prevent memory leaks
                        fullScreenContentCallback = null
                    }

                    override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                        analytics.trackEvent(InterstitialAdFailedToShow)
                        // Clear callback to prevent memory leaks
                        fullScreenContentCallback = null
                    }

                    override fun onAdShowedFullScreenContent() {
                        analytics.trackEvent(InterstitialAdDisplayed)
                    }
                }
                show(activity)
            }
        }
    }
}
