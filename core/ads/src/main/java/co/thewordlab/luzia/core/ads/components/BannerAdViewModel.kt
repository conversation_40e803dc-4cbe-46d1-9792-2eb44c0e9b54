package co.thewordlab.luzia.core.ads.components

import androidx.lifecycle.ViewModel
import co.thewordlab.luzia.core.ads.BuildConfig
import co.thewordlab.luzia.core.ads.service.BannerAdService
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class BannerAdViewModel @Inject constructor(
    val bannerAdService: BannerAdService,
    private val featureFlagManager: FeatureFlagManager
) : ViewModel() {

    suspend fun getBannerAdUnitId(): String = featureFlagManager.getString(
        FeatureFlag.BannerAdsEnabled,
        AD_UNIT_KEY,
        BuildConfig.BANNER_AD_UNIT_ID
    )

    private companion object {
        private const val AD_UNIT_KEY = "adUnit"
    }
}
