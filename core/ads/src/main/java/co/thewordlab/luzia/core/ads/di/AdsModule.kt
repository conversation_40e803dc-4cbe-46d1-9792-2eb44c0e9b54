package co.thewordlab.luzia.core.ads.di

import co.thewordlab.luzia.core.ads.AdHandler
import co.thewordlab.luzia.core.ads.ChatAdHandler
import co.thewordlab.luzia.core.ads.provider.AdMobProvider
import co.thewordlab.luzia.core.ads.provider.AdProvider
import co.thewordlab.luzia.core.ads.provider.KoahAdProvider
import co.thewordlab.luzia.core.ads.service.AdLoaderFactory
import co.thewordlab.luzia.core.ads.service.AdLoaderFactoryImpl
import co.thewordlab.luzia.core.ads.service.AdLoaderService
import co.thewordlab.luzia.core.ads.service.AdLoaderServiceImpl
import co.thewordlab.luzia.core.ads.service.BannerAdService
import co.thewordlab.luzia.core.ads.service.BannerAdServiceImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import dagger.multibindings.IntoSet
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class AdsModule {

    @Binds
    @Singleton
    abstract fun bindAdHandler(
        implementation: ChatAdHandler
    ): AdHandler

    @Binds
    @Singleton
    abstract fun bindAdLoaderService(impl: AdLoaderServiceImpl): AdLoaderService

    @Binds
    @Singleton
    abstract fun bindBannerAdService(impl: BannerAdServiceImpl): BannerAdService

    @Binds
    @Singleton
    abstract fun bindAdLoaderFactory(impl: AdLoaderFactoryImpl): AdLoaderFactory

    @Binds
    @Singleton
    @IntoSet
    abstract fun bindAdMobProvider(impl: AdMobProvider): AdProvider

    @Binds
    @Singleton
    @IntoSet
    abstract fun bindKoahAdProvider(impl: KoahAdProvider): AdProvider
}
