package co.thewordlab.luzia.core.ads.service

import android.content.Context
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdLoader
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback
import com.google.android.gms.ads.nativead.NativeAd
import com.google.android.gms.ads.nativead.NativeAdOptions
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

interface AdLoaderFactory {
    fun loadNativeAd(
        adUnitId: String,
        onNativeAdLoaded: (NativeAd) -> Unit,
        adListener: AdListener,
        nativeAdOptions: NativeAdOptions,
        keywords: List<String>
    )

    fun loadInterstitialAd(
        adUnitId: String,
        adListener: InterstitialAdLoadCallback
    )
}

@Singleton
class AdLoaderFactoryImpl @Inject constructor(
    @ApplicationContext private val context: Context
) : AdLoaderFactory {

    override fun loadNativeAd(
        adUnitId: String,
        onNativeAdLoaded: (NativeAd) -> Unit,
        adListener: AdListener,
        nativeAdOptions: NativeAdOptions,
        keywords: List<String>
    ) {
        val adLoader = AdLoader.Builder(context, adUnitId)
            .forNativeAd(onNativeAdLoaded)
            .withAdListener(adListener)
            .withNativeAdOptions(nativeAdOptions)
            .build()

        val adRequest = AdRequest.Builder().apply {
            keywords.forEach { keyword ->
                addKeyword(keyword)
            }
        }.build()

        adLoader.loadAd(adRequest)
    }

    override fun loadInterstitialAd(
        adUnitId: String,
        adListener: InterstitialAdLoadCallback,
    ) {
        val adRequest = AdRequest.Builder().build()

        InterstitialAd.load(
            context,
            adUnitId,
            adRequest,
            adListener
        )
    }
}
