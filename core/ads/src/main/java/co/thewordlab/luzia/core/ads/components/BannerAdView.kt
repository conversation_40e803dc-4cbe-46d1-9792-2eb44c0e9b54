package co.thewordlab.luzia.core.ads.components

import android.annotation.SuppressLint
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.hilt.navigation.compose.hiltViewModel
import co.thewordlab.luzia.foundation.networking.model.isSuccess
import co.theworldlab.luzia.foundation.design.system.components.skeleton.SkeletonBannerAd
import co.theworldlab.luzia.foundation.design.system.legacy.composables.LuziaCardDefaults
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import com.google.android.gms.ads.AdSize
import com.google.android.gms.ads.AdView

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun BannerAdView(
    modifier: Modifier = Modifier,
    viewModel: BannerAdViewModel = hiltViewModel()
) {
    val context = LocalContext.current.applicationContext
    var adLoadFailed by remember { mutableStateOf(false) }

    AnimatedVisibility(!adLoadFailed) {
        Box(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = Spacing.X16.dp, vertical = Spacing.X8.dp)
        ) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(Corners.X4.dp),
                colors = LuziaCardDefaults.cardColors(containerColor = LuziaTheme.palette.surface.content)
            ) {
                BoxWithConstraints(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(Spacing.X8.dp)
                ) {
                    var isAdLoaded by remember { mutableStateOf(false) }
                    val adView = remember {
                        AdView(context).apply {
                            setAdSize(
                                AdSize.getCurrentOrientationAnchoredAdaptiveBannerAdSize(
                                    context,
                                    maxWidth.value.toInt()
                                )
                            )
                        }
                    }

                    LaunchedEffect(adView) {
                        adView.adUnitId = viewModel.getBannerAdUnitId()
                        val result = viewModel.bannerAdService.loadBannerAd(adView)
                        isAdLoaded = result.isSuccess()
                        adLoadFailed = !result.isSuccess()
                    }

                    DisposableEffect(adView) {
                        onDispose {
                            adView.destroy()
                        }
                    }

                    if (isAdLoaded) {
                        AndroidView(
                            modifier = Modifier.fillMaxWidth(),
                            factory = { adView }
                        )
                    } else {
                        SkeletonBannerAd(
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }
        }
    }
}
