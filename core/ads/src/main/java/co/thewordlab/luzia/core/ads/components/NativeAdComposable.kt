package co.thewordlab.luzia.core.ads.components

import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.viewinterop.AndroidView
import co.thewordlab.luzia.core.ads.R
import co.thewordlab.luzia.core.ads.model.AdViewData
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.ButtonSize
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.ButtonState
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.LuziaSecondaryButton
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import com.google.android.gms.ads.nativead.NativeAd
import com.google.android.gms.ads.nativead.NativeAdView

@Composable
fun NativeAdViewContainer(
    adViewData: AdViewData.NativeAd,
    modifier: Modifier = Modifier
) {
    val nativeAd = adViewData.adObject as? NativeAd ?: return

    val backgroundColor = LuziaTheme.palette.surface.content.toArgb()
    val secondaryTextColor = LuziaTheme.palette.text.secondary
    val isDark = LuziaTheme.isDarkTheme
    val sponsoredLabel = stringResource(id = co.thewordlab.luzia.foundation.localization.R.string.ads_sponsored_label)

    AndroidView(
        factory = { context ->
            val layoutInflater = LayoutInflater.from(context)
            val nativeAdView =
                layoutInflater.inflate(R.layout.native_ad_layout, null) as NativeAdView
            nativeAdView.setBackgroundColor(backgroundColor)

            nativeAdView.headlineView = nativeAdView.findViewById(R.id.ad_headline)
            nativeAdView.bodyView = nativeAdView.findViewById(R.id.ad_body)
            nativeAdView.callToActionView =
                nativeAdView.findViewById(R.id.ad_call_to_action_click_overlay) // Click handled by overlay
            nativeAdView.iconView = nativeAdView.findViewById(R.id.ad_icon)
            nativeAdView.storeView = nativeAdView.findViewById(R.id.ad_store)
            nativeAdView.advertiserView = nativeAdView.findViewById(R.id.ad_advertiser)

            val composeViews = listOfNotNull(
                nativeAdView.headlineView as? ComposeView,
                nativeAdView.bodyView as? ComposeView,
                nativeAdView.findViewById(R.id.ad_call_to_action),
                nativeAdView.storeView as? ComposeView,
                nativeAdView.advertiserView as? ComposeView,
                nativeAdView.findViewById(R.id.ad_attribution)
            )
            composeViews.forEach {
                it.setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            }

            nativeAdView
        },
        modifier = modifier,
        update = { nativeAdView ->
            (nativeAdView.headlineView as? ComposeView)?.setComposeTextContent(
                text = nativeAd.headline,
                style = LuziaTheme.typography.body.semiBold.small,
                isDark = isDark
            )

            val bodyText = adViewData.modifiedDescription ?: nativeAd.body
            (nativeAdView.bodyView as? ComposeView)?.setComposeTextContent(
                text = bodyText,
                style = LuziaTheme.typography.body.regular.small,
                isDark = isDark
            )

            val callToActionComposeView =
                nativeAdView.findViewById<ComposeView>(R.id.ad_call_to_action)
            val callToActionClickOverlay =
                nativeAdView.findViewById<View>(R.id.ad_call_to_action_click_overlay)
            nativeAd.callToAction?.let { callToActionText ->
                callToActionComposeView?.visibility = View.VISIBLE
                callToActionClickOverlay?.visibility = View.VISIBLE
                callToActionComposeView?.setContent {
                    LuziaTheme(darkTheme = isDark) {
                        LuziaSecondaryButton(
                            onClick = { /* No action needed here - NativeAdView handles click */ },
                            text = callToActionText,
                            size = ButtonSize.SMALL,
                            state = ButtonState.ENABLED
                        )
                    }
                }
            } ?: run {
                callToActionComposeView?.visibility = View.GONE
                callToActionClickOverlay?.visibility = View.GONE
            }

            nativeAd.icon?.let {
                (nativeAdView.iconView as? ImageView)?.apply {
                    setImageDrawable(it.drawable)
                    visibility = View.VISIBLE
                }
            } ?: run {
                nativeAdView.iconView?.visibility = View.GONE
            }

            (nativeAdView.storeView as? ComposeView)?.setComposeTextContent(
                text = nativeAd.store,
                style = LuziaTheme.typography.body.semiBold.footnote,
                isDark = isDark
            )

            (nativeAdView.advertiserView as? ComposeView)?.setComposeTextContent(
                text = nativeAd.advertiser,
                style = LuziaTheme.typography.body.semiBold.footnote,
                isDark = isDark
            )

            nativeAdView.findViewById<ComposeView>(R.id.ad_attribution)?.setComposeTextContent(
                text = sponsoredLabel,
                style = LuziaTheme.typography.body.regular.footnote,
                isDark = isDark,
                color = secondaryTextColor
            )

            nativeAdView.setNativeAd(nativeAd)
        }
    )
}

private fun ComposeView.setComposeTextContent(
    text: String?,
    style: TextStyle,
    isDark: Boolean,
    color: Color? = null
) {
    if (!text.isNullOrEmpty()) {
        visibility = View.VISIBLE
        setContent {
            LuziaTheme(darkTheme = isDark) {
                LuziaText(
                    text = text,
                    style = style,
                    color = color ?: LuziaTheme.palette.text.primary
                )
            }
        }
    } else {
        visibility = View.GONE
    }
}
