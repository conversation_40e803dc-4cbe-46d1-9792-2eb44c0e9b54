package co.thewordlab.luzia.core.ads

import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import co.thewordlab.fouundation.persistence.LuziaDataStore
import co.thewordlab.luzia.core.ads.analytics.KeywordExtractedForAd
import co.thewordlab.luzia.core.ads.model.AdViewData
import co.thewordlab.luzia.core.ads.service.AdLoaderService
import co.thewordlab.luzia.core.ads.tracker.AdImpressionTracker
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.foundation.config.FeatureFlagTreatment
import co.thewordlab.luzia.foundation.messages.domain.model.AdContent
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of the AdHandler to manage ads in chat.
 * This contains the logic for when ads should appear and how they're formatted.
 * Includes logic to defer ads if word count requirements are not met at the scheduled message interval.
 * This simplified version assumes a single pending state across personalities.
 */
@Singleton
class ChatAdHandler @Inject constructor(
    private val featureFlagManager: FeatureFlagManager,
    private val adLoaderService: AdLoaderService,
    private val adImpressionTracker: AdImpressionTracker,
    private val analytics: Analytics,
    private val dataStore: LuziaDataStore
) : AdHandler {

    override suspend fun getAdData(
        personalityId: String,
        text: String,
        messageCount: Int
    ): AdViewData? {
        val contextualAdsFeatureFlag =
            FeatureFlagTreatment.getValue(featureFlagManager.get(FeatureFlag.ContextualAdsEnabled))
        return when (contextualAdsFeatureFlag) {
            FeatureFlagTreatment.CONTROL -> null
            FeatureFlagTreatment.TREATMENT_A -> getNativeAdMobAd(personalityId, messageCount, text)
            FeatureFlagTreatment.TREATMENT_B -> AdViewData.KoahAd(prompt = text)
            else -> getNativeAdMobAd(personalityId, messageCount, text)
        }
    }

    private suspend fun getNativeAdMobAd(
        personalityId: String,
        messageCount: Int,
        text: String
    ): AdViewData.NativeAd? {
        if (canShowNativeGoogleAd(personalityId, messageCount, text)) {
            val keywords = extractKeywords(text)
            trackKeywords(keywords, personalityId)
            val adResult = adLoaderService.loadNativeAd(keywords)
            if (adResult is ResultOf.Success) {
                return adResult.data
            }
        }
        return null
    }

    private fun trackKeywords(
        keywords: List<String>,
        personalityId: String
    ) {
        keywords.forEach { keyword ->
            analytics.trackEvent(KeywordExtractedForAd(personalityId, keyword))
        }
    }

    override fun destroyAd(value: AdViewData?) {
        if (value is AdViewData.NativeAd) {
            adLoaderService.destroyAd(value)
        }
    }

    override fun mapToAdContent(adViewData: AdViewData?): AdContent? {
        return adViewData?.let {
            when (it) {
                is AdViewData.KoahAd -> AdContent(
                    headline = null,
                    body = null,
                    callToAction = null,
                    icon = null,
                    store = null,
                    advertiser = null
                )

                is AdViewData.NativeAd -> AdContent(
                    headline = it.headline ?: "",
                    body = it.body ?: "",
                    callToAction = it.callToAction,
                    advertiser = it.advertiser,
                    icon = it.iconUriString,
                    store = it.store
                )
            }
        }
    }

    override fun updateAdData(
        adViewData: AdViewData,
        content: String,
        requestId: String,
        modifiedAdDescription: String?
    ): AdViewData {
        return when (adViewData) {
            is AdViewData.KoahAd -> adViewData.copy(response = content, messageId = requestId)
            is AdViewData.NativeAd -> {
                if (modifiedAdDescription != null) {
                    adViewData.copy(modifiedDescription = modifiedAdDescription)
                } else {
                    adViewData
                }
            }
        }
    }

    private fun extractKeywords(text: String): List<String> {
        val wordRegex = Regex("[a-zA-Z]+")
        return wordRegex.findAll(text.lowercase())
            .map { it.value }
            .filter { it.length > MIN_KEYWORD_LENGTH }
            .distinct()
            .toList()
    }

    private suspend fun canShowNativeGoogleAd(
        personalityId: String,
        messageIndex: Int,
        message: String
    ): Boolean {
        val wordCount = message.trim().split("\\s+".toRegex()).filter { it.isNotBlank() }.size
        val minWordCount = featureFlagManager.getInt(
            FeatureFlag.ContextualAdsEnabled,
            AD_MIN_WORD_COUNT_KEY,
            MIN_WORD_COUNT_FOR_AD
        )
        val meetsWordCountRequirement = wordCount >= minWordCount
        val isLuziaPersonality = personalityId == LUZIA_ID_PERSONALITY
        val isMoreThanMinMessage = messageIndex > MIN_MESSAGES_BEFORE_AD
        val adFrequency = featureFlagManager.getInt(
            FeatureFlag.ContextualAdsEnabled,
            AD_RECURRENCE_KEY,
            AD_FREQUENCY_DEFAULT
        )
        val adDailyMax = featureFlagManager.getInt(FeatureFlag.ContextualAdsEnabled, AD_DAILY_MAX_KEY, MAX_ADS_PER_DAY)
        val canShowMoreAds =
            adImpressionTracker.canShowMoreAds(
                LUZIA_ID_PERSONALITY,
                messageIndex,
                adDailyMax,
                adFrequency
            )
        return isLuziaPersonality && isMoreThanMinMessage && meetsWordCountRequirement && canShowMoreAds
    }

    override suspend fun recordAdImpression(personalityId: String, messageIndex: Int) {
        if (personalityId == LUZIA_ID_PERSONALITY) {
            adImpressionTracker.recordImpression(personalityId, messageIndex)
        }
    }

    override suspend fun checkAndMarkPendingAd(
        personalityId: String,
        messageCount: Int
    ) {
        if (personalityId != LUZIA_ID_PERSONALITY) return

        val shouldShowAd = shouldShowAdForMessage(personalityId, messageCount)

        if (shouldShowAd) {
            val pendingKey = booleanPreferencesKey(getPendingAdKey(personalityId))
            val pendingMessageCountKey = intPreferencesKey(getPendingMessageCountKey(personalityId))

            dataStore.saveData(pendingKey, true)
            dataStore.saveData(pendingMessageCountKey, messageCount)
        }
    }

    override suspend fun getPendingAdData(
        personalityId: String,
        text: String,
        messageCount: Int
    ): AdViewData? {
        if (personalityId != LUZIA_ID_PERSONALITY) return null

        val pendingKey = booleanPreferencesKey(getPendingAdKey(personalityId))
        val pendingMessageCountKey = intPreferencesKey(getPendingMessageCountKey(personalityId))

        val hasPendingAd = dataStore.getData(pendingKey) ?: false

        return if (hasPendingAd) {
            dataStore.saveData(pendingKey, false)
            val pendingMessageCount = dataStore.getData(pendingMessageCountKey) ?: messageCount

            val contextualAdsFeatureFlag =
                FeatureFlagTreatment.getValue(featureFlagManager.get(FeatureFlag.ContextualAdsEnabled))

            when (contextualAdsFeatureFlag) {
                FeatureFlagTreatment.TREATMENT_A -> getNativeAdMobAdForPending(personalityId, pendingMessageCount, text)
                FeatureFlagTreatment.TREATMENT_B -> AdViewData.KoahAd(prompt = text)
                else -> null
            }
        } else {
            null
        }
    }

    private suspend fun getNativeAdMobAdForPending(
        personalityId: String,
        messageCount: Int,
        text: String
    ): AdViewData.NativeAd? {
        if (canShowNativeGoogleAd(personalityId, messageCount, text)) {
            val keywords = extractKeywords(text)
            trackKeywords(keywords, personalityId)
            val adResult = adLoaderService.loadNativeAd(keywords)
            if (adResult is ResultOf.Success) {
                return adResult.data
            }
        }
        return null
    }

    private suspend fun shouldShowAdForMessage(
        personalityId: String,
        messageCount: Int
    ): Boolean {
        val isLuziaPersonality = personalityId == LUZIA_ID_PERSONALITY
        val isMoreThanMinMessage = messageCount > MIN_MESSAGES_BEFORE_AD
        val adFrequency = featureFlagManager.getInt(
            FeatureFlag.ContextualAdsEnabled,
            AD_RECURRENCE_KEY,
            AD_FREQUENCY_DEFAULT
        )
        val adDailyMax = featureFlagManager.getInt(FeatureFlag.ContextualAdsEnabled, AD_DAILY_MAX_KEY, MAX_ADS_PER_DAY)
        val canShowMoreAds =
            adImpressionTracker.canShowMoreAds(
                LUZIA_ID_PERSONALITY,
                messageCount,
                adDailyMax,
                adFrequency
            )
        return isLuziaPersonality && isMoreThanMinMessage && canShowMoreAds
    }

    private fun getPendingAdKey(personalityId: String): String {
        return "pending_ad_$personalityId"
    }

    private fun getPendingMessageCountKey(personalityId: String): String {
        return "pending_ad_message_count_$personalityId"
    }

    companion object {
        const val LUZIA_ID_PERSONALITY = "LuzIA"
        const val MAX_ADS_PER_DAY = 5
        const val MIN_MESSAGES_BEFORE_AD = 1
        const val AD_FREQUENCY_DEFAULT = 4
        const val MIN_WORD_COUNT_FOR_AD = 3
        const val MIN_KEYWORD_LENGTH = 3
        const val AD_RECURRENCE_KEY = "recurrence"
        const val AD_DAILY_MAX_KEY = "dailyMax"
        const val AD_MIN_WORD_COUNT_KEY = "minWords"
    }
}
