package co.thewordlab.luzia.core.ads.navigation

import android.app.Activity
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavBackStackEntry
import co.thewordlab.luzia.core.navigation.observer.NavigationRouteRecorder
import co.thewordlab.luzia.foundation.analytics.Analytics
import dagger.hilt.android.scopes.ActivityScoped
import kotlinx.coroutines.launch
import javax.inject.Inject

@Suppress("TooGenericExceptionCaught")
@ActivityScoped
class NavigationAdManager @Inject constructor(
    private val activity: Activity,
    private val navigationRouteRecorder: NavigationRouteRecorder,
    private val adRules: Set<@JvmSuppressWildcards NavigationAdRule>,
    private val analytics: Analytics
) {

    fun startNavigationTracking() {
        (activity as AppCompatActivity).lifecycleScope.launch {
            try {
                navigationRouteRecorder.navigationHistory.collect {
                    val navigationHistory = navigationRouteRecorder.getRecentEntries()
                    processNavigationRules(navigationHistory)
                }
            } catch (e: Exception) {
                analytics.reportException(
                    "NavigationAdManager navigation tracking failed",
                    e
                )
            }
        }
    }

    internal suspend fun processNavigationRules(navigationHistory: List<NavBackStackEntry>) {
        adRules.forEach { rule ->
            try {
                rule.processNavigation(navigationHistory)
            } catch (e: Exception) {
                analytics.reportException("NavigationAdRule ${rule.ruleName} execution failed", e)
            }
        }
    }
}
