package co.thewordlab.luzia.core.ads.cache

import co.thewordlab.luzia.core.ads.model.Ad
import dagger.hilt.android.scopes.ActivityScoped
import jakarta.inject.Inject

@ActivityScoped
class AdCacheManager @Inject constructor() {

    private val ads = mutableListOf<Ad>()
    private val lock = Any()

    internal fun add(ad: Ad) {
        synchronized(lock) {
            ads.add(ad)
        }
    }

    fun <T : Ad> get(clazz: Class<T>): T? {
        synchronized(lock) {
            cleanUpExpiredAds()

            return ads
                .asSequence()
                .filterIsInstance(clazz)
                .firstOrNull { !it.used }
                ?.apply { used = true }
        }
    }

    inline fun <reified T : Ad> get(): T? = get(T::class.java)

    private fun cleanUpExpiredAds() {
        synchronized(lock) {
            ads.filter { it.isExpired() }
                .forEach {
                    it.destroy()
                    removeAdFromCache(it)
                }
        }
    }

    private fun removeAdFromCache(ad: Ad): Bo<PERSON>an {
        synchronized(lock) {
            return ads.remove(ad)
        }
    }

    fun clean() {
        synchronized(lock) {
            ads.forEach(Ad::destroy)
            ads.clear()
        }
    }

    internal val adsSize: Int
        get() = synchronized(lock) { ads.size }
}
