package co.thewordlab.luzia.core.ads.provider

import android.content.Context
import com.google.android.gms.ads.MobileAds
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject

class AdMobProvider @Inject constructor(
    @ApplicationContext private val applicationContext: Context
) : AdProvider {
    override suspend fun initiate() {
        MobileAds.initialize(applicationContext)
    }
}
