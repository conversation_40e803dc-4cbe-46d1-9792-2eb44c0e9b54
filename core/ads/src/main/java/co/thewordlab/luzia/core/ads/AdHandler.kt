package co.thewordlab.luzia.core.ads

import co.thewordlab.luzia.core.ads.model.AdViewData
import co.thewordlab.luzia.foundation.messages.domain.model.AdContent

interface AdHandler {

    suspend fun recordAdImpression(personalityId: String, messageIndex: Int)

    suspend fun getAdData(
        personalityId: String,
        text: String,
        messageCount: Int
    ): AdViewData?

    fun destroyAd(value: AdViewData?)
    fun mapToAdContent(adViewData: AdViewData?): AdContent?
    fun updateAdData(
        adViewData: AdViewData,
        content: String,
        requestId: String,
        modifiedAdDescription: String?
    ): AdViewData

    suspend fun checkAndMarkPendingAd(
        personalityId: String,
        messageCount: Int
    )

    suspend fun getPendingAdData(
        personalityId: String,
        text: String,
        messageCount: Int
    ): AdViewData?
}
