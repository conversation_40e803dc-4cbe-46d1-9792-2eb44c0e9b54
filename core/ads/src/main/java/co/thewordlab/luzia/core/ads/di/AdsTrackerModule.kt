package co.thewordlab.luzia.core.ads.di

import co.thewordlab.luzia.core.ads.tracker.DateProvider
import co.thewordlab.luzia.core.ads.tracker.DefaultDateProvider
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class AdsTrackerModule {

    @Binds
    abstract fun bindDateProvider(impl: DefaultDateProvider): DateProvider
}
