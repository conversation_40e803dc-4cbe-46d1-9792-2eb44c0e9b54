package co.thewordlab.luzia.core.ads.provider

import android.content.Context
import co.thewordlab.luzia.core.ads.BuildConfig
import com.koahlabs.android.Koah
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject

class KoahAdProvider @Inject constructor(
    @ApplicationContext private val applicationContext: Context
) : AdProvider {
    override suspend fun initiate() {
        Koah(context = applicationContext) {
            publisherId = BuildConfig.KOAH_PUBLISHER_ID
            debug = BuildConfig.DEBUG
        }
    }
}
