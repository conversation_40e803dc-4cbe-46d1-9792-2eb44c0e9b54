package co.thewordlab.luzia.core.ads.di

import co.thewordlab.luzia.core.ads.AdManager
import co.thewordlab.luzia.core.ads.AdManagerImpl
import co.thewordlab.luzia.core.ads.navigation.NavigationAdRule
import co.thewordlab.luzia.core.ads.navigation.rules.InterstitialAdRule
import co.thewordlab.luzia.core.ads.service.InterstitialAdService
import co.thewordlab.luzia.core.ads.service.InterstitialAdServiceImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityComponent
import dagger.hilt.android.scopes.ActivityScoped
import dagger.multibindings.IntoSet

@Module
@InstallIn(ActivityComponent::class)
abstract class ActivityAdsModule {

    @Binds
    @ActivityScoped
    abstract fun bindAdManager(impl: AdManagerImpl): AdManager

    @Binds
    @ActivityScoped
    abstract fun bindInterstitialAdService(impl: InterstitialAdServiceImpl): InterstitialAdService

    @Binds
    @ActivityScoped
    @IntoSet
    abstract fun bindInterstitialAdRule(rule: InterstitialAdRule): NavigationAdRule
}
