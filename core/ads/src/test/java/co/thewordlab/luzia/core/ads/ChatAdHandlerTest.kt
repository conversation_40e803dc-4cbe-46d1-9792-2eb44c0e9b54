package co.thewordlab.luzia.core.ads

import co.thewordlab.luzia.core.ads.ChatAdHandler.Companion.LUZIA_ID_PERSONALITY
import co.thewordlab.luzia.core.ads.ChatAdHandler.Companion.MIN_MESSAGES_BEFORE_AD
import co.thewordlab.luzia.core.ads.analytics.KeywordExtractedForAd
import co.thewordlab.luzia.core.ads.model.AdViewData
import co.thewordlab.luzia.core.ads.stub.FakeAdLoaderService
import co.thewordlab.luzia.core.ads.stub.FakeLuziaDataStore
import co.thewordlab.luzia.core.ads.tracker.AdImpressionTracker
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.foundation.config.FeatureFlagTreatment
import co.thewordlab.luzia.foundation.messages.domain.model.AdContent
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull

class ChatAdHandlerTest {

    private val featureFlagManager: FeatureFlagManager = mockk()
    private val fakeAdLoaderService = FakeAdLoaderService()
    private val adImpressionTracker: AdImpressionTracker = mockk()
    private val analytics: Analytics = mockk(relaxed = true)
    private val fakeDataStore = FakeLuziaDataStore()
    private val chatAdHandler =
        ChatAdHandler(featureFlagManager, fakeAdLoaderService, adImpressionTracker, analytics, fakeDataStore)

    private fun setupDefaultFeatureFlagMocks(
        minWordCount: Int = ChatAdHandler.MIN_WORD_COUNT_FOR_AD,
        adFrequency: Int = ChatAdHandler.AD_FREQUENCY_DEFAULT,
        dailyMax: Int = ChatAdHandler.MAX_ADS_PER_DAY
    ) {
        coEvery {
            featureFlagManager.getInt(
                FeatureFlag.ContextualAdsEnabled,
                ChatAdHandler.AD_MIN_WORD_COUNT_KEY,
                ChatAdHandler.MIN_WORD_COUNT_FOR_AD
            )
        } returns minWordCount
        coEvery {
            featureFlagManager.getInt(
                FeatureFlag.ContextualAdsEnabled,
                ChatAdHandler.AD_RECURRENCE_KEY,
                ChatAdHandler.AD_FREQUENCY_DEFAULT
            )
        } returns adFrequency
        coEvery {
            featureFlagManager.getInt(
                FeatureFlag.ContextualAdsEnabled,
                ChatAdHandler.AD_DAILY_MAX_KEY,
                ChatAdHandler.MAX_ADS_PER_DAY
            )
        } returns dailyMax
    }

    @Test
    fun `GIVEN admob enabled and valid conditions WHEN getAdData THEN returns native ad data`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.TREATMENT_A.value
        setupDefaultFeatureFlagMocks()
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true
        val expectedAdData = AdViewData.NativeAd(
            headline = "Test Ad",
            body = "Test Body",
            callToAction = "Click",
            advertiser = "Test Advertiser",
            iconUriString = "test://icon",
            store = "Test Store",
            adObject = mockk()
        )
        fakeAdLoaderService.adDataToReturn = expectedAdData
        fakeAdLoaderService.shouldReturnSuccess = true

        val result = chatAdHandler.getAdData(
            LUZIA_ID_PERSONALITY,
            "What is your favorite color today?",
            MIN_MESSAGES_BEFORE_AD + 1
        )

        assertEquals(expectedAdData, result)
        assertEquals(1, fakeAdLoaderService.loadNativeAdCallCount)
        assertEquals(listOf("what", "your", "favorite", "color", "today"), fakeAdLoaderService.lastKeywords)
    }

    @Test
    fun `GIVEN admob enabled but ad loading fails WHEN getAdData THEN returns null`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.TREATMENT_A.value
        setupDefaultFeatureFlagMocks()
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true
        fakeAdLoaderService.shouldReturnSuccess = false

        val result = chatAdHandler.getAdData(
            LUZIA_ID_PERSONALITY,
            "What is your favorite color today?",
            MIN_MESSAGES_BEFORE_AD + 1
        )

        assertNull(result)
        assertEquals(1, fakeAdLoaderService.loadNativeAdCallCount)
    }

    @Test
    fun `GIVEN koah ads enabled WHEN getAdData THEN returns koah ad data`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.TREATMENT_B.value
        val prompt = "What is your favorite color today?"

        val result = chatAdHandler.getAdData(
            LUZIA_ID_PERSONALITY,
            prompt,
            MIN_MESSAGES_BEFORE_AD + 1
        )

        assertEquals(AdViewData.KoahAd(prompt = prompt), result)
        assertEquals(0, fakeAdLoaderService.loadNativeAdCallCount) // Should not call ad loader for Koah ads
    }

    @Test
    fun `GIVEN treatment C enabled and valid conditions WHEN getAdData THEN defaults to admob ad`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.TREATMENT_C.value
        setupDefaultFeatureFlagMocks()
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true
        val expectedAdData = AdViewData.NativeAd(
            headline = "Test Ad",
            body = "Test Body",
            callToAction = "Click",
            advertiser = "Test Advertiser",
            iconUriString = "test://icon",
            store = "Test Store",
            adObject = mockk()
        )
        fakeAdLoaderService.adDataToReturn = expectedAdData
        fakeAdLoaderService.shouldReturnSuccess = true

        val result = chatAdHandler.getAdData(
            LUZIA_ID_PERSONALITY,
            "What is your favorite color today?",
            MIN_MESSAGES_BEFORE_AD + 1
        )

        assertEquals(expectedAdData, result)
        assertEquals(1, fakeAdLoaderService.loadNativeAdCallCount)
        assertEquals(listOf("what", "your", "favorite", "color", "today"), fakeAdLoaderService.lastKeywords)
    }

    @Test
    fun `GIVEN ads disabled WHEN getAdData THEN returns null`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.CONTROL.value

        val result = chatAdHandler.getAdData(
            LUZIA_ID_PERSONALITY,
            "What is your favorite color today?",
            MIN_MESSAGES_BEFORE_AD + 1
        )

        assertNull(result)
        assertEquals(0, fakeAdLoaderService.loadNativeAdCallCount)
    }

    @Test
    fun `GIVEN unknown feature flag value WHEN getAdData THEN defaults to control and returns null`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns "unknown_treatment"

        val result = chatAdHandler.getAdData(
            LUZIA_ID_PERSONALITY,
            "What is your favorite color today?",
            MIN_MESSAGES_BEFORE_AD + 1
        )

        assertNull(result)
        assertEquals(0, fakeAdLoaderService.loadNativeAdCallCount)
    }

    @Test
    fun `GIVEN null feature flag value WHEN getAdData THEN defaults to control and returns null`() = runTest {
        coEvery {
            featureFlagManager.get<String?>(FeatureFlag.ContextualAdsEnabled)
        } returns null

        val result = chatAdHandler.getAdData(
            LUZIA_ID_PERSONALITY,
            "What is your favorite color today?",
            MIN_MESSAGES_BEFORE_AD + 1
        )

        assertNull(result)
        assertEquals(0, fakeAdLoaderService.loadNativeAdCallCount)
    }

    @Test
    fun `GIVEN message has less than minimum word count WHEN getAdData THEN returns null`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.TREATMENT_A.value
        setupDefaultFeatureFlagMocks()
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true

        val result = chatAdHandler.getAdData(
            LUZIA_ID_PERSONALITY,
            "Hi!",
            MIN_MESSAGES_BEFORE_AD + 1
        )

        assertNull(result)
        assertEquals(0, fakeAdLoaderService.loadNativeAdCallCount)
    }

    @Test
    fun `GIVEN message count is minimum required WHEN getAdData THEN returns null`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.TREATMENT_A.value
        setupDefaultFeatureFlagMocks()
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true

        val result = chatAdHandler.getAdData(
            LUZIA_ID_PERSONALITY,
            "What is your favorite color today?",
            MIN_MESSAGES_BEFORE_AD
        )

        assertNull(result)
        assertEquals(0, fakeAdLoaderService.loadNativeAdCallCount)
    }

    @Test
    fun `GIVEN personality is not LuzIA WHEN getAdData THEN returns null`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.TREATMENT_A.value
        setupDefaultFeatureFlagMocks()
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true

        val result = chatAdHandler.getAdData(
            "Elias",
            "What is your favorite color today?",
            MIN_MESSAGES_BEFORE_AD + 1
        )

        assertNull(result)
        assertEquals(0, fakeAdLoaderService.loadNativeAdCallCount)
    }

    @Test
    fun `GIVEN ad impression limit reached WHEN getAdData THEN returns null`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.TREATMENT_A.value
        setupDefaultFeatureFlagMocks()
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns false

        val result = chatAdHandler.getAdData(
            LUZIA_ID_PERSONALITY,
            "What is your favorite color today?",
            MIN_MESSAGES_BEFORE_AD + 1
        )

        assertNull(result)
        assertEquals(0, fakeAdLoaderService.loadNativeAdCallCount)
    }

    @Test
    fun `GIVEN native ad WHEN destroyAd THEN service destroyAd is called`() = runTest {
        val nativeAd = AdViewData.NativeAd(
            headline = "Test",
            body = "Test",
            callToAction = "Click",
            advertiser = "Test",
            iconUriString = "test://icon",
            store = "Test Store",
            adObject = mockk()
        )

        chatAdHandler.destroyAd(nativeAd)

        assertEquals(1, fakeAdLoaderService.destroyAdCallCount)
        assertEquals(nativeAd, fakeAdLoaderService.lastDestroyedAd)
    }

    @Test
    fun `GIVEN koah ad WHEN destroyAd THEN service destroyAd is not called`() = runTest {
        val koahAd = AdViewData.KoahAd(prompt = "test")

        chatAdHandler.destroyAd(koahAd)

        assertEquals(0, fakeAdLoaderService.destroyAdCallCount)
    }

    @Test
    fun `GIVEN native ad WHEN mapToAdContent THEN returns mapped content`() = runTest {
        val nativeAd = AdViewData.NativeAd(
            headline = "Test Headline",
            body = "Test Body",
            callToAction = "Click Now",
            advertiser = "Test Advertiser",
            iconUriString = "test://icon",
            store = "Test Store",
            adObject = mockk()
        )

        val result = chatAdHandler.mapToAdContent(nativeAd)

        val expected = AdContent(
            headline = "Test Headline",
            body = "Test Body",
            callToAction = "Click Now",
            advertiser = "Test Advertiser",
            icon = "test://icon",
            store = "Test Store"
        )
        assertEquals(expected, result)
    }

    @Test
    fun `GIVEN koah ad WHEN mapToAdContent THEN returns empty content`() = runTest {
        val koahAd = AdViewData.KoahAd(prompt = "test")

        val result = chatAdHandler.mapToAdContent(koahAd)

        val expected = AdContent(
            headline = null,
            body = null,
            callToAction = null,
            icon = null,
            store = null,
            advertiser = null
        )
        assertEquals(expected, result)
    }

    @Test
    fun `GIVEN koah ad WHEN updateAdData THEN returns updated koah ad with response and message id`() = runTest {
        val koahAd = AdViewData.KoahAd(prompt = "test")
        val content = "Updated response"
        val requestId = "123"

        val result = chatAdHandler.updateAdData(koahAd, content, requestId, null)

        val expected = koahAd.copy(response = content, messageId = requestId)
        assertEquals(expected, result)
    }

    @Test
    fun `GIVEN native ad with modified description WHEN updateAdData THEN returns updated native ad`() = runTest {
        val nativeAd = AdViewData.NativeAd(
            headline = "Test",
            body = "Test",
            callToAction = "Click",
            advertiser = "Test",
            iconUriString = "test://icon",
            store = "Test Store",
            adObject = mockk()
        )
        val modifiedDescription = "Modified description"

        val result = chatAdHandler.updateAdData(nativeAd, "content", "123", modifiedDescription)

        val expected = nativeAd.copy(modifiedDescription = modifiedDescription)
        assertEquals(expected, result)
    }

    @Test
    fun `GIVEN native ad without modified description WHEN updateAdData THEN returns unchanged native ad`() = runTest {
        val nativeAd = AdViewData.NativeAd(
            headline = "Test",
            body = "Test",
            callToAction = "Click",
            advertiser = "Test",
            iconUriString = "test://icon",
            store = "Test Store",
            adObject = mockk()
        )

        val result = chatAdHandler.updateAdData(nativeAd, "content", "123", null)

        assertEquals(nativeAd, result)
    }

    @Test
    fun `GIVEN native ad loads successfully WHEN getAdData THEN tracks keywords`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.TREATMENT_A.value
        setupDefaultFeatureFlagMocks()
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true
        val expectedAdData = AdViewData.NativeAd(
            headline = "Test Ad",
            body = "Test Body",
            callToAction = "Click",
            advertiser = "Test Advertiser",
            iconUriString = "test://icon",
            store = "Test Store",
            adObject = mockk()
        )
        fakeAdLoaderService.adDataToReturn = expectedAdData
        fakeAdLoaderService.shouldReturnSuccess = true

        chatAdHandler.getAdData(
            LUZIA_ID_PERSONALITY,
            "What is your favorite color today?",
            MIN_MESSAGES_BEFORE_AD + 1
        )

        verify { analytics.trackEvent(KeywordExtractedForAd(LUZIA_ID_PERSONALITY, "what")) }
        verify { analytics.trackEvent(KeywordExtractedForAd(LUZIA_ID_PERSONALITY, "your")) }
        verify { analytics.trackEvent(KeywordExtractedForAd(LUZIA_ID_PERSONALITY, "favorite")) }
        verify { analytics.trackEvent(KeywordExtractedForAd(LUZIA_ID_PERSONALITY, "color")) }
        verify { analytics.trackEvent(KeywordExtractedForAd(LUZIA_ID_PERSONALITY, "today")) }
    }

    @Test
    fun `GIVEN koah ads enabled WHEN getAdData THEN does not track keywords`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.TREATMENT_B.value

        chatAdHandler.getAdData(
            LUZIA_ID_PERSONALITY,
            "What is your favorite color today?",
            MIN_MESSAGES_BEFORE_AD + 1
        )

        verify(exactly = 0) { analytics.trackEvent(any<KeywordExtractedForAd>()) }
    }

    @Test
    fun `GIVEN custom min word count from feature flag WHEN message has fewer words THEN returns null`() = runTest {
        val customMinWordCount = 8
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.TREATMENT_A.value
        setupDefaultFeatureFlagMocks(minWordCount = customMinWordCount)
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true

        val result = chatAdHandler.getAdData(
            LUZIA_ID_PERSONALITY,
            "What is your favorite color?", // Only 5 words, less than custom minimum of 8
            MIN_MESSAGES_BEFORE_AD + 1
        )

        assertNull(result)
        assertEquals(0, fakeAdLoaderService.loadNativeAdCallCount)
    }

    @Test
    fun `GIVEN custom min word count from feature flag WHEN message meets word requirement THEN loads ad`() = runTest {
        val customMinWordCount = 2
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.TREATMENT_A.value
        setupDefaultFeatureFlagMocks(minWordCount = customMinWordCount)
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true
        val expectedAdData = AdViewData.NativeAd(
            headline = "Test Ad",
            body = "Test Body",
            callToAction = "Click",
            advertiser = "Test Advertiser",
            iconUriString = "test://icon",
            store = "Test Store",
            adObject = mockk()
        )
        fakeAdLoaderService.adDataToReturn = expectedAdData
        fakeAdLoaderService.shouldReturnSuccess = true

        val result = chatAdHandler.getAdData(
            LUZIA_ID_PERSONALITY,
            "Hello world", // Only 2 words, meets custom minimum of 2
            MIN_MESSAGES_BEFORE_AD + 1
        )

        assertEquals(expectedAdData, result)
        assertEquals(1, fakeAdLoaderService.loadNativeAdCallCount)
    }

    @Test
    fun `GIVEN custom ad frequency and daily max from feature flags WHEN getAdData THEN passes correct parameters`() =
        runTest {
            val customAdFrequency = 6
            val customDailyMax = 10
            coEvery {
                featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
            } returns FeatureFlagTreatment.TREATMENT_A.value
            setupDefaultFeatureFlagMocks(adFrequency = customAdFrequency, dailyMax = customDailyMax)
            coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true
            val expectedAdData = AdViewData.NativeAd(
                headline = "Test Ad",
                body = "Test Body",
                callToAction = "Click",
                advertiser = "Test Advertiser",
                iconUriString = "test://icon",
                store = "Test Store",
                adObject = mockk()
            )
            fakeAdLoaderService.adDataToReturn = expectedAdData
            fakeAdLoaderService.shouldReturnSuccess = true

            chatAdHandler.getAdData(
                LUZIA_ID_PERSONALITY,
                "What is your favorite color today?",
                MIN_MESSAGES_BEFORE_AD + 1
            )

            coVerify {
                adImpressionTracker.canShowMoreAds(
                    LUZIA_ID_PERSONALITY,
                    MIN_MESSAGES_BEFORE_AD + 1,
                    customDailyMax,
                    customAdFrequency
                )
            }
        }

    @Test
    fun `GIVEN default feature flag values WHEN getAdData THEN uses default constants`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.TREATMENT_A.value
        setupDefaultFeatureFlagMocks()
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true
        val expectedAdData = AdViewData.NativeAd(
            headline = "Test Ad",
            body = "Test Body",
            callToAction = "Click",
            advertiser = "Test Advertiser",
            iconUriString = "test://icon",
            store = "Test Store",
            adObject = mockk()
        )
        fakeAdLoaderService.adDataToReturn = expectedAdData
        fakeAdLoaderService.shouldReturnSuccess = true

        chatAdHandler.getAdData(
            LUZIA_ID_PERSONALITY,
            "What is your favorite color today?",
            MIN_MESSAGES_BEFORE_AD + 1
        )

        coVerify {
            featureFlagManager.getInt(
                FeatureFlag.ContextualAdsEnabled,
                ChatAdHandler.AD_MIN_WORD_COUNT_KEY,
                ChatAdHandler.MIN_WORD_COUNT_FOR_AD
            )
        }
        coVerify {
            featureFlagManager.getInt(
                FeatureFlag.ContextualAdsEnabled,
                ChatAdHandler.AD_RECURRENCE_KEY,
                ChatAdHandler.AD_FREQUENCY_DEFAULT
            )
        }
        coVerify {
            featureFlagManager.getInt(
                FeatureFlag.ContextualAdsEnabled,
                ChatAdHandler.AD_DAILY_MAX_KEY,
                ChatAdHandler.MAX_ADS_PER_DAY
            )
        }
        coVerify {
            adImpressionTracker.canShowMoreAds(
                LUZIA_ID_PERSONALITY,
                MIN_MESSAGES_BEFORE_AD + 1,
                ChatAdHandler.MAX_ADS_PER_DAY,
                ChatAdHandler.AD_FREQUENCY_DEFAULT
            )
        }
    }

    @Test
    fun `GIVEN valid conditions for ad WHEN checkAndMarkPendingAd THEN marks ad as pending`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.CONTROL.value
        setupDefaultFeatureFlagMocks()
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true

        chatAdHandler.checkAndMarkPendingAd(LUZIA_ID_PERSONALITY, MIN_MESSAGES_BEFORE_AD + 1)

        val pendingAdData = chatAdHandler.getPendingAdData(
            LUZIA_ID_PERSONALITY,
            "test message",
            MIN_MESSAGES_BEFORE_AD + 2
        )

        assertNull(pendingAdData)
    }

    @Test
    fun `GIVEN non-LuzIA personality WHEN checkAndMarkPendingAd THEN does not mark as pending`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.CONTROL.value
        setupDefaultFeatureFlagMocks()
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true

        chatAdHandler.checkAndMarkPendingAd("Elias", MIN_MESSAGES_BEFORE_AD + 1)

        val pendingAdData = chatAdHandler.getPendingAdData("Elias", "test message", MIN_MESSAGES_BEFORE_AD + 2)
        assertNull(pendingAdData)
    }

    @Test
    fun `GIVEN insufficient message count WHEN checkAndMarkPendingAd THEN does not mark as pending`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.CONTROL.value
        setupDefaultFeatureFlagMocks()
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true

        chatAdHandler.checkAndMarkPendingAd(LUZIA_ID_PERSONALITY, MIN_MESSAGES_BEFORE_AD)

        val pendingAdData = chatAdHandler.getPendingAdData(
            LUZIA_ID_PERSONALITY,
            "test message",
            MIN_MESSAGES_BEFORE_AD + 1
        )
        assertNull(pendingAdData)
    }

    @Test
    fun `GIVEN no pending ad WHEN getPendingAdData THEN returns null`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.CONTROL.value

        val result = chatAdHandler.getPendingAdData(LUZIA_ID_PERSONALITY, "test message", MIN_MESSAGES_BEFORE_AD + 1)

        assertNull(result)
    }

    @Test
    fun `GIVEN pending ad WHEN getPendingAdData THEN returns ad and clears pending state`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.TREATMENT_A.value
        setupDefaultFeatureFlagMocks()
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true
        val expectedAdData = AdViewData.NativeAd(
            headline = "Pending Ad",
            body = "Pending Body",
            callToAction = "Click",
            advertiser = "Test Advertiser",
            iconUriString = "test://icon",
            store = "Test Store",
            adObject = mockk()
        )
        fakeAdLoaderService.adDataToReturn = expectedAdData
        fakeAdLoaderService.shouldReturnSuccess = true

        chatAdHandler.checkAndMarkPendingAd(LUZIA_ID_PERSONALITY, MIN_MESSAGES_BEFORE_AD + 1)

        val result1 = chatAdHandler.getPendingAdData(
            LUZIA_ID_PERSONALITY,
            "test pending message",
            MIN_MESSAGES_BEFORE_AD + 2
        )
        val result2 = chatAdHandler.getPendingAdData(
            LUZIA_ID_PERSONALITY,
            "test pending message",
            MIN_MESSAGES_BEFORE_AD + 3
        )

        assertEquals(expectedAdData, result1)
        assertNull(result2)
    }

    @Test
    fun `GIVEN pending ad exists and koah enabled WHEN getPendingAdData THEN returns koah ad`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.TREATMENT_B.value
        setupDefaultFeatureFlagMocks()
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true

        chatAdHandler.checkAndMarkPendingAd(LUZIA_ID_PERSONALITY, MIN_MESSAGES_BEFORE_AD + 1)

        val testMessage = "test pending message"

        val result = chatAdHandler.getPendingAdData(LUZIA_ID_PERSONALITY, testMessage, MIN_MESSAGES_BEFORE_AD + 2)

        assertEquals(AdViewData.KoahAd(prompt = testMessage), result)
    }

    @Test
    fun `GIVEN non-LuzIA personality WHEN getPendingAdData THEN returns null`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.ContextualAdsEnabled)
        } returns FeatureFlagTreatment.CONTROL.value
        setupDefaultFeatureFlagMocks()
        coEvery { adImpressionTracker.canShowMoreAds(any(), any(), any(), any()) } returns true

        chatAdHandler.checkAndMarkPendingAd(LUZIA_ID_PERSONALITY, MIN_MESSAGES_BEFORE_AD + 1)

        val result = chatAdHandler.getPendingAdData("Elias", "test message", MIN_MESSAGES_BEFORE_AD + 2)

        assertNull(result)
    }
}
