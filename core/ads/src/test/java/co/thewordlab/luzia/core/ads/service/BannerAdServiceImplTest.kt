package co.thewordlab.luzia.core.ads.service

import co.thewordlab.luzia.core.ads.analytics.BannerClicked
import co.thewordlab.luzia.core.ads.analytics.BannerDisplayed
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdView
import com.google.android.gms.ads.LoadAdError
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Test

@ExperimentalCoroutinesApi
class BannerAdServiceImplTest {

    private val analytics: Analytics = mockk(relaxed = true)
    private val bannerAdService: BannerAdServiceImpl = BannerAdServiceImpl(analytics)
    private val adView: AdView = mockk(relaxed = true)

    @Test
    fun `Given ad loads successfully When loadBannerAd Then returns success and tracks banner displayed`() = runTest {
        // Given
        val adListenerSlot = slot<AdListener>()
        every { adView.adListener = capture(adListenerSlot) } returns Unit
        every { adView.loadAd(any()) } answers {
            adListenerSlot.captured.onAdLoaded()
        }

        // When
        val result = bannerAdService.loadBannerAd(adView)

        // Then
        assertTrue(result is ResultOf.Success)
        verify { analytics.trackEvent(BannerDisplayed) }
        verify { adView.loadAd(any()) }
    }

    @Test
    fun `Given ad fails to load When loadBannerAd Then returns failure and does not track event`() = runTest {
        // Given
        val errorCode = 3
        val errorMessage = "No network connection"
        val loadAdError = mockk<LoadAdError> {
            every { code } returns errorCode
            every { message } returns errorMessage
        }
        val adListenerSlot = slot<AdListener>()
        every { adView.adListener = capture(adListenerSlot) } returns Unit
        every { adView.loadAd(any()) } answers {
            adListenerSlot.captured.onAdFailedToLoad(loadAdError)
        }

        // When
        val result = bannerAdService.loadBannerAd(adView)

        // Then
        assertTrue(result is ResultOf.Failure)
        val failure = result as ResultOf.Failure
        assertEquals(errorCode, failure.error.code)
        assertEquals(errorMessage, failure.error.message)
        verify(exactly = 0) { analytics.trackEvent(BannerDisplayed) }
    }

    @Test
    fun `Given ad is clicked When loadBannerAd Then tracks both display and click events`() = runTest {
        // Given
        val adListenerSlot = slot<AdListener>()
        every { adView.adListener = capture(adListenerSlot) } returns Unit
        every { adView.loadAd(any()) } answers {
            adListenerSlot.captured.onAdLoaded()
            adListenerSlot.captured.onAdClicked()
        }

        // When
        bannerAdService.loadBannerAd(adView)

        // Then
        verify { analytics.trackEvent(BannerDisplayed) }
        verify { analytics.trackEvent(BannerClicked) }
    }

    @Test
    fun `Given ad view When loadBannerAd Then sets ad listener and loads ad`() = runTest {
        // Given
        val adListenerSlot = slot<AdListener>()
        every { adView.adListener = capture(adListenerSlot) } returns Unit
        every { adView.loadAd(any()) } answers {
            adListenerSlot.captured.onAdLoaded()
        }

        // When
        bannerAdService.loadBannerAd(adView)

        // Then
        verify { adView.adListener = any() }
        verify { adView.loadAd(any()) }
    }

    @Test
    fun `Given ad view When loadBannerAd Then creates and loads ad request`() = runTest {
        // Given
        val adListenerSlot = slot<AdListener>()
        every { adView.adListener = capture(adListenerSlot) } returns Unit
        every { adView.loadAd(any()) } answers {
            adListenerSlot.captured.onAdLoaded()
        }

        // When
        bannerAdService.loadBannerAd(adView)

        // Then
        verify { adView.loadAd(any()) }
    }

    @Test
    fun `Given ad is clicked after loading When onAdClicked Then tracks banner clicked event`() = runTest {
        // Given
        val adListenerSlot = slot<AdListener>()
        every { adView.adListener = capture(adListenerSlot) } returns Unit
        every { adView.loadAd(any()) } answers {
            adListenerSlot.captured.onAdLoaded()
        }
        bannerAdService.loadBannerAd(adView)

        // When
        adListenerSlot.captured.onAdClicked()

        // Then
        verify { analytics.trackEvent(BannerClicked) }
    }
}
