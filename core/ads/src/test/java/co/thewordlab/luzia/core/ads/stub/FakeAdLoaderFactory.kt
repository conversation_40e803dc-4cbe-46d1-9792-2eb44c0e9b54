package co.thewordlab.luzia.core.ads.stub

import co.thewordlab.luzia.core.ads.service.AdLoaderFactory
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.ResponseInfo
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback
import com.google.android.gms.ads.nativead.NativeAd
import com.google.android.gms.ads.nativead.NativeAdOptions
import io.mockk.every
import io.mockk.mockk

class FakeAdLoaderFactory : AdLoaderFactory {

    var shouldReturnSuccess = true
    var loadNativeAdCallCount = 0
    var loadInterstitialAdCallCount = 0
    var lastAdUnitId: String? = null
    var lastInterstitialAdUnitId: String? = null
    var lastKeywords: List<String>? = null
    var lastNativeAdOptions: NativeAdOptions? = null
    var mockLoadAdError: LoadAdError? = null
    var storedOnNativeAdLoaded: ((NativeAd) -> Unit)? = null
    var storedAdListener: AdListener? = null
    private var customResponseInfo: ResponseInfo? = null
    var storedInterstitialAdLoadCallback: InterstitialAdLoadCallback? = null

    override fun loadNativeAd(
        adUnitId: String,
        onNativeAdLoaded: (NativeAd) -> Unit,
        adListener: AdListener,
        nativeAdOptions: NativeAdOptions,
        keywords: List<String>
    ) {
        loadNativeAdCallCount++
        lastAdUnitId = adUnitId
        lastKeywords = keywords
        lastNativeAdOptions = nativeAdOptions
        storedOnNativeAdLoaded = onNativeAdLoaded
        storedAdListener = adListener

        if (shouldReturnSuccess) {
            simulateAdLoadSuccess()
        } else {
            simulateAdLoadFailure()
        }
    }

    override fun loadInterstitialAd(
        adUnitId: String,
        adListener: InterstitialAdLoadCallback
    ) {
        loadInterstitialAdCallCount++
        lastInterstitialAdUnitId = adUnitId
        storedInterstitialAdLoadCallback = adListener

        if (shouldReturnSuccess) {
            simulateInterstitialAdLoadSuccess()
        } else {
            simulateInterstitialAdLoadFailure()
        }
    }

    fun simulateAdLoadSuccess() {
        val mockNativeAd = createMockNativeAd()
        storedOnNativeAdLoaded?.invoke(mockNativeAd)
    }

    fun simulateAdLoadFailure() {
        val loadAdError = mockLoadAdError ?: mockk<LoadAdError> {
            every { code } returns 1
            every { message } returns "Test ad load error"
        }
        storedAdListener?.onAdFailedToLoad(loadAdError)
    }

    fun simulateInterstitialAdLoadSuccess() {
        val mockInterstitialAd = createMockInterstitialAd()
        storedInterstitialAdLoadCallback?.onAdLoaded(mockInterstitialAd)
    }

    fun simulateInterstitialAdLoadFailure() {
        val loadAdError = mockLoadAdError ?: mockk<LoadAdError> {
            every { code } returns 1
            every { message } returns "Test interstitial ad load error"
        }
        storedInterstitialAdLoadCallback?.onAdFailedToLoad(loadAdError)
    }

    fun simulateAdClick() {
        storedAdListener?.onAdClicked()
    }

    fun mockNativeAdWithResponseInfo(responseInfo: ResponseInfo) {
        customResponseInfo = responseInfo
    }

    private fun createMockNativeAd(): NativeAd {
        return mockk {
            every { headline } returns "Test Headline"
            every { body } returns "Test Body"
            every { callToAction } returns "Test CTA"
            every { advertiser } returns "Test Advertiser"
            every { icon?.uri?.toString() } returns "https://test-icon.com/icon.png"
            every { store } returns "Test Store"
            every { destroy() } returns Unit
            every { responseInfo } returns customResponseInfo
        }
    }

    private fun createMockInterstitialAd(): InterstitialAd {
        return mockk(relaxed = true) {
            every { show(any()) } returns Unit
        }
    }

    fun reset() {
        shouldReturnSuccess = true
        loadNativeAdCallCount = 0
        loadInterstitialAdCallCount = 0
        lastAdUnitId = null
        lastInterstitialAdUnitId = null
        lastKeywords = null
        lastNativeAdOptions = null
        mockLoadAdError = null
        storedOnNativeAdLoaded = null
        storedAdListener = null
        customResponseInfo = null
        storedInterstitialAdLoadCallback = null
    }
}
