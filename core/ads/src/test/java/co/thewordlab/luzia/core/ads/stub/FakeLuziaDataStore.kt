package co.thewordlab.luzia.core.ads.stub

import androidx.datastore.preferences.core.Preferences
import co.thewordlab.fouundation.persistence.LuziaDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.mapNotNull

class FakeLuziaDataStore : LuziaDataStore {

    private val map = mutableMapOf<Preferences.Key<*>, Any>()
    private val flowMap = mutableMapOf<Preferences.Key<*>, MutableStateFlow<Any?>>()

    override suspend fun <T : Any> delete(key: Preferences.Key<T>) {
        map.remove(key)
        flowMap[key]?.value = null
    }

    override suspend fun <T : Any> saveData(
        key: Preferences.Key<T>,
        value: T
    ) {
        map[key] = value
        getMutableFlow(key).value = value
    }

    override suspend fun <T : Any> getData(key: Preferences.Key<T>): T? {
        @Suppress("UNCHECKED_CAST")
        return map[key] as? T
    }

    override fun <T : Any> getDataFlow(key: Preferences.Key<T>): Flow<T?> {
        @Suppress("UNCHECKED_CAST")
        return getMutableFlow(key).mapNotNull { it as? T }
    }
    private fun <T : Any> getMutableFlow(
        key: Preferences.Key<T>
    ): MutableStateFlow<Any?> = flowMap.getOrPut(key) { MutableStateFlow(map[key]) }
}
