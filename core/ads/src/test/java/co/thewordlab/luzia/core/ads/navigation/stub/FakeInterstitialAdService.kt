package co.thewordlab.luzia.core.ads.navigation.stub

import co.thewordlab.luzia.core.ads.service.InterstitialAdService

class FakeInterstitialAdService : InterstitialAdService {

    var showCallCount: Int = 0
        private set
    var preLoadCallCount: Int = 0
        private set
    var shouldReturnSuccess: Boolean = true
    var exceptionToThrow: Exception = RuntimeException("Test exception")

    override suspend fun show(): Result<Unit> {
        showCallCount++

        return if (shouldReturnSuccess) {
            Result.success(Unit)
        } else {
            Result.failure(exceptionToThrow)
        }
    }

    override suspend fun preLoad(): Result<Unit> {
        preLoadCallCount++

        return if (shouldReturnSuccess) {
            Result.success(Unit)
        } else {
            Result.failure(exceptionToThrow)
        }
    }

    fun reset() {
        showCallCount = 0
        preLoadCallCount = 0
        shouldReturnSuccess = true
        exceptionToThrow = RuntimeException("Test exception")
    }
}
