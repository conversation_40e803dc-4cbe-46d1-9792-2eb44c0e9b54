package co.thewordlab.luzia.core.ads.cache

import co.thewordlab.luzia.core.ads.model.Ad
import com.google.android.gms.ads.interstitial.InterstitialAd
import io.mockk.mockk
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import java.util.Collections
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class AdCacheManagerTest {

    private lateinit var adCacheManager: AdCacheManager
    private lateinit var mockInterstitialAd: InterstitialAd

    @Before
    fun setUp() {
        adCacheManager = AdCacheManager()
        mockInterstitialAd = mockk(relaxed = true)
    }

    @Test
    fun `GIVEN empty cache WHEN add ad THEN cache size increases`() {
        val ad = createValidInterstitialAd()

        adCacheManager.add(ad)

        assertEquals(1, adCacheManager.adsSize)
    }

    @Test
    fun `GIVEN cached ad WHEN get by class THEN returns ad and marks as used`() {
        val ad = createValidInterstitialAd()
        adCacheManager.add(ad)

        val retrievedAd = adCacheManager.get(Ad.Interstitial::class.java)

        assertNotNull(retrievedAd)
        assertEquals(ad, retrievedAd)
        assertTrue(retrievedAd.used)
    }

    @Test
    fun `GIVEN cached ad WHEN get with reified type THEN returns ad and marks as used`() {
        val ad = createValidInterstitialAd()
        adCacheManager.add(ad)

        val retrievedAd = adCacheManager.get<Ad.Interstitial>()

        assertNotNull(retrievedAd)
        assertEquals(ad, retrievedAd)
        assertTrue(retrievedAd.used)
    }

    @Test
    fun `GIVEN empty cache WHEN get ad THEN returns null`() {
        val retrievedAd = adCacheManager.get<Ad.Interstitial>()

        assertNull(retrievedAd)
    }

    @Test
    fun `GIVEN used ad WHEN get ad THEN returns null`() {
        val ad = createValidInterstitialAd()
        ad.used = true
        adCacheManager.add(ad)

        val retrievedAd = adCacheManager.get<Ad.Interstitial>()

        assertNull(retrievedAd)
    }

    @Test
    fun `GIVEN multiple ads with one used WHEN get ad THEN returns first unused ad`() {
        val usedAd = createValidInterstitialAd()
        usedAd.used = true
        val availableAd = createValidInterstitialAd()

        adCacheManager.add(usedAd)
        adCacheManager.add(availableAd)

        val retrievedAd = adCacheManager.get<Ad.Interstitial>()

        assertNotNull(retrievedAd)
        assertEquals(availableAd, retrievedAd)
        assertTrue(retrievedAd.used)
    }

    @Test
    fun `GIVEN expired ad WHEN get ad THEN expired ad is cleaned up and returns null`() {
        val expiredAd = createExpiredInterstitialAd()
        adCacheManager.add(expiredAd)

        val retrievedAd = adCacheManager.get<Ad.Interstitial>()

        assertNull(retrievedAd)
        assertEquals(0, adCacheManager.adsSize)
    }

    @Test
    fun `GIVEN expired and valid ads WHEN get ad THEN expired ad is cleaned up and valid ad returned`() {
        val expiredAd = createExpiredInterstitialAd()
        val validAd = createValidInterstitialAd()

        adCacheManager.add(expiredAd)
        adCacheManager.add(validAd)

        val retrievedAd = adCacheManager.get<Ad.Interstitial>()

        assertNotNull(retrievedAd)
        assertEquals(validAd, retrievedAd)
        assertEquals(1, adCacheManager.adsSize)
    }

    @Test
    fun `GIVEN cached ads WHEN clean THEN all ads destroyed and cache cleared`() {
        val ad1 = createValidInterstitialAd()
        val ad2 = createValidInterstitialAd()

        adCacheManager.add(ad1)
        adCacheManager.add(ad2)

        adCacheManager.clean()

        assertEquals(0, adCacheManager.adsSize)
    }

    @Test
    fun `GIVEN empty cache WHEN clean THEN no exception thrown`() {
        adCacheManager.clean()

        assertEquals(0, adCacheManager.adsSize)
    }

    @Test
    fun `GIVEN multiple threads adding ads WHEN concurrent add operations THEN all ads are added safely`() = runTest {
        val numberOfThreads = 10
        val adsPerThread = 100

        val jobs = (1..numberOfThreads).map { threadId ->
            async {
                repeat(adsPerThread) { adId ->
                    val ad = createValidInterstitialAd()
                    adCacheManager.add(ad)
                }
            }
        }

        jobs.forEach { it.await() }

        assertEquals(numberOfThreads * adsPerThread, adCacheManager.adsSize)
    }

    @Test
    fun `GIVEN multiple threads getting ads WHEN concurrent get operations THEN operations are thread safe`() =
        runTest {
            val numberOfAds = 100
            repeat(numberOfAds) { index ->
                adCacheManager.add(createUniqueInterstitialAd(index))
            }

            val retrievedAds = Collections.synchronizedList(mutableListOf<Ad.Interstitial>())

            val jobs = (1..5).map {
                async {
                    repeat(10) {
                        val ad = adCacheManager.get<Ad.Interstitial>()
                        if (ad != null) retrievedAds.add(ad)
                    }
                }
            }

            jobs.forEach { job ->
                job.await()
            }

            // Verify thread safety: all retrieved ads should be marked as used and be unique
            assertTrue(retrievedAds.isNotEmpty())
            retrievedAds.forEach { ad ->
                assertTrue(ad.used)
            }
            // All retrieved ads should be unique (no duplicates due to race conditions)
            assertTrue(retrievedAds.size == retrievedAds.toSet().size, "Retrieved ads should be unique")
            assertTrue(retrievedAds.size <= numberOfAds, "Should not retrieve more ads than available")
        }

    @Test
    fun `GIVEN concurrent add and get operations WHEN mixed operations THEN data consistency maintained`() = runTest {
        val addJobs = (1..5).map {
            async {
                repeat(20) {
                    delay(1) // Small delay to interleave operations
                    adCacheManager.add(createValidInterstitialAd())
                }
            }
        }

        val getJobs = (1..3).map {
            async {
                val retrievedAds = mutableListOf<Ad.Interstitial>()
                repeat(10) {
                    delay(2) // Different delay pattern
                    val ad = adCacheManager.get<Ad.Interstitial>()
                    if (ad != null) {
                        retrievedAds.add(ad)
                    }
                }
                retrievedAds
            }
        }

        addJobs.forEach { it.await() }
        val allRetrievedAds = getJobs.flatMap { it.await() }

        allRetrievedAds.forEach { ad ->
            assertTrue(ad.used)
        }
        assertTrue(adCacheManager.adsSize >= 0)
    }

    @Test
    fun `GIVEN concurrent clean operations WHEN multiple clean calls THEN operations are thread safe`() = runTest {
        repeat(50) {
            adCacheManager.add(createValidInterstitialAd())
        }

        val cleanJobs = (1..5).map {
            async {
                adCacheManager.clean()
            }
        }

        cleanJobs.forEach { it.await() }

        assertEquals(0, adCacheManager.adsSize)
    }

    @Test
    fun `GIVEN concurrent add and clean operations WHEN mixed operations THEN no deadlock occurs`() = runTest {
        val addJob = async {
            repeat(50) {
                adCacheManager.add(createValidInterstitialAd())
                delay(1)
            }
        }

        val cleanJob = async {
            delay(25)
            adCacheManager.clean()
        }

        val getJob = async {
            repeat(10) {
                adCacheManager.get<Ad.Interstitial>()
                delay(2)
            }
        }

        addJob.await()
        cleanJob.await()
        getJob.await()

        // After clean is called, cache should be empty
        assertTrue(adCacheManager.adsSize >= 0)
    }

    @Test
    fun `GIVEN high contention scenario WHEN many concurrent operations THEN system remains stable`() = runBlocking {
        val numberOfOperations = 1000
        val jobs = mutableListOf<kotlinx.coroutines.Job>()

        repeat(numberOfOperations) { i ->
            when (i % 4) {
                0 -> jobs.add(launch { adCacheManager.add(createValidInterstitialAd()) })
                1 -> jobs.add(launch { adCacheManager.get<Ad.Interstitial>() })
                2 -> jobs.add(launch { adCacheManager.adsSize })
                3 -> if (i > numberOfOperations / 2) jobs.add(launch { adCacheManager.clean() })
            }
        }

        jobs.forEach { it.join() }

        assertTrue(adCacheManager.adsSize >= 0)
    }

    @Test
    fun `GIVEN ads with different types WHEN get specific type THEN only returns matching type`() {
        val interstitialAd = createValidInterstitialAd()
        adCacheManager.add(interstitialAd)

        val retrievedAd = adCacheManager.get<Ad.Interstitial>()

        assertNotNull(retrievedAd)
        assertEquals(interstitialAd, retrievedAd)
    }

    @Test
    fun `GIVEN cache manager WHEN adsSize accessed concurrently THEN returns consistent values`() = runTest {
        val jobs = (1..20).map { threadId ->
            async {
                val sizes = mutableListOf<Int>()
                repeat(50) {
                    if (threadId % 2 == 0) {
                        adCacheManager.add(createValidInterstitialAd())
                    }
                    sizes.add(adCacheManager.adsSize)
                }
                sizes
            }
        }

        val allSizes = jobs.flatMap { it.await() }

        allSizes.forEach { size ->
            assertTrue(size >= 0)
        }
    }

    private fun createValidInterstitialAd(): Ad.Interstitial {
        return Ad.Interstitial(
            interstitialAd = mockInterstitialAd,
            validUntil = System.currentTimeMillis() + 60000 // Valid for 1 minute
        )
    }

    private fun createUniqueInterstitialAd(index: Int): Ad.Interstitial {
        return Ad.Interstitial(
            interstitialAd = mockk(relaxed = true), // Each ad gets a unique mock
            validUntil = System.currentTimeMillis() + 60000 + index // Unique timestamp
        )
    }

    private fun createExpiredInterstitialAd(): Ad.Interstitial {
        return Ad.Interstitial(
            interstitialAd = mockInterstitialAd,
            validUntil = System.currentTimeMillis() - 1000 // Expired 1 second ago
        )
    }
}
