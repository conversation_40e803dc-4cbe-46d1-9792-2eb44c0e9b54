package co.thewordlab.luzia.core.ads.service

import co.thewordlab.luzia.core.ads.analytics.AdClicked
import co.thewordlab.luzia.core.ads.analytics.CreativeSummaryReceived
import co.thewordlab.luzia.core.ads.model.AdViewData
import co.thewordlab.luzia.core.ads.model.CreativeSummary
import co.thewordlab.luzia.core.ads.stub.FakeAdLoaderFactory
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.ResponseInfo
import com.google.android.gms.ads.nativead.NativeAd
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import junit.framework.TestCase.assertNull
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Assert
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertIs

@ExperimentalCoroutinesApi
class AdLoaderServiceImplTest {

    private val fakeAdLoaderFactory = FakeAdLoaderFactory()
    private val mockAnalytics = mockk<Analytics>(relaxed = true)
    private val featureFlagManager: FeatureFlagManager = mockk(relaxed = true)
    private val mockMoshi = mockk<Moshi>(relaxed = true)
    private val mockJsonAdapter = mockk<JsonAdapter<CreativeSummary>>(relaxed = true)

    private val adLoaderService = AdLoaderServiceImpl(
        fakeAdLoaderFactory,
        mockAnalytics,
        featureFlagManager,
        mockMoshi
    )

    init {
        every { mockMoshi.adapter(CreativeSummary::class.java) } returns mockJsonAdapter
        every { mockJsonAdapter.fromJson(any<String>()) } returns null
    }

    private fun setupFeatureFlagMock() {
        coEvery {
            featureFlagManager.getString(FeatureFlag.ContextualAdsEnabled, "adUnit", any())
        } returns co.thewordlab.luzia.core.ads.BuildConfig.NATIVE_AD_UNIT_ID
    }

    @Test
    fun `GIVEN ad loads successfully WHEN loadNativeAd THEN returns success and maps ad data correctly`() = runTest {
        val keywords = listOf("test", "keywords")
        fakeAdLoaderFactory.shouldReturnSuccess = true
        setupFeatureFlagMock()

        val result = adLoaderService.loadNativeAd(keywords)

        assertIs<ResultOf.Success<AdViewData.NativeAd, AdLoadError>>(result)
        assertEquals("Test Headline", result.data.headline)
        assertEquals("Test Body", result.data.body)
        assertEquals("Test CTA", result.data.callToAction)
        assertEquals("Test Advertiser", result.data.advertiser)
        assertEquals("https://test-icon.com/icon.png", result.data.iconUriString)
        assertEquals("Test Store", result.data.store)
        assertEquals(1, fakeAdLoaderFactory.loadNativeAdCallCount)
        assertEquals(keywords, fakeAdLoaderFactory.lastKeywords)
    }

    @Test
    fun `GIVEN no creative summary WHEN loadNativeAd THEN does not track CreativeSummaryReceived`() = runTest {
        val keywords = listOf("test", "keywords")
        fakeAdLoaderFactory.shouldReturnSuccess = true
        setupFeatureFlagMock()

        val result = adLoaderService.loadNativeAd(keywords)

        assertIs<ResultOf.Success<AdViewData.NativeAd, AdLoadError>>(result)
        verify(exactly = 0) { mockAnalytics.trackEvent(any<CreativeSummaryReceived>()) }
    }

    @Test
    fun `tracks creative summary analytics when display is enabled`() = runTest {
        val keywords = listOf("test", "keywords")
        val creativeSummaryJson = """{"creative_summary":"Test summary","display_raw_summary":true}"""
        val mockResponseInfo = mockk<ResponseInfo> {
            every { responseExtras?.getString(AdLoaderServiceImpl.CREATIVE_SUMMARY) } returns creativeSummaryJson
        }
        val mockCreativeSummary = CreativeSummary("Test summary", true)

        fakeAdLoaderFactory.shouldReturnSuccess = true
        fakeAdLoaderFactory.mockNativeAdWithResponseInfo(mockResponseInfo)
        every { mockJsonAdapter.fromJson(creativeSummaryJson) } returns mockCreativeSummary
        setupFeatureFlagMock()

        val result = adLoaderService.loadNativeAd(keywords)

        assertIs<ResultOf.Success<AdViewData.NativeAd, AdLoadError>>(result)
        verify { mockAnalytics.trackEvent(CreativeSummaryReceived("Test summary", true)) }
        assertEquals("Test summary", result.data.modifiedDescription)
        assertEquals("Test summary", result.data.creativeSummary)
    }

    @Test
    fun `tracks creative summary analytics when display is disabled`() = runTest {
        val keywords = listOf("test", "keywords")
        val creativeSummaryJson = """{"creative_summary":"Test summary","display_raw_summary":false}"""
        val mockResponseInfo = mockk<ResponseInfo> {
            every { responseExtras?.getString(AdLoaderServiceImpl.CREATIVE_SUMMARY) } returns creativeSummaryJson
        }
        val mockCreativeSummary = CreativeSummary("Test summary", false)

        fakeAdLoaderFactory.shouldReturnSuccess = true
        fakeAdLoaderFactory.mockNativeAdWithResponseInfo(mockResponseInfo)
        every { mockJsonAdapter.fromJson(creativeSummaryJson) } returns mockCreativeSummary
        setupFeatureFlagMock()

        val result = adLoaderService.loadNativeAd(keywords)

        assertIs<ResultOf.Success<AdViewData.NativeAd, AdLoadError>>(result)
        verify { mockAnalytics.trackEvent(CreativeSummaryReceived("Test summary", false)) }
        assertNull(result.data.modifiedDescription)
        assertEquals("Test summary", result.data.creativeSummary)
    }

    @Test
    fun `does not track creative summary and reports exception for invalid JSON`() = runTest {
        val keywords = listOf("test", "keywords")
        val invalidJson = """{"invalid": json}"""
        val mockResponseInfo = mockk<ResponseInfo> {
            every { responseExtras?.getString(AdLoaderServiceImpl.CREATIVE_SUMMARY) } returns invalidJson
        }

        fakeAdLoaderFactory.shouldReturnSuccess = true
        fakeAdLoaderFactory.mockNativeAdWithResponseInfo(mockResponseInfo)
        every { mockJsonAdapter.fromJson(invalidJson) } throws com.squareup.moshi.JsonDataException("Invalid JSON")
        setupFeatureFlagMock()

        val result = adLoaderService.loadNativeAd(keywords)

        assertIs<ResultOf.Success<AdViewData.NativeAd, AdLoadError>>(result)
        verify(exactly = 0) { mockAnalytics.trackEvent(any<CreativeSummaryReceived>()) }
        verify { mockAnalytics.reportException("Failed to parse creative summary JSON: Invalid JSON") }
        assertNull(result.data.modifiedDescription)
        assertNull(result.data.creativeSummary)
    }

    @Test
    fun `reports encoding exception for JSON encoding error`() = runTest {
        val keywords = listOf("test", "keywords")
        val invalidJson = """{"creative_summary":"test"}"""
        val mockResponseInfo = mockk<ResponseInfo> {
            every { responseExtras?.getString(AdLoaderServiceImpl.CREATIVE_SUMMARY) } returns invalidJson
        }

        fakeAdLoaderFactory.shouldReturnSuccess = true
        fakeAdLoaderFactory.mockNativeAdWithResponseInfo(mockResponseInfo)
        val encodingException = com.squareup.moshi.JsonEncodingException("Encoding error")
        every { mockJsonAdapter.fromJson(invalidJson) } throws encodingException
        setupFeatureFlagMock()

        adLoaderService.loadNativeAd(keywords)

        verify { mockAnalytics.reportException("JSON encoding error for creative summary: Encoding error") }
    }

    @Test
    fun `reports IO exception for IO error`() = runTest {
        val keywords = listOf("test", "keywords")
        val invalidJson = """{"creative_summary":"test"}"""
        val mockResponseInfo = mockk<ResponseInfo> {
            every { responseExtras?.getString(AdLoaderServiceImpl.CREATIVE_SUMMARY) } returns invalidJson
        }

        fakeAdLoaderFactory.shouldReturnSuccess = true
        fakeAdLoaderFactory.mockNativeAdWithResponseInfo(mockResponseInfo)
        every { mockJsonAdapter.fromJson(invalidJson) } throws java.io.IOException("IO error")
        setupFeatureFlagMock()

        adLoaderService.loadNativeAd(keywords)

        verify { mockAnalytics.reportException("IO error while parsing creative summary: IO error") }
    }

    @Test
    fun `returns failure with error details when ad load fails`() = runTest {
        val keywords = listOf("test", "keywords")
        val mockLoadAdError = mockk<LoadAdError> {
            every { code } returns 3
            every { message } returns "Network error"
        }
        fakeAdLoaderFactory.shouldReturnSuccess = false
        fakeAdLoaderFactory.mockLoadAdError = mockLoadAdError
        setupFeatureFlagMock()

        val result = adLoaderService.loadNativeAd(keywords)

        assertIs<ResultOf.Failure<AdViewData.NativeAd, AdLoadError>>(result)
        assertEquals(3, result.error.code)
        assertEquals("Network error", result.error.message)
        assertEquals(1, fakeAdLoaderFactory.loadNativeAdCallCount)
    }

    @Test
    fun `destroys previous ad before loading new one`() = runTest {
        val keywords = listOf("test")
        fakeAdLoaderFactory.shouldReturnSuccess = true
        setupFeatureFlagMock()

        val firstResult = adLoaderService.loadNativeAd(keywords)
        assertIs<ResultOf.Success<AdViewData.NativeAd, AdLoadError>>(firstResult)

        val secondResult = adLoaderService.loadNativeAd(keywords)

        assertIs<ResultOf.Success<AdViewData.NativeAd, AdLoadError>>(secondResult)
        assertEquals(2, fakeAdLoaderFactory.loadNativeAdCallCount)
    }

    @Test
    fun `tracks AdClicked analytics event when ad is clicked`() = runTest {
        val keywords = listOf("test")
        fakeAdLoaderFactory.shouldReturnSuccess = true
        setupFeatureFlagMock()

        val result = adLoaderService.loadNativeAd(keywords)
        assertIs<ResultOf.Success<AdViewData.NativeAd, AdLoadError>>(result)

        fakeAdLoaderFactory.simulateAdClick()

        verify { mockAnalytics.trackEvent(AdClicked) }
    }

    @Test
    fun `destroys matching ad when called with valid ad data`() = runTest {
        val keywords = listOf("test")
        fakeAdLoaderFactory.shouldReturnSuccess = true
        setupFeatureFlagMock()

        val result = adLoaderService.loadNativeAd(keywords)
        assertIs<ResultOf.Success<AdViewData.NativeAd, AdLoadError>>(result)
        val adData = result.data

        adLoaderService.destroyAd(adData)

        // Verify ad is destroyed by checking that the nativeAd.destroy() was called
        // This is implicitly tested by the mock behavior
    }

    @Test
    fun `handles null ad data gracefully without error`() {
        val adData: AdViewData.NativeAd? = null

        adLoaderService.destroyAd(adData) // Should not throw exception
    }

    @Test
    fun `does not destroy current ad when called with different ad data`() = runTest {
        val keywords = listOf("test")
        fakeAdLoaderFactory.shouldReturnSuccess = true
        setupFeatureFlagMock()

        val result = adLoaderService.loadNativeAd(keywords)
        assertIs<ResultOf.Success<AdViewData.NativeAd, AdLoadError>>(result)

        val differentAdData = AdViewData.NativeAd(
            headline = "Different",
            body = "Different",
            callToAction = "Different",
            advertiser = "Different",
            iconUriString = "Different",
            store = "Different",
            adObject = mockk<NativeAd>(relaxed = true)
        )

        adLoaderService.destroyAd(differentAdData)

        // Current ad should still be intact, next load should still work
        setupFeatureFlagMock() // Setup for second call
        val secondResult = adLoaderService.loadNativeAd(keywords)
        assertIs<ResultOf.Success<AdViewData.NativeAd, AdLoadError>>(secondResult)
    }

    @Test
    fun `passes empty keywords to factory when keywords list is empty`() = runTest {
        val emptyKeywords = emptyList<String>()
        fakeAdLoaderFactory.shouldReturnSuccess = true
        setupFeatureFlagMock()

        val result = adLoaderService.loadNativeAd(emptyKeywords)

        assertIs<ResultOf.Success<AdViewData.NativeAd, AdLoadError>>(result)
        assertEquals(emptyKeywords, fakeAdLoaderFactory.lastKeywords)
    }

    @Test
    fun `passes correct BuildConfig ad unit ID to factory`() = runTest {
        val keywords = listOf("test")
        fakeAdLoaderFactory.shouldReturnSuccess = true
        setupFeatureFlagMock()

        val result = adLoaderService.loadNativeAd(keywords)

        assertIs<ResultOf.Success<AdViewData.NativeAd, AdLoadError>>(result)
        assertEquals(co.thewordlab.luzia.core.ads.BuildConfig.NATIVE_AD_UNIT_ID, fakeAdLoaderFactory.lastAdUnitId)
    }

    @Test
    fun `Given feature flag manager When getString called Then delegates to feature flag manager`() = runTest {
        val expectedValue = "test-ad-unit-id"
        val defaultValue = "default-ad-unit"
        coEvery {
            featureFlagManager.getString(FeatureFlag.ContextualAdsEnabled, "adUnit", defaultValue)
        } returns expectedValue

        val result = featureFlagManager.getString(FeatureFlag.ContextualAdsEnabled, "adUnit", defaultValue)

        Assert.assertEquals(expectedValue, result)
    }

    @Test
    fun `Given feature flag manager returns default When getString called Then returns default value`() = runTest {
        val defaultValue = "default-ad-unit"
        coEvery {
            featureFlagManager.getString(
                FeatureFlag.ContextualAdsEnabled,
                "adUnit",
                defaultValue
            )
        } returns defaultValue

        val result = featureFlagManager.getString(FeatureFlag.ContextualAdsEnabled, "adUnit", defaultValue)

        Assert.assertEquals(defaultValue, result)
    }
}
