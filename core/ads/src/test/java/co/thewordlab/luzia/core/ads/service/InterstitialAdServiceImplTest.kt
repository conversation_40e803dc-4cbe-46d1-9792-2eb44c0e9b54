package co.thewordlab.luzia.core.ads.service

import android.app.Activity
import co.thewordlab.luzia.core.ads.BuildConfig
import co.thewordlab.luzia.core.ads.analytics.InterstitialAdDismissed
import co.thewordlab.luzia.core.ads.analytics.InterstitialAdDisplayed
import co.thewordlab.luzia.core.ads.analytics.InterstitialAdFailedToShow
import co.thewordlab.luzia.core.ads.analytics.InterstitialAdPreloaded
import co.thewordlab.luzia.core.ads.cache.AdCacheManager
import co.thewordlab.luzia.core.ads.model.Ad
import co.thewordlab.luzia.core.ads.stub.FakeAdLoaderFactory
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.testing.dispatchers.MainDispatcherRule
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.interstitial.InterstitialAd
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class InterstitialAdServiceImplTest {

    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()

    private lateinit var fakeAdLoaderFactory: FakeAdLoaderFactory
    private lateinit var adCacheManager: AdCacheManager
    private lateinit var analytics: Analytics
    private lateinit var interstitialAdService: InterstitialAdServiceImpl
    private lateinit var mockActivity: Activity

    @Before
    fun setUp() {
        fakeAdLoaderFactory = FakeAdLoaderFactory()
        adCacheManager = AdCacheManager()
        analytics = mockk(relaxed = true)
        mockActivity = mockk(relaxed = true)
        interstitialAdService =
            InterstitialAdServiceImpl(mockActivity, fakeAdLoaderFactory, adCacheManager, analytics)
    }

    @Test
    fun `GIVEN successful ad loading WHEN preLoad THEN returns success result and ad is cached`() =
        runTest {
            fakeAdLoaderFactory.shouldReturnSuccess = true

            val result = interstitialAdService.preLoad()

            assertTrue(result.isSuccess)
            assertEquals(1, fakeAdLoaderFactory.loadInterstitialAdCallCount)
            assertEquals(
                BuildConfig.INTERSTITIAL_AD_UNIT_ID,
                fakeAdLoaderFactory.lastInterstitialAdUnitId
            )
            assertEquals(1, adCacheManager.adsSize)

            val cachedAd = adCacheManager.get<Ad.Interstitial>()
            assertNotNull(cachedAd)
            assertTrue(cachedAd.validUntil > System.currentTimeMillis())
            assertTrue(cachedAd.used)

            mainDispatcherRule.advanceUntilIdle()
            verify { analytics.trackEvent(InterstitialAdPreloaded) }
        }

    @Test
    fun `GIVEN ad loading failure WHEN preLoad THEN returns success but no ad cached`() = runTest {
        fakeAdLoaderFactory.shouldReturnSuccess = false

        val result = interstitialAdService.preLoad()

        assertTrue(result.isSuccess) // preLoad itself succeeds, but callback won't be called
        assertEquals(1, fakeAdLoaderFactory.loadInterstitialAdCallCount)
        assertEquals(
            BuildConfig.INTERSTITIAL_AD_UNIT_ID,
            fakeAdLoaderFactory.lastInterstitialAdUnitId
        )
        assertEquals(0, adCacheManager.adsSize)
    }

    @Test
    fun `GIVEN multiple preLoad calls WHEN executed THEN each returns success`() = runTest {
        fakeAdLoaderFactory.shouldReturnSuccess = true

        val result1 = interstitialAdService.preLoad()
        val result2 = interstitialAdService.preLoad()
        val result3 = interstitialAdService.preLoad()

        assertTrue(result1.isSuccess)
        assertTrue(result2.isSuccess)
        assertTrue(result3.isSuccess)
        assertEquals(3, fakeAdLoaderFactory.loadInterstitialAdCallCount)
        assertEquals(3, adCacheManager.adsSize)
    }

    @Test
    fun `GIVEN cached ad WHEN show THEN returns success and ad is shown`() = runTest {
        val mockInterstitialAd = mockk<InterstitialAd>(relaxed = true)
        val callbackSlot = slot<FullScreenContentCallback>()
        every { mockInterstitialAd.fullScreenContentCallback = capture(callbackSlot) } returns Unit

        val cachedAd = Ad.Interstitial(
            interstitialAd = mockInterstitialAd,
            validUntil = System.currentTimeMillis() + 60000
        )
        adCacheManager.add(cachedAd)

        val result = interstitialAdService.show()

        assertTrue(result.isSuccess)
        verify(exactly = 1) { mockInterstitialAd.show(mockActivity) }
        val remainingAd = adCacheManager.get<Ad.Interstitial>()
        assertNull(remainingAd)

        // Simulate ad shown
        callbackSlot.captured.onAdShowedFullScreenContent()
        verify { analytics.trackEvent(InterstitialAdDisplayed) }

        callbackSlot.captured.onAdDismissedFullScreenContent()
        verify { analytics.trackEvent(InterstitialAdDismissed) }
    }

    @Test
    fun `GIVEN no cached ad WHEN show THEN returns success but no ad shown`() = runTest {
        assertEquals(0, adCacheManager.adsSize)

        val result = interstitialAdService.show()

        assertTrue(result.isSuccess) // show succeeds even when no ad available
        assertEquals(0, adCacheManager.adsSize)
    }

    @Test
    fun `GIVEN expired cached ad WHEN show THEN returns success and cleans up expired ad`() =
        runTest {
            val mockInterstitialAd = mockk<InterstitialAd>(relaxed = true)
            val expiredAd = Ad.Interstitial(
                interstitialAd = mockInterstitialAd,
                validUntil = System.currentTimeMillis() - 1000 // Expired 1 second ago
            )
            adCacheManager.add(expiredAd)

            val result = interstitialAdService.show()

            assertTrue(result.isSuccess)
            verify(exactly = 0) { mockInterstitialAd.show(any()) }
            assertEquals(0, adCacheManager.adsSize)
        }

    @Test
    fun `GIVEN used cached ad WHEN show THEN returns success but used ad not shown`() = runTest {
        val mockInterstitialAd = mockk<InterstitialAd>(relaxed = true)
        val usedAd = Ad.Interstitial(
            interstitialAd = mockInterstitialAd,
            validUntil = System.currentTimeMillis() + 60000
        )
        usedAd.used = true
        adCacheManager.add(usedAd)

        val result = interstitialAdService.show()

        assertTrue(result.isSuccess)
        verify(exactly = 0) { mockInterstitialAd.show(any()) }
    }

    @Test
    fun `GIVEN multiple cached ads WHEN show THEN returns success and shows first unused ad`() =
        runTest {
            val mockInterstitialAd1 = mockk<InterstitialAd>(relaxed = true)
            val mockInterstitialAd2 = mockk<InterstitialAd>(relaxed = true)

            val usedAd = Ad.Interstitial(
                interstitialAd = mockInterstitialAd1,
                validUntil = System.currentTimeMillis() + 60000
            )
            usedAd.used = true

            val availableAd = Ad.Interstitial(
                interstitialAd = mockInterstitialAd2,
                validUntil = System.currentTimeMillis() + 60000
            )

            adCacheManager.add(usedAd)
            adCacheManager.add(availableAd)

            val result = interstitialAdService.show()

            assertTrue(result.isSuccess)
            verify(exactly = 0) { mockInterstitialAd1.show(any()) }
            verify(exactly = 1) { mockInterstitialAd2.show(mockActivity) }
        }

    @Test
    fun `GIVEN successful ad load callback WHEN onAdLoaded called THEN caches ad with 1 hour expiration`() =
        runTest {
            val timeBeforeLoad = System.currentTimeMillis()
            fakeAdLoaderFactory.shouldReturnSuccess = true

            val result = interstitialAdService.preLoad()

            assertTrue(result.isSuccess)
            val cachedAd = adCacheManager.get<Ad.Interstitial>()
            assertNotNull(cachedAd)

            val expectedMinExpiration = timeBeforeLoad + (60 * 60 * 1000) // 1 hour in ms
            val expectedMaxExpiration = System.currentTimeMillis() + (60 * 60 * 1000)

            assertTrue(cachedAd.validUntil >= expectedMinExpiration)
            assertTrue(cachedAd.validUntil <= expectedMaxExpiration)
        }

    @Test
    fun `GIVEN ad loader factory throws exception WHEN preLoad THEN returns failure result`() =
        runTest {
            val exceptionThrowingFactory = object : AdLoaderFactory {
                override fun loadNativeAd(
                    adUnitId: String,
                    onNativeAdLoaded: (com.google.android.gms.ads.nativead.NativeAd) -> Unit,
                    adListener: com.google.android.gms.ads.AdListener,
                    nativeAdOptions: com.google.android.gms.ads.nativead.NativeAdOptions,
                    keywords: List<String>
                ) = Unit

                override fun loadInterstitialAd(
                    adUnitId: String,
                    adListener: com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback
                ) {
                    error("Ad loading failed")
                }
            }

            val serviceWithFailingFactory =
                InterstitialAdServiceImpl(mockActivity, exceptionThrowingFactory, adCacheManager, analytics)

            val result = serviceWithFailingFactory.preLoad()

            assertTrue(result.isFailure)
            val exception = result.exceptionOrNull()
            assertTrue(exception is IllegalStateException)
            assertTrue(exception?.message?.contains("Ad loading failed") == true)
            assertEquals(0, adCacheManager.adsSize)
        }

    @Test
    fun `GIVEN ad load success after failure WHEN preLoad called again THEN second attempt succeeds`() =
        runTest {
            // First attempt fails (but returns success since preLoad itself succeeds)
            fakeAdLoaderFactory.shouldReturnSuccess = false
            val firstResult = interstitialAdService.preLoad()
            assertTrue(firstResult.isSuccess)
            assertEquals(0, adCacheManager.adsSize)

            // Second attempt succeeds
            fakeAdLoaderFactory.shouldReturnSuccess = true
            val secondResult = interstitialAdService.preLoad()
            assertTrue(secondResult.isSuccess)
            assertEquals(1, adCacheManager.adsSize)

            val cachedAd = adCacheManager.get<Ad.Interstitial>()
            assertNotNull(cachedAd)
        }

    @Test
    fun `GIVEN ad show throws exception WHEN show THEN returns failure result`() = runTest {
        val mockInterstitialAd = mockk<InterstitialAd>(relaxed = true) {
            every { show(any()) } throws RuntimeException("Ad show failed")
        }
        val cachedAd = Ad.Interstitial(
            interstitialAd = mockInterstitialAd,
            validUntil = System.currentTimeMillis() + 60000
        )
        adCacheManager.add(cachedAd)

        val result = interstitialAdService.show()

        assertTrue(result.isFailure)
        val exception = result.exceptionOrNull()
        assertTrue(exception is RuntimeException)
        assertTrue(exception?.message?.contains("Ad show failed") == true)
    }

    @Test
    fun `GIVEN valid ad WHEN show called multiple times THEN subsequent calls return success but no ad`() =
        runTest {
            val mockInterstitialAd = mockk<InterstitialAd>(relaxed = true)
            val cachedAd = Ad.Interstitial(
                interstitialAd = mockInterstitialAd,
                validUntil = System.currentTimeMillis() + 60000
            )
            adCacheManager.add(cachedAd)

            // First call shows the ad
            val result1 = interstitialAdService.show()
            assertTrue(result1.isSuccess)
            verify(exactly = 1) { mockInterstitialAd.show(mockActivity) }

            // Second call should not show any ad (none available)
            val result2 = interstitialAdService.show()
            assertTrue(result2.isSuccess)
            verify(exactly = 1) { mockInterstitialAd.show(mockActivity) } // Still only 1 call
        }

    @Test
    fun `GIVEN ad fails to show WHEN show THEN tracks InterstitialAdFailedToShow event`() =
        runTest {
            val mockInterstitialAd = mockk<InterstitialAd>(relaxed = true)
            val callbackSlot = slot<FullScreenContentCallback>()
            every {
                mockInterstitialAd.fullScreenContentCallback = capture(callbackSlot)
            } returns Unit

            val cachedAd = Ad.Interstitial(
                interstitialAd = mockInterstitialAd,
                validUntil = System.currentTimeMillis() + 60000
            )
            adCacheManager.add(cachedAd)

            val result = interstitialAdService.show()

            assertTrue(result.isSuccess)
            verify(exactly = 1) { mockInterstitialAd.show(mockActivity) }

            // Simulate ad failed to show
            val mockAdError = mockk<com.google.android.gms.ads.AdError>(relaxed = true)
            callbackSlot.captured.onAdFailedToShowFullScreenContent(mockAdError)
            verify { analytics.trackEvent(InterstitialAdFailedToShow) }
        }

    @Test
    fun `GIVEN ad shown WHEN onAdDismissedFullScreenContent THEN clears callback to prevent memory leak`() =
        runTest {
            val mockInterstitialAd = mockk<InterstitialAd>(relaxed = true)
            val callbackSlot = slot<FullScreenContentCallback>()
            every {
                mockInterstitialAd.fullScreenContentCallback = capture(callbackSlot)
            } returns Unit

            val cachedAd = Ad.Interstitial(
                interstitialAd = mockInterstitialAd,
                validUntil = System.currentTimeMillis() + 60000
            )
            adCacheManager.add(cachedAd)

            val result = interstitialAdService.show()

            assertTrue(result.isSuccess)
            verify(exactly = 1) { mockInterstitialAd.show(mockActivity) }

            // Simulate ad dismissed
            callbackSlot.captured.onAdDismissedFullScreenContent()

            verify { analytics.trackEvent(InterstitialAdDismissed) }
            verify { mockInterstitialAd.fullScreenContentCallback = null }
        }

    @Test
    fun `GIVEN ad fails to show WHEN onAdFailedToShowFullScreenContent THEN clears callback`() =
        runTest {
            val mockInterstitialAd = mockk<InterstitialAd>(relaxed = true)
            val callbackSlot = slot<FullScreenContentCallback>()
            every {
                mockInterstitialAd.fullScreenContentCallback = capture(callbackSlot)
            } returns Unit

            val cachedAd = Ad.Interstitial(
                interstitialAd = mockInterstitialAd,
                validUntil = System.currentTimeMillis() + 60000
            )
            adCacheManager.add(cachedAd)

            val result = interstitialAdService.show()

            assertTrue(result.isSuccess)
            verify(exactly = 1) { mockInterstitialAd.show(mockActivity) }

            // Simulate ad failed to show
            val mockAdError = mockk<com.google.android.gms.ads.AdError>(relaxed = true)
            callbackSlot.captured.onAdFailedToShowFullScreenContent(mockAdError)

            verify { analytics.trackEvent(InterstitialAdFailedToShow) }
            verify { mockInterstitialAd.fullScreenContentCallback = null }
        }
}
