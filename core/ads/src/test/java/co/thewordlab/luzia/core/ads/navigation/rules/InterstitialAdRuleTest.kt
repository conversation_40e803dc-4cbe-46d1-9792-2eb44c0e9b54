package co.thewordlab.luzia.core.ads.navigation.rules

import androidx.navigation.NavBackStackEntry
import co.thewordlab.luzia.core.ads.navigation.stub.FakeInterstitialAdService
import co.thewordlab.luzia.core.navigation.common.RouteChecker
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.foundation.testing.dispatchers.MainDispatcherRule
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import kotlin.test.assertEquals

@ExperimentalCoroutinesApi
class InterstitialAdRuleTest {

    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()

    private lateinit var fakeInterstitialAdService: FakeInterstitialAdService
    private lateinit var featureFlagManager: FeatureFlagManager
    private lateinit var interstitialAdRule: InterstitialAdRule

    @Before
    fun setup() {
        fakeInterstitialAdService = FakeInterstitialAdService()
        featureFlagManager = mockk()

        coEvery { featureFlagManager.get<Boolean>(FeatureFlag.InterstitialAdsEnabled) } returns true

        interstitialAdRule = InterstitialAdRule(
            interstitialAdService = fakeInterstitialAdService,
            featureFlagManager = featureFlagManager
        )

        mockkObject(RouteChecker)
    }

    @After
    fun tearDown() {
        unmockkObject(RouteChecker)
    }

    @Test
    fun `GIVEN feature flag disabled WHEN processNavigation THEN no ad actions are taken`() = runTest {
        coEvery { featureFlagManager.get<Boolean>(FeatureFlag.InterstitialAdsEnabled) } returns false
        val ruleWithDisabledFlag = InterstitialAdRule(
            interstitialAdService = fakeInterstitialAdService,
            featureFlagManager = featureFlagManager
        )
        val mockEntry = mockk<NavBackStackEntry>()
        val navigationHistory = listOf(mockEntry)

        ruleWithDisabledFlag.processNavigation(navigationHistory)

        assertEquals(0, fakeInterstitialAdService.showCallCount)
        assertEquals(0, fakeInterstitialAdService.preLoadCallCount)
    }

    @Test
    fun `GIVEN pattern matches show ad pattern WHEN processNavigation THEN shows interstitial ad`() = runTest {
        val homeEntry = mockk<NavBackStackEntry>()
        val chatEntry = mockk<NavBackStackEntry>()
        val homeEntry2 = mockk<NavBackStackEntry>()
        val navigationHistory = listOf(homeEntry, chatEntry, homeEntry2)

        every {
            RouteChecker.matchesEntryPattern(
                navigationHistory,
                listOf(
                    RouteChecker::isHomeRouteEntry,
                    RouteChecker::isChatDetailRouteEntry,
                    RouteChecker::isHomeRouteEntry
                )
            )
        } returns true

        every {
            RouteChecker.matchesEntryPattern(
                navigationHistory,
                listOf(
                    RouteChecker::isHomeRouteEntry,
                    RouteChecker::isChatDetailRouteEntry
                )
            )
        } returns false

        interstitialAdRule.processNavigation(navigationHistory)

        assertEquals(1, fakeInterstitialAdService.showCallCount)
        assertEquals(0, fakeInterstitialAdService.preLoadCallCount)
    }

    @Test
    fun `GIVEN pattern matches preload pattern WHEN processNavigation THEN preloads interstitial ad`() = runTest {
        val homeEntry = mockk<NavBackStackEntry>()
        val chatEntry = mockk<NavBackStackEntry>()
        val navigationHistory = listOf(homeEntry, chatEntry)

        every {
            RouteChecker.matchesEntryPattern(
                navigationHistory,
                listOf(
                    RouteChecker::isHomeRouteEntry,
                    RouteChecker::isChatDetailRouteEntry
                )
            )
        } returns true

        every {
            RouteChecker.matchesEntryPattern(
                navigationHistory,
                listOf(
                    RouteChecker::isHomeRouteEntry,
                    RouteChecker::isChatDetailRouteEntry,
                    RouteChecker::isHomeRouteEntry
                )
            )
        } returns false

        interstitialAdRule.processNavigation(navigationHistory)

        assertEquals(0, fakeInterstitialAdService.showCallCount)
        assertEquals(1, fakeInterstitialAdService.preLoadCallCount)
    }

    @Test
    fun `GIVEN pattern matches both patterns WHEN processNavigation THEN shows ad takes precedence`() = runTest {
        val homeEntry = mockk<NavBackStackEntry>()
        val chatEntry = mockk<NavBackStackEntry>()
        val homeEntry2 = mockk<NavBackStackEntry>()
        val navigationHistory = listOf(homeEntry, chatEntry, homeEntry2)

        every {
            RouteChecker.matchesEntryPattern(
                navigationHistory,
                listOf(
                    RouteChecker::isHomeRouteEntry,
                    RouteChecker::isChatDetailRouteEntry,
                    RouteChecker::isHomeRouteEntry
                )
            )
        } returns true

        every {
            RouteChecker.matchesEntryPattern(
                navigationHistory,
                listOf(
                    RouteChecker::isHomeRouteEntry,
                    RouteChecker::isChatDetailRouteEntry
                )
            )
        } returns true

        interstitialAdRule.processNavigation(navigationHistory)

        assertEquals(1, fakeInterstitialAdService.showCallCount)
        assertEquals(0, fakeInterstitialAdService.preLoadCallCount)
    }

    @Test
    fun `GIVEN pattern matches neither pattern WHEN processNavigation THEN no ad actions are taken`() = runTest {
        val otherEntry = mockk<NavBackStackEntry>()
        val navigationHistory = listOf(otherEntry)

        every {
            RouteChecker.matchesEntryPattern(
                navigationHistory,
                listOf(
                    RouteChecker::isHomeRouteEntry,
                    RouteChecker::isChatDetailRouteEntry,
                    RouteChecker::isHomeRouteEntry
                )
            )
        } returns false

        every {
            RouteChecker.matchesEntryPattern(
                navigationHistory,
                listOf(
                    RouteChecker::isHomeRouteEntry,
                    RouteChecker::isChatDetailRouteEntry
                )
            )
        } returns false

        interstitialAdRule.processNavigation(navigationHistory)

        assertEquals(0, fakeInterstitialAdService.showCallCount)
        assertEquals(0, fakeInterstitialAdService.preLoadCallCount)
    }

    @Test
    fun `GIVEN ad service fails WHEN showing ad THEN exception is handled gracefully`() = runTest {
        val homeEntry = mockk<NavBackStackEntry>()
        val chatEntry = mockk<NavBackStackEntry>()
        val homeEntry2 = mockk<NavBackStackEntry>()
        val navigationHistory = listOf(homeEntry, chatEntry, homeEntry2)

        fakeInterstitialAdService.shouldReturnSuccess = false
        fakeInterstitialAdService.exceptionToThrow = RuntimeException("Ad show failed")

        every {
            RouteChecker.matchesEntryPattern(
                navigationHistory,
                listOf(
                    RouteChecker::isHomeRouteEntry,
                    RouteChecker::isChatDetailRouteEntry,
                    RouteChecker::isHomeRouteEntry
                )
            )
        } returns true

        every {
            RouteChecker.matchesEntryPattern(
                navigationHistory,
                listOf(
                    RouteChecker::isHomeRouteEntry,
                    RouteChecker::isChatDetailRouteEntry
                )
            )
        } returns false

        interstitialAdRule.processNavigation(navigationHistory)

        assertEquals(1, fakeInterstitialAdService.showCallCount)
    }

    @Test
    fun `GIVEN ad service fails WHEN preloading ad THEN exception is handled gracefully`() = runTest {
        val homeEntry = mockk<NavBackStackEntry>()
        val chatEntry = mockk<NavBackStackEntry>()
        val navigationHistory = listOf(homeEntry, chatEntry)

        fakeInterstitialAdService.shouldReturnSuccess = false
        fakeInterstitialAdService.exceptionToThrow = RuntimeException("Ad preload failed")

        every {
            RouteChecker.matchesEntryPattern(
                navigationHistory,
                listOf(
                    RouteChecker::isHomeRouteEntry,
                    RouteChecker::isChatDetailRouteEntry
                )
            )
        } returns true

        every {
            RouteChecker.matchesEntryPattern(
                navigationHistory,
                listOf(
                    RouteChecker::isHomeRouteEntry,
                    RouteChecker::isChatDetailRouteEntry,
                    RouteChecker::isHomeRouteEntry
                )
            )
        } returns false

        interstitialAdRule.processNavigation(navigationHistory)

        assertEquals(1, fakeInterstitialAdService.preLoadCallCount)
        assertEquals(0, fakeInterstitialAdService.showCallCount)
    }

    @Test
    fun `GIVEN rule name WHEN accessed THEN returns correct name`() {
        assertEquals("InterstitialAdRule", interstitialAdRule.ruleName)
    }
}
