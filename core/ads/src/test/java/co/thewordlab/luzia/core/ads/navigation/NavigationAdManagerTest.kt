package co.thewordlab.luzia.core.ads.navigation

import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.NavBackStackEntry
import co.thewordlab.luzia.core.ads.navigation.stub.FakeNavigationAdRule
import co.thewordlab.luzia.core.navigation.observer.NavigationRouteRecorder
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.testing.dispatchers.MainDispatcherRule
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Rule
import org.junit.Test

@ExperimentalCoroutinesApi
class NavigationAdManagerTest {

    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()

    private lateinit var navigationAdManager: NavigationAdManager
    private val activity: AppCompatActivity = mockk(relaxed = true)
    private val navigationRouteRecorder: NavigationRouteRecorder = mockk(relaxed = true)
    private val adRule1: FakeNavigationAdRule = FakeNavigationAdRule("rule1")
    private val adRule2: FakeNavigationAdRule = FakeNavigationAdRule("rule2")
    private val analytics: Analytics = mockk(relaxed = true)

    @Before
    fun setUp() {
        navigationAdManager = NavigationAdManager(
            activity = activity,
            navigationRouteRecorder = navigationRouteRecorder,
            adRules = setOf(adRule1, adRule2),
            analytics = analytics
        )
    }

    @Test
    fun `GIVEN navigation history WHEN processNavigationRules called THEN all rules process navigation`() = runTest {
        val navBackStackEntry: NavBackStackEntry = mockk(relaxed = true)
        val navigationHistory = listOf(navBackStackEntry)

        navigationAdManager.processNavigationRules(navigationHistory)

        assertEquals(1, adRule1.processNavigationCallCount)
        assertEquals(1, adRule2.processNavigationCallCount)
        assertEquals(navigationHistory, adRule1.lastNavigationHistory)
        assertEquals(navigationHistory, adRule2.lastNavigationHistory)
    }

    @Test
    fun `GIVEN rule throws exception WHEN processNavigationRules called THEN exception is reported`() = runTest {
        val navBackStackEntry: NavBackStackEntry = mockk(relaxed = true)
        val navigationHistory = listOf(navBackStackEntry)
        val exception = RuntimeException("Rule exception")

        adRule1.shouldThrowException = true
        adRule1.exceptionToThrow = exception

        navigationAdManager.processNavigationRules(navigationHistory)

        assertEquals(1, adRule1.processNavigationCallCount)
        assertEquals(1, adRule2.processNavigationCallCount)
        assertEquals(navigationHistory, adRule1.lastNavigationHistory)
        assertEquals(navigationHistory, adRule2.lastNavigationHistory)
        verify {
            analytics.reportException(
                "NavigationAdRule ${adRule1.ruleName} execution failed",
                exception
            )
        }
    }
}
