package co.thewordlab.luzia.core.ads.navigation.stub

import androidx.navigation.NavBackStackEntry
import co.thewordlab.luzia.core.ads.navigation.NavigationAdRule

class FakeNavigationAdRule(
    override val ruleName: String = "FakeRule"
) : NavigationAdRule {

    var processNavigationCallCount: Int = 0
        private set
    var lastNavigationHistory: List<NavBackStackEntry> = emptyList()
        private set
    var shouldThrowException: Boolean = false
    var exceptionToThrow: Exception = RuntimeException("Test exception")

    override suspend fun processNavigation(
        navigationHistory: List<NavBackStackEntry>,
    ) {
        processNavigationCallCount++
        lastNavigationHistory = navigationHistory

        if (shouldThrowException) {
            throw exceptionToThrow
        }
    }

    fun reset() {
        processNavigationCallCount = 0
        lastNavigationHistory = emptyList()
        shouldThrowException = false
        exceptionToThrow = RuntimeException("Test exception")
    }
}
