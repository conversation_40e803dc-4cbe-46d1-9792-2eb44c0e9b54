plugins {
    alias(libs.plugins.luzia.android.feature)
}

android {
    namespace = "co.thewordlab.luzia.core.ads"

    buildTypes {
        debug {
            buildConfigField(
                "String",
                "NATIVE_AD_UNIT_ID",
                "\"ca-app-pub-4550997302809394/1081599027\""
            )
            buildConfigField(
                "String",
                "BANNER_AD_UNIT_ID",
                "\"ca-app-pub-3940256099942544/9214589741\""
            )
            buildConfigField(
                "String",
                "INTERSTITIAL_AD_UNIT_ID",
                "\"ca-app-pub-3940256099942544/1033173712\""
            )
            buildConfigField(
                "String",
                "KOAH_PUBLISHER_ID",
                "\"861835d2-59ed-4d58-9897-a3374aa72185\""
            )
        }
        release {
            buildConfigField(
                "String",
                "NATIVE_AD_UNIT_ID",
                "\"ca-app-pub-4550997302809394/3062080909\""
            )
            buildConfigField(
                "String",
                "BANNER_AD_UNIT_ID",
                "\"ca-app-pub-4550997302809394/3103468572\""
            )
            buildConfigField(
                "String",
                "INTERSTITIAL_AD_UNIT_ID",
                "\"ca-app-pub-4550997302809394/5788339648\""
            )
            buildConfigField(
                "String",
                "KOAH_PUBLISHER_ID",
                "\"cf4bb02e-11cc-4fbb-a441-9bdc4d7dffcf\""
            )
        }
    }
    
    testOptions {
        unitTests {
            isIncludeAndroidResources = true
        }
    }
}

dependencies {
    implementation(projects.foundation.designSystem)
    implementation(projects.foundation.localization)
    implementation(projects.foundation.persistence)
    implementation(projects.foundation.networking)
    implementation(projects.foundation.common)
    implementation(projects.foundation.config)
    implementation(projects.foundation.architectureSystem)
    implementation(projects.foundation.messages)
    implementation(projects.foundation.analytics)
    implementation(projects.core.navigation)
    implementation(libs.admob)
    implementation(libs.koah)
    implementation(libs.androidx.hilt.navigation.compose)
    implementation(libs.androidx.lifecycle.runtimeCompose)
    implementation(libs.androidx.lifecycle.viewModelCompose)
    implementation(libs.androidx.tracing.ktx)
    implementation(libs.androidx.dataStore.preferences)
    implementation(libs.moshi.core)
    implementation(libs.moshi.kotlin)
    implementation(libs.moshi.adapters)
    
    testImplementation(projects.foundation.testing)
    testImplementation(libs.junit)
    testImplementation(libs.kotlinx.coroutines.test)
    testImplementation(libs.mockk)
    testImplementation(libs.androidx.dataStore.preferences)
    testImplementation(projects.foundation.testing)
} 