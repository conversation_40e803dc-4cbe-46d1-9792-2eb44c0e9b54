package co.thewordlab.luzia.core.camera.result

import android.net.Uri
import co.thewordlab.luzia.foundation.testing.dispatchers.MainDispatcherRule
import co.theworldlab.luzia.foundation.design.system.components.input.model.CameraResult
import io.mockk.mockk
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNull
import org.junit.Rule
import org.junit.Test

@ExperimentalCoroutinesApi
class CameraResultViewModelTest {

    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()

    private val viewModel = CameraResultViewModel()

    @Test
    fun `Given viewModel When initialized Then cameraResult is null`() = runTest {
        val initialState = viewModel.viewState.first()
        assertNull(initialState.cameraResult)
    }

    @Test
    fun `Given viewModel When OnCameraResult Then updates state with result`() = runTest {
        val mockUri: Uri = mockk(relaxed = true)
        val cameraResult = CameraResult(uri = mockUri, prompt = "test prompt")

        viewModel.onViewAction(CameraResultViewActions.OnCameraResult(cameraResult))

        val updatedState = viewModel.viewState.first()
        assertEquals(cameraResult, updatedState.cameraResult)
    }

    @Test
    fun `Given viewModel with cameraResult When OnClearCameraResult Then clears result`() = runTest {
        val mockUri: Uri = mockk(relaxed = true)
        val cameraResult = CameraResult(uri = mockUri, prompt = "test prompt")

        viewModel.onViewAction(CameraResultViewActions.OnCameraResult(cameraResult))
        var state = viewModel.viewState.first()
        assertEquals(cameraResult, state.cameraResult)

        viewModel.onViewAction(CameraResultViewActions.OnClearCameraResult)

        state = viewModel.viewState.first()
        assertNull(state.cameraResult)
    }
}
