package co.thewordlab.luzia.core.camera.advanced.analysis

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import co.thewordlab.luzia.core.navigation.camera.CameraAnalysisModel
import co.thewordlab.luzia.foundation.localization.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun AnalysisResultView(
    modifier: Modifier = Modifier,
    resultFor: CameraAnalysisModel,
    result: AnalysisResult
) {
    val text = getAnalysisResultText(resultFor, result)
    text?.let {
        LuziaText(
            modifier = modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(Corners.X4.dp))
                .background(LuziaTheme.palette.surface.alternative)
                .padding(Spacing.X10.dp),
            text = it,
            style = LuziaTheme.typography.body.regular.small,
            color = LuziaTheme.palette.text.primary
        )
    }
}

@Composable
private fun getAnalysisResultText(
    resultFor: CameraAnalysisModel,
    result: AnalysisResult
): String? {
    return when (resultFor) {
        CameraAnalysisModel.Vision -> resultForVision(result)
        CameraAnalysisModel.Math -> resultForMath(result)
        CameraAnalysisModel.DynamicFlow -> resultForDynamicTool(result)
    }?.let { stringResource(it) }
}

private fun resultForVision(result: AnalysisResult): Int? {
    return when (result) {
        AnalysisResult.SingleTextBlock -> R.string.camera_mlkit_vision_single_text
        AnalysisResult.BlurryImage -> R.string.camera_mlkit_vision_blur_text
        else -> null
    }
}

private fun resultForMath(result: AnalysisResult): Int? {
    return when (result) {
        AnalysisResult.None -> null
        AnalysisResult.TextNotFound -> R.string.camera_mlkit_math_no_text
        AnalysisResult.MultipleTextBlock -> R.string.camera_mlkit_math_multi_text
        AnalysisResult.SingleTextBlock -> R.string.camera_mlkit_math_single_text
        AnalysisResult.BlurryImage -> R.string.camera_mlkit_math_blur_text
    }
}

private fun resultForDynamicTool(result: AnalysisResult): Int? {
    return when (result) {
        AnalysisResult.SingleTextBlock -> R.string.camera_mlkit_dynamic_tool_single_text
        AnalysisResult.BlurryImage -> R.string.camera_mlkit_dynamic_tool_blur_text
        else -> null
    }
}
