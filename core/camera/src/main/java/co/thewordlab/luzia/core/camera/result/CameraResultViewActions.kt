package co.thewordlab.luzia.core.camera.result

import co.thewordlab.luzia.foundation.architecture.system.ViewAction
import co.theworldlab.luzia.foundation.design.system.components.input.model.CameraResult

sealed class CameraResultViewActions : ViewAction {
    data object OnClearCameraResult : CameraResultViewActions()
    data class OnCameraResult(val result: CameraResult) : CameraResultViewActions()
}
