package co.thewordlab.luzia.core.camera.advanced.capture

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.net.Uri
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.ImageProxy
import androidx.core.content.ContextCompat
import co.theworldlab.luzia.foundation.design.system.components.camera.Orientation
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.android.scopes.ViewModelScoped
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.isActive
import javax.inject.Inject

@ViewModelScoped
class CaptureImageHandler @Inject constructor(
    @ApplicationContext private val context: Context,
    private val saveImageHandler: SaveImageHandler
) {

    private val imageCapture = ImageCapture.Builder()
        .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
        .setFlashMode(ImageCapture.FLASH_MODE_AUTO)
        .setJpegQuality(DEFAULT_JPEG_QUALITY)
        .build()

    fun get(): ImageCapture = imageCapture

    fun capture(orientation: Orientation, selector: CameraSelector): Flow<CaptureInfo> =
        callbackFlow {
            imageCapture.takePicture(
                ContextCompat.getMainExecutor(context),
                object : ImageCapture.OnImageCapturedCallback() {
                    override fun onCaptureSuccess(image: ImageProxy) {
                        val bitmap = image.asBitmap(orientation, selector)
                        val uri: Uri = saveImageHandler.saveToFile(bitmap)
                        image.close()
                        if (isActive) trySend(CaptureInfo.Result(uri))
                    }

                    override fun onError(exception: ImageCaptureException) {
                        if (isActive) trySend(CaptureInfo.Result(null))
                    }
                }
            )
            awaitClose()
        }

    private fun ImageProxy.asBitmap(orientation: Orientation, selector: CameraSelector): Bitmap {
        val buffer = planes[0].buffer
        buffer.rewind()
        val bytes = ByteArray(buffer.capacity())
        buffer.get(bytes)
        val options = BitmapFactory.Options()
        options.inPreferredConfig = Bitmap.Config.ARGB_8888
        return BitmapFactory.decodeByteArray(bytes, 0, bytes.size, options)
            .rotate(imageInfo.rotationDegrees.toFloat())
            .rotate(orientation.degrees(selector))
    }

    private fun Bitmap.rotate(degrees: Float): Bitmap {
        val matrix = Matrix().apply { postRotate(degrees) }
        return Bitmap.createBitmap(this, 0, 0, width, height, matrix, true)
    }

    private companion object {
        private const val DEFAULT_JPEG_QUALITY = 100
    }
}
