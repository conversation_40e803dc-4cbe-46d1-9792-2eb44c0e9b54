package co.thewordlab.luzia.core.camera.advanced

import android.net.Uri
import androidx.activity.result.PickVisualMediaRequest
import androidx.camera.core.FocusMeteringAction
import androidx.lifecycle.LifecycleOwner
import co.thewordlab.luzia.foundation.architecture.system.ViewAction
import co.theworldlab.luzia.foundation.design.system.components.camera.Orientation

sealed class AdvancedCameraViewActions : ViewAction {
    data object OnCreate : AdvancedCameraViewActions()
    data object OnDismiss : AdvancedCameraViewActions()
    data class OnCapture(val orientation: Orientation) : AdvancedCameraViewActions()
    data class OnRotateCamera(val owner: LifecycleOwner) : AdvancedCameraViewActions()
    data class OnGalleryTapped(val request: PickVisualMediaRequest) : AdvancedCameraViewActions()
    data class OnCameraGranted(val owner: LifecycleOwner) : AdvancedCameraViewActions()
    data class OnPickImageFromGallery(val uri: Uri) : AdvancedCameraViewActions()
    data class OnFocus(val action: FocusMeteringAction) : AdvancedCameraViewActions()
    data class OnFinalize(val uri: Uri) : AdvancedCameraViewActions()
}
