package co.thewordlab.luzia.core.camera.advanced.capture

import android.content.Context
import android.graphics.Bitmap
import android.graphics.ImageDecoder
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.util.Log
import androidx.core.net.toFile
import androidx.core.net.toUri
import dagger.hilt.android.qualifiers.ApplicationContext
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject

@Suppress("NestedBlockDepth")
class SaveImageHandler @Inject constructor(
    @ApplicationContext private val context: Context
) {

    fun saveToFile(bitmap: Bitmap): Uri {
        val file = File.createTempFile("IMG_", ".jpg", context.cacheDir)
        return reduceFileSizeIfNeeded(bitmap, file)
    }

    fun reduceFileSizeIfNeeded(uri: Uri): Uri {
        val bitmap = uri.toBitmap()
        val file = try {
            uri.toFile()
        } catch (e: IllegalArgumentException) {
            Log.e("SaveImageHandler", "Error converting Uri to File: ${e.message}")
            File.createTempFile("IMG_", ".jpg", context.cacheDir)
        }
        return reduceFileSizeIfNeeded(bitmap, file)
    }

    private fun reduceFileSizeIfNeeded(bitmap: Bitmap, file: File): Uri {
        ByteArrayOutputStream().use { stream ->
            // Start with maximum quality
            val quality = INITIAL_JPEG_QUALITY
            // Strict rule: Image file size should be less than 1MB
            val maxFileSize = MAX_FILE_SIZE
            // Target range: 900KB-1MB for optimal quality/size balance
            val targetMinSize = TARGET_MIN_SIZE

            // First pass: compress with maximum quality
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, stream)
            var currentSize = stream.toByteArray().size

            // If image is already under max size, we're done
            if (currentSize > maxFileSize) {
                // Binary search approach for finding optimal quality
                // This is more efficient than linear reduction and produces better results
                var minQuality = MIN_QUALITY // Absolute minimum quality we'll accept
                var maxQuality = MAX_QUALITY
                var bestQuality = quality

                // Binary search to find optimal quality that fits size requirements
                while (minQuality <= maxQuality) {
                    val midQuality = (minQuality + maxQuality) / 2

                    stream.reset()
                    bitmap.compress(Bitmap.CompressFormat.JPEG, midQuality, stream)
                    currentSize = stream.toByteArray().size
                    if (currentSize <= maxFileSize) {
                        // This quality works, but we might be able to increase quality
                        bestQuality = midQuality

                        // If we're in the target range and quality is good, we can stop
                        if (currentSize >= targetMinSize && midQuality >= QUALITY_THRESHOLD) {
                            break
                        }

                        // Try to increase quality
                        minQuality = midQuality + 1
                    } else {
                        // Too large, decrease quality
                        maxQuality = midQuality - 1
                    }
                }

                // If we didn't find a quality that fits in the target range,
                // use the best quality that fits under max size
                stream.reset()
                bitmap.compress(Bitmap.CompressFormat.JPEG, bestQuality, stream)
            }

            FileOutputStream(file).use { fos ->
                fos.write(stream.toByteArray())
            }
        }
        return file.toUri()
    }

    private fun Uri.toBitmap(): Bitmap {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            ImageDecoder.decodeBitmap(ImageDecoder.createSource(context.contentResolver, this))
        } else {
            MediaStore.Images.Media.getBitmap(context.contentResolver, this)
        }
    }

    private companion object {
        const val INITIAL_JPEG_QUALITY = 100
        const val MAX_FILE_SIZE = 1 * 1024 * 1024 // 1MB
        const val TARGET_MIN_SIZE = 900 * 1024 // 900KB
        const val MIN_QUALITY = 5
        const val MAX_QUALITY = 100
        const val QUALITY_THRESHOLD = 70
    }
}
