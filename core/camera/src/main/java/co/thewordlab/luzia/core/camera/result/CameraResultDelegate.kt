package co.thewordlab.luzia.core.camera.result

import androidx.compose.runtime.staticCompositionLocalOf
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

interface CameraResultDelegate {
    val viewState: StateFlow<CameraResultViewState>
    fun onViewAction(actions: CameraResultViewActions)
}

val LocalCameraResultDelegate = staticCompositionLocalOf<CameraResultDelegate> {
    object : CameraResultDelegate {
        override val viewState: StateFlow<CameraResultViewState> =
            MutableStateFlow(CameraResultViewState())

        override fun onViewAction(actions: CameraResultViewActions) {
            DO_NOTHING
        }
    }
}
