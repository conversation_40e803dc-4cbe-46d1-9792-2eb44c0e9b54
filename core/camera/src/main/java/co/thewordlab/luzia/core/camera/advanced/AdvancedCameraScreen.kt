package co.thewordlab.luzia.core.camera.advanced

import android.content.Context
import android.net.Uri
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts.PickVisualMedia
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.tooling.preview.Preview
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.camera.advanced.analysis.AnalysisResultView
import co.thewordlab.luzia.core.camera.advanced.capture.CaptureInfo
import co.thewordlab.luzia.core.camera.common.CameraActionsView
import co.thewordlab.luzia.core.camera.common.CameraPreviewContentView
import co.thewordlab.luzia.core.camera.common.OrientationProviderView
import co.thewordlab.luzia.core.navigation.camera.CameraAnalysisModel
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.OnResume
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.theworldlab.luzia.foundation.design.system.components.camera.Orientation
import co.theworldlab.luzia.foundation.design.system.components.camera.permission.rememberCameraPermissionState
import co.theworldlab.luzia.foundation.design.system.components.input.model.CameraResult
import co.theworldlab.luzia.foundation.design.system.legacy.composables.CameraAction
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import com.canhub.cropper.CropImageContract
import com.canhub.cropper.CropImageContractOptions
import com.canhub.cropper.CropImageOptions
import com.canhub.cropper.CropImageView
import java.io.File
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun AdvancedCameraScreen(
    modifier: Modifier = Modifier.fillMaxSize(),
    startWithGallery: Boolean = false,
    analysisResultFor: CameraAnalysisModel,
    onFinalOutput: (CameraResult) -> Unit
) {
    val viewModel: AdvancedCameraViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val navigation = LocalNavigation.current
    val owner = LocalLifecycleOwner.current
    val context = LocalContext.current
    val cameraPermission = rememberCameraPermissionState {
        viewModel.onViewAction(AdvancedCameraViewActions.OnCameraGranted(owner))
    }
    val galleryLauncher = rememberLauncherForActivityResult(contract = PickVisualMedia()) {
        if (it != null) {
            viewModel.onViewAction(AdvancedCameraViewActions.OnPickImageFromGallery(it))
        }
    }
    val cropIntent =
        rememberLauncherForActivityResult(CropImageContract()) { result ->
            if (result.isSuccessful) {
                result.uriContent?.let { uri ->
                    viewModel.onViewAction(AdvancedCameraViewActions.OnFinalize(uri))
                }
            }
        }
    LaunchedEffect(startWithGallery) {
        if (startWithGallery) {
            val request = PickVisualMediaRequest.Builder()
                .setMediaType(PickVisualMedia.ImageOnly)
                .build()
            viewModel.onViewAction(AdvancedCameraViewActions.OnGalleryTapped(request))
        }
    }
    OnCreate("AdvancedCameraScreen") {
        viewModel.onViewAction(AdvancedCameraViewActions.OnCreate)
    }
    OnResume {
        cameraPermission.launchCamera()
    }
    ViewModelEventEffect(viewModel) {
        when (it) {
            AdvancedCameraViewEvents.NavigateBack -> navigation.goBack()
            is AdvancedCameraViewEvents.NavigateToGallery -> galleryLauncher.launch(it.request)
            is AdvancedCameraViewEvents.NavigateToCrop -> {
                navigateToCrop(context, it.uri, cropIntent)
            }

            is AdvancedCameraViewEvents.Finalize -> onFinalOutput(CameraResult(it.uri))
        }
    }
    if (cameraPermission.isGranted) {
        AdvancedCameraContent(
            modifier = modifier,
            viewState = viewState,
            analysisResultFor = analysisResultFor,
            onViewActions = viewModel::onViewAction
        )
    }
}

private fun navigateToCrop(
    context: Context,
    uri: Uri,
    cropIntent: ManagedActivityResultLauncher<CropImageContractOptions, CropImageView.CropResult>
) {
    val file = File.createTempFile("cropped", "", context.cacheDir)
    val options =
        CropImageOptions(
            imageSourceIncludeGallery = false,
            imageSourceIncludeCamera = false,
            allowFlipping = true,
            initialCropWindowPaddingRatio = 0.1f,
            cropMenuCropButtonTitle = context.getString(localizationR.string.signup_done_button),
            multiTouchEnabled = false,
            cropperLabelTextColor = Color.Black.toArgb(),
            toolbarColor = Color.White.toArgb(),
            toolbarTintColor = Color.White.toArgb(),
            activityBackgroundColor = Color.Black.toArgb(),
            activityMenuIconColor = Color.Black.toArgb(),
            toolbarBackButtonColor = Color.Black.toArgb(),
            activityMenuTextColor = Color.Black.toArgb(),
            toolbarTitleColor = Color.Black.toArgb(),
            customOutputUri = file.toUri()
        )
    cropIntent.launch(CropImageContractOptions(uri, options))
}

@Composable
private fun AdvancedCameraContent(
    modifier: Modifier,
    viewState: AdvancedCameraViewState,
    analysisResultFor: CameraAnalysisModel,
    onViewActions: (AdvancedCameraViewActions) -> Unit
) {
    val owner = LocalLifecycleOwner.current
    var rotationOrientation: Orientation by remember { mutableStateOf(Orientation.PORTRAIT) }
    OrientationProviderView { rotationOrientation = it }
    Box(
        modifier = modifier
            .background(Color.Black)
            .testTag("containerCaptureImage")
    ) {
        when (val request = viewState.surfaceRequest) {
            null -> CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center),
                color = Color.White
            )

            else -> {
                CameraPreviewContentView(
                    surfaceRequest = request,
                    onFocus = { onViewActions(AdvancedCameraViewActions.OnFocus(it)) }
                )
            }
        }
        viewState.captureInfo?.let { captureInfo -> CaptureImageProgressView(captureInfo) }
        AnalysisResultView(
            result = viewState.analysisResult,
            modifier = Modifier.padding(Spacing.X64.dp),
            resultFor = analysisResultFor
        )
        CameraActionsView(
            modifier = Modifier
                .navigationBarsPadding()
                .align(Alignment.BottomCenter),
            onCapture = {
                onViewActions(AdvancedCameraViewActions.OnCapture(rotationOrientation))
            },
            onRotateCamera = {
                onViewActions(AdvancedCameraViewActions.OnRotateCamera(owner))
            },
            onGalleryTapped = {
                val request = PickVisualMediaRequest.Builder()
                    .setMediaType(PickVisualMedia.ImageOnly)
                    .build()
                onViewActions(AdvancedCameraViewActions.OnGalleryTapped(request))
            }
        )
        CameraAction(
            modifier = Modifier
                .statusBarsPadding()
                .padding(Spacing.X16.dp),
            drawableRes = designR.drawable.ic_close,
            onClick = { onViewActions(AdvancedCameraViewActions.OnDismiss) }
        )
    }
}

@Composable
private fun BoxScope.CaptureImageProgressView(info: CaptureInfo) {
    if (info is CaptureInfo.Loading) {
        CircularProgressIndicator(
            modifier = Modifier.align(Alignment.Center),
            color = Color.White
        )
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        AdvancedCameraContent(
            modifier = Modifier.fillMaxSize(),
            viewState = AdvancedCameraViewState(),
            analysisResultFor = CameraAnalysisModel.DynamicFlow,
            onViewActions = {}
        )
    }
}
