package co.thewordlab.luzia.core.camera.advanced.analysis

import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageAnalysis.COORDINATE_SYSTEM_ORIGINAL
import androidx.camera.core.ImageProxy
import androidx.camera.mlkit.vision.MlKitAnalyzer
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import dagger.hilt.android.scopes.ViewModelScoped
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import java.util.concurrent.Executors
import javax.inject.Inject

@ViewModelScoped
class CameraAnalysisHandler @Inject constructor(
    private val featureFlagManager: FeatureFlagManager,
    private val blurryImageHandler: BlurryImageHandler
) {

    private val imageAnalysis: ImageAnalysis = ImageAnalysis.Builder()
        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
        .build()

    // Use a thread pool instead for better performance
    private val cameraExecutor = Executors.newFixedThreadPool(2)

    private val internalState = MutableStateFlow<AnalysisResult>(AnalysisResult.None)

    // Frame counter to reduce frequency of blurry image checks
    private var frameCounter = 0

    // Create MlKitAnalyzer once and reuse it
    private val textRecognition by lazy { TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS) }

    private val mlKitAnalyzer by lazy {
        MlKitAnalyzer(
            listOf(textRecognition),
            COORDINATE_SYSTEM_ORIGINAL,
            cameraExecutor
        ) { analyzerResult ->
            analyzerResult.getValue(textRecognition)?.let { recognizedText ->
                val result = when {
                    recognizedText.text.isEmpty() -> AnalysisResult.TextNotFound
                    recognizedText.textBlocks.size > 1 -> AnalysisResult.MultipleTextBlock
                    else -> AnalysisResult.SingleTextBlock
                }
                internalState.update { result }
            }
        }
    }

    private val analyzer by lazy {
        object : ImageAnalysis.Analyzer {
            override fun analyze(imageProxy: ImageProxy) {
                // Only check for blurry image every 5 frames to reduce processing overhead
                if (frameCounter % 5 == 0) {
                    if (blurryImageHandler.isBlurry(imageProxy)) {
                        internalState.update { AnalysisResult.BlurryImage }
                        imageProxy.close()
                        return
                    }
                }
                // Reset counter after 30 frames
                frameCounter = (frameCounter + 1) % 30
                mlKitAnalyzer.analyze(imageProxy)
            }
        }
    }

    val analysisResult = internalState.asStateFlow()

    fun get(): ImageAnalysis? {
        val isEnabled: Boolean = featureFlagManager.getImmediate(FeatureFlag.CameraMlKitEnabled)
        if (!isEnabled) return null
        imageAnalysis.clearAnalyzer()
        imageAnalysis.setAnalyzer(cameraExecutor, analyzer)
        return imageAnalysis
    }

    fun shutdown() {
        imageAnalysis.clearAnalyzer()
        cameraExecutor.shutdown()
    }
}
