package co.thewordlab.luzia.core.camera.common

import androidx.camera.compose.CameraXViewfinder
import androidx.camera.core.FocusMeteringAction
import androidx.camera.core.SurfaceOrientedMeteringPointFactory
import androidx.camera.core.SurfaceRequest
import androidx.camera.viewfinder.compose.MutableCoordinateTransformer
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.isSpecified
import androidx.compose.ui.geometry.takeOrElse
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.round
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import kotlinx.coroutines.delay
import java.util.UUID

private const val DELAY_FOCUS = 1000L

@Composable
fun CameraPreviewContentView(
    surfaceRequest: SurfaceRequest,
    onFocus: (FocusMeteringAction) -> Unit
) {
    var autofocusRequest by remember { mutableStateOf(UUID.randomUUID() to Offset.Unspecified) }
    val autofocusRequestId = autofocusRequest.first
    // Show the autofocus indicator if the offset is specified
    val showAutofocusIndicator = autofocusRequest.second.isSpecified
    // Cache the initial coords for each autofocus request
    val autofocusCoords = remember(autofocusRequestId) { autofocusRequest.second }

    // Queue hiding the request for each unique autofocus tap
    if (showAutofocusIndicator) {
        LaunchedEffect(autofocusRequestId) {
            delay(DELAY_FOCUS)
            // Clear the offset to finish the request and hide the indicator
            autofocusRequest = autofocusRequestId to Offset.Unspecified
        }
    }

    val surfaceMeteringPointFactory = remember {
        SurfaceOrientedMeteringPointFactory(
            surfaceRequest.resolution.width.toFloat(),
            surfaceRequest.resolution.height.toFloat()
        )
    }
    val coordinateTransformer = remember { MutableCoordinateTransformer() }
    CameraXViewfinder(
        surfaceRequest = surfaceRequest,
        coordinateTransformer = coordinateTransformer,
        modifier = Modifier.pointerInput(coordinateTransformer) {
            detectTapGestures { tapCoords ->
                with(coordinateTransformer) {
                    val newCoordinates = tapCoords.transform()
                    val point = surfaceMeteringPointFactory.createPoint(
                        newCoordinates.x,
                        newCoordinates.y
                    )
                    val meteringAction = FocusMeteringAction.Builder(point).build()
                    onFocus(meteringAction)
                }
                autofocusRequest = UUID.randomUUID() to tapCoords
            }
        }
    )

    AnimatedVisibility(
        visible = showAutofocusIndicator,
        enter = fadeIn(),
        exit = fadeOut(),
        modifier = Modifier
            .offset { autofocusCoords.takeOrElse { Offset.Zero }.round() }
            .offset((-Spacing.X24.dp), (-Spacing.X24.dp))
    ) {
        Spacer(
            Modifier
                .border(Spacing.X2.dp, Color.White, CircleShape)
                .size(Spacing.X48.dp)
        )
    }
}
