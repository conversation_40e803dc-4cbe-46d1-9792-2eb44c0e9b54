package co.thewordlab.luzia.core.camera.common

import androidx.camera.core.FocusMeteringAction
import androidx.camera.core.MeteringPoint
import androidx.camera.core.SurfaceOrientedMeteringPointFactory
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalWindowInfo
import kotlinx.coroutines.delay
import java.util.concurrent.TimeUnit

private const val FOCUS_ANIMATION_DURATION = 300
private const val FOCUS_CIRCLE_SCALE = 0.8f
private const val FOCUS_CIRCLE_DEFAULT_SCALE = 1f
private const val FOCUS_POINT_HIDE_DELAY = 500L
private const val FOCUS_CIRCLE_RESET_DELAY = 300L
private const val FOCUS_CIRCLE_RADIUS = 50f
private const val FOCUS_CIRCLE_STROKE_WIDTH = 2f
private const val FOCUS_AUTO_CANCEL_DURATION = 3L

@Composable
fun TapToFocusView(action: (FocusMeteringAction) -> Unit) {
    var focusPoint by remember { mutableStateOf<Offset?>(null) }
    var focusCircleScale by remember { mutableFloatStateOf(FOCUS_CIRCLE_DEFAULT_SCALE) }
    val animatedScale by animateFloatAsState(
        targetValue = focusCircleScale,
        animationSpec = tween(durationMillis = FOCUS_ANIMATION_DURATION),
        label = "focusCircleScale"
    )
    val viewWidth = LocalWindowInfo.current.containerSize.width.toFloat()
    val viewHeight = LocalWindowInfo.current.containerSize.height.toFloat()
    LaunchedEffect(focusPoint) {
        if (focusPoint != null) {
            focusCircleScale = FOCUS_CIRCLE_SCALE
            delay(FOCUS_CIRCLE_RESET_DELAY)
            focusCircleScale = FOCUS_CIRCLE_DEFAULT_SCALE
            delay(FOCUS_POINT_HIDE_DELAY)
            focusPoint = null
        }
    }
    Canvas(
        modifier = Modifier
            .fillMaxSize()
            .pointerInput(Unit) {
                detectTapGestures(
                    onTap = { tapOffset ->
                        val factory = SurfaceOrientedMeteringPointFactory(viewWidth, viewHeight)
                        val meteringPoint = factory.createPoint(tapOffset.x, tapOffset.y)
                        focusCamera(meteringPoint, action)
                        focusPoint = tapOffset
                    }
                )
            }
    ) {
        focusPoint?.let { point ->
            drawCircle(
                color = Color.White,
                radius = FOCUS_CIRCLE_RADIUS * animatedScale,
                center = point,
                style = Stroke(width = FOCUS_CIRCLE_STROKE_WIDTH)
            )
        }
    }
}

private fun focusCamera(meteringPoint: MeteringPoint, action: (FocusMeteringAction) -> Unit) {
    val focusAction = FocusMeteringAction.Builder(meteringPoint, FocusMeteringAction.FLAG_AF)
        .setAutoCancelDuration(FOCUS_AUTO_CANCEL_DURATION, TimeUnit.SECONDS)
        .build()
    action(focusAction)
}
