plugins {
    alias(libs.plugins.luzia.android.feature)
}

android {
    namespace = "co.theworldlab.luzia.core.camera"
}

dependencies {
    implementation(projects.foundation.localization)
    implementation(projects.foundation.analytics)
    implementation(projects.core.navigation)
    implementation(libs.image.editor)

    // CameraX
    implementation(libs.androidx.camera.core)
    implementation(libs.androidx.camera.camera2)
    implementation(libs.androidx.camera.lifecycle)
    implementation(libs.androidx.camera.view)
    implementation(libs.androidx.camera.extensions)
    implementation(libs.androidx.camera.compose)
    implementation(libs.google.guava)

    // ML Kit
    implementation(libs.androidx.camera.mlkit.vision)
    implementation(libs.play.services.mlkit.text.recognition)

    // Permissions
    implementation(libs.accompanist.permissions)

    // Testing
    testImplementation(projects.foundation.testing)
}
