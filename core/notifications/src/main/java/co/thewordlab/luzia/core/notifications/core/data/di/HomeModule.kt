package co.thewordlab.luzia.core.notifications.core.data.di

import co.thewordlab.luzia.core.notifications.core.data.api.NotificationsApi
import co.thewordlab.luzia.foundation.networking.di.BaseHost
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit

@Module
@InstallIn(SingletonComponent::class)
object HomeModule {
    @Provides
    fun provideNotificationsApi(@BaseHost retrofit: Retrofit): NotificationsApi {
        return retrofit.create(NotificationsApi::class.java)
    }
}
