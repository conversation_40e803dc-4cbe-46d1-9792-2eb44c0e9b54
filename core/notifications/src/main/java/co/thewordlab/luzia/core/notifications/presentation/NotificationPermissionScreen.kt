package co.thewordlab.luzia.core.notifications.presentation

import android.Manifest.permission.POST_NOTIFICATIONS
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.hilt.navigation.compose.hiltViewModel
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.common.extensions.findActivity
import co.thewordlab.luzia.foundation.common.permission.PermissionState
import co.thewordlab.luzia.foundation.common.permission.notificationPermissionState
import co.thewordlab.luzia.foundation.common.permission.openNotificationSettings
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonFilled
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.PermissionStatus
import com.google.accompanist.permissions.rememberPermissionState
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun NotificationPermissionScreen(
    onDismiss: () -> Unit
) {
    val notificationPermissionState = rememberPermissionState(permission = POST_NOTIFICATIONS)
    val activity = LocalContext.current.findActivity()
    val viewModel: NotificationPermissionViewModel = hiltViewModel()
    OnCreate("NotificationPermissionScreen") {
        viewModel.onViewAction(NotificationPermissionViewActions.OnCreate)
    }
    ViewModelEventEffect(viewModel) {
        when (it) {
            NotificationPermissionViewEvents.NavigateBack -> onDismiss()
            NotificationPermissionViewEvents.AskPermission -> {
                when (val state = activity.notificationPermissionState()) {
                    PermissionState.Granted -> onDismiss()
                    is PermissionState.Denied -> {
                        if (state.redirectToSettings) {
                            activity.openNotificationSettings()
                        } else {
                            notificationPermissionState.launchPermissionRequest()
                        }
                    }
                }
            }
        }
    }
    LaunchedEffect(notificationPermissionState.status) {
        val state = notificationPermissionState.status
        when {
            state is PermissionStatus.Denied && state.shouldShowRationale -> onDismiss.invoke()
            state is PermissionStatus.Granted -> onDismiss.invoke()
            else -> DO_NOTHING
        }
    }
    NotificationPermissionContent(onViewActions = viewModel::onViewAction)
}

@Composable
private fun NotificationPermissionContent(onViewActions: (NotificationPermissionViewActions) -> Unit) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = Spacing.X32.dp)
    ) {
        val (refTitle, refCard) = createRefs()
        TitleView(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = Spacing.X24.dp)
                .constrainAs(refTitle) {
                    bottom.linkTo(refCard.top)
                }
        )
        CardView(
            modifier = Modifier
                .fillMaxWidth()
                .constrainAs(refCard) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                },
            onViewActions = onViewActions
        )
    }
}

@Composable
private fun CardView(
    modifier: Modifier,
    onViewActions: (NotificationPermissionViewActions) -> Unit
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(Corners.X5.dp),
        elevation = CardDefaults.elevatedCardElevation(defaultElevation = Spacing.X20.dp),
        colors = CardDefaults.cardColors(containerColor = LuziaTheme.palette.surface.content)
    ) {
        Column(
            modifier
                .fillMaxWidth()
                .padding(horizontal = Spacing.X24.dp, vertical = Spacing.X16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
        ) {
            Icon(
                modifier = Modifier.size(Spacing.X160.dp),
                painter = painterResource(designR.drawable.luzia_with_background),
                tint = Color.Unspecified,
                contentDescription = null
            )
            LuziaText(
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                text = stringResource(localizationR.string.signup_notis_popup_header),
                style = LuziaTheme.typography.body.semiBold.default,
                color = LuziaTheme.palette.text.primary
            )
            LuziaText(
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                text = stringResource(localizationR.string.signup_notis_popup_body),
                style = LuziaTheme.typography.body.regular.small,
                color = LuziaTheme.palette.text.primary
            )
            Spacer(Modifier.height(Spacing.X16.dp))
            Row(
                horizontalArrangement = Arrangement.spacedBy(
                    Spacing.X8.dp,
                    Alignment.CenterHorizontally
                )
            ) {
                ButtonFilled(
                    buttonText = stringResource(localizationR.string.signup_notis_popup_dont_allow),
                    containerColor = Color.Transparent,
                    contentColor = LuziaTheme.palette.text.primary,
                    onClick = { onViewActions(NotificationPermissionViewActions.OnDismiss) }
                )
                ButtonFilled(
                    buttonText = stringResource(localizationR.string.signup_notis_popup_allow),
                    onClick = { onViewActions(NotificationPermissionViewActions.OnAllow) }
                )
            }
        }
    }
}

@Composable
private fun TitleView(modifier: Modifier) {
    Column(
        modifier = modifier.padding(horizontal = Spacing.X16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
    ) {
        LuziaText(
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center,
            text = stringResource(localizationR.string.signup_notis_title),
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary
        )
        LuziaText(
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center,
            text = stringResource(localizationR.string.signup_notis_body),
            style = LuziaTheme.typography.body.regular.default,
            color = LuziaTheme.palette.text.secondary
        )
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        NotificationPermissionContent(onViewActions = {})
    }
}
