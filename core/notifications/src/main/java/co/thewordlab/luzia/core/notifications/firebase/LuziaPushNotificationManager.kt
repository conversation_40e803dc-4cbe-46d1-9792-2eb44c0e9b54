package co.thewordlab.luzia.core.notifications.firebase

import co.thewordlab.luzia.core.notifications.core.data.repository.NotificationsRepository
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.messages.domain.repository.ChatRepository
import co.theworldlab.luzia.features.proactive.messaging.domain.usecases.HandleProactiveChatMessagesUseCase
import com.google.firebase.messaging.RemoteMessage
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import javax.inject.Inject

class LuziaPushNotificationManager @Inject constructor(
    private val analytics: Analytics,
    private val chatRepository: ChatRepository,
    private val notificationsRepository: NotificationsRepository,
    private val handleProactiveMessagesUseCase: HandleProactiveChatMessagesUseCase
) {

    private val coroutineScope =
        CoroutineScope(
            Dispatchers.IO + CoroutineExceptionHandler { _, throwable ->
                analytics.reportException(TAG, Exception(throwable))
            }
        )

    fun handleDataMessage(message: RemoteMessage, packageName: String): Boolean {
        val clickAction = message.data[DATA_CLICK_ACTION]
        val sendAction = "$packageName.$ACTION_SEND_MESSAGE"
        val type = message.data[DATA_SOURCE].orEmpty()
        if (type == DATA_SOURCE_PROACTIVE) {
            handleProactiveMessages()
        } else if (message.data.containsKey(DATA_PERSONALITY) &&
            message.data.containsKey(DATA_TEXT) &&
            clickAction == sendAction
        ) {
            handleChatMessages(message.data[DATA_PERSONALITY], message.data[DATA_TEXT])
        }
        return true
    }

    fun updateNotificationToken(token: String) = coroutineScope.launch {
        notificationsRepository.updateNotificationToken(token)
    }

    fun cancelOperations() {
        coroutineScope.cancel()
    }

    private fun handleChatMessages(personalityId: String?, text: String?) {
        if (!personalityId.isNullOrEmpty() && !text.isNullOrEmpty()) {
            coroutineScope.launch { chatRepository.saveRemoteMessage(personalityId, text) }
        }
    }

    private fun handleProactiveMessages() {
        coroutineScope.launch { handleProactiveMessagesUseCase() }
    }

    companion object {
        const val TAG = "PushNotificationService"
        const val DATA_PERSONALITY = "personality_id"
        const val DATA_TEXT = "text"
        const val ACTION_SEND_MESSAGE = "send_message"
        const val DATA_CLICK_ACTION = "click_action"
        const val DATA_SOURCE = "service_source"
        const val DATA_SOURCE_PROACTIVE = "proactive"
    }
}
