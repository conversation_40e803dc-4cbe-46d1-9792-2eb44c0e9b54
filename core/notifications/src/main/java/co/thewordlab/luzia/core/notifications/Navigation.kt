package co.thewordlab.luzia.core.notifications

import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavOptions
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.core.notifications.firebase.PushNotificationService.Companion.LUZIA_ID_PERSONALITY
import co.thewordlab.luzia.core.notifications.presentation.NotificationPermissionScreen
import co.theworldlab.luzia.foundation.design.system.components.bottombar.LandingDestinations

fun NavGraphBuilder.notificationPermission(navController: NavController) {
    composable<UserSessionRoutes.NotificationPermission> {
        val route = it.toRoute<UserSessionRoutes.NotificationPermission>()
        NotificationPermissionScreen(
            onDismiss = {
                if (route.openChat) {
                    val popupTo = UserSessionRoutes.Landing(LandingDestinations.HOME)
                    val options = NavOptions.Builder()
                        .setPopUpTo(popupTo, false)
                        .build()
                    navController.navigate(
                        route = UserSessionRoutes.ChatDetail(
                            personalityId = LUZIA_ID_PERSONALITY,
                            isCustomBestie = false
                        ),
                        navOptions = options
                    )
                } else {
                    navController.popBackStack()
                }
            }
        )
    }
}
