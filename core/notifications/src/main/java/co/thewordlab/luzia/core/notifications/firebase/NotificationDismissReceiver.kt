package co.thewordlab.luzia.core.notifications.firebase

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import co.thewordlab.luzia.core.notifications.firebase.PushNotificationService.Companion.GROUP_ID
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Event
import co.thewordlab.luzia.foundation.analytics.Parameter
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class NotificationDismissReceiver : BroadcastReceiver() {
    @Inject
    lateinit var analytics: Analytics

    override fun onReceive(context: Context?, intent: Intent?) {
        val groupId = intent?.getStringExtra(GROUP_ID).orEmpty()
        analytics.logEvent(Event.NotificationDismiss, mapOf(Parameter.GroupId to groupId))
    }

    companion object {
        private const val REQUEST_CODE = 5356
        private const val BROADCAST_ACTION = "co.thewordlab.luzia.receiver.NotificationDismissReceiver"

        fun dismissIntent(context: Context, groupId: String?): PendingIntent {
            val intent = Intent(BROADCAST_ACTION)
            intent.putExtra(GROUP_ID, groupId)
            intent.setPackage(context.packageName)
            val flags = PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE
            return PendingIntent.getBroadcast(context, REQUEST_CODE, intent, flags)
        }
    }
}
