package co.thewordlab.luzia.core.notifications.firebase

import android.Manifest.permission.POST_NOTIFICATIONS
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.PendingIntent.FLAG_IMMUTABLE
import android.app.PendingIntent.FLAG_UPDATE_CURRENT
import android.content.Intent
import android.content.pm.PackageManager.PERMISSION_GRANTED
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.app.Person
import androidx.core.content.ContextCompat.checkSelfPermission
import androidx.core.graphics.drawable.IconCompat
import androidx.core.net.toUri
import co.thewordlab.luzia.core.notifications.R
import co.thewordlab.luzia.core.notifications.braze.BrazeManager
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.providers.CrashlyticsAnalyticsProvider
import co.thewordlab.luzia.foundation.securelib.SecureKey
import co.thewordlab.luzia.foundation.securelib.SecureStorage
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import dagger.hilt.android.AndroidEntryPoint
import io.getstream.android.push.firebase.FirebaseMessagingDelegate
import java.util.Date
import javax.inject.Inject
import kotlin.random.Random
import co.thewordlab.luzia.foundation.design.system.R as designR

@AndroidEntryPoint
class PushNotificationService : FirebaseMessagingService() {

    @Inject
    lateinit var luziaPushNotificationManager: LuziaPushNotificationManager

    @Inject
    lateinit var brazeManager: BrazeManager

    @Inject
    lateinit var analytics: Analytics

    @Inject
    lateinit var secureStorage: SecureStorage

    @Inject
    lateinit var crashlyticsProvider: CrashlyticsAnalyticsProvider

    private val notificationManager by lazy {
        NotificationManagerCompat.from(this)
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            getString(R.string.channel_default),
            getString(R.string.channel_default_name),
            NotificationManager.IMPORTANCE_HIGH
        )
        notificationManager.createNotificationChannel(channel)
    }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        updateGetStreamToken(token)
        if (checkSelfPermission(this, POST_NOTIFICATIONS) == PERMISSION_GRANTED) {
            luziaPushNotificationManager.updateNotificationToken(token)
        }
    }

    private fun updateGetStreamToken(token: String) {
        try {
            val provider = secureStorage.get(SecureKey.KEY_GET_STREAM_NOTIFICATION)
            FirebaseMessagingDelegate.registerFirebaseToken(token, provider)
        } catch (exception: IllegalStateException) {
            analytics.reportException("Get stream push token fetch failed", exception)
        }
    }

    override fun onMessageReceived(message: RemoteMessage) {
        super.onMessageReceived(message)
        crashlyticsProvider.reportException("Push received: id: ${message.messageId} || data: ${message.data}", null)
        try {
            if (FirebaseMessagingDelegate.handleRemoteMessage(message)) return
            if (brazeManager.handleBrazeMessage(this, message)) return

            // Fallback handling if no handler processes the message
            if (luziaPushNotificationManager.handleDataMessage(message, packageName)) {
                createNotificationMessage(message)
            }
        } catch (ex: IllegalStateException) {
            analytics.reportException("Push message failed", ex)
        }
    }

    private fun createNotificationMessage(message: RemoteMessage) {
        val personalityId = message.data[DATA_PERSONALITY] ?: LUZIA_ID_PERSONALITY
        val title = message.data[NOTIFICATION_TITLE]
        val body = message.data[NOTIFICATION_BODY]
        val deepLink = message.data[DATA_DEEPLINK]
        val groupId = message.data[GROUP_ID]
        if (!title.isNullOrEmpty() && !body.isNullOrEmpty()) {
            val icon =
                BitmapFactory.decodeResource(resources, designR.drawable.luzia_with_background)
            val style = getMessagingStyle(title, body, icon)
            val intent = packageManager.getLaunchIntentForPackage(packageName)
            intent?.data = deepLink?.toUri()
            intent?.action = Intent.ACTION_VIEW
            intent?.putExtra(GROUP_ID, groupId)
            intent?.putExtra(EXTRA_FROM_NOTIFICATION, true)
            val pendingIntent =
                PendingIntent.getActivity(this, 0, intent, FLAG_IMMUTABLE or FLAG_UPDATE_CURRENT)
            val notification =
                NotificationCompat.Builder(this, getString(R.string.channel_default))
                    .setSmallIcon(designR.drawable.luzia_logo_letter)
                    .setLargeIcon(icon)
                    .setContentTitle(title)
                    .setContentText(body)
                    .setPriority(NotificationCompat.PRIORITY_HIGH)
                    .setContentIntent(pendingIntent)
                    .setDeleteIntent(NotificationDismissReceiver.dismissIntent(this, groupId))
                    .setAutoCancel(true)
                    .setStyle(style)
                    .setShortcutId(personalityId)
                    .build()
            notificationManager.notify(Random.nextInt(), notification)
        }
    }

    private fun getMessagingStyle(
        title: String,
        body: String,
        icon: Bitmap,
    ): NotificationCompat.MessagingStyle {
        val user: Person = Person.Builder()
            .setIcon(IconCompat.createWithBitmap(icon))
            .setName(title)
            .build()
        return NotificationCompat.MessagingStyle(user)
            .addMessage(body, Date().time, user)
    }

    override fun onDestroy() {
        luziaPushNotificationManager.cancelOperations()
        super.onDestroy()
    }

    companion object {
        const val LUZIA_ID_PERSONALITY = "LuzIA"
        const val DATA_PERSONALITY = "personality_id"
        const val DATA_DEEPLINK = "deeplink"
        const val NOTIFICATION_TITLE = "notification_title"
        const val NOTIFICATION_BODY = "notification_body"
        const val GROUP_ID = "group_id"
        const val EXTRA_FROM_NOTIFICATION = "extra_from_notification"
    }
}
