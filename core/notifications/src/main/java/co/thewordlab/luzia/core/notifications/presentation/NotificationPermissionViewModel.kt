package co.thewordlab.luzia.core.notifications.presentation

import androidx.lifecycle.ViewModel
import co.thewordlab.luzia.core.notifications.NotificationPermissionAnalytics
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Event
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class NotificationPermissionViewModel @Inject constructor(
    private val analytics: Analytics
) :
    ViewModel(),
    ViewModelActions<NotificationPermissionViewActions>,
    ViewModelEvents<NotificationPermissionViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<NotificationPermissionViewState> by ViewModelStatesImpl(
        NotificationPermissionViewState()
    ) {

    override fun onViewAction(action: NotificationPermissionViewActions) {
        when (action) {
            NotificationPermissionViewActions.OnAllow -> {
                analytics.logEvent(Event.FakeNotificationAllow)
                sendEvent(NotificationPermissionViewEvents.AskPermission)
            }

            NotificationPermissionViewActions.OnDismiss -> {
                analytics.logEvent(Event.FakeNotificationDeny)
                sendEvent(NotificationPermissionViewEvents.NavigateBack)
            }

            NotificationPermissionViewActions.OnCreate -> {
                analytics.logScreen(NotificationPermissionAnalytics.FakeNotificationScreen)
            }
        }
    }
}
