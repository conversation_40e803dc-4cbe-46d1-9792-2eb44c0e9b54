package co.thewordlab.luzia.core.connectivity.screen

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.foundation.analytics.helpers.ConnectivityObserver
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject

@HiltViewModel
class ConnectivityScreenViewModel @Inject constructor(
    private val connectivityObserver: ConnectivityObserver
) : ViewModel(),
    ViewModelActions<ConnectivityScreenViewActions>,
    ViewModelStates<ConnectivityScreenViewState> by ViewModelStatesImpl(ConnectivityScreenViewState()) {

    init {
        listenToConnectivityChanges()
    }

    override fun onViewAction(action: ConnectivityScreenViewActions) {
        when (action) {
            ConnectivityScreenViewActions.OnRetry -> onRetry()
        }
    }

    private fun listenToConnectivityChanges() {
        connectivityObserver.observe()
            .distinctUntilChanged()
            .onEach { state -> updateState { it.copy(isConnected = state.isConnected) } }
            .launchIn(viewModelScope)
    }

    private fun onRetry() {
        connectivityObserver.refreshState()
    }
}
