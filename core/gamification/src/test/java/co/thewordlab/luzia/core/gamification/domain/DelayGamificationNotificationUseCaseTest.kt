package co.thewordlab.luzia.core.gamification.domain

import io.mockk.Runs
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.just
import io.mockk.mockk
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test

@OptIn(ExperimentalCoroutinesApi::class)
class DelayGamificationNotificationUseCaseTest {

    private val gamificationRepository: GamificationRepository = mockk()
    private val testDispatcher = StandardTestDispatcher()
    private val testScope = TestScope(testDispatcher)

    private lateinit var delayGamificationNotificationUseCase: DelayGamificationNotificationUseCase

    @Before
    fun setUp() {
        delayGamificationNotificationUseCase = DelayGamificationNotificationUseCase(gamificationRepository)
    }

    @Test
    fun `invoke WHEN called THEN delegates to repository`() = testScope.runTest {
        // Given
        val rewardId = "reward1"
        coEvery { gamificationRepository.delayReward(any()) } just Runs

        // When
        delayGamificationNotificationUseCase.invoke(rewardId)

        // Then
        coVerify { gamificationRepository.delayReward(rewardId) }
    }
}
