package co.thewordlab.luzia.core.gamification.data.persistence

import co.thewordlab.fouundation.persistence.gamification.GamificationDao
import co.thewordlab.fouundation.persistence.gamification.LevelEntity
import co.thewordlab.fouundation.persistence.gamification.LevelState
import co.thewordlab.fouundation.persistence.gamification.LevelUpEntity
import co.thewordlab.fouundation.persistence.gamification.PendingRewardEntity
import co.thewordlab.fouundation.persistence.gamification.RewardEntity
import co.thewordlab.fouundation.persistence.gamification.RewardState
import co.thewordlab.luzia.core.gamification.data.model.LevelDto
import co.thewordlab.luzia.core.gamification.data.model.LevelFeatureCategory
import co.thewordlab.luzia.core.gamification.data.model.LevelFeatureDto
import co.thewordlab.luzia.core.gamification.data.model.LevelFeatureResponseDto
import co.thewordlab.luzia.core.gamification.data.model.LevelFeatureStatus
import co.thewordlab.luzia.core.gamification.data.model.UserScoreDto
import io.mockk.Runs
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertNull
import org.junit.Before
import org.junit.Test

@OptIn(ExperimentalCoroutinesApi::class)
class GamificationPersistenceTest {

    private val gamificationDao: GamificationDao = mockk()
    private val testDispatcher = StandardTestDispatcher()
    private val testScope = TestScope(testDispatcher)

    private lateinit var gamificationPersistence: GamificationPersistence

    @Before
    fun setUp() {
        gamificationPersistence = GamificationPersistence(gamificationDao)
    }

    @Test
    fun `getData WHEN data exists THEN returns mapped GamificationData`() = testScope.runTest {
        // Given
        val levelEntity = LevelEntity(
            currentLevel = 5,
            currentPoints = 100,
            totalPointsForCurrentLevel = 50,
            totalPointsForNextLevel = 200,
            streak = 3
        )
        val rewardEntity = RewardEntity(
            id = "reward1",
            level = 5,
            points = 50,
            image = "image.png",
            imageCelebration = "celebration.png",
            message = "Reward message",
            messageCelebration = "Celebration message",
            title = "Reward title",
            titleCelebration = "Celebration title",
            type = "type1",
            state = RewardState.Unclaimed,
            deeplink = "app://reward",
            buttonClose = "Close",
            buttonAction = "Claim"
        )
        val levelUpEntity = LevelUpEntity(
            id = "levelup1",
            level = 5,
            points = 100,
            image = "image.png",
            imageCelebration = "celebration.png",
            message = "Level up message",
            messageCelebration = "Celebration message",
            title = "Level up title",
            titleCelebration = "Celebration title",
            type = "type1",
            state = LevelState.Unclaimed,
            deeplink = "app://levelup",
            buttonClose = "Close",
            buttonAction = "Continue"
        )
        val pendingRewardEntity = PendingRewardEntity(
            id = "pending1",
            dismissed = false,
            isMajor = true,
            earnedPoints = 50,
            level = 5,
            isClaimed = false
        )

        every { gamificationDao.getLevel() } returns flowOf(levelEntity)
        every { gamificationDao.getRewards() } returns flowOf(listOf(rewardEntity))
        every { gamificationDao.getLevelUps() } returns flowOf(listOf(levelUpEntity))
        every { gamificationDao.getMajorPendingRewards() } returns flowOf(listOf(pendingRewardEntity))

        // When
        val result = gamificationPersistence.getData().first()

        // Then
        assertNotNull(result)
        assertEquals(5, result?.level)
        assertEquals(100, result?.points)
        assertEquals(3, result?.streak)
        assertEquals(50, result?.totalPointsForCurrentLevel)
        assertEquals(200, result?.totalPointsForNextLevel)
        assertEquals(1, result?.rewards?.size)
        assertEquals(1, result?.levelUps?.size)
        assertEquals(1, result?.pendingRewards?.size)
        assertEquals("reward1", result?.rewards?.first()?.id)
        assertEquals("levelup1", result?.levelUps?.first()?.id)
        assertEquals("pending1", result?.pendingRewards?.first()?.id)
    }

    @Test
    fun `saveUserScore WHEN called THEN saves level entity and checks level up actions`() = testScope.runTest {
        // Given
        val userScoreDto = UserScoreDto(
            level = LevelDto(
                currentLevel = 5,
                currentPoints = 100,
                totalPointsForCurrentLevel = 50,
                totalPointsForNextLevel = 200
            ),
            points = 100,
            streak = 3
        )
        val levelEntitySlot = slot<LevelEntity>()

        coEvery { gamificationDao.saveLevel(capture(levelEntitySlot)) } just Runs
        every { gamificationDao.getLevel() } returns flowOf(LevelEntity(currentLevel = 4))
        coEvery { gamificationDao.findReward(5) } returns RewardEntity(id = "reward1", level = 5)
        coEvery { gamificationDao.savePendingReward(any()) } just Runs
        coEvery { gamificationDao.findPastRewards(any()) } returns emptyList()
        coEvery { gamificationDao.savePendingRewards(any()) } just Runs

        // When
        gamificationPersistence.saveUserScore(userScoreDto)

        // Then
        coVerify { gamificationDao.saveLevel(any()) }
        assertEquals(5, levelEntitySlot.captured.currentLevel)
        assertEquals(100, levelEntitySlot.captured.currentPoints)
        assertEquals(50, levelEntitySlot.captured.totalPointsForCurrentLevel)
        assertEquals(200, levelEntitySlot.captured.totalPointsForNextLevel)
        assertEquals(3, levelEntitySlot.captured.streak)
        coVerify { gamificationDao.savePendingReward(any()) }
    }

    @Test
    fun `saveFeatures WHEN called THEN saves filtered rewards`() = testScope.runTest {
        // Given
        val featureDto1 = LevelFeatureDto(
            id = "feature1",
            level = 5,
            points = 50,
            category = LevelFeatureCategory.FeatureGating,
            status = LevelFeatureStatus.Unclaimed
        )
        val featureDto2 = LevelFeatureDto(
            id = "feature2",
            level = 6,
            points = 60,
            category = LevelFeatureCategory.LevelUp,
            status = LevelFeatureStatus.Locked
        )
        val featuresData = LevelFeatureResponseDto(
            rewards = listOf(featureDto1, featureDto2)
        )
        val rewardEntitiesSlot = slot<List<RewardEntity>>()

        coEvery { gamificationDao.clearRewards() } just Runs
        coEvery { gamificationDao.saveRewards(capture(rewardEntitiesSlot)) } just Runs

        // When
        gamificationPersistence.saveFeatures(featuresData)

        // Then
        coVerify { gamificationDao.clearRewards() }
        coVerify { gamificationDao.saveRewards(any()) }
        assertEquals(1, rewardEntitiesSlot.captured.size)
        assertEquals("feature1", rewardEntitiesSlot.captured[0].id)
    }

    @Test
    fun `saveLevelUps WHEN called THEN saves filtered level ups`() = testScope.runTest {
        // Given
        val featureDto1 = LevelFeatureDto(
            id = "feature1",
            level = 5,
            points = 50,
            category = LevelFeatureCategory.FeatureGating,
            status = LevelFeatureStatus.Unclaimed
        )
        val featureDto2 = LevelFeatureDto(
            id = "feature2",
            level = 6,
            points = 60,
            category = LevelFeatureCategory.LevelUp,
            status = LevelFeatureStatus.Locked
        )
        val levelUpsData = LevelFeatureResponseDto(
            rewards = listOf(featureDto1, featureDto2)
        )
        val levelUpEntitiesSlot = slot<List<LevelUpEntity>>()

        coEvery { gamificationDao.clearLevelUps() } just Runs
        coEvery { gamificationDao.saveLevelUps(capture(levelUpEntitiesSlot)) } just Runs

        // When
        gamificationPersistence.saveLevelUps(levelUpsData)

        // Then
        coVerify { gamificationDao.clearLevelUps() }
        coVerify { gamificationDao.saveLevelUps(any()) }
        assertEquals(1, levelUpEntitiesSlot.captured.size)
        assertEquals("feature2", levelUpEntitiesSlot.captured[0].id)
    }

    @Test
    fun `getPendingRewards WHEN rewards exist THEN returns first pending reward`() = testScope.runTest {
        // Given
        val pendingRewardEntity = PendingRewardEntity(
            id = "pending1",
            dismissed = false,
            isMajor = true,
            earnedPoints = 50,
            level = 5,
            isClaimed = false
        )

        every { gamificationDao.getPendingRewards(true, false) } returns flowOf(listOf(pendingRewardEntity))

        // When
        val result = gamificationPersistence.getPendingRewards(true, false).first()

        // Then
        assertNotNull(result)
        assertEquals("pending1", result?.id)
        assertEquals(5, result?.level)
        assertEquals(50, result?.earnedPoints)
        assertEquals(false, result?.isClaimed)
    }

    @Test
    fun `getPendingRewards WHEN no rewards exist THEN returns null`() = testScope.runTest {
        // Given
        every { gamificationDao.getPendingRewards(true, false) } returns flowOf(emptyList())

        // When
        val result = gamificationPersistence.getPendingRewards(true, false).first()

        // Then
        assertNull(result)
    }

    @Test
    fun `dismissReward WHEN shouldAlsoClaim is true THEN calls dismissAndClaimReward`() = testScope.runTest {
        // Given
        coEvery { gamificationDao.dismissAndClaimReward(any()) } just Runs

        // When
        gamificationPersistence.dismissReward("reward1", true)

        // Then
        coVerify { gamificationDao.dismissAndClaimReward("reward1") }
    }

    @Test
    fun `dismissReward WHEN shouldAlsoClaim is false THEN calls dismissReward`() = testScope.runTest {
        // Given
        coEvery { gamificationDao.dismissReward(any()) } just Runs

        // When
        gamificationPersistence.dismissReward("reward1", false)

        // Then
        coVerify { gamificationDao.dismissReward("reward1") }
    }

    @Test
    fun `delayReward WHEN called THEN calls delayReward on dao`() = testScope.runTest {
        // Given
        coEvery { gamificationDao.delayReward(any()) } just Runs

        // When
        gamificationPersistence.delayReward("reward1")

        // Then
        coVerify { gamificationDao.delayReward("reward1") }
    }

    @Test
    fun `getRewardById WHEN reward exists THEN returns mapped reward`() = testScope.runTest {
        // Given
        val rewardEntity = RewardEntity(
            id = "reward1",
            level = 5,
            points = 50,
            image = "image.png",
            imageCelebration = "celebration.png",
            message = "Reward message",
            messageCelebration = "Celebration message",
            title = "Reward title",
            titleCelebration = "Celebration title",
            type = "type1",
            state = RewardState.Unclaimed,
            deeplink = "app://reward",
            buttonClose = "Close",
            buttonAction = "Claim"
        )

        coEvery { gamificationDao.getRewardById("reward1") } returns rewardEntity

        // When
        val result = gamificationPersistence.getRewardById("reward1")

        // Then
        assertNotNull(result)
        assertEquals("reward1", result?.id)
        assertEquals("Reward title", result?.title)
        assertEquals("Reward message", result?.message)
        assertEquals(5, result?.level)
        assertEquals(50, result?.point)
    }

    @Test
    fun `getRewardById WHEN reward does not exist THEN returns null`() = testScope.runTest {
        // Given
        coEvery { gamificationDao.getRewardById("reward1") } returns null

        // When
        val result = gamificationPersistence.getRewardById("reward1")

        // Then
        assertNull(result)
    }

    @Test
    fun `clearAll WHEN called THEN calls clear methods on dao`() = testScope.runTest {
        // Given
        coEvery { gamificationDao.deleteLevel() } just Runs
        coEvery { gamificationDao.clearLevelUps() } just Runs
        coEvery { gamificationDao.clearRewards() } just Runs
        coEvery { gamificationDao.clearPendingRewards() } just Runs

        // When
        gamificationPersistence.clearAll()

        // Then
        coVerify { gamificationDao.deleteLevel() }
        coVerify { gamificationDao.clearLevelUps() }
        coVerify { gamificationDao.clearRewards() }
        coVerify { gamificationDao.clearPendingRewards() }
    }
}
