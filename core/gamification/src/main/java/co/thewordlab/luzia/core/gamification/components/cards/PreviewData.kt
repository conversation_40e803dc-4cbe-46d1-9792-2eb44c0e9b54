package co.thewordlab.luzia.core.gamification.components.cards

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import co.thewordlab.luzia.core.gamification.components.cards.GamificationCardUiModel.AvailableCard
import co.thewordlab.luzia.core.gamification.components.cards.GamificationCardUiModel.ClaimedCard
import co.thewordlab.luzia.core.gamification.components.cards.GamificationCardUiModel.ComingSoonCard
import co.thewordlab.luzia.core.gamification.components.cards.GamificationCardUiModel.LockedCard

internal class PreviewData : PreviewParameterProvider<GamificationCardUiModel> {
    override val values: Sequence<GamificationCardUiModel> = sequenceOf(
        LockedCard(
            id = "bestie_reward_locked",
            title = "Create your Bestie",
            body = "Design a friend that matches your vibe",
            imageUrl = "",
            levelRequired = 5,
            onClick = {}
        ),
        AvailableCard(
            id = "bestie_reward_available",
            title = "Create your Bestie",
            body = "Design a friend that matches your vibe",
            imageUrl = "",
            onClick = {}
        ),
        ClaimedCard(
            id = "bestie_reward_claimed",
            title = "Create your Bestie",
            onClick = {}
        ),
        ComingSoonCard
    )
}
