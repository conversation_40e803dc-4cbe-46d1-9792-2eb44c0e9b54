package co.thewordlab.luzia.core.gamification.data.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class UserScoreDto(
    @Json(name = "points")
    val points: Int,
    @<PERSON><PERSON>(name = "streak")
    val streak: Int,
    @<PERSON><PERSON>(name = "level")
    val level: LevelDto? = null
)

@JsonClass(generateAdapter = true)
data class LevelDto(
    @Json(name = "currentLevel")
    val currentLevel: Int,
    @Json(name = "currentPoints")
    val currentPoints: Int,
    @Json(name = "totalPointsForCurrentLevel")
    val totalPointsForCurrentLevel: Int,
    @<PERSON><PERSON>(name = "totalPointsForNextLevel")
    val totalPointsForNextLevel: Int
)
