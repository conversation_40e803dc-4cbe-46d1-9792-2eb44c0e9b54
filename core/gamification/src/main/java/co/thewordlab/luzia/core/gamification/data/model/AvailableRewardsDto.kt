package co.thewordlab.luzia.core.gamification.data.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class AvailableRewardsDto(
    @Json(name = "data")
    val data: List<AvailableRewardDto> = emptyList()
)

@JsonClass(generateAdapter = true)
data class AvailableRewardDto(
    @Json(name = "title")
    val title: String = "",
    @Json(name = "points")
    val points: Int = 0
)
