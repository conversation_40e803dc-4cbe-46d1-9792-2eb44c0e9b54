package co.thewordlab.luzia.core.gamification.domain

import co.thewordlab.luzia.core.profile.domain.GetUserProfileUseCase
import co.thewordlab.luzia.core.profile.domain.model.UserType
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.mapLatest
import javax.inject.Inject

class FetchGamificationNotificationUseCase @Inject constructor(
    private val gamificationRepository: GamificationRepository,
    private val getUserProfileUseCase: GetUserProfileUseCase,
    private val featureFlagManager: FeatureFlagManager
) {

    operator fun invoke(isMajor: Boolean, isDelayed: Boolean) = getUserProfileUseCase.invoke()
        .mapLatest { it?.userType == UserType.FULL_USER }
        .filter { it && featureFlagManager.get(FeatureFlag.GamificationEnabled) }
        .flatMapLatest { gamificationRepository.getPendingRewards(isMajor, isDelayed) }
        .distinctUntilChanged()
}
