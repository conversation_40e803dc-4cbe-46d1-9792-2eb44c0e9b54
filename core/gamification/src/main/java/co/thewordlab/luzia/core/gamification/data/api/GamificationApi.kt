package co.thewordlab.luzia.core.gamification.data.api

import co.thewordlab.luzia.core.gamification.data.model.AvailableRewardsDto
import co.thewordlab.luzia.core.gamification.data.model.LevelFeatureResponseDto
import co.thewordlab.luzia.core.gamification.data.model.UserScoreDto
import co.thewordlab.luzia.foundation.networking.headers.FullUserNeeded
import co.thewordlab.luzia.foundation.networking.model.ErrorDto
import com.slack.eithernet.ApiResult
import com.slack.eithernet.DecodeErrorBody
import retrofit2.http.GET

interface GamificationApi {

    @FullUserNeeded
    @DecodeErrorBody
    @GET("user/score")
    suspend fun getUserScore(): ApiResult<UserScoreDto, ErrorDto>

    @DecodeErrorBody
    @GET("user/rewards/feature-gating")
    suspend fun getFeatures(): ApiResult<LevelFeatureResponseDto, ErrorDto>

    @FullUserNeeded
    @DecodeErrorBody
    @GET("user/rewards/level-up")
    suspend fun getLevelUps(): ApiResult<LevelFeatureResponseDto, ErrorDto>

    @DecodeErrorBody
    @GET("user/rewards/available-rewards")
    suspend fun getGamificationInfo(): ApiResult<AvailableRewardsDto, ErrorDto>
}
