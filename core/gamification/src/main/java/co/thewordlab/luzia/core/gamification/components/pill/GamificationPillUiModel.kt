package co.thewordlab.luzia.core.gamification.components.pill

import androidx.compose.runtime.Immutable

@Immutable
sealed class GamificationPillUiModel(
    open val streakCount: Int,
    open val level: Int,
    open val currentBpCount: Int = 0,
    open val totalBpCount: Int = 0
) {

    data class Active(
        override val streakCount: Int,
        override val level: Int,
        override val currentBpCount: Int,
        override val totalBpCount: Int
    ) : GamificationPillUiModel(streakCount, level, currentBpCount, totalBpCount)

    data object Disabled : GamificationPillUiModel(0, 0, 0, DISABLED_TOTAL_BP_COUNT)

    private companion object {
        const val DISABLED_TOTAL_BP_COUNT = 100
    }
}
