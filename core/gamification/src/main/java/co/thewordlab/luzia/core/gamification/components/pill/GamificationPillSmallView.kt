package co.thewordlab.luzia.core.gamification.components.pill

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.tooling.preview.PreviewParameter
import co.thewordlab.luzia.foundation.common.extensions.toSocialNumberFormat
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

/**
 * Displays Gamification stats like Streak, BP (Bestie Points), and Level.
 *
 * @param data The data to display in the pill.
 * @param modifier Modifier for this composable.
 */
@Composable
fun GamificationPillSmallView(
    data: GamificationPillUiModel,
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
    activeBackgroundColor: Color = LuziaTheme.palette.surface.content
) {
    val enabled = data is GamificationPillUiModel.Active
    val backgroundColor = if (enabled) {
        activeBackgroundColor
    } else {
        LuziaTheme.palette.primitives.neutral.neutral7
    }
    Row(
        modifier = modifier
            .clip(RoundedCornerShape(Corners.X4.dp))
            .background(backgroundColor)
            .padding(horizontal = Spacing.X12.dp, vertical = Spacing.X6.dp)
            .click(action = onClick)
            .testTag("viewGamificationPillSmall"),
        horizontalArrangement = Arrangement.spacedBy(Spacing.X10.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        GamificationItem(
            icon = R.drawable.ic_thunder_20,
            text = data.streakCount.toString(),
            colorTint = if (enabled) Color.Unspecified else LuziaTheme.palette.primitives.neutral.neutral30
        )
        GamificationItem(
            icon = R.drawable.ic_heart_20,
            text = data.currentBpCount.toSocialNumberFormat(),
            colorTint = if (enabled) Color.Unspecified else LuziaTheme.palette.primitives.neutral.neutral30
        )
        GamificationItem(
            icon = R.drawable.ic_level_20,
            text = data.level.toString(),
            colorTint = if (enabled) Color.Unspecified else LuziaTheme.palette.primitives.neutral.neutral30
        )
    }
}

@Composable
fun GamificationItem(
    @DrawableRes icon: Int,
    text: String,
    colorTint: Color = Color.Unspecified,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            painter = painterResource(icon),
            contentDescription = null,
            tint = colorTint,
            modifier = Modifier.size(IconSizes.X20.dp)
        )
        LuziaText(
            text = text,
            style = LuziaTheme.typography.body.semiBold.footnote,
            color = LuziaTheme.palette.text.primary
        )
    }
}

@PreviewLightDark
@Composable
private fun GamificationPillPreview(
    @PreviewParameter(PreviewData::class) data: GamificationPillUiModel
) {
    LuziaTheme {
        Surface(color = LuziaTheme.palette.surface.background) {
            Box(modifier = Modifier.padding(Spacing.X4.dp)) {
                GamificationPillSmallView(data = data, onClick = {})
            }
        }
    }
}
