package co.thewordlab.luzia.core.gamification.domain

import co.thewordlab.luzia.core.gamification.domain.model.GamificationData
import co.thewordlab.luzia.core.gamification.domain.model.InfoItem
import co.thewordlab.luzia.core.gamification.domain.model.PendingReward
import co.thewordlab.luzia.core.gamification.domain.model.Reward
import co.thewordlab.luzia.foundation.networking.model.Error
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import kotlinx.coroutines.flow.Flow

interface GamificationRepository {

    suspend fun fetchData()

    suspend fun fetchAndSaveFeatures()

    fun getData(): Flow<GamificationData?>

    fun getPendingRewards(isMajor: Boolean, isDelayed: Boolean): Flow<PendingReward?>

    fun getPendingReward(id: String): Flow<PendingReward?>

    suspend fun getGamificationInfo(): ResultOf<List<InfoItem>, Error>

    suspend fun delayReward(rewardId: String)

    suspend fun dismissReward(rewardId: String, shouldAlsoClaim: Boolean)

    suspend fun getRewardById(rewardId: String): Reward?

    suspend fun clearAll()
}
