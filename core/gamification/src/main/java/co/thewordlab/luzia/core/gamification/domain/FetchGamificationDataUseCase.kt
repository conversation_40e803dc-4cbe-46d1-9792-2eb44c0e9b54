package co.thewordlab.luzia.core.gamification.domain

import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import co.thewordlab.luzia.foundation.networking.session.UserType
import javax.inject.Inject

class FetchGamificationDataUseCase @Inject constructor(
    private val gamificationRepository: GamificationRepository,
    private val userSessionManager: UserSessionManager
) {

    suspend operator fun invoke() {
        val session = userSessionManager.getUserSessionImmediate()
        val fullUser = session?.userType == UserType.FULL_USER
        if (fullUser) {
            gamificationRepository.fetchData()
        } else {
            gamificationRepository.fetchAndSaveFeatures()
        }
    }
}
