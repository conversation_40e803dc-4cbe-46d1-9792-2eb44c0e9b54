package co.thewordlab.luzia.core.gamification.components.popup

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import co.thewordlab.luzia.core.gamification.domain.model.InfoItem
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun GamificationInfoPopupView(
    modifier: Modifier,
    items: List<InfoItem>,
    onDismiss: () -> Unit
) {
    Dialog(onDismiss, DialogProperties(usePlatformDefaultWidth = false)) {
        Box(Modifier.fillMaxSize()) {
            Box(
                modifier = modifier
                    .align(Alignment.Center)
                    .fillMaxWidth()
                    .navigationBarsPadding()
                    .padding(horizontal = Spacing.X32.dp, vertical = Spacing.X24.dp)
                    .clip(RoundedCornerShape(Corners.X4.dp))
                    .background(LuziaTheme.palette.surface.content)
            ) {
                Column(
                    modifier = Modifier.padding(
                        horizontal = Spacing.X16.dp,
                        vertical = Spacing.X16.dp
                    ),
                    verticalArrangement = Arrangement.spacedBy(Spacing.X16.dp)
                ) {
                    Icon(
                        modifier = Modifier.size(IconSizes.X48.dp),
                        tint = Color.Unspecified,
                        painter = painterResource(id = designR.drawable.im_streaks_thunder),
                        contentDescription = null
                    )
                    LuziaText(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(localizationR.string.gamification_bp_info_streak_title),
                        style = LuziaTheme.typography.body.semiBold.default,
                        color = LuziaTheme.palette.text.primary
                    )
                    LuziaText(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(localizationR.string.gamification_bp_info_streak_desc),
                        style = LuziaTheme.typography.body.regular.small,
                        color = LuziaTheme.palette.text.secondary,
                    )
                    Icon(
                        modifier = Modifier.size(IconSizes.X48.dp),
                        tint = Color.Unspecified,
                        painter = painterResource(id = designR.drawable.im_streaks_heart),
                        contentDescription = null
                    )
                    LuziaText(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(localizationR.string.gamification_bp_info_bestie_points_title),
                        style = LuziaTheme.typography.body.semiBold.default,
                        color = LuziaTheme.palette.text.primary
                    )
                    LuziaText(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(localizationR.string.gamification_bp_info_bestie_points_desc),
                        style = LuziaTheme.typography.body.regular.small,
                        color = LuziaTheme.palette.text.secondary,
                    )
                    InfoItemsView(items = items)
                }
                IconButton(
                    modifier = Modifier.align(Alignment.TopEnd),
                    onClick = onDismiss
                ) {
                    Icon(
                        painter = painterResource(designR.drawable.ic_close),
                        contentDescription = null,
                        tint = LuziaTheme.palette.interactive.primary
                    )
                }
            }
        }
    }
}

@Composable
private fun InfoItemsView(items: List<InfoItem>) {
    val state = rememberScrollState()
    Column(
        verticalArrangement = Arrangement.spacedBy(Spacing.X4.dp),
        modifier = Modifier
            .fillMaxWidth()
            .height(Spacing.X150.dp)
            .verticalScroll(state)
    ) {
        items.forEach { item ->
            InfoItemView(item)
        }
    }
}

@Composable
private fun InfoItemView(item: InfoItem) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(Corners.X2.dp))
            .background(LuziaTheme.palette.surface.background)
            .padding(start = Spacing.X12.dp)
            .padding(end = Spacing.X4.dp)
            .padding(vertical = Spacing.X8.dp)
    ) {
        LuziaText(
            modifier = Modifier.weight(1f),
            text = item.title,
            style = LuziaTheme.typography.body.semiBold.footnote,
            color = LuziaTheme.palette.text.primary
        )
        LuziaText(
            modifier = Modifier
                .clip(CircleShape)
                .background(LuziaTheme.palette.accents.pink.pink10)
                .padding(horizontal = Spacing.X8.dp, vertical = Spacing.X2.dp),
            text = "+${item.points} BP",
            style = LuziaTheme.typography.body.semiBold.footnote,
            color = LuziaTheme.palette.text.primary
        )
    }
}
