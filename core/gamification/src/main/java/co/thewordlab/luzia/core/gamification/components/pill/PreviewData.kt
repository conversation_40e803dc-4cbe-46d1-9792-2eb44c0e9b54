package co.thewordlab.luzia.core.gamification.components.pill

import androidx.compose.ui.tooling.preview.PreviewParameterProvider

internal class PreviewData : PreviewParameterProvider<GamificationPillUiModel> {
    override val values: Sequence<GamificationPillUiModel> = sequenceOf(
        GamificationPillUiModel.Active(
            streakCount = 32,
            level = 2,
            currentBpCount = 20,
            totalBpCount = 100
        ),
        GamificationPillUiModel.Disabled
    )
}
