package co.thewordlab.luzia.core.gamification.components.cards

import androidx.compose.runtime.Immutable

@Immutable
sealed class GamificationCardUiModel {
    data class LockedCard(
        val id: String,
        val title: String,
        val body: String,
        val imageUrl: String,
        val levelRequired: Int,
        val onClick: () -> Unit
    ) : GamificationCardUiModel()

    data class AvailableCard(
        val id: String,
        val title: String,
        val body: String,
        val imageUrl: String,
        val onClick: () -> Unit
    ) : GamificationCardUiModel()

    data class ClaimedCard(
        val id: String,
        val title: String,
        val onClick: () -> Unit
    ) : GamificationCardUiModel()

    data object ComingSoonCard : GamificationCardUiModel()
}
