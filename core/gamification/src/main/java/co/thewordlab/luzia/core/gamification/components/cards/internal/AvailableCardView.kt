package co.thewordlab.luzia.core.gamification.components.cards.internal

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import co.thewordlab.luzia.core.gamification.components.cards.GamificationCardUiModel
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.ButtonSize
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.LuziaSecondaryButton
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.addShadow
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import coil3.size.Scale
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

private const val IMAGE_ASPECT_RATIO = 160f / 180f
private const val IMAGE_WIDTH_RATIO = 160f / 330f

@Suppress("DestructuringDeclarationWithTooManyEntries")
@Composable
internal fun AvailableCardView(modifier: Modifier, data: GamificationCardUiModel.AvailableCard) {
    val textColor = LuziaTheme.palette.text.contrast
    val buttonSize = if (isSmallScreen()) ButtonSize.SMALL else ButtonSize.LARGE
    val defaultPadding = getDefaultPadding()

    ConstraintLayout(
        modifier = modifier
            .addShadow()
            .fillMaxWidth()
            .clip(RoundedCornerShape(Corners.X3.dp))
            .background(LuziaTheme.palette.primitives.brand.brand50)
    ) {
        val (textColumn, illustration, statusArea) = createRefs()

        Column(
            modifier = Modifier
                .constrainAs(textColumn) {
                    start.linkTo(parent.start, margin = defaultPadding)
                    end.linkTo(illustration.start)
                    top.linkTo(parent.top, margin = defaultPadding)
                    bottom.linkTo(statusArea.top)
                    width = Dimension.fillToConstraints
                    verticalBias = 0f
                },
            verticalArrangement = Arrangement.spacedBy(Spacing.X12.dp)
        ) {
            LuziaText(
                modifier = Modifier.fillMaxWidth(),
                text = data.title,
                maxLines = 2,
                style = LuziaTheme.typography.headlines.h4,
                color = textColor
            )
            LuziaText(
                modifier = Modifier.fillMaxWidth(),
                text = data.body,
                style = LuziaTheme.typography.body.regular.footnote,
                color = textColor
            )
        }

        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(data.imageUrl)
                .crossfade(true)
                .scale(Scale.FILL)
                .build(),
            contentDescription = data.title,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .fillMaxWidth(IMAGE_WIDTH_RATIO)
                .aspectRatio(IMAGE_ASPECT_RATIO)
                .padding(Spacing.X8.dp)
                .constrainAs(illustration) {
                    end.linkTo(parent.end)
                    top.linkTo(parent.top)
                }
        )

        LuziaSecondaryButton(
            modifier = Modifier
                .constrainAs(statusArea) {
                    start.linkTo(parent.start, defaultPadding)
                    bottom.linkTo(parent.bottom, defaultPadding)
                    width = Dimension.wrapContent
                },
            icon = painterResource(designR.drawable.ic_gift_24),
            onClick = data.onClick,
            size = buttonSize,
            text = stringResource(localizationR.string.gamification_claim)
        )
    }
}
