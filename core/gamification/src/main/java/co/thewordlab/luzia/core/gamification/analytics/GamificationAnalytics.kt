package co.thewordlab.luzia.core.gamification.analytics

import co.thewordlab.luzia.foundation.analytics.AnalyticsActions
import co.thewordlab.luzia.foundation.analytics.AnalyticsScreens

class GamificationAnalytics {

    data class TapGamificationPillHome(val origin: GamificationOrigin) :
        AnalyticsActions(
            "tap_gamification_pill_home",
            mapOf("origin" to origin.value)
        )

    data class TapClaimReward(val origin: GamificationOrigin, val rewardId: String) :
        AnalyticsActions(
            "tap_claim_reward",
            mapOf(
                "origin" to origin.value,
                "reward_type" to rewardId
            )
        )

    data class TapLockedReward(val origin: GamificationOrigin, val rewardId: String) :
        AnalyticsActions(
            "tap_locked_reward",
            mapOf(
                "origin" to origin.value,
                "reward_type" to rewardId
            )
        )

    data class TapClaimedReward(val origin: GamificationOrigin, val rewardId: String) :
        AnalyticsActions(
            "tap_claimed_reward",
            mapOf(
                "origin" to origin.value,
                "reward_type" to rewardId
            )
        )

    data class TapSurpriseComingReward(val origin: GamificationOrigin) :
        AnalyticsActions(
            "tap_surprise_coming",
            mapOf("origin" to origin.value)
        )

    data class TapDismissReward(val origin: GamificationOrigin, val rewardId: String) :
        AnalyticsActions(
            "tap_dismiss_reward",
            mapOf(
                "origin" to origin.value,
                "reward_type" to rewardId
            )
        )

    data class TapDismissAlert(val origin: GamificationOrigin, val rewardId: String) :
        AnalyticsActions(
            "tap_dismiss_alert",
            mapOf("reward_type" to rewardId)
        )

    data object TapGamificationInfo :
        AnalyticsActions(
            "tap_gamification_info"
        )

    data object ScrollRewardCarousel :
        AnalyticsActions(
            "scroll_reward_carousel"
        )

    data class ProfileRegister(val origin: GamificationOrigin) :
        AnalyticsActions(
            "profile_register",
            mapOf("origin" to origin.value)
        )

    data class LevelUnlocked(val level: Int) :
        AnalyticsActions(
            "level_unlocked",
            mapOf("level_number" to level)
        )

    data object GamificationTabScreen :
        AnalyticsScreens(
            "gamification_tab"
        )

    data class RewardCelebrationScreen(val level: Int) :
        AnalyticsScreens(
            "reward_celebration",
            mapOf("level_number" to level)
        )
}

enum class GamificationOrigin(val value: String) {
    Home("home"),
    GamificationDetail("gamification_screen"),
    Celebration("reward_celebration")
}
