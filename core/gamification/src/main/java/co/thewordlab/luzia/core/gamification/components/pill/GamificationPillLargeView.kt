package co.thewordlab.luzia.core.gamification.components.pill

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import co.thewordlab.luzia.foundation.common.extensions.toSocialNumberFormat
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.addShadow
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.localization.R as localizationR

// Custom text style to achieve Bold Footnote
private val BoldFootnote: TextStyle
    @Composable
    get() = LuziaTheme.typography.body.semiBold.footnote.copy(fontWeight = FontWeight.Bold)

/**
 * Displays Gamification stats (Streak, BP Progress) in a larger format.
 *
 * @param data The data to display in the pill.
 * @param modifier Modifier for this composable.
 */
@Composable
fun GamificationPillLargeView(
    data: GamificationPillUiModel,
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    val enabled = data is GamificationPillUiModel.Active
    val backgroundColor = if (enabled) {
        LuziaTheme.palette.surface.content
    } else {
        LuziaTheme.palette.primitives.neutral.neutral7
    }
    Row(
        modifier = modifier
            .addShadow()
            .height(Spacing.X32.dp)
            .fillMaxWidth()
            .clip(RoundedCornerShape(Corners.X4.dp))
            .background(backgroundColor)
            .click(action = onClick)
            .padding(
                start = Spacing.X8.dp,
                end = Spacing.X12.dp,
                top = Spacing.X2.dp,
                bottom = Spacing.X2.dp
            )
            .testTag("viewGamificationPill"),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        StreakSection(streakCount = data.streakCount, enabled = enabled)
        Spacing.X8.Horizontal()
        BpProgressSection(data = data, enabled = enabled)
    }
}

@Composable
private fun StreakSection(streakCount: Int, enabled: Boolean) {
    val background = if (enabled) {
        LuziaTheme.palette.accents.yellow.yellow10
    } else {
        LuziaTheme.palette.primitives.neutral.neutral10
    }
    Row(
        modifier = Modifier
            .clip(RoundedCornerShape(Spacing.X24.dp))
            .background(background)
            .padding(horizontal = Spacing.X12.dp, vertical = Spacing.X2.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Spacing.X4.dp)
    ) {
        Icon(
            painter = painterResource(R.drawable.ic_thunder),
            contentDescription = null,
            tint = if (enabled) Color.Unspecified else LuziaTheme.palette.primitives.neutral.neutral30,
            modifier = Modifier.size(IconSizes.X16.dp)
        )
        LuziaText(
            text = streakCount.toString(),
            style = BoldFootnote,
            color = LuziaTheme.palette.text.primary
        )
    }
}

@Composable
private fun BpProgressSection(data: GamificationPillUiModel, enabled: Boolean) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
    ) {
        val bp = data.currentBpCount.toSocialNumberFormat()
        val totalBp = data.totalBpCount.toSocialNumberFormat()
        LuziaText(
            text = if (enabled) {
                stringResource(id = localizationR.string.bestie_points_level, data.level)
            } else {
                stringResource(id = localizationR.string.core_level)
            },
            style = BoldFootnote,
            color = LuziaTheme.palette.text.primary
        )
        Icon(
            painter = painterResource(R.drawable.ic_hearth),
            contentDescription = null,
            tint = if (enabled) Color.Unspecified else LuziaTheme.palette.primitives.neutral.neutral30,
            modifier = Modifier.size(IconSizes.X16.dp)
        )
        val totalBpCoerced = data.totalBpCount.coerceAtLeast(1)
        val progressValue = if (totalBpCoerced > 0) {
            data.currentBpCount.toFloat() / totalBpCoerced.toFloat()
        } else {
            0.0f
        }
        CustomProgressBar(
            enabled = enabled,
            progress = progressValue,
            modifier = Modifier.weight(1f)
        )
        LuziaText(
            text = "$bp/$totalBp",
            style = BoldFootnote,
            color = LuziaTheme.palette.text.primary
        )
        if (!enabled) {
            Icon(
                painter = painterResource(R.drawable.ic_lock_reward_20),
                contentDescription = null,
                tint = LuziaTheme.palette.interactive.secondary,
                modifier = Modifier
                    .size(IconSizes.X20.dp)
                    .clip(CircleShape)
                    .background(LuziaTheme.palette.surface.content)
            )
        }
    }
}

@Composable
private fun CustomProgressBar(
    progress: Float,
    modifier: Modifier = Modifier,
    color: Color = LuziaTheme.palette.accents.pink.pink50,
    trackColor: Color = LuziaTheme.palette.accents.pink.pink10,
    enabled: Boolean
) {
    val backColor = if (enabled) trackColor else LuziaTheme.palette.primitives.neutral.neutral10
    val indicatorColor = if (enabled) color else LuziaTheme.palette.primitives.neutral.neutral30
    LinearProgressIndicator(
        progress = { progress },
        modifier = modifier
            .height(Spacing.X6.dp)
            .clip(RoundedCornerShape(Corners.X3.dp))
            .background(backColor),
        color = indicatorColor,
        gapSize = 0.dp,
        trackColor = backColor,
        strokeCap = StrokeCap.Round,
        drawStopIndicator = {}
    )
}

@PreviewLightDark
@Composable
private fun GamificationPillPreview(
    @PreviewParameter(PreviewData::class) data: GamificationPillUiModel
) {
    LuziaTheme {
        Surface(color = LuziaTheme.palette.surface.background) {
            Box(modifier = Modifier.padding(Spacing.X4.dp)) {
                GamificationPillLargeView(data = data, onClick = {})
            }
        }
    }
}
