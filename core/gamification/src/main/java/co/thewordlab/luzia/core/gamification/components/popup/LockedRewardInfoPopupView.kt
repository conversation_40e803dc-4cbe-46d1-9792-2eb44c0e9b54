package co.thewordlab.luzia.core.gamification.components.popup

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.LuziaPrimaryButton
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun LockedRewardInfoPopupView(
    modifier: Modifier,
    level: Int,
    onDismiss: () -> Unit
) {
    Dialog(onDismiss, DialogProperties(usePlatformDefaultWidth = false)) {
        Box(Modifier.fillMaxSize()) {
            Box(
                modifier = modifier
                    .padding(horizontal = Spacing.X24.dp)
                    .align(Alignment.Center)
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(Corners.X4.dp))
                    .background(LuziaTheme.palette.surface.content)
            ) {
                Column(
                    modifier = Modifier
                        .padding(horizontal = Spacing.X16.dp)
                        .padding(top = Spacing.X40.dp)
                        .padding(bottom = Spacing.X12.dp),
                    verticalArrangement = Arrangement.spacedBy(Spacing.X16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    LuziaText(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(localizationR.string.gamification_locked_title),
                        style = LuziaTheme.typography.headlines.h4,
                        color = LuziaTheme.palette.text.primary,
                        textAlign = TextAlign.Center
                    )
                    LuziaText(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(localizationR.string.gamification_locked_desc, level),
                        style = LuziaTheme.typography.body.regular.small,
                        color = LuziaTheme.palette.text.secondary,
                        textAlign = TextAlign.Center
                    )
                    LuziaPrimaryButton(
                        modifier = Modifier.fillMaxWidth(),
                        onClick = onDismiss,
                        text = stringResource(localizationR.string.got_it_text)
                    )
                }
                IconButton(
                    modifier = Modifier.align(Alignment.TopEnd),
                    onClick = onDismiss
                ) {
                    Icon(
                        painter = painterResource(designR.drawable.ic_close),
                        contentDescription = null,
                        tint = LuziaTheme.palette.interactive.primary
                    )
                }
            }
        }
    }
}
