package co.thewordlab.luzia.core.gamification.components.cards.internal

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import co.thewordlab.luzia.core.gamification.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.addShadow
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
internal fun ComingSoonCardView(modifier: Modifier, onClick: () -> Unit) {
    val textColor = LuziaTheme.palette.text.contrast
    val statusBackgroundColor = LuziaTheme.palette.accents.blue.blue30
    val statusTextColor = LuziaTheme.palette.text.primary
    val statusIconColor = LuziaTheme.palette.interactive.secondary
    val defaultPadding = getDefaultPadding()
    ConstraintLayout(
        modifier = modifier
            .addShadow()
            .fillMaxWidth()
            .clip(RoundedCornerShape(Corners.X3.dp))
            .background(LuziaTheme.palette.accents.blue.blue90)
            .click(action = onClick)
    ) {
        val (textColumn, statusArea, art) = createRefs()

        Image(
            modifier = Modifier
                .constrainAs(art) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    end.linkTo(parent.end)
                    verticalBias = 1f
                },
            painter = painterResource(R.drawable.im_luzia_coming_soon),
            contentScale = ContentScale.FillHeight,
            contentDescription = null
        )

        Column(
            modifier = Modifier.constrainAs(textColumn) {
                start.linkTo(parent.start, margin = defaultPadding)
                end.linkTo(art.start)
                top.linkTo(parent.top, margin = defaultPadding)
                width = Dimension.fillToConstraints
            },
            verticalArrangement = Arrangement.spacedBy(Spacing.X12.dp)
        ) {
            LuziaText(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(localizationR.string.gamification_coming_soon_title),
                style = LuziaTheme.typography.headlines.h4,
                color = textColor
            )
            LuziaText(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(localizationR.string.gamification_coming_soon_body),
                style = LuziaTheme.typography.body.regular.footnote,
                color = textColor
            )
        }
        Row(
            modifier = Modifier
                .constrainAs(statusArea) {
                    start.linkTo(parent.start)
                    bottom.linkTo(parent.bottom, defaultPadding)
                    top.linkTo(textColumn.bottom, defaultPadding)
                    verticalBias = 1f
                    width = Dimension.wrapContent
                }
                .clip(RoundedCornerShape(topEnd = Spacing.X24.dp, bottomEnd = Spacing.X24.dp))
                .background(statusBackgroundColor),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
        ) {
            LuziaText(
                modifier = Modifier
                    .padding(vertical = Spacing.X16.dp)
                    .padding(start = Spacing.X16.dp),
                text = stringResource(localizationR.string.gamification_coming_soon),
                style = LuziaTheme.typography.body.semiBold.footnote,
                color = statusTextColor
            )
            Icon(
                painter = painterResource(id = designR.drawable.ic_lock_reward_20),
                contentDescription = null,
                tint = statusIconColor,
                modifier = Modifier
                    .padding(Spacing.X4.dp)
                    .size(IconSizes.X40.dp)
                    .clip(CircleShape)
                    .background(LuziaTheme.palette.primitives.neutral.neutral0)
                    .padding(Spacing.X8.dp)
            )
        }
    }
}
