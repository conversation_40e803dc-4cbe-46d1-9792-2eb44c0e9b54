package co.thewordlab.luzia.core.gamification.components.streaks

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.addShadow
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun StreaksCardView(
    data: StreaksCardUiModel,
    modifier: Modifier = Modifier
) {
    val progress =
        if (data.totalDays > 0) data.currentDay.toFloat() / data.totalDays.toFloat() else 0f
    val progressColor = LuziaTheme.palette.accents.yellow.yellow50
    val trackColor = LuziaTheme.palette.accents.yellow.yellow10
    val nextStreak = data.milestoneDays.firstOrNull { it > data.currentDay } ?: data.totalDays
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = Spacing.X16.dp)
            .addShadow()
            .clip(RoundedCornerShape(Corners.X4.dp))
            .background(LuziaTheme.palette.surface.content)
            .padding(horizontal = Spacing.X16.dp, vertical = Spacing.X12.dp),
        verticalArrangement = Arrangement.spacedBy(Spacing.X16.dp)
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(Spacing.X4.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Bottom
            ) {
                LuziaText(
                    text = stringResource(localizationR.string.gamification_streaks),
                    style = LuziaTheme.typography.body.semiBold.default,
                    color = LuziaTheme.palette.text.primary
                )
                LuziaText(
                    text = stringResource(
                        localizationR.string.gamification_day_of_day,
                        data.currentDay,
                        data.totalDays
                    ),
                    style = LuziaTheme.typography.body.semiBold.caption,
                    color = LuziaTheme.palette.text.primary,
                    textAlign = TextAlign.End
                )
            }
            LuziaText(
                text = stringResource(localizationR.string.gamification_day_challenge, nextStreak),
                style = LuziaTheme.typography.body.regular.footnote,
                color = LuziaTheme.palette.text.secondary
            )
        }
        BoxWithConstraints(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = Spacing.X8.dp),
            contentAlignment = Alignment.CenterStart
        ) {
            val progressBarHeight = Spacing.X6.dp
            val milestoneCircleSize = Spacing.X24.dp
            val milestoneIconSize = IconSizes.X16.dp
            val totalWidth = this.maxWidth

            LinearProgressIndicator(
                progress = { progress },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(progressBarHeight)
                    .clip(CircleShape)
                    .background(trackColor),
                color = progressColor,
                trackColor = trackColor,
                strokeCap = StrokeCap.Round
            )

            data.milestoneDays.forEachIndexed { index, day ->
                MilestoneView(
                    data = data,
                    day = day,
                    milestoneCircleSize = milestoneCircleSize,
                    totalWidth = totalWidth,
                    milestoneIconSize = milestoneIconSize,
                    progressColor = progressColor,
                    isLastItem = index == data.milestoneDays.lastIndex
                )
            }
        }
    }
}

@Composable
private fun MilestoneView(
    data: StreaksCardUiModel,
    day: Int,
    milestoneCircleSize: Dp,
    totalWidth: Dp,
    milestoneIconSize: Dp,
    progressColor: Color,
    isLastItem: Boolean
) {
    val dayProgress =
        if (data.totalDays > 0) day.toFloat() / data.totalDays.toFloat() else 0f
    val lastItemOffset = if (isLastItem) -milestoneCircleSize / 2f else 0.dp
    val horizontalOffset = (totalWidth * dayProgress) - (milestoneCircleSize / 2) + lastItemOffset
    Box(
        modifier = Modifier
            .offset(x = horizontalOffset)
            .size(milestoneCircleSize)
            .clip(CircleShape)
            .background(progressColor),
        contentAlignment = Alignment.Center
    ) {
        LuziaText(
            text = day.toString(),
            style = LuziaTheme.typography.body.semiBold.footnote,
            color = LuziaTheme.palette.surface.content,
            textAlign = TextAlign.Center
        )
    }
    Icon(
        painter = painterResource(id = designR.drawable.ic_flag),
        contentDescription = null,
        tint = progressColor,
        modifier = Modifier
            .offset(
                x = horizontalOffset + (milestoneCircleSize / 2) - (milestoneIconSize / 2),
                y = -Spacing.X20.dp
            )
            .size(milestoneIconSize)
    )
}

@PreviewLightDark
@Composable
private fun StreaksCardViewPreview() {
    val previewData = StreaksCardUiModel(
        currentDay = 17,
        totalDays = 30,
        milestoneDays = listOf(3, 7, 14, 30)
    )
    LuziaTheme {
        Surface(color = LuziaTheme.palette.surface.background) {
            Box(modifier = Modifier.padding(Spacing.X16.dp)) {
                StreaksCardView(data = previewData)
            }
        }
    }
}
