plugins {
    alias(libs.plugins.luzia.android.feature)
}

android {
    namespace = "co.thewordlab.luzia.core.sharing"
}

dependencies {
    implementation(projects.core.notifications)
    implementation(projects.core.profile)
    implementation(projects.core.navigation)
    implementation(projects.core.chat)
    implementation(projects.features.proactiveMessaging)
    implementation(projects.foundation.analytics)
    implementation(projects.foundation.architectureSystem)
    implementation(projects.foundation.designSystem)
    implementation(projects.foundation.localization)
    implementation(projects.foundation.messages)
    implementation(projects.foundation.securelib)
    implementation(libs.stream.chat.android.compose)
    implementation(libs.stream.chat.android.offline)
    implementation(libs.stream.chat.android.push)
    implementation(libs.androidx.dataStore.core)
    implementation(libs.androidx.dataStore.preferences)
}