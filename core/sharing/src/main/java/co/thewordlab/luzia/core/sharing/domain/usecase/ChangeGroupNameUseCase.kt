package co.thewordlab.luzia.core.sharing.domain.usecase

import co.thewordlab.luzia.core.sharing.domain.ClientState
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import io.getstream.result.Result
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.withContext
import javax.inject.Inject

class ChangeGroupNameUseCase @Inject constructor(
    private val getConnectedClientUseCase: GetConnectedClientUseCase,
    private val dispatcher: CoroutineDispatcher,
) {

    suspend operator fun invoke(cid: String, name: String) = withContext(dispatcher) {
        val result = getConnectedClientUseCase.invoke()
            .filterIsInstance<ClientState.Ready>()
            .firstOrNull() ?: return@withContext

        val updateData = mapOf(CHANNEL_NAME to name)
        when (result.client.channel(cid).updatePartial(set = updateData).await()) {
            is Result.Failure -> DO_NOTHING
            is Result.Success -> DO_NOTHING
        }
    }

    private companion object {
        const val CHANNEL_NAME = "name"
    }
}
