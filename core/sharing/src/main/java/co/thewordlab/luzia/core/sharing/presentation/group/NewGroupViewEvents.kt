package co.thewordlab.luzia.core.sharing.presentation.group

import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class NewGroupViewEvents : ViewEvent {
    data object CloseShareFlow : NewGroupViewEvents()
    data object NavigateBack : NewGroupViewEvents()
    data object DismissKeyboard : NewGroupViewEvents()
    data class NavigateToChannel(val channelId: String) : NewGroupViewEvents()
    data object ShowUsersLoadingError : NewGroupViewEvents()
    data object ShowGroupCreationError : NewGroupViewEvents()
}
