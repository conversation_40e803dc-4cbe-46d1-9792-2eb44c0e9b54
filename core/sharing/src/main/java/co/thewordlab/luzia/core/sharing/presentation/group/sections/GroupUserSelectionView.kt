package co.thewordlab.luzia.core.sharing.presentation.group.sections

import androidx.compose.foundation.layout.Column
import androidx.compose.material3.BottomSheetDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import co.thewordlab.luzia.core.navigation.usersession.model.NewChatPurpose
import co.thewordlab.luzia.core.sharing.domain.model.ShareChannel
import co.thewordlab.luzia.core.sharing.presentation.common.ShareListSelectionView
import co.thewordlab.luzia.core.sharing.presentation.group.NewGroupViewActions
import co.thewordlab.luzia.core.sharing.presentation.group.NewGroupViewState
import co.thewordlab.luzia.foundation.localization.R as localizationR

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GroupUserSelectionView(
    modifier: Modifier,
    viewState: NewGroupViewState,
    onViewActions: (NewGroupViewActions) -> Unit
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        BottomSheetDefaults.DragHandle()
        ShareListSelectionView(
            title = stringResource(localizationR.string.group_chat_new_group_title),
            ctaText = stringResource(localizationR.string.signup_next_button),
            searchViewState = viewState.searchViewState,
            searchHint = stringResource(localizationR.string.group_chat_create_group_search_hint),
            selectedFriends = viewState.selectedUsers,
            selectedGroups = emptyList(),
            shareChannels = viewState.mates.map { ShareChannel.Mate(it) },
            ctaEnabled = viewState.selectedUsers.size > 1,
            ctaLoading = false,
            purpose = NewChatPurpose.SHARE,
            searchedChannels = viewState.searchedChannels,
            onQueryChanged = { onViewActions(NewGroupViewActions.OnQueryChanged(it)) },
            onChannelSelected = {
                if (it is ShareChannel.Mate) {
                    onViewActions(NewGroupViewActions.OnUsersSelected(it.schoolMate))
                }
            },
            onCtaClicked = { onViewActions(NewGroupViewActions.OnSubmitFriends) }
        )
    }
}
