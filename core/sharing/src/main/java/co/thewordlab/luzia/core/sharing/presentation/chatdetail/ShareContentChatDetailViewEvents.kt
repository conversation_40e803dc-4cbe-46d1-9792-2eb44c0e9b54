package co.thewordlab.luzia.core.sharing.presentation.chatdetail

import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class ShareContentChatDetailViewEvents : ViewEvent {
    data class NavigateToProfile(val userId: String) : ShareContentChatDetailViewEvents()
    data class OnLinkClicked(val link: String) : ShareContentChatDetailViewEvents()
    data object OnNavigateBack : ShareContentChatDetailViewEvents()
    data object OnErrorAppears : ShareContentChatDetailViewEvents()
}
