package co.thewordlab.luzia.core.sharing.presentation.chat

import co.thewordlab.luzia.core.chat.presentation.search.SearchViewState
import co.thewordlab.luzia.core.navigation.usersession.model.NewChatPurpose
import co.thewordlab.luzia.core.sharing.domain.model.ShareChannel
import co.thewordlab.luzia.foundation.architecture.system.ViewState

data class NewChatViewState(
    val purpose: NewChatPurpose = NewChatPurpose.MESSAGE,
    val messagesToShare: List<String> = emptyList(),
    val channels: List<ShareChannel>? = null,
    val searchedChannels: List<ShareChannel>? = null,
    val searchViewState: SearchViewState<ShareChannel.Mate>? = null,
    val ctaLoading: Boolean = false,
    val newGroupLoading: Boolean = false,
    val selectedChannels: List<String> = emptyList(),
    val selectedMasterIds: List<String> = emptyList()
) : ViewState
