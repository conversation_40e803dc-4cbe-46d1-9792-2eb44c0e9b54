package co.thewordlab.luzia.core.sharing.domain.usecase

import co.thewordlab.luzia.core.sharing.domain.repository.StreamRepository
import javax.inject.Inject

class ReportMessageUseCase @Inject constructor(
    private val streamRepository: StreamRepository
) {

    suspend operator fun invoke(masterId: String, messageId: String, cid: String) {
        streamRepository.reportMessage(masterId, messageId, cid)
    }
}
