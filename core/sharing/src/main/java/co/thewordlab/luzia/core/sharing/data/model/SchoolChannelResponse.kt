package co.thewordlab.luzia.core.sharing.data.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class SchoolChannelResponse(
    @Json(name = "streamChannelId")
    val streamChannelId: String,
    @Json(name = "memberCount")
    val memberCount: Int,
    @Json(name = "name")
    val name: String,
    @Json(name = "image")
    val image: String?
)
