package co.thewordlab.luzia.core.sharing.domain.model

import co.thewordlab.luzia.foundation.networking.model.CommonErrors
import co.thewordlab.luzia.foundation.networking.model.Error

sealed class SharingErrors(val description: String) : Error {

    data class CommonError(val error: CommonErrors) : SharingErrors(error.description)
    data object SharingLimitReached : SharingErrors("Sharing limit reached")
    data class GenerateLink(val errorMessage: String) : SharingErrors("Link generation failed")
}
