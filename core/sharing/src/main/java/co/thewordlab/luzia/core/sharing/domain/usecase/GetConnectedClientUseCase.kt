package co.thewordlab.luzia.core.sharing.domain.usecase

import co.thewordlab.luzia.core.profile.domain.GetUserProfileUseCase
import co.thewordlab.luzia.core.sharing.domain.ClientState
import co.thewordlab.luzia.core.sharing.domain.repository.StreamRepository
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import io.getstream.chat.android.models.FilterObject
import io.getstream.chat.android.models.Filters
import io.getstream.chat.android.models.Filters.eq
import io.getstream.chat.android.models.Filters.`in`
import io.getstream.chat.android.models.Filters.or
import io.getstream.chat.android.models.User
import io.getstream.result.Result
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNot
import kotlinx.coroutines.flow.filterNotNull
import javax.inject.Inject

class GetConnectedClientUseCase @Inject constructor(
    private val repository: StreamRepository,
    getUserProfileUseCase: GetUserProfileUseCase,
    sessionManager: UserSessionManager,
) {

    private val profile = getUserProfileUseCase()
        .filterNotNull()
        .filterNot { it.streamUserId.isNullOrEmpty() }
        .distinctUntilChanged()

    private val userSession = sessionManager
        .userSession
        .filterNotNull()
        .distinctUntilChanged()

    operator fun invoke(): Flow<ClientState> = combine(profile, userSession) { profile, _ ->
        val client = repository.initialize()
        val user = User(
            id = profile.streamUserId.orEmpty(),
            name = profile.displayName.orEmpty(),
            image = profile.avatarURL.orEmpty()
        )
        val filter = createFilters(user.id)
        val token = profile.streamToken.orEmpty()
        if (client.getCurrentUser() != null) {
            if (!client.isSocketConnected()) {
                client.reconnectSocket().await()
            }
            ClientState.Ready(client, filter)
        } else {
            when (client.connectUser(user, token).await()) {
                is Result.Failure -> ClientState.Failed
                is Result.Success -> ClientState.Ready(client, filter)
            }
        }
    }

    private fun createFilters(streamUserId: String): FilterObject {
        return Filters.and(
            `in`(MEMBERS, listOf(streamUserId)),
            or(
                eq(HIDDEN, false),
                eq(HIDDEN, true)
            )
        )
    }

    private companion object {
        const val MEMBERS = "members"
        const val HIDDEN = "hidden"
    }
}
