package co.thewordlab.luzia.core.sharing.data.repository

import co.thewordlab.luzia.core.sharing.data.api.SharingApi
import co.thewordlab.luzia.core.sharing.data.model.ShareContentRequest
import co.thewordlab.luzia.core.sharing.data.model.ShareContentWithLinkItemType
import co.thewordlab.luzia.core.sharing.data.model.ShareMessageRequest
import co.thewordlab.luzia.core.sharing.data.model.SharedMessageContentRequest
import co.thewordlab.luzia.core.sharing.domain.mapToRequestDto
import co.thewordlab.luzia.core.sharing.domain.model.SharedMessageItem
import co.thewordlab.luzia.core.sharing.domain.model.SharingErrors
import co.thewordlab.luzia.core.sharing.domain.model.SharingErrors.GenerateLink
import co.thewordlab.luzia.core.sharing.domain.repository.SharingRepository
import co.thewordlab.luzia.foundation.analytics.providers.AppsFlyerAnalyticsProvider
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.networking.model.CommonErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.asFailure
import co.thewordlab.luzia.foundation.networking.model.asResult
import co.thewordlab.luzia.foundation.networking.model.asSuccess
import co.thewordlab.luzia.foundation.networking.model.map
import co.theworldlab.luzia.foundation.localization.TextProvider
import com.slack.eithernet.ApiResult
import io.getstream.chat.android.models.Channel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import co.thewordlab.luzia.foundation.localization.R as localizationR

class SharingRepositoryImp(
    private val apiDataSource: SharingApi,
    private val textProvider: TextProvider,
    private val dispatcher: CoroutineDispatcher,
    private val appsFlyerAnalyticsProvider: AppsFlyerAnalyticsProvider
) : SharingRepository {

    override suspend fun shareMessages(
        users: List<String>,
        messages: List<SharedMessageItem>,
    ): ResultOf<Unit, SharingErrors> =
        if (messages.isNotEmpty() && users.isNotEmpty()) {
            val result = apiDataSource.sendMessageToShare(
                ShareMessageRequest(
                    masterUserIds = users,
                    messages = messages.mapToRequestDto()
                )
            ).asResult { error ->
                val shareError = error.mapToSharingError()
                shareError.asFailure()
            }
            result.map { DO_NOTHING }
        } else {
            Unit.asSuccess()
        }

    override suspend fun shareMessagesWithLink(
        personalityId: String,
        messages: List<SharedMessageItem>,
        personalityName: String?
    ): ResultOf<String, SharingErrors> =
        if (messages.isNotEmpty()) {
            val request = buildShareRequest(personalityId, messages)
            apiDataSource.shareMessageLink(request).asResult { error ->
                val shareError = error.mapToSharingError()
                shareError.asFailure()
            }.map {
                StringBuilder().apply {
                    append(
                        personalityName?.let {
                            textProvider.getString(
                                localizationR.string.share_custom_character_conversation,
                                personalityName
                            )
                        } ?: textProvider.getString(localizationR.string.external_share_message)
                    )
                    appendLine()
                    append(link)
                }.toString()
            }
        } else {
            "".asSuccess()
        }

    private fun buildShareRequest(
        personalityId: String,
        messages: List<SharedMessageItem>,
    ): ShareContentRequest {
        val isImage = messages.first() is SharedMessageItem.Image
        return if (personalityId.isEmpty() && isImage) {
            ShareContentRequest(
                type = ShareContentWithLinkItemType.IMAGE,
                content = SharedMessageContentRequest(
                    image = "$IMAGE_PREFIX${messages.first().value}",
                )
            )
        } else {
            ShareContentRequest(
                type = ShareContentWithLinkItemType.TEXT,
                content = SharedMessageContentRequest(
                    personalityId = personalityId,
                    messages = messages.mapToRequestDto()
                )
            )
        }
    }

    @Suppress("TooGenericExceptionCaught")
    override suspend fun generateInviteLink(channel: Channel): ResultOf<String, GenerateLink> =
        withContext(dispatcher) {
            try {
                val link = appsFlyerAnalyticsProvider.generateLink(
                    cid = channel.cid,
                    name = channel.name,
                    memberCount = channel.memberCount,
                    image = channel.image
                )
                ResultOf.Success(link)
            } catch (ex: Exception) {
                GenerateLink(ex.message.toString()).asFailure()
            }
        }

    private fun ApiResult.Failure<*>.mapToSharingError(): SharingErrors =
        when (this) {
            is ApiResult.Failure.NetworkFailure -> SharingErrors.CommonError(CommonErrors.NetworkError)
            is ApiResult.Failure.HttpFailure -> {
                when (code) {
                    SHARING_LIMIT_REACHED -> SharingErrors.SharingLimitReached
                    else -> SharingErrors.CommonError(CommonErrors.UnspecifiedError)
                }
            }

            else -> SharingErrors.CommonError(CommonErrors.UnspecifiedError)
        }

    private companion object {
        const val SHARING_LIMIT_REACHED = 429
        const val IMAGE_PREFIX = "data:image/png;base64,"
    }
}
