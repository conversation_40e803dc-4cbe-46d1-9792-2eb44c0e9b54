package co.thewordlab.luzia.core.sharing.domain.usecase

import co.thewordlab.luzia.core.sharing.domain.ClientState
import co.thewordlab.luzia.core.sharing.domain.repository.StreamRepository
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import io.getstream.chat.android.client.ChatClient
import io.getstream.chat.android.client.api.models.QueryChannelsRequest
import io.getstream.chat.android.models.Channel
import io.getstream.chat.android.models.Filters
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.firstOrNull
import javax.inject.Inject

class BlockUserUseCase @Inject constructor(
    private val userSessionManager: UserSessionManager,
    private val streamRepository: StreamRepository,
    private val getConnectedClientUseCase: GetConnectedClientUseCase
) {

    suspend operator fun invoke(isBlocked: <PERSON><PERSON><PERSON>, masterId: String) {
        streamRepository.blockUser(isBlocked, masterId)
        val state = getConnectedClientUseCase().filterIsInstance<ClientState.Ready>().firstOrNull()
        state?.client?.let { client ->
            val channel = client.findGetStreamChannel(masterId)
            if (channel != null) {
                val blockJob = if (isBlocked) {
                    client.freezeChannel(channel.cid)
                    client.blockUser(masterId)
                } else {
                    client.unfreezeChannel(channel.cid)
                    client.unblockUser(masterId)
                }
                blockJob.await()
            }
        }
    }

    private suspend fun ChatClient.findGetStreamChannel(userId: String): Channel? {
        val session = userSessionManager.userSession.firstOrNull()
        val currentUserMasterId = session?.masterUserId.orEmpty()
        val request = QueryChannelsRequest(
            filter = Filters.and(
                Filters.eq(TYPE, TYPE_MESSAGING),
                Filters.`in`(MEMBERS, listOf(currentUserMasterId, userId)),
                Filters.eq(MEMBER_COUNT, MEMBER_COUNT_DM)
            ),
            offset = 0,
            limit = 1
        )
        return queryChannels(request).await().getOrNull()?.firstOrNull()
    }

    private suspend fun ChatClient.freezeChannel(cid: String) = runCatching {
        channel(cid = cid).updatePartial(
            set = mapOf(FROZEN to true)
        ).await()
    }

    private suspend fun ChatClient.unfreezeChannel(cid: String) = runCatching {
        channel(cid = cid).updatePartial(
            set = mapOf(FROZEN to false)
        ).await()
    }

    private companion object {
        const val TYPE = "type"
        const val TYPE_MESSAGING = "messaging"
        const val MEMBERS = "members"
        const val FROZEN = "frozen"
        const val MEMBER_COUNT = "member_count"
        const val MEMBER_COUNT_DM = 2
    }
}
