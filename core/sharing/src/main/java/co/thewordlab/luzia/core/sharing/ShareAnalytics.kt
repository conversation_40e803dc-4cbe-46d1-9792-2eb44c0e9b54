package co.thewordlab.luzia.core.sharing

import co.thewordlab.luzia.core.navigation.usersession.model.GroupJoinSource
import co.thewordlab.luzia.foundation.analytics.AnalyticsActions
import co.thewordlab.luzia.foundation.analytics.AnalyticsScreens

enum class BlockReportEntryPoint(val value: String) {
    PROFILE("profile"),
    ONE_TO_ONE_CHAT("1on1chat"),
    CHAT_MESSAGE("chat_message"),
}

data class ScreenGroupJoin(val source: GroupJoinSource, val cid: String, val countGroup: Int) :
    AnalyticsScreens(
        value = "group_join",
        properties = mapOf(
            "source" to source.value,
            "cid" to cid,
            "count_group" to countGroup
        )
    )

data class JoinGroup(val source: GroupJoinSource) : AnalyticsActions(
    value = "join_group",
    properties = mapOf("source" to source.value)
)

data class CancelJoinGroup(val source: GroupJoinSource) : AnalyticsActions(
    value = "cancel_join_group",
    properties = mapOf("source" to source.value)
)

data class BlockUser(val entryPoint: BlockReportEntryPoint) : AnalyticsActions(
    value = "block_user",
    properties = mapOf("entry_point" to entryPoint.value)
)

data class UnBlockUser(val entryPoint: BlockReportEntryPoint) : AnalyticsActions(
    value = "unblock_user",
    properties = mapOf("entry_point" to entryPoint.value)
)

data class FlagMessage(val entryPoint: BlockReportEntryPoint) : AnalyticsActions(
    value = "flag_message",
    properties = mapOf("entry_point" to entryPoint.value)
)

data class ReportUser(val entryPoint: BlockReportEntryPoint) : AnalyticsActions(
    value = "report_user",
    properties = mapOf("entry_point" to entryPoint.value)
)
