package co.thewordlab.luzia.core.sharing.di

import co.thewordlab.luzia.core.profile.domain.ExternalUserSendMessageHandler
import co.thewordlab.luzia.core.sharing.data.api.SharingApi
import co.thewordlab.luzia.core.sharing.data.repository.SharingRepositoryImp
import co.thewordlab.luzia.core.sharing.domain.repository.SharingRepository
import co.thewordlab.luzia.core.sharing.domain.usecase.CreateChannelUseCase
import co.thewordlab.luzia.foundation.analytics.providers.AppsFlyerAnalyticsProvider
import co.thewordlab.luzia.foundation.networking.di.BaseHost
import co.theworldlab.luzia.foundation.localization.TextProvider
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import retrofit2.Retrofit

@Module
@InstallIn(SingletonComponent::class)
object SharingModule {

    @Provides
    fun provideApi(@BaseHost retrofit: Retrofit): SharingApi =
        retrofit.create(SharingApi::class.java)

    @Provides
    fun provideRepository(
        api: SharingApi,
        textProvider: TextProvider,
        dispatcher: CoroutineDispatcher,
        appsFlyerAnalyticsProvider: AppsFlyerAnalyticsProvider
    ): SharingRepository =
        SharingRepositoryImp(api, textProvider, dispatcher, appsFlyerAnalyticsProvider)

    @Provides
    fun provideExternalUserSendMessageHandler(
        useCase: CreateChannelUseCase,
    ): ExternalUserSendMessageHandler = useCase
}
