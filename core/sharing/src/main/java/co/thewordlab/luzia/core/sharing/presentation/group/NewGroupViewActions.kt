package co.thewordlab.luzia.core.sharing.presentation.group

import co.thewordlab.luzia.foundation.architecture.system.ViewAction
import co.theworldlab.luzia.foundation.design.system.components.mate.UserMateUiModel

sealed class NewGroupViewActions : ViewAction {
    data class OnCreate(
        val source: String,
        val messagesToShare: List<String>,
    ) : NewGroupViewActions()

    data object OnDismiss : NewGroupViewActions()
    data object OnSubmitFriends : NewGroupViewActions()
    data object OnBackToSelection : NewGroupViewActions()
    data object OnCreateGroup : NewGroupViewActions()
    data object OnBackToName : NewGroupViewActions()
    data object OnNavigateToImageSelection : NewGroupViewActions()
    data class OnSelectImage(val image: String) : NewGroupViewActions()
    data class OnNameChanged(val text: String) : NewGroupViewActions()
    data class OnQueryChanged(val query: String) : NewGroupViewActions()
    data class OnUsersSelected(val user: UserMateUiModel) : NewGroupViewActions()
}
