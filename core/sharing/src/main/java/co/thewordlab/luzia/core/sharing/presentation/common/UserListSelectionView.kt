package co.thewordlab.luzia.core.sharing.presentation.common

import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import co.thewordlab.luzia.core.chat.presentation.search.SearchViewState
import co.thewordlab.luzia.core.navigation.usersession.model.NewChatPurpose
import co.thewordlab.luzia.core.sharing.domain.model.ShareChannel
import co.thewordlab.luzia.foundation.localization.R
import co.theworldlab.luzia.foundation.design.system.components.button.CtaContainerView
import co.theworldlab.luzia.foundation.design.system.components.input.SearchInputView
import co.theworldlab.luzia.foundation.design.system.components.mate.ShareChannelView
import co.theworldlab.luzia.foundation.design.system.components.profile.AvatarState
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.localization.R as localizationR

@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
fun ShareListSelectionView(
    purpose: NewChatPurpose,
    title: String,
    ctaText: String,
    ctaEnabled: Boolean,
    ctaLoading: Boolean,
    searchViewState: SearchViewState<ShareChannel.Mate>?,
    searchHint: String? = null,
    searchedChannels: List<ShareChannel.Mate>? = null,
    selectedFriends: List<String> = emptyList(),
    selectedGroups: List<String> = emptyList(),
    shareChannels: List<ShareChannel> = emptyList(),
    onQueryChanged: (String) -> Unit,
    onChannelSelected: (ShareChannel) -> Unit,
    onCtaClicked: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        LuziaText(
            text = title,
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary,
            textAlign = TextAlign.Center
        )
        Spacer(Modifier.height(Spacing.X16.dp))
        SearchInputView(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = Spacing.X16.dp),
            value = searchViewState?.query.orEmpty(),
            searchHint = searchHint,
            onValueChange = { onQueryChanged(it) },
            placeholderText = stringResource(R.string.profile_school_search)
        )
        Spacer(Modifier.height(Spacing.X16.dp))
        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .padding(horizontal = Spacing.X4.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (searchViewState == null) {
                searchIdle(
                    shareChannels = shareChannels,
                    selectedGroups = selectedGroups,
                    selectedFriends = selectedFriends,
                    onChannelSelected = onChannelSelected
                )
            } else {
                searchView(
                    purpose = purpose,
                    searchedChannels = searchedChannels,
                    searchViewState = searchViewState,
                    selectedChannels = selectedGroups,
                    selectedMasterIds = selectedFriends,
                    onSendMessage = {},
                    onSelectChannel = onChannelSelected
                )
            }
        }
        CtaContainerView(
            modifier = Modifier
                .navigationBarsPadding()
                .imePadding(),
            ctaText = ctaText,
            ctaEnabled = ctaEnabled,
            ctaLoading = ctaLoading,
            onClick = onCtaClicked
        )
    }
}

private fun LazyListScope.searchIdle(
    shareChannels: List<ShareChannel>,
    selectedGroups: List<String>,
    selectedFriends: List<String>,
    onChannelSelected: (ShareChannel) -> Unit
) {
    items(shareChannels) { channel ->
        when (channel) {
            is ShareChannel.Channel -> ShareChannelView(
                title = channel.channelName,
                subtitle = stringResource(localizationR.string.group_chat_title),
                avatarState = AvatarState.ProfileImage(channel.image),
                isSelected = selectedGroups.contains(channel.id),
                isPrivate = !channel.isPublic,
                onClick = { if (channel.isPublic) onChannelSelected(channel) },
                showSelection = channel.isPublic
            )

            is ShareChannel.Mate -> ShareChannelView(
                title = channel.schoolMate.name,
                subtitle = channel.schoolMate.username,
                avatarState = channel.schoolMate.avatarState,
                isSelected = selectedFriends.contains(channel.schoolMate.masterUserId),
                isPrivate = !channel.isPublic,
                onClick = { if (channel.isPublic) onChannelSelected(channel) },
                showSelection = channel.isPublic
            )
        }
    }
}
