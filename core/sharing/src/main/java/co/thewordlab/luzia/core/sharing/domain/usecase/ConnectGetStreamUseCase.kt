package co.thewordlab.luzia.core.sharing.domain.usecase

import co.thewordlab.luzia.core.sharing.domain.repository.StreamRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject

class ConnectGetStreamUseCase @Inject constructor(
    private val repository: StreamRepository,
    private val dispatcher: CoroutineDispatcher
) {

    enum class Action {
        CONNECT,
        DISCONNECT
    }

    suspend operator fun invoke(action: Action) = withContext(dispatcher) {
        val client = repository.initialize()
        when (action) {
            Action.CONNECT -> {
                if (!client.isSocketConnected()) {
                    client.reconnectSocket().await()
                }
            }

            Action.DISCONNECT -> {
                if (client.isSocketConnected()) {
                    client.disconnectSocket().await()
                }
            }
        }
    }
}
