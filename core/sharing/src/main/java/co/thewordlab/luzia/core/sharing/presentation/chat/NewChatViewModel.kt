package co.thewordlab.luzia.core.sharing.presentation.chat

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.chat.presentation.search.SearchViewState
import co.thewordlab.luzia.core.navigation.usersession.model.NewChatPurpose
import co.thewordlab.luzia.core.profile.domain.GetReferralLinkUseCase
import co.thewordlab.luzia.core.profile.domain.ProfileRepository
import co.thewordlab.luzia.core.sharing.ShareList
import co.thewordlab.luzia.core.sharing.domain.model.ShareChannel
import co.thewordlab.luzia.core.sharing.domain.usecase.CreateChannelUseCase
import co.thewordlab.luzia.core.sharing.domain.usecase.CreateGroupUseCase
import co.thewordlab.luzia.core.sharing.domain.usecase.GetChannelsToShareUseCase
import co.thewordlab.luzia.core.sharing.domain.usecase.SendMessageUseCase
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.getDataOrNull
import co.theworldlab.luzia.foundation.design.system.components.mate.UserMateUiModel
import co.theworldlab.luzia.foundation.localization.TextProvider
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject
import co.thewordlab.luzia.foundation.localization.R as localizationR

@HiltViewModel
class NewChatViewModel @Inject constructor(
    private val profileRepository: ProfileRepository,
    private val getChannelsToShareUseCase: GetChannelsToShareUseCase,
    private val createChannelUseCase: CreateChannelUseCase,
    private val getReferralLinkUseCase: GetReferralLinkUseCase,
    private val analytics: Analytics,
    private val sendMessageUseCase: SendMessageUseCase,
    private val createGroupUseCase: CreateGroupUseCase,
    private val provider: TextProvider,
) : ViewModel(),
    ViewModelActions<NewChatViewActions>,
    ViewModelEvents<NewChatViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<NewChatViewState> by ViewModelStatesImpl(NewChatViewState()) {

    private val searchQuery = MutableStateFlow("")

    override fun onViewAction(action: NewChatViewActions) {
        when (action) {
            is NewChatViewActions.OnBack -> sendEvent(NewChatViewEvents.NavigateBack)
            is NewChatViewActions.OnCreate -> onCreate(action)
            is NewChatViewActions.OnSendMessage -> tapOnChannel(action.channel)
            NewChatViewActions.OnBannerClicked -> onBannerClicked()
            NewChatViewActions.OnReferralLink -> onReferralLink()
            is NewChatViewActions.OnQueryChanged -> onQueryChanged(action.query)
            NewChatViewActions.OnShareMessage -> onShareMessage()
            is NewChatViewActions.SelectChannel -> selectChannel(action.channel)
        }
    }

    private fun onCreate(action: NewChatViewActions.OnCreate) {
        analytics.logScreen(ShareList, mapOf(Parameter.EntryPoint to action.purpose.source))
        updateState { it.copy(purpose = action.purpose, messagesToShare = action.messages) }
        loadChannels()
        listenToSearchQuery()
    }

    private fun onShareMessage() {
        val messages = viewState.value.messagesToShare
        analytics.logAction(SubmitShare, mapOf(Parameter.Count to messages.size))
        if (messages.isEmpty()) return
        viewModelScope.launch {
            updateState { it.copy(ctaLoading = true) }
            val (selectedMasterIds, selectedChannels) = viewState.value.run { selectedMasterIds to selectedChannels }
            val result = sendMessageUseCase(selectedMasterIds, selectedChannels, messages)
            updateState { it.copy(ctaLoading = false) }
            sendEvent(NewChatViewEvents.NavigateBack)
            if (result.failureReasons.isEmpty()) {
                result.channelToNavigate?.let {
                    sendEvent(NewChatViewEvents.OpenChannel(it))
                } ?: sendEvent(NewChatViewEvents.ShowSnackBar(ShareSnackBar.SUCCESS))
            } else {
                sendEvent(NewChatViewEvents.ShowSnackBar(ShareSnackBar.FAILURE))
            }
        }
    }

    private fun onQueryChanged(query: String) {
        searchQuery.update { query }
    }

    private fun onReferralLink() = viewModelScope.launch {
        getReferralLinkUseCase()?.let { model ->
            sendEvent(NewChatViewEvents.ShareReferralLink(model))
        }
    }

    private fun onSendMessage(channel: ShareChannel) = viewModelScope.launch {
        when (channel) {
            is ShareChannel.Channel -> {
                sendEvent(NewChatViewEvents.OpenChannel(channel.channelId))
            }

            is ShareChannel.Mate -> {
                createChannelUseCase.createChat(channel.channelId)?.let { id ->
                    sendEvent(NewChatViewEvents.OpenChannel(id))
                }
            }
        }
        trackSelection(channel)
    }

    private fun onBannerClicked() {
        when (viewState.value.purpose) {
            NewChatPurpose.MESSAGE -> createGroupWithoutMembers()
            NewChatPurpose.SHARE -> goToCreateGroupWithMembers()
        }
    }

    private fun goToCreateGroupWithMembers() {
        sendEvent(NewChatViewEvents.NavigateToNewGroup)
    }

    private fun createGroupWithoutMembers() {
        if (viewState.value.newGroupLoading) return
        analytics.logAction(
            action = StartGroupCreation,
            params = mapOf(
                Parameter.EntryPoint to viewState.value.purpose.source
            )
        )
        viewModelScope.launch {
            updateState { it.copy(newGroupLoading = true) }
            val name = profileRepository.getUserProfile()?.displayName.orEmpty()
            val title = provider.getString(localizationR.string.group_chats_new_chat_title, name)
            val response = createGroupUseCase.invoke(title, emptyList(), null, emptyList())
            response.getDataOrNull()?.let { channelId ->
                sendEvent(NewChatViewEvents.OpenChannel(channelId))
            }
            updateState { it.copy(newGroupLoading = false) }
        }
    }

    @OptIn(FlowPreview::class)
    private fun listenToSearchQuery() {
        searchQuery
            .onEach { query ->
                updateState { state ->
                    state.copy(
                        searchViewState = query.takeIf { it.isNotEmpty() }?.let {
                            SearchViewState(
                                isSearching = true,
                                query = it,
                                searchResults = emptyList()
                            )
                        },
                        searchedChannels = query.takeIf { it.isNotEmpty() }?.let {
                            state.channels.filterByQuery(it)
                        }
                    )
                }
            }
            .filter { it.isNotEmpty() }
            .debounce(DEBOUNCE_TYPING)
            .onEach { searchUsername(it) }
            .launchIn(viewModelScope)
    }

    private suspend fun searchUsername(query: String) {
        val results = profileRepository.searchUsername(query).getDataOrNull().orEmpty()
        updateState { state ->
            state.copy(
                searchViewState = state.searchViewState?.copy(
                    isSearching = false,
                    searchResults = results.filterAndMapToResults(state.searchedChannels.orEmpty())
                )
            )
        }
        if (query.length >= MIN_QUERY_LENGTH) {
            analytics.logEvent(
                event = UsernameSearchState,
                params = mapOf(
                    Parameter.ResultCount to results.size,
                    Parameter.EntryPoint to viewState.value.purpose.source
                )
            )
        }
    }

    private fun loadChannels() {
        val isSharing = viewState.value.purpose == NewChatPurpose.SHARE
        getChannelsToShareUseCase(loadGroups = isSharing, loadPrivateProfiles = !isSharing)
            .onEach { result ->
                when (result) {
                    is ResultOf.Failure -> sendEvent(NewChatViewEvents.ShowUsersLoadingError)
                    is ResultOf.Success -> {
                        updateState { it.copy(channels = result.data) }
                    }
                }
            }
            .launchIn(viewModelScope)
    }

    private fun List<ShareChannel>?.filterByQuery(query: String): List<ShareChannel> {
        return this.orEmpty()
            .filter { channel ->
                when (channel) {
                    is ShareChannel.Channel -> {
                        channel.channelName.contains(query, ignoreCase = true)
                    }

                    is ShareChannel.Mate -> {
                        channel.schoolMate.username.contains(query, ignoreCase = true) ||
                            channel.schoolMate.name.contains(query, ignoreCase = true)
                    }
                }
            }
    }

    private fun List<UserMateUiModel>.filterAndMapToResults(
        searchedChannels: List<ShareChannel>
    ): List<ShareChannel.Mate> {
        return this.filter { user ->
            searchedChannels
                .filterIsInstance<ShareChannel.Mate>()
                .none { it.schoolMate.masterUserId == user.masterUserId }
        }.map { mate -> ShareChannel.Mate(mate) }
    }

    private fun tapOnChannel(shareChannel: ShareChannel) {
        if (!shareChannel.isPublic && shareChannel is ShareChannel.Mate) {
            val id = shareChannel.schoolMate.masterUserId
            sendEvent(NewChatViewEvents.NavigateToProfile(id))
        } else {
            onSendMessage(shareChannel)
        }
    }

    private fun selectChannel(shareChannel: ShareChannel) {
        val state = viewState.value
        val id = shareChannel.channelId
        val userType = extractUserType(shareChannel)
        when (shareChannel) {
            is ShareChannel.Channel -> {
                val contains = state.selectedChannels.contains(id)
                if (contains) {
                    updateState { it.copy(selectedChannels = it.selectedChannels.minus(id)) }
                } else {
                    doIfRecipientLimitNotReached(state) {
                        val props = mapOf(Parameter.UserType to userType)
                        analytics.logAction(MemberSelected, props)
                        updateState { it.copy(selectedChannels = it.selectedChannels.plus(id)) }
                        trackSelection(channel = shareChannel)
                    }
                }
            }

            is ShareChannel.Mate -> {
                val contains = state.selectedMasterIds.contains(id)
                if (contains) {
                    updateState { it.copy(selectedMasterIds = it.selectedMasterIds.minus(id)) }
                } else {
                    doIfRecipientLimitNotReached(state) {
                        val props = mapOf(Parameter.UserType to userType)
                        analytics.logAction(MemberSelected, props)
                        updateState { it.copy(selectedMasterIds = it.selectedMasterIds.plus(id)) }
                        trackSelection(channel = shareChannel)
                    }
                }
            }
        }
    }

    private fun trackSelection(channel: ShareChannel) {
        val isFromSchool = viewState.value.searchedChannels.orEmpty()
            .any { it.channelId == channel.channelId }
        val type = when {
            channel is ShareChannel.Channel -> "group"
            channel is ShareChannel.Mate && isFromSchool -> "student"
            else -> "other"
        }
        if (viewState.value.searchViewState != null) {
            analytics.logAction(
                action = UsernameSearchResultClick,
                params = mapOf(
                    Parameter.EntryPoint to viewState.value.purpose.source,
                    Parameter.SelectedUsername to type
                )
            )
        }
    }

    private fun doIfRecipientLimitNotReached(state: NewChatViewState, onInvoke: () -> Unit) {
        val numOfRecipients = state.run { selectedChannels.size + selectedMasterIds.size }
        if (numOfRecipients == MAX_RECIPIENTS) {
            sendEvent(NewChatViewEvents.ShowSnackBar(ShareSnackBar.RECIPIENT_LIMIT))
        } else {
            onInvoke()
        }
    }

    private companion object {
        const val DEBOUNCE_TYPING = 1_500L
        const val MAX_RECIPIENTS = 5
        const val MIN_QUERY_LENGTH = 3
    }
}
