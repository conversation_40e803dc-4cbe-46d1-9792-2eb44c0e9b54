package co.thewordlab.luzia.core.sharing.domain.model

import co.theworldlab.luzia.foundation.design.system.components.mate.UserMateUiModel

sealed class ShareChannel(
    val channelId: String,
    val channelName: String,
    val isPublic: Boolean
) {

    data class Mate(val schoolMate: UserMateUiModel) :
        ShareChannel(schoolMate.masterUserId, schoolMate.name, schoolMate.isPublic)

    data class Channel(val id: String, val name: String, val image: String) :
        ShareChannel(id, name, true)
}
