package co.thewordlab.luzia.core.sharing.presentation.container

import co.thewordlab.luzia.core.sharing.domain.model.SharedMessageItem
import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class MessageActionsViewEvents : ViewEvent {
    data class NativeShare(val text: String) : MessageActionsViewEvents()

    data class StreamShare(
        val personalityId: String,
        val messages: List<SharedMessageItem>
    ) : MessageActionsViewEvents()

    data class ShowDeleteConfirmation(val messagesCount: Int) : MessageActionsViewEvents()
}
