package co.thewordlab.luzia.core.sharing.domain.usecase

import co.thewordlab.luzia.core.sharing.domain.ClientState
import io.getstream.chat.android.state.extensions.globalState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import javax.inject.Inject

class GetUnreadMessageCountUseCase @Inject constructor(
    private val getConnectedClientUseCase: GetConnectedClientUseCase
) {

    operator fun invoke(): Flow<Int> = getConnectedClientUseCase()
        .mapNotNull { it as? ClientState.Ready }
        .map { it.client }
        .distinctUntilChanged()
        .flatMapLatest { client ->
            client.globalState.channelUnreadCount
        }
}
