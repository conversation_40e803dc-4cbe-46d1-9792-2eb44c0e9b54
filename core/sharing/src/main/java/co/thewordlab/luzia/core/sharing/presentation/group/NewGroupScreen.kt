package co.thewordlab.luzia.core.sharing.presentation.group

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.tooling.preview.Preview
import co.thewordlab.luzia.core.sharing.presentation.group.sections.GroupImageView
import co.thewordlab.luzia.core.sharing.presentation.group.sections.GroupNameView
import co.thewordlab.luzia.core.sharing.presentation.group.sections.GroupUserSelectionView
import co.theworldlab.luzia.foundation.design.system.helpers.pxToDp
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme

@Composable
fun NewGroupScreen(
    viewState: NewGroupViewState,
    onViewActions: (NewGroupViewActions) -> Unit,
) {
    val pagerState = rememberPagerState { NewGroupSection.entries.size }
    LaunchedEffect(viewState.section) {
        pagerState.animateScrollToPage(viewState.section.ordinal)
    }
    var maxHeight by remember { mutableIntStateOf(0) }
    HorizontalPager(
        modifier = Modifier
            .imePadding()
            .fillMaxWidth(),
        state = pagerState,
        userScrollEnabled = false
    ) {
        when (NewGroupSection.entries[it]) {
            NewGroupSection.UserSelection -> GroupUserSelectionView(
                modifier = Modifier
                    .fillMaxSize()
                    .onSizeChanged { size -> if (size.height > maxHeight) maxHeight = size.height },
                viewState = viewState,
                onViewActions = onViewActions
            )

            NewGroupSection.Name -> GroupNameView(
                modifier = Modifier
                    .onSizeChanged { size -> if (size.height > maxHeight) maxHeight = size.height }
                    .then(if (maxHeight != 0) Modifier.height(maxHeight.pxToDp()) else Modifier),
                viewState = viewState,
                onViewActions = onViewActions
            )

            NewGroupSection.Image -> GroupImageView(
                modifier = Modifier
                    .onSizeChanged { size -> if (size.height > maxHeight) maxHeight = size.height }
                    .then(if (maxHeight != 0) Modifier.height(maxHeight.pxToDp()) else Modifier),
                viewState = viewState,
                onViewActions = onViewActions
            )
        }
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        NewGroupScreen(
            viewState = NewGroupViewState(),
            onViewActions = {}
        )
    }
}
