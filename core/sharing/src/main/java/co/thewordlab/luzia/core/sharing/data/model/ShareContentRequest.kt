package co.thewordlab.luzia.core.sharing.data.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class ShareContentRequest(
    @Json(name = "messageType") val type: ShareContentWithLinkItemType =
        ShareContentWithLinkItemType.UNKNOWN,
    @Json(name = "content") val content: SharedMessageContentRequest
)

@JsonClass(generateAdapter = true)
data class SharedMessageContentRequest(
    @J<PERSON>(name = "personalityId") val personalityId: String? = null,
    @<PERSON><PERSON>(name = "messages") val messages: List<SharedMessageItemRequest>? = null,
    @<PERSON><PERSON>(name = "image") val image: String? = null
)

@JsonClass(generateAdapter = false)
enum class ShareContentWithLinkItemType(val value: String) {
    @Json(name = "text")
    TEXT("text"),

    @<PERSON><PERSON>(name = "image")
    IMAGE("image"),

    @<PERSON><PERSON>(name = "unknown")
    UNKNOWN("unknown")
}
