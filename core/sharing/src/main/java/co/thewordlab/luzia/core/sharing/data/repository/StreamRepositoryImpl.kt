package co.thewordlab.luzia.core.sharing.data.repository

import android.content.Context
import android.content.Intent
import androidx.core.net.toUri
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import co.thewordlab.fouundation.persistence.LuziaDataStore
import co.thewordlab.luzia.core.sharing.BuildConfig
import co.thewordlab.luzia.core.sharing.data.api.StreamApi
import co.thewordlab.luzia.core.sharing.data.model.AddMembersRequest
import co.thewordlab.luzia.core.sharing.data.model.BlockUserRequest
import co.thewordlab.luzia.core.sharing.data.model.CreateChannelRequest
import co.thewordlab.luzia.core.sharing.data.model.ReportMessageRequest
import co.thewordlab.luzia.core.sharing.data.model.ReportUserRequest
import co.thewordlab.luzia.core.sharing.data.model.SchoolChannelResponse
import co.thewordlab.luzia.core.sharing.domain.model.GroupChatErrors
import co.thewordlab.luzia.core.sharing.domain.model.SchoolChannel
import co.thewordlab.luzia.core.sharing.domain.repository.StreamRepository
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.asResult
import co.thewordlab.luzia.foundation.networking.model.map
import co.thewordlab.luzia.foundation.securelib.SecureKey
import co.thewordlab.luzia.foundation.securelib.SecureStorage
import com.slack.eithernet.successOrNull
import com.squareup.moshi.Moshi
import dagger.hilt.android.qualifiers.ApplicationContext
import io.getstream.android.push.firebase.FirebasePushDeviceGenerator
import io.getstream.chat.android.client.ChatClient
import io.getstream.chat.android.client.logger.ChatLogLevel
import io.getstream.chat.android.client.notifications.handler.NotificationConfig
import io.getstream.chat.android.client.notifications.handler.NotificationHandler
import io.getstream.chat.android.client.notifications.handler.NotificationHandlerFactory
import io.getstream.chat.android.models.Channel
import io.getstream.chat.android.models.Message
import io.getstream.chat.android.offline.plugin.factory.StreamOfflinePluginFactory
import io.getstream.chat.android.state.plugin.config.StatePluginConfig
import io.getstream.chat.android.state.plugin.factory.StreamStatePluginFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class StreamRepositoryImpl @Inject constructor(
    @ApplicationContext private val context: Context,
    private val secureStorage: SecureStorage,
    private val streamApi: StreamApi,
    private val luziaDataStore: LuziaDataStore,
    private val moshi: Moshi
) : StreamRepository {

    private var client: ChatClient? = null

    override suspend fun initialize(): ChatClient =
        client ?: createClient()

    override suspend fun createChannel(
        isDm: Boolean,
        recipients: List<String>
    ): ResultOf<String, GroupChatErrors> {
        val request = CreateChannelRequest(recipients, isDm)
        return streamApi.createChannel(request)
            .asResult { ResultOf.Failure(GroupChatErrors.CreateChannelFailed) }
            .map { streamChannelId }
    }

    override suspend fun addMembers(
        channelId: String,
        recipients: List<String>
    ): ResultOf<String, GroupChatErrors> {
        val request = AddMembersRequest(recipients)
        return streamApi.addMembers(channelId, request)
            .asResult { ResultOf.Failure(GroupChatErrors.CreateChannelFailed) }
            .map { streamChannelId }
    }

    override suspend fun joinChannel(channelId: String): ResultOf<String, GroupChatErrors> {
        return streamApi.joinChannel(channelId)
            .asResult { ResultOf.Failure(GroupChatErrors.JoinChannelsFailed) }
            .map { streamChannelId }
    }

    override suspend fun logout() {
        val existingClient = initialize()
        existingClient.disconnectSocket().await()
        existingClient.disconnect(true).await()
        client = null
    }

    override suspend fun isUserAcceptedOrRejected(masterUserId: String): Boolean {
        return luziaDataStore.getData(userAcceptedRejected)
            ?.split(SEPARATOR)?.contains(masterUserId) == true
    }

    override suspend fun setUserAcceptedOrRejected(masterUserId: String) {
        val newList = luziaDataStore.getData(userAcceptedRejected)?.split(SEPARATOR)
            .orEmpty()
            .plus(masterUserId)
        luziaDataStore.saveData(userAcceptedRejected, newList.joinToString(SEPARATOR))
    }

    override fun isFriendsTabSelectedBefore(): Flow<Boolean> {
        return luziaDataStore.getDataFlow(hasVisitedFriendsTabBefore)
            .map { it == true }
            .distinctUntilChanged()
    }

    override suspend fun setFriendsTabSelected() {
        luziaDataStore.saveData(hasVisitedFriendsTabBefore, true)
    }

    override suspend fun blockUser(isBlocked: Boolean, masterId: String) {
        val request = BlockUserRequest(masterId, isBlocked)
        streamApi.blockUser(request)
    }

    override suspend fun reportUser(masterId: String, cid: String?) {
        val request = ReportUserRequest(masterId, cid)
        streamApi.reportUser(request)
    }

    override suspend fun reportMessage(masterId: String, messageId: String, cid: String) {
        val request = ReportMessageRequest(messageId, masterId, cid)
        streamApi.reportMessage(request)
    }

    private suspend fun createClient(): ChatClient = withContext(Dispatchers.IO) {
        val logLevel = if (BuildConfig.DEBUG) ChatLogLevel.ALL else ChatLogLevel.NOTHING
        val offlinePluginFactory = StreamOfflinePluginFactory(appContext = context)
        val provider = secureStorage.get(SecureKey.KEY_GET_STREAM_NOTIFICATION)
        val statePluginFactory =
            StreamStatePluginFactory(config = StatePluginConfig(), appContext = context)
        val notificationConfig = NotificationConfig(
            pushDeviceGenerators = listOf(
                FirebasePushDeviceGenerator(providerName = provider, context = context)
            ),
            pushNotificationsEnabled = true,
            autoTranslationEnabled = true,
            requestPermissionOnAppLaunch = { false }
        )

        ChatClient.Builder(secureStorage.get(SecureKey.KEY_GET_STREAM_API), context)
            .notifications(notificationConfig, getNotificationHandler(notificationConfig))
            .withPlugins(offlinePluginFactory, statePluginFactory)
            .logLevel(logLevel)
            .build().also { client = it }
    }

    private fun getNotificationHandler(notificationConfig: NotificationConfig): NotificationHandler {
        return NotificationHandlerFactory.createNotificationHandler(
            context = context,
            notificationConfig = notificationConfig,
            newMessageIntent = { _: Message, channel: Channel ->
                val packageManager = context.packageManager
                val intent: Intent = packageManager.getLaunchIntentForPackage(context.packageName)!!
                intent.data = "luzia://groups/${channel.cid}".toUri()
                intent
            }
        )
    }

    override suspend fun fetchAndSaveSchoolInformation() {
        runCatching {
            val data = streamApi.getSchoolChannel().successOrNull()
            if (data != null) {
                val json = moshi.adapter(SchoolChannelResponse::class.java).toJson(data)
                luziaDataStore.saveData(schoolChannelData, json)
            }
        }
    }

    override suspend fun getSchoolChannelToJoin(): SchoolChannel? {
        return luziaDataStore.getData(schoolChannelData)?.let { json ->
            val response = moshi.adapter(SchoolChannelResponse::class.java).fromJson(json)
            response?.let {
                SchoolChannel(
                    streamChannelId = it.streamChannelId,
                    name = it.name,
                    image = it.image.orEmpty(),
                    memberCount = it.memberCount
                )
            }
        }
    }

    override suspend fun removeSchoolChannelToJoin() {
        luziaDataStore.delete(schoolChannelData)
    }

    private companion object {
        const val SEPARATOR = "|"
        const val USER_ACCEPTED_REJECTED = "userAcceptedRejected"
        val userAcceptedRejected = stringPreferencesKey(USER_ACCEPTED_REJECTED)
        const val VISITED_FRIENDS_TAB = "hasVisitedFriendsTabBefore"
        val hasVisitedFriendsTabBefore = booleanPreferencesKey(VISITED_FRIENDS_TAB)
        const val SCHOOL_CHANNEL_DATA = "schoolChannelData"
        val schoolChannelData = stringPreferencesKey(SCHOOL_CHANNEL_DATA)
    }
}
