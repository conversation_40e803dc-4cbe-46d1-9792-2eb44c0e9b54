package co.thewordlab.luzia.core.sharing.presentation.chatdetail

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.chat.domain.models.proactive.ProactiveMessages
import co.thewordlab.luzia.core.profile.domain.ProfileRepository
import co.thewordlab.luzia.core.sharing.presentation.chatdetail.models.SharedMessageUiModel
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Event
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.getDataOrNull
import co.theworldlab.luzia.features.proactive.messaging.SHARE_CONTENT
import co.theworldlab.luzia.features.proactive.messaging.ShareContentOpenProfile
import co.theworldlab.luzia.features.proactive.messaging.ShareContentScreen
import co.theworldlab.luzia.features.proactive.messaging.domain.repository.ProActiveMessagingRepository
import co.theworldlab.luzia.features.proactive.messaging.domain.usecases.GetProactiveSharedMessagesUseCase
import co.theworldlab.luzia.features.proactive.messaging.domain.usecases.model.ProactiveAction
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ShareContentChatDetailViewModel @Inject constructor(
    private val getProactiveSharedMessagesUseCase: GetProactiveSharedMessagesUseCase,
    private val proactiveRepository: ProActiveMessagingRepository,
    private val profileRepository: ProfileRepository,
    private val analytics: Analytics,
) : ViewModel(),
    ViewModelActions<ShareContentChatDetailViewActions>,
    ViewModelEvents<ShareContentChatDetailViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<ShareContentChatDetailViewState> by ViewModelStatesImpl(
        ShareContentChatDetailViewState()
    ) {

    private var userId: String = ""

    override fun onViewAction(action: ShareContentChatDetailViewActions) {
        when (action) {
            is ShareContentChatDetailViewActions.OnCreate -> {
                userId = action.userId
                getUserName()
                getSharedMessages(action.userId)
            }

            is ShareContentChatDetailViewActions.OnLinkClicked ->
                sendEvent(ShareContentChatDetailViewEvents.OnLinkClicked(action.url))

            ShareContentChatDetailViewActions.OnTextCopied -> {
                analytics.logEvent(
                    Event.CopyMessage,
                    mapOf(Parameter.Source to SHARE_CONTENT)
                )
            }

            is ShareContentChatDetailViewActions.OnTextReported -> {
                analytics.logEvent(
                    Event.ReportMessage,
                    mapOf(
                        Parameter.Source to SHARE_CONTENT,
                        Parameter.Text to action.text,
                        Parameter.MessageId to action.messageId
                    )
                )
            }

            ShareContentChatDetailViewActions.OnTextShared -> {
                analytics.logEvent(
                    Event.ShareMessage,
                    mapOf(Parameter.Source to SHARE_CONTENT)
                )
            }

            ShareContentChatDetailViewActions.OnNavigateToProfile -> {
                analytics.logAction(
                    ShareContentOpenProfile,
                    mapOf(Parameter.Source to SHARE_CONTENT)
                )
                sendEvent(ShareContentChatDetailViewEvents.NavigateToProfile(userId))
            }
        }
    }

    private fun getUserName() = viewModelScope.launch {
        val displayName =
            profileRepository.getUserProfile()?.displayName.orEmpty()
        updateState { viewState.value.copy(userName = displayName) }
    }

    private fun getSharedMessages(userId: String) = viewModelScope.launch {
        if (userId.isNotEmpty()) {
            val displayName =
                profileRepository.getUserProfileById(userId).getDataOrNull()?.displayName.orEmpty()
            when (val result = getProactiveSharedMessagesUseCase()) {
                is ResultOf.Failure -> {
                    sendEvent(ShareContentChatDetailViewEvents.OnErrorAppears)
                    sendEvent(ShareContentChatDetailViewEvents.OnNavigateBack)
                }

                is ResultOf.Success -> {
                    when (val shared = result.data) {
                        is ProactiveAction.SharedMessages -> if (shared.items.isNotEmpty()) {
                            extractMessages(shared.items, displayName, userId)
                        } else {
                            sendEvent(ShareContentChatDetailViewEvents.OnNavigateBack)
                        }

                        is ProactiveAction.ShowPopupMessage -> DO_NOTHING
                    }
                }
            }
        }
    }

    private suspend fun extractMessages(
        items: List<ProactiveMessages>,
        displayName: String,
        userId: String,
    ) {
        analytics.logScreen(ShareContentScreen)
        val userMessages = mutableListOf<SharedMessageUiModel>()
        userMessages.add(SharedMessageUiModel(isAI = true, name = displayName))
        items.forEach { proactiveMessage ->
            proactiveMessage.messages.filter {
                it.masterUserId == userId
            }.forEach {
                it.sharedMessages.forEach { sharedMessage ->
                    userMessages.add(
                        SharedMessageUiModel(
                            isAI = false,
                            name = displayName,
                            messageId = proactiveMessage.messageId,
                            message = sharedMessage.aiResponse
                        )
                    )
                }
            }
        }

        userMessages.firstOrNull { !it.isAI }?.let {
            updateState { viewState.value.copy(messages = userMessages) }
            proactiveRepository.markMessagesAsRead(
                userMessages.filter { !it.isAI }.map { it.messageId }
            )
        } ?: DO_NOTHING
    }
}
