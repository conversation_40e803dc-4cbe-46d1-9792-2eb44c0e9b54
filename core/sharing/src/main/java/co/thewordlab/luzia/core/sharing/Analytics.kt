package co.thewordlab.luzia.core.sharing

import co.thewordlab.luzia.foundation.analytics.AnalyticsActions
import co.thewordlab.luzia.foundation.analytics.AnalyticsScreens
import co.thewordlab.luzia.foundation.analytics.Parameter

internal data object ShareList : AnalyticsScreens("share_list")
internal data object ForwardIconClicked : AnalyticsActions("forward_click")
internal data object NativeShareClicked : AnalyticsActions("share_native_click")
data object CreateGroupIconAction : AnalyticsActions("create_group_icon")

data object DeleteSelectorOpen : AnalyticsScreens("message_elimination")
data object DeleteMessagesClicked : AnalyticsActions("message_elimination_select")
data object DeleteMessagesCancelled : AnalyticsActions("message_elimination_cancel")
data object DeleteMessagesConfirmed : AnalyticsActions("message_elimination_confirm")
data object DeleteMessagesUndoClicked : AnalyticsActions("message_elimination_undo")
data object ChatListUpperSellBannerScreen : AnalyticsScreens("chat_list_upsell_banner")
data object ChatListClickedBannerAction : AnalyticsActions("chat_list_clicked_banner")
data object ShareGroupLink : AnalyticsActions("share_group_link")
data object AddMembers : AnalyticsActions("add_members")

data object GroupSettingsOpen : AnalyticsActions("group_settings_open")
data object GroupSettingsOptionClick : AnalyticsActions("group_settings_option_click")
data object GroupNameUpdateConfirm : AnalyticsActions("group_name_update_confirm")
data object GroupImageUpdateConfirm : AnalyticsActions("group_image_update_confirm")
data object DmRequestDeny : AnalyticsActions("dm_request_deny")
data object DmRequestAccept : AnalyticsActions("dm_request_accept")
data object AddMembersScreen : AnalyticsScreens("add_members")

const val MESSAGE_DELETE_POPUP = "confirmation-alert"

fun buildSharingParams(count: Int, personalityId: String?) =
    mapOf(
        Parameter.Personality to (personalityId ?: Parameter.Empty.value),
        Parameter.Count to count
    )

fun buildMessageAttributes(personalityId: String, messageType: String, messageId: String) =
    mutableMapOf<Parameter, Any>(
        Parameter.MessageId to messageId,
        Parameter.Type to messageType,
        Parameter.Personality to personalityId
    )
