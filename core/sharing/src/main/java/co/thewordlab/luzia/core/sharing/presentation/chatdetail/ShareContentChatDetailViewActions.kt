package co.thewordlab.luzia.core.sharing.presentation.chatdetail

import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class ShareContentChatDetailViewActions : ViewAction {
    data class OnCreate(val userId: String) : ShareContentChatDetailViewActions()
    data class OnTextReported(val text: String, val messageId: String) : ShareContentChatDetailViewActions()
    data object OnTextCopied : ShareContentChatDetailViewActions()
    data object OnTextShared : ShareContentChatDetailViewActions()
    data class OnLinkClicked(val url: String) : ShareContentChatDetailViewActions()
    data object OnNavigateToProfile : ShareContentChatDetailViewActions()
}
