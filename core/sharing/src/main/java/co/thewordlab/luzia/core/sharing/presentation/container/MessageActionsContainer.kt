package co.thewordlab.luzia.core.sharing.presentation.container

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarResult
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.chat.domain.models.message.LocalSelectedMessageState
import co.thewordlab.luzia.core.chat.domain.models.message.SelectedMessageContext
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.core.navigation.usersession.model.NewChatPurpose
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.shareText
import co.theworldlab.luzia.foundation.design.system.components.item.SelectedItemState
import co.theworldlab.luzia.foundation.design.system.components.item.rememberSelectedItems
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageModel
import co.theworldlab.luzia.foundation.design.system.components.scaffold.LocalAppState
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.Loading
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialog
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogButton
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogTextDefaults
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun MessageActionsContainer(
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit,
) {
    val context = LocalContext.current
    val viewModel: MessageActionsViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val appState = LocalAppState.current
    val navigation = LocalNavigation.current

    OnCreate("MessageActionsContainer") { viewModel.onViewAction(MessageActionsViewActions.OnCreate) }

    ViewModelEventEffect(viewModel) { event ->
        when (event) {
            is MessageActionsViewEvents.NativeShare -> context.shareText(event.text)
            is MessageActionsViewEvents.ShowDeleteConfirmation -> {
                appState.showSnackBarWithAction(
                    message = String.format(
                        context.resources.getQuantityString(
                            localizationR.plurals.delete_multiple_messages_done,
                            event.messagesCount
                        ),
                        event.messagesCount
                    ),
                    actionLabel = context.getString(localizationR.string.delete_multiple_messages_undo),
                    duration = SnackbarDuration.Short,
                    onActionReceived = { action ->
                        when (action) {
                            SnackbarResult.Dismissed -> viewModel.onViewAction(
                                MessageActionsViewActions.OnDeleteConfirmed
                            )

                            SnackbarResult.ActionPerformed -> viewModel.onViewAction(
                                MessageActionsViewActions.OnDeleteUndo
                            )
                        }
                    }
                )
            }

            is MessageActionsViewEvents.StreamShare -> {
                navigation.navigate(
                    UserSessionRoutes.NewChat(
                        purpose = NewChatPurpose.SHARE,
                        messagesToShare = event.messages.map { it.value }
                    )
                )
            }
        }
    }

    MessageActionsContent(
        modifier = modifier,
        content = content,
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )

    if (viewState.isLoading) {
        Loading(showTranslucentBackground = true)
    }
}

@Composable
private fun MessageActionsContent(
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit,
    viewState: MessageActionsViewState,
    onViewActions: (MessageActionsViewActions) -> Unit,
) {
    val selectedState = rememberSelectedItems<MessageModel> { item, other ->
        item.messageId == other.messageId
    }
    BackHandler(selectedState.isActive) {
        selectedState.clear()
    }

    if (viewState.showDeleteConfirmation) {
        DeleteConfirmationPopup(selectedState, onViewActions)
    }

    CompositionLocalProvider(
        LocalSelectedMessageState provides selectedState,
    ) {
        Column(modifier) {
            content()
            selectedState.run {
                AnimatedVisibility(isActive && context.value == SelectedMessageContext.Share) {
                    LaunchedEffect(Unit) {
                        onViewActions(MessageActionsViewActions.OnShareContainerShown)
                    }
                    ShareMessageBottomBar(getItems().value, viewState, onViewActions)
                }
                AnimatedVisibility(isActive && context.value == SelectedMessageContext.Delete) {
                    LaunchedEffect(Unit) {
                        onViewActions(
                            MessageActionsViewActions.OnDeletionContainerShown(
                                getItems().value.firstOrNull()
                            )
                        )
                    }
                    DeleteMessageBottomBar(getItems().value, onViewActions)
                }
            }
        }
    }
}

@Composable
private fun ShareMessageBottomBar(
    messages: List<MessageModel>,
    viewState: MessageActionsViewState,
    onViewActions: (MessageActionsViewActions) -> Unit,
) {
    val shareState = LocalSelectedMessageState.current
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(LuziaTheme.palette.surface.content)
            .navigationBarsPadding()
            .padding(top = Spacing.X8.dp)
            .padding(bottom = Spacing.X16.dp)
            .padding(horizontal = Spacing.X16.dp),
        contentAlignment = Alignment.Center
    ) {
        if (viewState.isForwardVisible) {
            IconButton(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .clip(CircleShape)
                    .background(LuziaTheme.palette.interactive.brandLight)
                    .size(Spacing.X40.dp)
                    .testTag("buttonInternalShare"),
                onClick = {
                    onViewActions(MessageActionsViewActions.OnForwardClicked(messages))
                    shareState.clear()
                }
            ) {
                Icon(
                    painter = painterResource(designR.drawable.ic_forward),
                    tint = LuziaTheme.palette.text.brand,
                    contentDescription = null
                )
            }
        }
        LuziaText(
            textAlign = TextAlign.Center,
            text = stringResource(localizationR.string.group_chat_selected_message, messages.size),
            style = LuziaTheme.typography.body.regular.default,
            color = LuziaTheme.palette.text.helper
        )
        IconButton(
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .clip(CircleShape)
                .background(LuziaTheme.palette.interactive.brandLight)
                .size(Spacing.X40.dp)
                .testTag("buttonShare"),
            onClick = {
                onViewActions(MessageActionsViewActions.OnShareClicked(messages))
                shareState.clear()
            }
        ) {
            Icon(
                painter = painterResource(designR.drawable.ic_share_arrow),
                tint = LuziaTheme.palette.text.brand,
                contentDescription = null
            )
        }
    }
}

@Composable
private fun DeleteMessageBottomBar(
    messages: List<MessageModel>,
    onViewActions: (MessageActionsViewActions) -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(LuziaTheme.palette.surface.content)
            .navigationBarsPadding()
            .padding(top = Spacing.X8.dp)
            .padding(bottom = Spacing.X16.dp)
            .padding(horizontal = Spacing.X16.dp),
        contentAlignment = Alignment.Center
    ) {
        IconButton(
            modifier = Modifier
                .align(Alignment.CenterStart)
                .clip(CircleShape)
                .background(LuziaTheme.palette.interactive.brandLight)
                .size(Spacing.X40.dp),
            onClick = { onViewActions(MessageActionsViewActions.OnDeleteClicked(messages)) }
        ) {
            Icon(
                painter = painterResource(designR.drawable.ic_delete_24),
                tint = LuziaTheme.palette.text.brand,
                contentDescription = null
            )
        }
        LuziaText(
            textAlign = TextAlign.Center,
            text = stringResource(localizationR.string.group_chat_selected_message, messages.size),
            style = LuziaTheme.typography.body.regular.default,
            color = LuziaTheme.palette.text.helper
        )
    }
}

@Composable
private fun DeleteConfirmationPopup(
    deleteState: SelectedItemState<MessageModel>,
    onViewActions: (MessageActionsViewActions) -> Unit,
) {
    LuziaAlertDialog(
        confirmButton = LuziaAlertDialogButton(
            stringResource(id = localizationR.string.delete),
            action = {
                deleteState.clear()
                onViewActions(MessageActionsViewActions.OnStartDeleting)
            },
            isPrimaryAction = true
        ),
        dismissButton = LuziaAlertDialogButton(
            stringResource(id = localizationR.string.cancel),
            action = { onViewActions(MessageActionsViewActions.OnDeleteCancelled) }
        ),
        text = LuziaAlertDialogText(
            String.format(
                pluralStringResource(
                    id = localizationR.plurals.delete_multiple_messages_info,
                    deleteState.getItems().value.size
                ),
                deleteState.getItems().value.size
            ),
            LuziaAlertDialogTextDefaults.Title()
        )
    )
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        MessageActionsContainer(
            content = {}
        )
    }
}
