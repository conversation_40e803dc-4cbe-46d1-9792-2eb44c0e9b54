package co.thewordlab.luzia.core.sharing.domain.usecase

import co.thewordlab.luzia.core.profile.domain.GetUserProfileUseCase
import co.thewordlab.luzia.core.sharing.domain.ClientState
import co.thewordlab.luzia.core.sharing.domain.model.GroupChatErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import io.getstream.chat.android.client.api.models.QueryChannelsRequest
import io.getstream.chat.android.models.Channel
import io.getstream.chat.android.models.FilterObject
import io.getstream.chat.android.models.Filters
import io.getstream.result.Result
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import javax.inject.Inject

class FetchGetStreamChannelsUseCase @Inject constructor(
    private val getConnectedClientUseCase: GetConnectedClientUseCase,
    private val getUserProfileUseCase: GetUserProfileUseCase,
    private val dispatcher: CoroutineDispatcher
) {

    suspend operator fun invoke(memberLimit: Int): ResultOf<List<Channel>, GroupChatErrors> =
        withContext(dispatcher) {
            val client = getConnectedClientUseCase()
                .filterIsInstance<ClientState.Ready>()
                .map { it.client }
                .firstOrNull()
            val streamUserId = getUserProfileUseCase().firstOrNull()?.streamUserId

            if (client == null || streamUserId.isNullOrEmpty()) {
                return@withContext channelsLoadingFailed()
            }

            val filter = createFilter(streamUserId, memberLimit)
            val request = QueryChannelsRequest(filter = filter, limit = LIMIT)
            return@withContext when (val result = client.queryChannels(request).await()) {
                is Result.Failure -> channelsLoadingFailed()
                is Result.Success -> ResultOf.Success(result.value)
            }
        }

    private fun channelsLoadingFailed(): ResultOf<List<Channel>, GroupChatErrors> {
        return ResultOf.Failure(GroupChatErrors.FetchChannelsFailed)
    }

    private fun createFilter(streamUserId: String, memberLimit: Int): FilterObject {
        return Filters.and(
            Filters.`in`(MEMBERS, listOf(streamUserId)),
            Filters.greaterThan(MEMBER_COUNT, memberLimit),
            Filters.eq(DISABLED, false),
        )
    }

    private companion object {
        const val MEMBERS = "members"
        const val MEMBER_COUNT = "member_count"
        const val DISABLED = "disabled"
        const val LIMIT = 30
    }
}
