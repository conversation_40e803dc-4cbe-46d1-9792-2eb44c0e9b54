package co.thewordlab.luzia.core.sharing.presentation.group

import co.thewordlab.luzia.core.chat.presentation.search.SearchViewState
import co.thewordlab.luzia.core.sharing.domain.model.ShareChannel
import co.thewordlab.luzia.foundation.architecture.system.ViewState
import co.theworldlab.luzia.foundation.design.system.components.mate.UserMateUiModel

data class NewGroupViewState(
    val messagesToShare: List<String> = emptyList(),
    val groupName: String = "",
    val searchViewState: SearchViewState<ShareChannel.Mate>? = null,
    val searchedChannels: List<ShareChannel.Mate>? = null,
    val isCreatingGroup: Boolean = false,
    val groupImageUrl: String? = null,
    val mates: List<UserMateUiModel> = emptyList(),
    val section: NewGroupSection = NewGroupSection.UserSelection,
    val selectedUsers: List<String> = emptyList(),
) : ViewState

enum class NewGroupSection {
    UserSelection,
    Name,
    Image
}
