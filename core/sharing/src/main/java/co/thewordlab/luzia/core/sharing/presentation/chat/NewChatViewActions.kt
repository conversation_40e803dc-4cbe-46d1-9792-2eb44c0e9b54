package co.thewordlab.luzia.core.sharing.presentation.chat

import co.thewordlab.luzia.core.navigation.usersession.model.NewChatPurpose
import co.thewordlab.luzia.core.sharing.domain.model.ShareChannel
import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class NewChatViewActions : ViewAction {
    data class OnCreate(
        val purpose: NewChatPurpose,
        val messages: List<String>
    ) : NewChatViewActions()

    data object OnBack : NewChatViewActions()
    data object OnBannerClicked : NewChatViewActions()
    data object OnReferralLink : NewChatViewActions()
    data object OnShareMessage : NewChatViewActions()
    data class OnSendMessage(val channel: ShareChannel) : NewChatViewActions()
    data class OnQueryChanged(val query: String) : NewChatViewActions()
    data class SelectChannel(val channel: ShareChannel) : NewChatViewActions()
}
