package co.thewordlab.luzia.core.sharing.domain.usecase

import co.thewordlab.luzia.core.sharing.domain.ClientState
import co.thewordlab.luzia.core.sharing.domain.repository.StreamRepository
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.firstOrNull
import javax.inject.Inject

class ReportUserUseCase @Inject constructor(
    private val getConnectedClientUseCase: GetConnectedClientUseCase,
    private val streamRepository: StreamRepository
) {

    suspend operator fun invoke(masterId: String, cid: String?) {
        streamRepository.reportUser(masterId, cid)
        val state = getConnectedClientUseCase().filterIsInstance<ClientState.Ready>().firstOrNull()
        state?.client?.flagUser(masterId, null, mapOf())?.await()
    }
}
