package co.thewordlab.luzia.core.sharing.domain.usecase

import co.thewordlab.luzia.core.chat.analytics.SendMessageEventHelper
import co.thewordlab.luzia.core.chat.analytics.SendMessageEventHelperImp
import co.thewordlab.luzia.core.sharing.domain.SharingAnalytics
import co.thewordlab.luzia.core.sharing.domain.usecase.model.StreamType
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import io.getstream.chat.android.client.ChatClient
import io.getstream.chat.android.client.ChatEventListener
import io.getstream.chat.android.client.channel.subscribeFor
import io.getstream.chat.android.client.events.NewMessageEvent
import io.getstream.chat.android.models.Channel
import io.getstream.chat.android.models.Message
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class ListenToGetStreamEventsUseCase @Inject constructor(
    private val analytics: Analytics
) : SendMessageEventHelper by SendMessageEventHelperImp(analytics) {

    private var totalMembers = 0
    private var currentChannelId = ""
    private var currentStreamType = StreamType.DM
    private var currentUserId: String = ""

    operator fun invoke(client: ChatClient, channelId: String) = callbackFlow {
        val channel = client.channel(channelId).get(memberLimit = LIMIT).await().getOrNull()
        totalMembers = channel?.members?.size ?: 0
        val type = if (totalMembers == DM_MEMBER_COUNT) StreamType.DM else StreamType.GROUP
        currentChannelId = channelId
        currentStreamType = type
        currentUserId = client.getCurrentUser()?.id.orEmpty()
        sendViewChannelMetrics()
        val cid = getClientId(channelId)
        val disposable = client.channel(cid).subscribeFor(
            ChatEventListener<NewMessageEvent> {
                onEvent(coroutineContext, it)
                trySend(Unit)
            }
        )
        awaitClose {
            sendLastMessageMetrics()
            disposable.dispose()
        }
    }

    private fun getClientId(id: String): String {
        return if (!id.contains(TYPE)) {
            Channel.Builder().withType(TYPE).withId(id).build().cid
        } else {
            id
        }
    }

    private fun onEvent(coroutineContext: CoroutineContext, event: NewMessageEvent) {
        CoroutineScope(coroutineContext).launch {
            if (event.user.id == currentUserId) {
                val props = mutableMapOf<Parameter, Any>(
                    Parameter.ChannelId to currentChannelId,
                    Parameter.StreamType to currentStreamType,
                    Parameter.Type to event.message.getMessageType()
                )
                sendMessageMetrics(props)
            }
        }
    }

    private fun sendViewChannelMetrics() {
        val props = mapOf(
            Parameter.CountGroup to totalMembers,
            Parameter.ChannelId to currentChannelId,
            Parameter.StreamType to currentStreamType
        )
        analytics.logAction(SharingAnalytics.ViewStreamChannel, props)
    }

    private fun Message.getMessageType(): String {
        return when {
            text.isNotEmpty() && attachments.isEmpty() -> MESSAGE_TEXT_TYPE
            else -> this.attachments.firstOrNull()?.type.orEmpty()
        }
    }

    private companion object {
        const val TYPE = "messaging"
        const val MESSAGE_TEXT_TYPE = "text"
        const val LIMIT = 50
        const val DM_MEMBER_COUNT = 2
    }
}
