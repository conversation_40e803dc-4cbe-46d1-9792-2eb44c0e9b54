package co.thewordlab.luzia.core.sharing.domain.repository

import co.thewordlab.luzia.core.sharing.domain.model.SharedMessageItem
import co.thewordlab.luzia.core.sharing.domain.model.SharingErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import io.getstream.chat.android.models.Channel

interface SharingRepository {

    suspend fun shareMessages(
        users: List<String>,
        messages: List<SharedMessageItem>
    ): ResultOf<Unit, SharingErrors>

    suspend fun shareMessagesWithLink(
        personalityId: String,
        messages: List<SharedMessageItem>,
        personalityName: String? = null
    ): ResultOf<String, SharingErrors>

    suspend fun generateInviteLink(
        channel: Channel
    ): ResultOf<String, SharingErrors.GenerateLink>
}
