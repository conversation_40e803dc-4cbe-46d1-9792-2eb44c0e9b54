package co.thewordlab.luzia.core.sharing.di

import co.thewordlab.luzia.core.sharing.data.api.StreamApi
import co.thewordlab.luzia.core.sharing.data.repository.StreamRepositoryImpl
import co.thewordlab.luzia.core.sharing.domain.repository.StreamRepository
import co.thewordlab.luzia.foundation.networking.di.BaseHost
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit

@InstallIn(SingletonComponent::class)
@Module
object StreamModule {

    @Provides
    fun provideStreamRepository(impl: StreamRepositoryImpl): StreamRepository = impl

    @Provides
    fun provideStreamApi(
        @BaseHost retrofit: Retrofit
    ): StreamApi = retrofit.create(StreamApi::class.java)
}
