package co.thewordlab.luzia.core.tools.data.repository

import android.net.Uri
import app.cash.turbine.test
import co.thewordlab.fouundation.persistence.chat.ChatDataSource
import co.thewordlab.fouundation.persistence.chat.ChatMetadata
import co.thewordlab.luzia.foundation.messages.data.api.MathApi
import co.thewordlab.luzia.foundation.messages.data.model.ContentMetadataDto
import co.thewordlab.luzia.foundation.messages.data.model.MathImageDto
import com.slack.eithernet.ApiResult
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import java.io.File

class MathToolRepositoryImpTest {

    private val datasource: ChatDataSource = mockk(relaxed = true)
    private val mathApi: MathApi = mockk(relaxed = true)
    private val repository = MathToolRepositoryImp(
        datasource = datasource,
        luziaDataStore = mockk(),
        mathApi = mathApi,
        chatApi = mockk(),
        mediaFileProvider = mockk(),
        analytics = mockk(),
        proactiveMessageHandler = mockk()
    )

    @Before
    fun setup() {
        val uri: Uri = mockk()
        mockkStatic(Uri::class)
        every { Uri.fromFile(any()) } returns uri
    }

    @Test
    fun `getChatMetadata returns flow from datasource`() = runTest {
        val chatMetadata = mockk<ChatMetadata>()
        val metadataFlow: Flow<ChatMetadata?> = flowOf(chatMetadata)
        every { datasource.getChatMetadata("LuzIA") } returns metadataFlow

        val result = repository.getChatMetadata()

        result.test {
            assertEquals(chatMetadata, awaitItem())
            cancelAndIgnoreRemainingEvents()
        }
    }

    @Test
    fun `getChatMetadata returns flow of chat metadata`() = runTest {
        val metadata: ChatMetadata = mockk()
        every { datasource.getChatMetadata(LUZIA_ID_PERSONALITY) } returns flowOf(metadata)
        repository.getChatMetadata().test {
            assertEquals(metadata, awaitItem())
            cancelAndIgnoreRemainingEvents()
        }
    }

    @Test
    fun `send image action calls api with correct payload`() = runTest {
        val metadata = ContentMetadataDto("", 0)
        val response = MathImageDto("test", "test", metadata)
        coEvery { mathApi.uploadFile(any()) } returns ApiResult.success(response)
        val file = File("test.txt")
        repository.sendImage(file, "test")
        coVerify { datasource.insertMessage(any()) }
        coVerify { mathApi.uploadFile(any()) }
    }

    private companion object {
        const val LUZIA_ID_PERSONALITY = "LuzIA"
    }
}
