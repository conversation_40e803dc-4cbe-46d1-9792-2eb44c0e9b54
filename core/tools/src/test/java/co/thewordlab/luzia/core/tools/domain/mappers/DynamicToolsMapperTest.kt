package co.thewordlab.luzia.core.tools.domain.mappers

import co.thewordlab.luzia.core.tools.data.models.ContentMetadataDto
import co.thewordlab.luzia.core.tools.data.models.DynamicToolButtonTypeDto
import co.thewordlab.luzia.core.tools.data.models.DynamicToolComponentDto
import co.thewordlab.luzia.core.tools.data.models.DynamicToolComponentTypeDto
import co.thewordlab.luzia.core.tools.data.models.DynamicToolDto
import co.thewordlab.luzia.core.tools.data.models.DynamicToolOptionsDto
import co.thewordlab.luzia.core.tools.domain.model.DynamicToolButtonType
import co.thewordlab.luzia.core.tools.domain.model.DynamicToolComponentType
import co.thewordlab.luzia.core.tools.domain.model.DynamicToolConfig
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Test

@Suppress("MaxLineLength")
class DynamicToolsMapperTest {

    @Test
    fun `given DynamicToolDto when mapToDomain then returns DynamicTool`() = runTest {
        val dto = DynamicToolDto(
            id = "testId",
            name = "testName",
            description = "testDescription",
            footer = emptyList(),
            body = emptyList()
        )

        val result = dto.mapToDomain()

        assertEquals("testId", result.id)
        assertEquals("testName", result.name)
        assertEquals("testDescription", result.description)
        assertTrue(result.footer.isEmpty())
        assertTrue(result.body.isEmpty())
    }

    @Test
    fun `given DynamicToolDto with null values when mapToDomain then returns DynamicTool with empty strings and lists`() = runTest {
        val dto = DynamicToolDto(
            id = null,
            name = null,
            description = null,
            footer = emptyList(),
            body = emptyList()
        )

        val result = dto.mapToDomain()

        assertEquals("", result.id)
        assertEquals("", result.name)
        assertEquals("", result.description)
        assertTrue(result.footer.isEmpty())
        assertTrue(result.body.isEmpty())
        assertNull(result.resultId)
    }

    @Test
    fun `given DynamicToolDto with footer and body when mapToDomain then returns DynamicTool with mapped components`() = runTest {
        val footerComponentDto = DynamicToolComponentDto(
            id = "footerId",
            type = DynamicToolComponentTypeDto.TITLE,
            title = "footerTitle",
            subtitle = "footerSubtitle",
            options = listOf(DynamicToolOptionsDto("optId", "optTitle", "optSubtitle")),
            maxLength = 100,
            placeholder = "footerPlaceholder",
            content = "footerContent",
            actionType = null,
            action = "footerAction",
            rawHTML = "<p>footer</p>"
        )
        val bodyComponentDto = DynamicToolComponentDto(
            id = "bodyId",
            type = DynamicToolComponentTypeDto.BUTTON,
            title = "bodyTitle",
            subtitle = "bodySubtitle",
            options = emptyList(),
            maxLength = 0,
            placeholder = "",
            content = "bodyContent",
            actionType = DynamicToolButtonTypeDto.DEEPLINK,
            action = "bodyAction",
            rawHTML = ""
        )
        val dto = DynamicToolDto(
            id = "testId",
            name = "testName",
            description = "testDescription",
            footer = listOf(footerComponentDto),
            body = listOf(bodyComponentDto),
            metadata = ContentMetadataDto(requestId = "requestId")
        )

        val result = dto.mapToDomain()

        assertEquals("testId", result.id)
        assertEquals("testName", result.name)
        assertEquals("testDescription", result.description)

        assertEquals(1, result.footer.size)
        val footerResult = result.footer[0]
        assertEquals("footerId", footerResult.id)
        assertEquals(DynamicToolComponentType.TITLE, footerResult.type)
        assertEquals("footerTitle", footerResult.title)
        assertEquals("footerSubtitle", footerResult.subtitle)
        assertEquals(1, footerResult.options.size)
        assertEquals("optId", footerResult.options[0].id)
        assertEquals("optTitle", footerResult.options[0].title)
        assertEquals("optSubtitle", footerResult.options[0].subtitle)
        assertEquals(100, footerResult.maxLength)
        assertEquals("footerPlaceholder", footerResult.placeholder)
        assertEquals("footerContent", footerResult.content)
        assertEquals(DynamicToolButtonType.NONE, footerResult.actionType)
        assertEquals("footerAction", footerResult.action)
        assertEquals("<p>footer</p>", footerResult.rawHTML)

        assertEquals(1, result.body.size)
        val bodyResult = result.body[0]
        assertEquals("bodyId", bodyResult.id)
        assertEquals(DynamicToolComponentType.BUTTON, bodyResult.type)
        assertEquals("bodyTitle", bodyResult.title)
        assertEquals("bodySubtitle", bodyResult.subtitle)
        assertTrue(bodyResult.options.isEmpty())
        assertEquals(0, bodyResult.maxLength)
        assertEquals("", bodyResult.placeholder)
        assertEquals("bodyContent", bodyResult.content)
        assertEquals(DynamicToolButtonType.DEEPLINK, bodyResult.actionType)
        assertEquals("bodyAction", bodyResult.action)
        assertEquals("", bodyResult.rawHTML)
        assertEquals("requestId", result.resultId)
        assertEquals(true, bodyResult.enableScroll)
        assertEquals(false, bodyResult.enableFullScreen)
    }

    @Test
    fun `given DynamicToolDto with null footer properties when mapToDomain then returns DynamicTool with mapped components`() = runTest {
        val footerComponentDto = DynamicToolComponentDto(
            id = "footerId",
            type = null,
            title = null,
            subtitle = null,
            options = listOf(),
            maxLength = null,
            placeholder = null,
            content = null,
            actionType = null,
            action = null,
            rawHTML = null,
            enableScroll = null,
            enableFullScreen = null
        )
        val dto = DynamicToolDto(
            id = "testId",
            name = "testName",
            description = "testDescription",
            footer = listOf(footerComponentDto),
            body = listOf()
        )

        val result = dto.mapToDomain()

        assertEquals("testId", result.id)
        assertEquals("testName", result.name)
        assertEquals("testDescription", result.description)

        assertEquals(1, result.footer.size)
        val footerResult = result.footer[0]
        assertEquals("footerId", footerResult.id)
        assertEquals(DynamicToolComponentType.NONE, footerResult.type)
        assertEquals("", footerResult.title)
        assertEquals("", footerResult.subtitle)
        assertEquals(0, footerResult.options.size)
        assertEquals(0, footerResult.maxLength)
        assertEquals("", footerResult.placeholder)
        assertEquals("", footerResult.content)
        assertEquals(DynamicToolButtonType.NONE, footerResult.actionType)
        assertEquals("", footerResult.action)
        assertEquals("", footerResult.rawHTML)
        assertEquals(true, footerResult.enableScroll)
        assertEquals(false, footerResult.enableFullScreen)
    }

    @Test
    fun `given listOfDynamicToolConfig when mapToDto then returns DynamicToolConfigDto`() = runTest {
        val configItem = DynamicToolConfig(
            componentId = "compId1",
            type = DynamicToolComponentType.SUBTITLE,
            selectedOption = "option1",
            content = "content1"
        )
        val configList = listOf(configItem)

        val result = configList.mapToDto()

        assertEquals(1, result.components.size)
        val componentResult = result.components[0]
        assertEquals("compId1", componentResult.componentId)
        assertEquals(DynamicToolComponentType.SUBTITLE.value, componentResult.type)
        assertEquals("option1", componentResult.option)
        assertEquals("content1", componentResult.content)
    }

    @Test
    fun `given emptyListOfDynamicToolConfig when mapToDto then returns DynamicToolConfigDto with empty components`() = runTest {
        val configList = emptyList<DynamicToolConfig>()

        val result = configList.mapToDto()

        assertTrue(result.components.isEmpty())
    }
}
