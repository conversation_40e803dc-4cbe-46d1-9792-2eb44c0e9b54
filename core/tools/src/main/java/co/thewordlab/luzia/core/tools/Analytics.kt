@file:Suppress("Filename", "MatchingDeclarationName")

package co.thewordlab.luzia.core.tools

import co.thewordlab.luzia.core.tools.domain.model.DynamicToolConfig
import co.thewordlab.luzia.foundation.analytics.AnalyticsActions
import co.thewordlab.luzia.foundation.analytics.AnalyticsScreens

data object Tools : AnalyticsScreens("tools")
data object DynamicTools : AnalyticsScreens("presentation_tool")
data object DynamicToolsResult : AnalyticsScreens("tool_response")

data object DynamicToolInputButtonClicked : AnalyticsActions("tool_input_tap")
data object DynamicToolAttachmentClicked : AnalyticsActions("tool_attachment_selection")
data object DynamicToolSubmitClicked : AnalyticsActions("tool_submit")
data object DynamicToolCopyClicked : AnalyticsActions("tool_response_copy")
data object DynamicToolShareClicked : AnalyticsActions("tool_response_share")
data object DynamicToolNewClicked : AnalyticsActions("tool_new")
data object ChatComposerToolClicked : AnalyticsActions("chat_composer_tool_tap")
data object ToolClicked : AnalyticsActions("tool_tap")

const val INPUT_TYPE_ATTACHMENT = "attachment"
const val INPUT_TYPE_PASTE = "paste"
const val INPUT_TYPE_CAMERA = "camera"
const val INPUT_TYPE_GALLERY = "gallery"
const val INPUT_TYPE_FILES = "files"
const val PARAMETER_TOOL = "tool"
const val PARAMETER_ORIGIN = "origin"
const val PARAMETER_INPUT = "input"
const val PARAMETER_TYPE = "type"
const val ORIGIN_CHAT = "chat"
const val TOOL_ID_CAMERA = "camera"

fun buildParamsForLaunchAction(toolId: String, origin: String, options: List<DynamicToolConfig>): Map<String, Any> {
    val params = mutableMapOf(
        PARAMETER_TOOL to toolId,
        PARAMETER_ORIGIN to origin
    )
    options.forEach { params[it.componentId] = it.selectedOption.orEmpty() }
    return params.toMap()
}
