package co.thewordlab.luzia.core.tools.data.models

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class DynamicToolDto(
    @Json(name = "id")
    val id: String? = null,
    @<PERSON><PERSON>(name = "name")
    val name: String? = null,
    @Json(name = "description")
    val description: String? = null,
    @<PERSON><PERSON>(name = "footer")
    val footer: List<DynamicToolComponentDto> = emptyList(),
    @Json(name = "body")
    val body: List<DynamicToolComponentDto> = emptyList(),
    @<PERSON>son(name = "metadata")
    val metadata: ContentMetadataDto? = null,
)

@JsonClass(generateAdapter = true)
data class ContentMetadataDto(
    @Json(name = "requestId")
    val requestId: String
)

@JsonClass(generateAdapter = true)
data class DynamicToolComponentDto(
    @Json(name = "id")
    val id: String = "",
    @Json(name = "type")
    val type: DynamicToolComponentTypeDto? = null,
    @J<PERSON>(name = "title")
    val title: String? = null,
    @<PERSON>son(name = "subtitle")
    val subtitle: String? = null,
    @Json(name = "options")
    val options: List<DynamicToolOptionsDto> = emptyList(),
    @Json(name = "maxLength")
    val maxLength: Int? = null,
    @Json(name = "placeholder")
    val placeholder: String? = null,
    @Json(name = "actionType")
    val actionType: DynamicToolButtonTypeDto? = null,
    @Json(name = "action")
    val action: String? = null,
    @Json(name = "content")
    val content: String? = null,
    @Json(name = "rawHTML")
    val rawHTML: String? = null,
    @Json(name = "enableFullScreen")
    val enableFullScreen: Boolean? = false,
    @Json(name = "enableScroll")
    val enableScroll: Boolean? = true,
)

@JsonClass(generateAdapter = true)
data class DynamicToolOptionsDto(
    @Json(name = "id")
    val id: String = "",
    @Json(name = "title")
    val title: String? = null,
    @Json(name = "subtitle")
    val subtitle: String? = null
)

@JsonClass(generateAdapter = false)
enum class DynamicToolButtonTypeDto(val value: String) {
    @Json(name = "deeplink")
    DEEPLINK("deeplink"),

    @Json(name = "navigate")
    NAVIGATE("navigate"),

    @Json(name = "copy")
    COPY("copy"),

    @Json(name = "web_url")
    WEB_URL("web_url")
}

@JsonClass(generateAdapter = false)
enum class DynamicToolComponentTypeDto(val value: String) {
    @Json(name = "media_input")
    MEDIA_INPUT("media_input"),

    @Json(name = "selector")
    SELECTOR("selector"),

    @Json(name = "button")
    BUTTON("button"),

    @Json(name = "subtitle")
    SUBTITLE("subtitle"),

    @Json(name = "html")
    HTML("html"),

    @Json(name = "title")
    TITLE("title")
}
