package co.thewordlab.luzia.core.tools.domain.usecases

import co.thewordlab.luzia.core.tools.domain.model.ToolInChatItem
import co.thewordlab.luzia.core.tools.domain.repository.ToolsRepository
import co.thewordlab.luzia.foundation.common.navigation.SupportedTools
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class GetToolsInChatUseCase @Inject constructor(
    private val toolsRepository: ToolsRepository
) {
    operator fun invoke(): Flow<List<ToolInChatItem>> =
        toolsRepository.getAvailableTools().map { tools ->
            tools.map { ToolInChatItem.Tool(it) }
                .filter { it.tool.id != SupportedTools.VISION_TOOL_ID.key }
        }
}
