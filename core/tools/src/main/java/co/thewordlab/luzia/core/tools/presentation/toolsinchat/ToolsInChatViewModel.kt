package co.thewordlab.luzia.core.tools.presentation.toolsinchat

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.tools.ORIGIN_CHAT
import co.thewordlab.luzia.core.tools.PARAMETER_ORIGIN
import co.thewordlab.luzia.core.tools.PARAMETER_TOOL
import co.thewordlab.luzia.core.tools.TOOL_ID_CAMERA
import co.thewordlab.luzia.core.tools.ToolClicked
import co.thewordlab.luzia.core.tools.domain.model.ToolInChatItem
import co.thewordlab.luzia.core.tools.domain.usecases.GetToolsInChatUseCase
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Event
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.common.navigation.SupportedTools
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ToolsInChatViewModel @Inject constructor(
    private val getToolsInChatUseCase: GetToolsInChatUseCase,
    private val analytics: Analytics,
) : ViewModel(),
    ViewModelStates<ToolsInChatViewState> by ViewModelStatesImpl(ToolsInChatViewState()),
    ViewModelActions<ToolsInChatViewActions>,
    ViewModelEvents<ToolsInChatViewEvents> by ViewModelEventsImpl() {

    init {
        fetchAvailableTools()
    }

    private fun fetchAvailableTools() = viewModelScope.launch {
        getToolsInChatUseCase()
            .onEach { items -> updateState { state -> state.copy(items = items) } }
            .launchIn(viewModelScope)
    }

    override fun onViewAction(action: ToolsInChatViewActions) = when (action) {
        is ToolsInChatViewActions.OnClick -> onItemClicked(action.item)
        is ToolsInChatViewActions.OnCameraClick -> {
            trackToolClick(TOOL_ID_CAMERA)
            analytics.logEvent(Event.PlusItem, mapOf(Parameter.Camera to true))
            sendEvent(ToolsInChatViewEvents.NavigateToCamera(action.openGallery))
        }
    }

    private fun onItemClicked(item: ToolInChatItem) = when (item) {
        ToolInChatItem.Camera -> DO_NOTHING
        is ToolInChatItem.Tool -> {
            trackToolClick(item.tool.id)
            analytics.logEvent(Event.PlusItem, mapOf(Parameter.Tool to item.tool.id))
            if (item.tool.id == SupportedTools.VISION_TOOL_ID.key) {
                sendEvent(ToolsInChatViewEvents.NavigateToCamera(openGallery = false))
            } else {
                sendEvent(ToolsInChatViewEvents.NavigateToTool(item.tool.id, item.tool.isDynamic))
            }
        }
    }

    private fun trackToolClick(toolId: String) {
        analytics.logActionWithProps(
            ToolClicked,
            mapOf(PARAMETER_TOOL to toolId, PARAMETER_ORIGIN to ORIGIN_CHAT)
        )
    }
}
