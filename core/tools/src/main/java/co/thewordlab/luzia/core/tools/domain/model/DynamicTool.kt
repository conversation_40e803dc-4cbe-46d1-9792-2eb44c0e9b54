package co.thewordlab.luzia.core.tools.domain.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

data class DynamicTool(
    val id: String,
    val name: String,
    val description: String,
    val footer: List<DynamicToolComponent>,
    val body: List<DynamicToolComponent>,
    val resultId: String?
)

@JsonClass(generateAdapter = true)
data class DynamicToolConfig(
    @Json(name = "componentId")
    val componentId: String,
    @<PERSON>son(name = "type")
    val type: DynamicToolComponentType,
    @<PERSON><PERSON>(name = "selectedOption")
    val selectedOption: String? = null,
    @<PERSON><PERSON>(name = "content")
    val content: String? = null
)

data class DynamicToolComponent(
    val id: String,
    val type: DynamicToolComponentType,
    val title: String,
    val subtitle: String,
    val options: List<DynamicToolOptions>,
    val maxLength: Int,
    val placeholder: String,
    val content: String,
    val rawHTML: String,
    val actionType: DynamicToolButtonType,
    val action: String,
    val enableFullScreen: Boolean,
    val enableScroll: Boolean,
)

data class DynamicToolOptions(
    val id: String,
    val title: String,
    val subtitle: String
)

enum class DynamicToolButtonType(val value: String) {
    DEEPLINK("deeplink"),
    NAVIGATE("navigate"),
    COPY("copy"),
    WEB_URL("web_url"),
    NONE("none");

    companion object {
        fun fromValue(value: String?): DynamicToolButtonType = entries.find { it.value == value } ?: NONE
    }
}

@JsonClass(generateAdapter = false)
enum class DynamicToolComponentType(val value: String) {
    @Json(name = "media_input")
    MEDIA_INPUT("media_input"),

    @Json(name = "selector")
    SELECTOR("selector"),

    @Json(name = "button")
    BUTTON("button"),

    @Json(name = "subtitle")
    SUBTITLE("subtitle"),

    @Json(name = "html")
    HTML("html"),

    @Json(name = "title")
    TITLE("title"),

    @Json(name = "none")
    NONE("none");

    companion object {
        fun fromValue(value: String?): DynamicToolComponentType = entries.find { it.value == value } ?: NONE
    }
}
