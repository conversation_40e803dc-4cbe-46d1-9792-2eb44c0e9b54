@file:Suppress("LongParameterList")

package co.thewordlab.luzia.core.tools.di

import co.thewordlab.fouundation.persistence.LuziaDataStore
import co.thewordlab.fouundation.persistence.chat.ChatDataSource
import co.thewordlab.luzia.core.tools.data.api.ToolsApi
import co.thewordlab.luzia.core.tools.data.repository.DocumentToolRepositoryImp
import co.thewordlab.luzia.core.tools.data.repository.MathToolRepositoryImp
import co.thewordlab.luzia.core.tools.data.repository.ToolsRepositoryImpl
import co.thewordlab.luzia.core.tools.domain.repository.DocumentToolRepository
import co.thewordlab.luzia.core.tools.domain.repository.MathToolRepository
import co.thewordlab.luzia.core.tools.domain.repository.ToolsRepository
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.messages.data.MediaFileProvider
import co.thewordlab.luzia.foundation.messages.data.api.ChatApi
import co.thewordlab.luzia.foundation.messages.data.api.MathApi
import co.thewordlab.luzia.foundation.messages.di.InMemory
import co.thewordlab.luzia.foundation.messages.di.Persistent
import co.thewordlab.luzia.foundation.messages.domain.repository.ProactiveMessageHandler
import co.thewordlab.luzia.foundation.networking.di.BaseHost
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit

@Module
@InstallIn(SingletonComponent::class)
object CoreToolsModule {

    @Provides
    fun provideRetrofit(@BaseHost retrofit: Retrofit): ToolsApi {
        return retrofit.create(ToolsApi::class.java)
    }

    @Provides
    fun provideToolsRepository(impl: ToolsRepositoryImpl): ToolsRepository = impl

    @InMemory
    @Provides
    fun provideDocumentsRepo(
        @InMemory chatDataSource: ChatDataSource,
        luziaDataStore: LuziaDataStore,
        toolsApi: ToolsApi,
        mediaFileProvider: MediaFileProvider,
        analytics: Analytics
    ): DocumentToolRepository = DocumentToolRepositoryImp(
        chatDataSource,
        luziaDataStore,
        toolsApi,
        mediaFileProvider,
        analytics,
        null
    )

    @Persistent
    @Provides
    fun providePersistentDocumentToolRepository(
        @Persistent chatDataSource: ChatDataSource,
        luziaDataStore: LuziaDataStore,
        toolsApi: ToolsApi,
        mediaFileProvider: MediaFileProvider,
        analytics: Analytics,
        proactiveMessageHandler: ProactiveMessageHandler
    ): DocumentToolRepository =
        DocumentToolRepositoryImp(
            chatDataSource,
            luziaDataStore,
            toolsApi,
            mediaFileProvider,
            analytics,
            proactiveMessageHandler
        )

    @Provides
    fun provideMathRepository(
        luziaDataStore: LuziaDataStore,
        @InMemory chatDataSource: ChatDataSource,
        mathApi: MathApi,
        chatApi: ChatApi,
        mediaFileProvider: MediaFileProvider,
        analytics: Analytics,
        proactiveMessageHandler: ProactiveMessageHandler,
    ): MathToolRepository = MathToolRepositoryImp(
        chatDataSource,
        luziaDataStore,
        mathApi,
        chatApi,
        mediaFileProvider,
        analytics,
        proactiveMessageHandler
    )
}
