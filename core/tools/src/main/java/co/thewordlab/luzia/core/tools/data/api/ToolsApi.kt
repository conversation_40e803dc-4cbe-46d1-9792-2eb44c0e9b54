package co.thewordlab.luzia.core.tools.data.api

import co.thewordlab.luzia.core.tools.data.models.DynamicToolDto
import co.thewordlab.luzia.core.tools.data.models.ToolsDTO
import co.thewordlab.luzia.foundation.messages.data.model.CompletionsRequest
import co.thewordlab.luzia.foundation.messages.data.model.ContentTextDto
import co.thewordlab.luzia.foundation.messages.data.model.ToolDetailDto
import co.thewordlab.luzia.foundation.networking.headers.CacheEnabled
import co.thewordlab.luzia.foundation.networking.headers.CacheExpiry
import co.thewordlab.luzia.foundation.networking.model.ErrorDto
import com.slack.eithernet.ApiResult
import com.slack.eithernet.DecodeErrorBody
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query
import java.util.Locale

interface ToolsApi {

    @CacheEnabled(CacheExpiry.HOUR_12)
    @DecodeErrorBody
    @GET("tools")
    suspend fun getTools(
        @Query("onboarding") onboarding: Boolean? = null
    ): ApiResult<ToolsDTO, ErrorDto>

    /**
     * Uploads and processes an audio file
     * * Responses:
     *  - 200: OK
     *  - 0: Unexpected error
     *
     * @param body
     * @param acceptLanguage Client&#39;s preferred language for response content following ISO-639-1 (optional)
     * @param personalityId  (optional)
     * @return [ContentTextDto]
     */
    @DecodeErrorBody
    @POST("audio-transcriptions")
    suspend fun postAudioTranscriptions(
        @Body body: RequestBody? = null,
        @Header("Accept-Language") acceptLanguage: String = Locale.getDefault().toLanguageTag(),
        @Header("Content-Type") contentType: String? = AUDIO_MP4,
        @Query("personalityId") personalityId: String? = null,
        @Query("forwarded") isForwarded: Boolean?
    ): ApiResult<ContentTextDto, ErrorDto>

    @DecodeErrorBody
    @GET("dynamic-tools/{toolId}")
    suspend fun getDynamicTools(
        @Path("toolId") toolId: String
    ): ApiResult<DynamicToolDto, ErrorDto>

    @DecodeErrorBody
    @POST("dynamic-tools/{toolId}/process")
    suspend fun getDynamicToolResult(
        @Path("toolId") toolId: String,
        @Body body: MultipartBody
    ): ApiResult<DynamicToolDto, ErrorDto>

    @DecodeErrorBody
    @POST("attachments/upload")
    suspend fun sendAttachedFiles(@Body body: MultipartBody): ApiResult<ContentTextDto, ErrorDto>

    @DecodeErrorBody
    @POST("tools/documents/chat")
    suspend fun sendChatWithDocuments(@Body body: CompletionsRequest): ApiResult<ContentTextDto, ErrorDto>

    @DecodeErrorBody
    @GET("tools/documents")
    suspend fun getDocumentsDetails(): ApiResult<ToolDetailDto, ErrorDto>

    companion object {
        const val AUDIO_MP4 = "audio/mp4"
    }
}
