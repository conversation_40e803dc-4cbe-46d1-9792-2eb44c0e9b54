package co.thewordlab.luzia.core.tools.data.repository

import android.util.Log
import androidx.datastore.preferences.core.booleanPreferencesKey
import co.thewordlab.fouundation.persistence.LuziaDataStore
import co.thewordlab.fouundation.persistence.tool.ToolDao
import co.thewordlab.fouundation.persistence.tool.ToolEntity
import co.thewordlab.fouundation.persistence.tool.Upcoming
import co.thewordlab.luzia.core.tools.data.api.ToolsApi
import co.thewordlab.luzia.core.tools.data.api.ToolsApi.Companion.AUDIO_MP4
import co.thewordlab.luzia.core.tools.data.models.DynamicToolConfigDto
import co.thewordlab.luzia.core.tools.data.models.ToolDTO
import co.thewordlab.luzia.core.tools.domain.mappers.mapToDomain
import co.thewordlab.luzia.core.tools.domain.mappers.mapToDto
import co.thewordlab.luzia.core.tools.domain.model.ContentText
import co.thewordlab.luzia.core.tools.domain.model.DynamicTool
import co.thewordlab.luzia.core.tools.domain.model.DynamicToolConfig
import co.thewordlab.luzia.core.tools.domain.model.ToolSupportedFile
import co.thewordlab.luzia.core.tools.domain.model.ToolsErrors
import co.thewordlab.luzia.core.tools.domain.repository.ToolsRepository
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.foundation.networking.model.CommonErrors
import co.thewordlab.luzia.foundation.networking.model.ErrorDto
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.asFailure
import co.thewordlab.luzia.foundation.networking.model.asResult
import co.thewordlab.luzia.foundation.networking.model.map
import co.theworldlab.luzia.foundation.data.cache.store.disk.DiskCache
import co.theworldlab.luzia.foundation.data.cache.store.memory.inMemoryStore
import com.slack.eithernet.ApiResult
import com.squareup.moshi.Moshi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import javax.inject.Inject
import kotlin.time.Duration.Companion.minutes

class ToolsRepositoryImpl @Inject constructor(
    private val toolsApi: ToolsApi,
    private val toolDao: ToolDao,
    private val luziaDataStore: LuziaDataStore,
    private val moshi: Moshi,
    private val featureFlagManager: FeatureFlagManager
) : ToolsRepository {

    private val store = inMemoryStore(
        fetcher = {
            val sortingEnabled = featureFlagManager.get<Boolean>(FeatureFlag.ToolsCharactersSortingEnabled)
            val result = toolsApi.getTools(
                onboarding = sortingEnabled
            )
            if (result is ApiResult.Success) {
                val featuredId = result.value.popular.firstOrNull()?.id
                val remoteTools = result.value.popular + result.value.tools
                remoteTools.mapIndexed { index, item ->
                    item.asEntity(
                        index = index,
                        isPopular = item.upcoming == null || item.id == featuredId,
                        featuredId = featuredId
                    )
                }
            } else {
                emptyList()
            }
        },
        diskCache = object : DiskCache<List<ToolEntity>> {
            override fun getFromCache(): Flow<List<ToolEntity>> {
                return toolDao.getAllTools()
            }

            override suspend fun saveIntoCache(item: List<ToolEntity>) {
                if (item.isNotEmpty()) {
                    toolDao.removeAll()
                    toolDao.insertTools(item)
                }
            }
        }
    ).withExpiration(TOOLS_CACHE_EXPIRY).build()

    override fun getAvailableTools(): Flow<List<ToolEntity>> = toolDao.getAllTools()
        .map { tools -> tools.take(MAX_AVAILABLE_TOOLS) }

    override fun getTools() = store.flow().mapNotNull { it }

    override suspend fun getDynamicTool(toolId: String): ResultOf<DynamicTool, ToolsErrors> =
        toolsApi.getDynamicTools(toolId).asResult { error ->
            error.mapToToolsError().asFailure()
        }.map { mapToDomain() }

    override suspend fun getDynamicToolResult(
        toolId: String,
        file: File?,
        fileInfo: ToolSupportedFile,
        config: List<DynamicToolConfig>
    ): ResultOf<DynamicTool, ToolsErrors> {
        val multipartBody = MultipartBody.Builder().setType(MultipartBody.FORM)
        file?.let {
            multipartBody.addFormDataPart(
                name = "file",
                filename = fileInfo.fileName,
                body = it.asRequestBody(fileInfo.mimeType.toMediaTypeOrNull())
            )
        }
        val jsonPayload = moshi.adapter(DynamicToolConfigDto::class.java).toJson(config.mapToDto())
        multipartBody.addFormDataPart(
            name = "payload",
            value = jsonPayload
        )
        return toolsApi.getDynamicToolResult(toolId, multipartBody.build()).asResult { error ->
            error.mapToToolsError().asFailure()
        }.map { mapToDomain() }
    }

    override suspend fun shouldShowBestiePointsBanner(): Boolean =
        luziaDataStore.getData(bestiePointsBannerShown)?.not() != false

    override suspend fun markBestiePointsBannerAsShown() {
        luziaDataStore.saveData(bestiePointsBannerShown, true)
    }

    override suspend fun sendAudioRecorder(
        audio: File,
        personalityId: String?,
        isForwarded: Boolean?
    ): ResultOf<ContentText, ToolsErrors> =
        toolsApi.postAudioTranscriptions(
            body = audio.asRequestBody(AUDIO_MP4.toMediaTypeOrNull()),
            personalityId = personalityId,
            isForwarded = isForwarded
        ).asResult { error ->
            val toolsError = error.mapToToolsError()
            toolsError.asFailure()
        }.map { ContentText(content) }

    private fun ApiResult.Failure<*>.mapToToolsError(): ToolsErrors =
        when (this) {
            is ApiResult.Failure.NetworkFailure -> ToolsErrors.CommonError(CommonErrors.NetworkError)
            is ApiResult.Failure.HttpFailure -> {
                try {
                    val errorDto = error as ErrorDto
                    ToolsErrors.DynamicToolError(errorDto.error.message)
                } catch (ex: ClassCastException) {
                    Log.d("LuziaApp", "Parsing error class exception: ", ex)
                    ToolsErrors.CommonError(CommonErrors.UnspecifiedError)
                }
            }

            else -> ToolsErrors.CommonError(CommonErrors.UnspecifiedError)
        }

    private fun ToolDTO.asEntity(index: Int, isPopular: Boolean, featuredId: String?) = ToolEntity(
        id = id,
        name = name,
        isFeatured = featuredId == id,
        isPopular = isPopular,
        order = index,
        description = description,
        icon = icon,
        iconBackgroundColor = iconBackgroundColor,
        image = image,
        upcoming = upcoming?.let {
            Upcoming(
                name = it.name,
                description = it.description
            )
        },
        isDynamic = isDynamic == true
    )

    private companion object {
        const val MAX_AVAILABLE_TOOLS = 7
        const val KEY_BESTIE_POINTS_BANNER_SHOWN = "bestiePointsBannerShownInTools"
        val bestiePointsBannerShown = booleanPreferencesKey(KEY_BESTIE_POINTS_BANNER_SHOWN)
        val TOOLS_CACHE_EXPIRY = 5.minutes
    }
}
