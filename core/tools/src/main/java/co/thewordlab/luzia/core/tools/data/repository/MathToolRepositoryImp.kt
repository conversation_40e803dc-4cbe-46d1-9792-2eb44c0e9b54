package co.thewordlab.luzia.core.tools.data.repository

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import co.thewordlab.fouundation.persistence.LuziaDataStore
import co.thewordlab.fouundation.persistence.chat.ChatDataSource
import co.thewordlab.fouundation.persistence.chat.ChatMetadata
import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.fouundation.persistence.chat.MessageType
import co.thewordlab.luzia.core.feedback.presentation.FeedbackDisplayed
import co.thewordlab.luzia.core.tools.domain.repository.MathToolRepository
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.messages.data.MediaFileProvider
import co.thewordlab.luzia.foundation.messages.data.PrrManager
import co.thewordlab.luzia.foundation.messages.data.api.ChatApi
import co.thewordlab.luzia.foundation.messages.data.api.Constants
import co.thewordlab.luzia.foundation.messages.data.api.MathApi
import co.thewordlab.luzia.foundation.messages.data.model.CompletionContextMessage
import co.thewordlab.luzia.foundation.messages.data.model.CompletionsRequest
import co.thewordlab.luzia.foundation.messages.data.model.ContentMetadataDto
import co.thewordlab.luzia.foundation.messages.data.model.ContentTextDto
import co.thewordlab.luzia.foundation.messages.data.model.MathImageDto
import co.thewordlab.luzia.foundation.messages.data.model.ToolDetailDto
import co.thewordlab.luzia.foundation.messages.data.model.asAiMessage
import co.thewordlab.luzia.foundation.messages.data.model.asFailed
import co.thewordlab.luzia.foundation.messages.data.model.asLoading
import co.thewordlab.luzia.foundation.messages.data.model.asSuccess
import co.thewordlab.luzia.foundation.messages.data.model.asUserMessage
import co.thewordlab.luzia.foundation.messages.di.InMemory
import co.thewordlab.luzia.foundation.messages.domain.buildContextMessagesFromChatHistory
import co.thewordlab.luzia.foundation.messages.domain.repository.ProactiveMessageHandler
import co.thewordlab.luzia.foundation.networking.model.AppErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.asResult
import co.thewordlab.luzia.foundation.networking.model.mapTo
import kotlinx.coroutines.flow.Flow
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File

class MathToolRepositoryImp(
    @InMemory private val datasource: ChatDataSource,
    luziaDataStore: LuziaDataStore,
    private val mathApi: MathApi,
    private val chatApi: ChatApi,
    private val mediaFileProvider: MediaFileProvider,
    private val analytics: Analytics,
    private val proactiveMessageHandler: ProactiveMessageHandler,
) : MathToolRepository {
    private var imageResult: MathImageDto? = null
    private val prrManager: PrrManager = PrrManager(datasource, luziaDataStore)

    override fun getMessages(): Flow<PagingData<MessageEntity>> {
        val pager =
            Pager(config = PagingConfig(pageSize = 15)) {
                datasource.getMessagesPaginated(
                    LUZIA_ID_PERSONALITY
                )
            }
        return pager.flow
    }

    override fun getChatMetadata(): Flow<ChatMetadata?> {
        return datasource.getChatMetadata(LUZIA_ID_PERSONALITY)
    }

    override suspend fun resendMessage(message: MessageEntity): ResultOf<ContentTextDto, AppErrors> {
        return when (message.messageType) {
            MessageType.Text -> chatCompletionInternal(message)
            MessageType.Image -> {
                when (val fileResult = mediaFileProvider.provideFile(message.fileName)) {
                    is ResultOf.Failure -> ResultOf.Failure(fileResult.error)
                    is ResultOf.Success -> {
                        sendImageInternal(message, fileResult.data).mapTo {
                            ContentTextDto(
                                content = content,
                                metadata = ContentMetadataDto("")
                            )
                        }
                    }
                }
            }

            MessageType.AudioRecord -> {
                when (val fileResult = mediaFileProvider.provideFile(message.fileName)) {
                    is ResultOf.Failure -> ResultOf.Failure(fileResult.error)
                    is ResultOf.Success -> {
                        sendAudioRecordingInternal(message, fileResult.data)
                    }
                }
            }

            else -> error("This message type is not supported for resending.")
        }
    }

    override suspend fun sendImage(file: File, text: String): ResultOf<MathImageDto, AppErrors> {
        val message =
            text.asUserMessage(
                personalityId = LUZIA_ID_PERSONALITY,
                type = MessageType.Image,
                file = file
            )
        return sendImageInternal(message, file)
    }

    override suspend fun chatCompletion(text: String): ResultOf<ContentTextDto, AppErrors> {
        val message = text.asUserMessage(LUZIA_ID_PERSONALITY, MessageType.Text)
        return chatCompletionInternal(message)
    }

    override suspend fun sendAudioRecording(file: File): ResultOf<ContentTextDto, AppErrors> {
        val message =
            "".asUserMessage(
                personalityId = LUZIA_ID_PERSONALITY,
                type = MessageType.AudioRecord,
                file = file
            )
        return sendAudioRecordingInternal(message, file)
    }

    override suspend fun getIceBreakers(): ResultOf<ToolDetailDto, AppErrors> {
        return mathApi.getDetails().asResult()
    }

    private suspend fun sendImageInternal(
        message: MessageEntity,
        file: File,
    ): ResultOf<MathImageDto, AppErrors> {
        var userMessage = message.asLoading()
        val id = datasource.insertMessage(userMessage)
        val multipartBody =
            MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("content", message.text)
                .addFormDataPart(
                    name = "image",
                    filename = file.name,
                    body = file.asRequestBody("image/jpg".toMediaTypeOrNull())
                )
                .build()
        return when (val result = mathApi.uploadFile(multipartBody).asResult()) {
            is ResultOf.Failure -> {
                datasource.insertMessage(userMessage.asFailed(id, result.error))
                result
            }

            is ResultOf.Success -> {
                imageResult = result.data
                val aiMessage = result.data.content.asAiMessage(LUZIA_ID_PERSONALITY)
                userMessage = userMessage.asSuccess(id)
                val enrichedMessage = prrManager.enrichWithFeedback(
                    userMessage = userMessage,
                    responseMessage = aiMessage,
                    metadata = result.data.metadata
                )
                datasource.insertMessage(userMessage)
                datasource.insertMessage(
                    enrichMessageWithMetadata(
                        enrichedMessage,
                        result.data.metadata
                    )
                )
                trackEnrichedFeedback(enrichedMessage)
                result
            }
        }
    }

    private fun enrichMessageWithMetadata(
        message: MessageEntity,
        metadata: ContentMetadataDto,
    ): MessageEntity =
        message.copy(requestId = metadata.requestId)

    private suspend fun chatCompletionInternal(message: MessageEntity): ResultOf<ContentTextDto, AppErrors> {
        var userMessage = message.asLoading()
        val id = datasource.insertMessage(userMessage)
        val request =
            CompletionsRequest(userMessage.text, LUZIA_ID_PERSONALITY, generateContextMessages())
        val result = mathApi.chat(request).asResult()
        when (result) {
            is ResultOf.Failure -> {
                userMessage = userMessage.asFailed(id, result.error)
                datasource.insertMessage(userMessage)
            }

            is ResultOf.Success -> {
                userMessage = userMessage.asSuccess(id)
                val responseMessage =
                    result.data.content.asAiMessage(userMessage.personalityId, MessageType.Text)
                val enrichedMessage =
                    prrManager.enrichWithFeedback(
                        userMessage,
                        responseMessage,
                        result.data.metadata
                    )
                datasource.insertMessage(userMessage)
                datasource.insertMessage(
                    enrichMessageWithMetadata(
                        enrichedMessage,
                        result.data.metadata
                    )
                )
                trackEnrichedFeedback(enrichedMessage)
            }
        }
        return result
    }

    private fun trackEnrichedFeedback(message: MessageEntity) {
        if (!message.feedbackId.isNullOrEmpty()) {
            analytics.logEvent(FeedbackDisplayed)
        }
    }

    private suspend fun sendAudioRecordingInternal(
        message: MessageEntity,
        file: File,
    ): ResultOf<ContentTextDto, AppErrors> {
        val userMessage = message.asLoading()
        val id = datasource.insertMessage(userMessage)
        val transcribeResult =
            chatApi.postAudioTranscriptions(
                body = file.asRequestBody(Constants.AUDIO_MP4.toMediaTypeOrNull()),
                personalityId = message.personalityId,
                isForwarded = null
            ).asResult()
        return when (transcribeResult) {
            is ResultOf.Failure -> {
                datasource.insertMessage(userMessage.asFailed(id, transcribeResult.error))
                transcribeResult
            }

            is ResultOf.Success -> {
                chatCompletionInternal(
                    userMessage.asSuccess(id).copy(text = transcribeResult.data.content)
                )
            }
        }
    }

    private suspend fun generateContextMessages(): List<CompletionContextMessage> {
        val list =
            datasource
                .getLastMessagesWithoutError(
                    personalityId = LUZIA_ID_PERSONALITY,
                    takeLast = LIMIT_CONTEXT_MESSAGES
                )
                .reversed()
                .drop(if (imageResult != null) 2 else 0)
                .toMutableList()
        val contextMessages = mutableListOf<CompletionContextMessage>()
        attachImageResultToContext(contextMessages)
        return contextMessages.buildContextMessagesFromChatHistory(list, proactiveMessageHandler)
    }

    private fun attachImageResultToContext(contextMessages: MutableList<CompletionContextMessage>) {
        imageResult?.let {
            contextMessages.add(
                CompletionContextMessage(
                    prompt = it.imageDescription,
                    completion = it.content
                )
            )
        }
    }

    override suspend fun clearMessages() {
        imageResult = null
        datasource.clearMessages(LUZIA_ID_PERSONALITY)
    }

    override suspend fun dismissFeedback(feedbackId: String) {
        prrManager.deletePrrFeedbacks(feedbackId)
    }

    private companion object {
        const val LIMIT_CONTEXT_MESSAGES = 1000
        const val LUZIA_ID_PERSONALITY = "LuzIA"
    }
}
