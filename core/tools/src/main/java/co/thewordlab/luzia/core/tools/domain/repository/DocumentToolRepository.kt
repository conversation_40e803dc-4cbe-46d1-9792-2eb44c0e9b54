package co.thewordlab.luzia.core.tools.domain.repository

import androidx.paging.PagingData
import co.thewordlab.fouundation.persistence.chat.ChatMetadata
import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.luzia.foundation.messages.data.model.ContentTextDto
import co.thewordlab.luzia.foundation.messages.data.model.ToolDetailDto
import co.thewordlab.luzia.foundation.networking.model.AppErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import kotlinx.coroutines.flow.Flow
import java.io.File

interface DocumentToolRepository {

    suspend fun getMessages(personalityId: String): Flow<PagingData<MessageEntity>>
    suspend fun resendMessage(
        attachedFileId: String?,
        message: MessageEntity
    ): ResultOf<ContentTextDto, AppErrors>

    suspend fun sendFileForDocumentTool(
        personalityId: String,
        file: File,
        insertDocumentInChatAfterNextMessage: Boolean
    ): ResultOf<ContentTextDto, AppErrors>

    suspend fun sendQuestionForDocumentTool(
        personalityId: String,
        attachedFileId: String?,
        text: String,
    ): ResultOf<ContentTextDto, AppErrors>

    suspend fun sendAudioRecordingForDocument(
        personalityId: String,
        attachedFileId: String?,
        file: File,
    ): ResultOf<ContentTextDto, AppErrors>

    suspend fun getIceBreakers(): ResultOf<ToolDetailDto, AppErrors>
    suspend fun dismissFeedback(feedbackId: String)
    fun setContextWindowSize(numberOfMessages: Int)
    fun clearDocuments()
    fun getChatMetadata(personalityId: String): Flow<ChatMetadata?>
}
