package co.thewordlab.luzia.core.tools.domain.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = false)
enum class ToolSupportedFile(
    val type: String,
    val fileName: String,
    val mimeType: String,
    val extension: String
) {
    @Json(name = "photo")
    PHOTO("image", "test.jpeg", "image/jpg", ".jpeg"),

    @Json(name = "pdf")
    PDF("file", "test.pdf", "application/pdf", ".pdf"),

    @<PERSON>son(name = "none")
    NONE("", "", "", "")
}
