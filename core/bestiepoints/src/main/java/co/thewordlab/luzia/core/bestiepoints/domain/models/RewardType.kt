package co.thewordlab.luzia.core.bestiepoints.domain.models

enum class RewardType(val value: String) {
    DAILY_USAGE("daily-usage-app"),
    DAY_3("day-streak-3"),
    DAY_7("day-streak-7"),
    DAY_14("day-streak-14"),
    DAY_30("day-streak-30"),
    NOT_SET("not-set");

    companion object {
        fun fromValue(value: String): RewardType = when {
            value.contains(DAILY_USAGE.value) -> DAILY_USAGE
            value.contains(DAY_3.value) -> DAY_3
            value.contains(DAY_7.value) -> DAY_7
            value.contains(DAY_14.value) -> DAY_14
            value.contains(DAY_30.value) -> DAY_30
            else -> NOT_SET
        }

        fun getDayStreakRewards(): List<RewardType> = listOf(DAY_3, DAY_7, DAY_14, DAY_30)
    }
}
