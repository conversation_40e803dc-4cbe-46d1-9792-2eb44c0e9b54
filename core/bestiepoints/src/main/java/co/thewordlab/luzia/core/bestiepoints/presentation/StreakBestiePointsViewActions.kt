package co.thewordlab.luzia.core.bestiepoints.presentation

import co.thewordlab.luzia.core.bestiepoints.presentation.components.BestiePopupPage
import co.thewordlab.luzia.core.bestiepoints.presentation.models.StreaksBpSource
import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class StreakBestiePointsViewActions : ViewAction {
    data object OnStart : StreakBestiePointsViewActions()
    data object OnScreenView : StreakBestiePointsViewActions()
    data object OnScreenClose : StreakBestiePointsViewActions()
    data object OnBackClicked : StreakBestiePointsViewActions()
    data class OpenBestiePoints(val bpSource: StreaksBpSource) : StreakBestiePointsViewActions()
    data class OnBannerInfoClicked(val page: BestiePopupPage) : StreakBestiePointsViewActions()
}
