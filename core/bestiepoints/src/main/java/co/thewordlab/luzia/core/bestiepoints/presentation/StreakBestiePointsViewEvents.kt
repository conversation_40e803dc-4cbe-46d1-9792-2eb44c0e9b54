package co.thewordlab.luzia.core.bestiepoints.presentation

import co.thewordlab.luzia.core.bestiepoints.presentation.components.BestiePopupPage
import co.thewordlab.luzia.core.bestiepoints.presentation.models.RewardUiModel
import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class StreakBestiePointsViewEvents : ViewEvent {
    data class ShowInfo(val page: BestiePopupPage) : StreakBestiePointsViewEvents()
    data class ShowReward(val reward: RewardUiModel) : StreakBestiePointsViewEvents()
    data object OpenBestiePointsScreen : StreakBestiePointsViewEvents()
    data object DoBack : StreakBestiePointsViewEvents()
    data object NavigateToSignUp : StreakBestiePointsViewEvents()
}
