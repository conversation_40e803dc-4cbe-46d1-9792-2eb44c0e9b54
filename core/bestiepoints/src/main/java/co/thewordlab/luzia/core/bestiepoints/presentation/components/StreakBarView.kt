package co.thewordlab.luzia.core.bestiepoints.presentation.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR

private val INDICATOR_SIZE = 24.dp
private val BAR_HEIGHT = 10.dp

@Composable
fun StreakBarView(modifier: Modifier, model: StreakBarUiModel) {
    val maxStreak = model.items.maxOfOrNull { it.streak } ?: 0
    BoxWithConstraints(modifier, contentAlignment = Alignment.BottomStart) {
        BarView(
            modifier = Modifier.padding(bottom = (INDICATOR_SIZE / 2) - BAR_HEIGHT / 2),
            percentageReached = calculatePercentageReached(maxStreak, model)
        )
        model.items.forEach { item ->
            StreakIndicator(
                modifier = Modifier.padding(
                    start = Dp(
                        calculateIndicatorPosition(
                            maxStreak,
                            item,
                            maxWidth.value
                        )
                    )
                ),
                item.streak,
                item.reached
            )
        }
    }
}

private fun calculateIndicatorPosition(
    maxStreak: Int,
    model: StreakBarItem,
    viewWidth: Float
): Float =
    if (model.streak == maxStreak) {
        viewWidth - INDICATOR_SIZE.value
    } else {
        ((model.streak * viewWidth) / maxStreak)
    }

private fun calculatePercentageReached(maxStreak: Int, model: StreakBarUiModel): Float = when {
    maxStreak == 0 -> 0f
    else -> model.currentStreak / maxStreak.toFloat()
}

@Composable
private fun BarView(modifier: Modifier, percentageReached: Float) {
    LinearProgressIndicator(
        progress = { percentageReached },
        modifier = modifier
            .fillMaxWidth()
            .height(BAR_HEIGHT),
        color = LuziaTheme.palette.accents.yellow.yellow30,
        trackColor = LuziaTheme.palette.primitives.neutral.neutral10,
        strokeCap = StrokeCap.Round
    )
}

@Composable
private fun StreakIndicator(modifier: Modifier, value: Int, reached: Boolean) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            painter = painterResource(id = designR.drawable.ic_flag),
            contentDescription = null,
            tint = LuziaTheme.palette.accents.yellow.yellow30
        )
        Spacer(modifier = Modifier.height(Spacing.X2.dp))
        StreakNumberIndicator(value, reached)
    }
}

@Composable
private fun StreakNumberIndicator(value: Int, reached: Boolean) {
    val modifier = if (reached) {
        Modifier
            .size(INDICATOR_SIZE)
            .background(color = LuziaTheme.palette.accents.yellow.yellow30, shape = CircleShape)
    } else {
        Modifier
            .size(INDICATOR_SIZE)
            .background(
                color = if (LuziaTheme.isDarkTheme) Color.Black else Color.White,
                shape = CircleShape
            )
            .border(
                width = 1.dp,
                color = LuziaTheme.palette.primitives.neutral.neutral10,
                shape = CircleShape
            )
    }

    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        LuziaText(
            text = value.toString(),
            style = LuziaTheme.typography.body.semiBold.footnote,
            color = getStreakNumberTextColor(reached)
        )
    }
}

@Composable
private fun getStreakNumberTextColor(reached: Boolean) =
    if (reached) {
        Color.Black
    } else {
        if (LuziaTheme.isDarkTheme) Color.White else Color.Black
    }

@PreviewLightDark
@Composable
private fun BarViewPreview() {
    BarView(modifier = Modifier, percentageReached = 0.5f)
}

@PreviewLightDark
@Composable
private fun StreakIndicatorPreviewCase1() {
    StreakIndicator(modifier = Modifier, value = 3, reached = true)
}

@PreviewLightDark
@Composable
private fun StreakNumberIndicatorPreview() {
    StreakNumberIndicator(value = 1, reached = false)
}

@PreviewLightDark
@Composable
private fun StreakNumberIndicatorPreviewCase2() {
    StreakNumberIndicator(value = 30, reached = true)
}

@PreviewLightDark
@Composable
private fun StreakBarViewPreview() {
    LuziaTheme {
        StreakBarView(
            modifier = Modifier,
            model = StreakBarUiModel(
                currentStreak = 3,
                items = listOf(
                    StreakBarItem(3, true),
                    StreakBarItem(7, false),
                    StreakBarItem(14, false),
                    StreakBarItem(30, false)
                )
            )
        )
    }
}
