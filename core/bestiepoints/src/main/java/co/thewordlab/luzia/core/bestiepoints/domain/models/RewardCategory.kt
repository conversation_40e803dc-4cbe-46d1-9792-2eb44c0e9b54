package co.thewordlab.luzia.core.bestiepoints.domain.models

enum class RewardCategory(val value: String) {
    DAILY_USAGE("daily-usage"),
    DAY_STREAK("day-streak"),
    NOT_SET("not-set");

    companion object {
        fun fromValue(value: String): RewardCategory = when (value) {
            DAILY_USAGE.value -> DAILY_USAGE
            DAY_STREAK.value -> DAY_STREAK
            else -> NOT_SET
        }
    }
}
