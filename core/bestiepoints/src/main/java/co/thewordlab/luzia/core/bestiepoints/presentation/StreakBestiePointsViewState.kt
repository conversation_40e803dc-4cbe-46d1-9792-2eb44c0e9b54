package co.thewordlab.luzia.core.bestiepoints.presentation

import co.thewordlab.luzia.core.bestiepoints.presentation.models.RewardUiModel
import co.thewordlab.luzia.core.bestiepoints.presentation.models.StreakChallengeUiModel
import co.thewordlab.luzia.foundation.architecture.system.ViewState

data class StreakBestiePointsViewState(
    val isFullUser: Boolean = false,
    val streaks: Int = 0,
    val points: Int = 0,
    val challenges: StreakChallengeUiModel = StreakChallengeUiModel(),
    val newRewards: List<RewardUiModel> = emptyList(),
    val rewards: List<RewardUiModel> = emptyList()
) : ViewState
