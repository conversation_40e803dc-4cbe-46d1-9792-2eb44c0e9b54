package co.thewordlab.luzia.core.bestiepoints.data

import co.thewordlab.luzia.core.bestiepoints.data.models.RewardsDTO
import co.thewordlab.luzia.core.bestiepoints.domain.BestiePointsErrors
import co.thewordlab.luzia.core.bestiepoints.domain.BestiePointsRepository
import co.thewordlab.luzia.core.bestiepoints.domain.models.RewardCategory
import co.thewordlab.luzia.core.bestiepoints.domain.models.RewardStatus
import co.thewordlab.luzia.core.bestiepoints.domain.models.RewardType
import co.thewordlab.luzia.core.bestiepoints.domain.models.UserRewards
import co.thewordlab.luzia.core.bestiepoints.domain.models.UserScore
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.networking.model.AppErrors
import co.thewordlab.luzia.foundation.networking.model.CommonErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.asFailure
import co.thewordlab.luzia.foundation.networking.model.asResult
import co.thewordlab.luzia.foundation.networking.model.asSuccess
import javax.inject.Inject

class BestiePointsRepositoryImp @Inject constructor(
    private val apiDataSource: BestiePointsApi,
    private val analytics: Analytics
) : BestiePointsRepository {

    override suspend fun getUserScoreAndRewards(): ResultOf<UserScore, BestiePointsErrors> =
        when (val result = apiDataSource.getUserScoreAndRewards().asResult()) {
            is ResultOf.Failure -> {
                val mappedError = result.error.mapToBestiePointsError()
                analytics.reportException("Requesting user bestie points failed: ${mappedError.description}")
                mappedError.asFailure()
            }

            is ResultOf.Success -> {
                with(result.data) {
                    UserScore(
                        points = points,
                        streak = streak,
                        rewards = rewards.mapToDomain()
                    ).asSuccess()
                }
            }
        }

    override suspend fun getUserAlreadyClaimedRewards(): ResultOf<List<UserRewards>, BestiePointsErrors> =
        when (val result = apiDataSource.getUserAlreadyClaimedRewards().asResult()) {
            is ResultOf.Failure -> {
                val mappedError = result.error.mapToBestiePointsError()
                analytics.reportException("Requesting claimed rewards failed: ${mappedError.description}")
                mappedError.asFailure()
            }

            is ResultOf.Success -> result.data.rewards.mapToDomain().asSuccess()
        }

    private fun List<RewardsDTO>?.mapToDomain(): List<UserRewards> = this?.map {
        UserRewards(
            id = it.id,
            type = RewardType.fromValue(it.type),
            category = RewardCategory.fromValue(it.category),
            points = it.points,
            status = RewardStatus.fromValue(it.status),
            title = it.title,
            message = it.message
        )
    } ?: emptyList()

    private fun AppErrors.mapToBestiePointsError(): BestiePointsErrors = when (this) {
        AppErrors.NoNetwork -> BestiePointsErrors.CommonError(CommonErrors.NetworkError)
        else -> BestiePointsErrors.UserScoreCannotBeRetrieved
    }
}
