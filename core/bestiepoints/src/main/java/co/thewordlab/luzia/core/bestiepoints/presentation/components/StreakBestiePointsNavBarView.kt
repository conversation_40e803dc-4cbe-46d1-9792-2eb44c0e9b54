package co.thewordlab.luzia.core.bestiepoints.presentation.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.sp
import co.thewordlab.luzia.core.bestiepoints.presentation.models.StreakBestiePointsNavBarUiModel
import co.thewordlab.luzia.core.bestiepoints.presentation.models.StreakBestiePointsViewType
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

private val FONT_SIZE_NAV_BAR = 30.sp
private val LINE_HEIGHT_NAV_BAR = 28.sp

@Composable
fun StreakBestiePointsNavBarView(
    modifier: Modifier = Modifier,
    iconModifier: Modifier = Modifier,
    state: StreakBestiePointsNavBarUiModel,
) {
    Row(
        modifier = modifier
            .statusBarsPadding()
            .fillMaxWidth()
            .padding(Spacing.X16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = state.title,
            color = LuziaTheme.palette.text.primary,
            style = LuziaTheme.typography.headlines.h2.copy(
                fontSize = FONT_SIZE_NAV_BAR,
                lineHeight = LINE_HEIGHT_NAV_BAR
            )
        )
        Spacer(Modifier.weight(1f))
        StreakBestiePointsView(
            modifier = Modifier
                .wrapContentWidth()
                .clip(RoundedCornerShape(Corners.X4.dp))
                .click { state.bestiePointsClicked() },
            type = StreakBestiePointsViewType.Compact,
            onNavigateToBestiePoints = state.bestiePointsClicked,
            onNavigateToSignup = state.onNavigateToSignup,
            source = state.source
        )
        AnimatedVisibility(state.endIcon != null) {
            Row {
                Spacer(Modifier.width(Spacing.X16.dp))
                state.endIcon?.let { icon ->
                    if (state.endIconLoading) {
                        CircularProgressIndicator(modifier = iconModifier)
                    } else {
                        IconButton(
                            modifier = iconModifier,
                            onClick = state.endIconClicked ?: {}
                        ) {
                            Icon(
                                painter = painterResource(id = icon),
                                tint = LuziaTheme.palette.text.primary,
                                contentDescription = null
                            )
                        }
                    }
                }
            }
        }
    }
}
