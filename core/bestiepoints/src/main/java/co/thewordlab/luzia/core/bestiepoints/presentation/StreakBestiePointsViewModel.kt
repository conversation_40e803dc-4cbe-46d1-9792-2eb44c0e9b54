package co.thewordlab.luzia.core.bestiepoints.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.bestiepoints.domain.BestiePointsRepository
import co.thewordlab.luzia.core.bestiepoints.domain.models.RewardCategory
import co.thewordlab.luzia.core.bestiepoints.domain.models.UserScore
import co.thewordlab.luzia.core.bestiepoints.presentation.components.BestiePopupPage
import co.thewordlab.luzia.core.bestiepoints.presentation.models.RewardUiModel
import co.thewordlab.luzia.core.bestiepoints.presentation.models.RewardsMapper
import co.thewordlab.luzia.core.bestiepoints.presentation.models.StreakAchievedUiModel
import co.thewordlab.luzia.core.bestiepoints.presentation.models.StreakChallengeUiModel
import co.thewordlab.luzia.core.profile.domain.GetUserProfileUseCase
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.getDataOrNull
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import co.thewordlab.luzia.foundation.networking.session.UserType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class StreakBestiePointsViewModel @Inject constructor(
    private val getUserProfileUseCase: GetUserProfileUseCase,
    private val bestiePointsRepository: BestiePointsRepository,
    private val userSessionManager: UserSessionManager,
    private val rewardsMapper: RewardsMapper,
    private val analytics: Analytics
) :
    ViewModel(),
    ViewModelStates<StreakBestiePointsViewState> by ViewModelStatesImpl(StreakBestiePointsViewState()),
    ViewModelEvents<StreakBestiePointsViewEvents> by ViewModelEventsImpl(),
    ViewModelActions<StreakBestiePointsViewActions> {

    override fun onViewAction(action: StreakBestiePointsViewActions) {
        when (action) {
            StreakBestiePointsViewActions.OnStart -> fetchBestiePointsAndStreaks()
            is StreakBestiePointsViewActions.OnBannerInfoClicked -> {
                sendEventForInfoBannerClicked(action.page)
                sendEvent(StreakBestiePointsViewEvents.ShowInfo(action.page))
            }

            is StreakBestiePointsViewActions.OpenBestiePoints -> attemptOpenBestiePoints(action)

            StreakBestiePointsViewActions.OnBackClicked ->
                sendEvent(StreakBestiePointsViewEvents.DoBack)

            StreakBestiePointsViewActions.OnScreenView -> analytics.logScreen(BestiePointsScreen)

            StreakBestiePointsViewActions.OnScreenClose -> analytics.logAction(
                CloseBestiePointsScreen
            )
        }
    }

    private fun attemptOpenBestiePoints(
        action: StreakBestiePointsViewActions.OpenBestiePoints
    ) = viewModelScope.launch {
        val email = getUserProfileUseCase().firstOrNull()?.email
        if (email.isNullOrEmpty()) {
            analytics.logAction(OpenSignUpFromBanner)
            sendEvent(StreakBestiePointsViewEvents.NavigateToSignUp)
        } else {
            val props = mapOf(Parameter.Source to action.bpSource.source)
            analytics.logAction(OpenBestiePointsScreen, props)
            sendEvent(StreakBestiePointsViewEvents.OpenBestiePointsScreen)
        }
    }

    private fun sendEventForInfoBannerClicked(page: BestiePopupPage) = when (page) {
        BestiePopupPage.BESTIE_POINTS -> analytics.logAction(OpenInfoForBestiePoints)
        BestiePopupPage.STREAKS -> analytics.logAction(OpenInfoForStreaks)
    }

    private fun fetchBestiePointsAndStreaks() = viewModelScope.launch {
        userSessionManager.userSession
            .map { it?.userType == UserType.FULL_USER }
            .collect { isFullUser ->
                if (!isFullUser) {
                    updateState {
                        it.copy(
                            streaks = 0,
                            points = 0,
                            isFullUser = false,
                            challenges = buildStreakChallengeUiModel(0),
                            newRewards = emptyList()
                        )
                    }
                } else {
                    val result = bestiePointsRepository.getUserScoreAndRewards()
                    val claimedRewards =
                        bestiePointsRepository.getUserAlreadyClaimedRewards().getDataOrNull()
                    if (result is ResultOf.Success) {
                        updateState {
                            val newRewards = result.data.rewards
                                .filter { reward -> reward.category == RewardCategory.DAY_STREAK }
                                .map { reward ->
                                    RewardUiModel(
                                        id = reward.id,
                                        title = reward.title,
                                        desc = reward.message,
                                        icon = rewardsMapper.getRewardIcon(reward.type, true),
                                        achieved = true
                                    )
                                }
                            if (it.newRewards != newRewards && newRewards.isNotEmpty()) {
                                val reward = newRewards.last()
                                analytics.logScreen(
                                    BestiePointsRewardScreen,
                                    mapOf(Parameter.Streak to result.data.streak)
                                )
                                sendEvent(StreakBestiePointsViewEvents.ShowReward(reward))
                            }
                            updateUserProperties(result.data)
                            it.copy(
                                streaks = result.data.streak,
                                points = result.data.points,
                                isFullUser = true,
                                challenges = buildStreakChallengeUiModel(result.data.streak),
                                newRewards = newRewards,
                                rewards = rewardsMapper.buildRewardsList(claimedRewards)
                            )
                        }
                    }
                }
            }
    }

    private fun updateUserProperties(data: UserScore) {
        analytics.setUserProperties(
            mapOf(
                Parameter.UserStreak to data.streak,
                Parameter.UserBestiePoints to data.points
            )
        )
    }

    private fun buildStreakChallengeUiModel(streak: Int): StreakChallengeUiModel {
        val currentChallenge = when (streak) {
            in STREAK_3_LOWER_RANGE..STREAK_3 -> STREAK_3
            in STREAK_7_LOWER_RANGE..STREAK_7 -> STREAK_7
            in STREAK_14_LOWER_RANGE..STREAK_14 -> STREAK_14
            else -> STREAK_30
        }

        val challenges = mutableListOf(
            StreakAchievedUiModel(STREAK_3, false),
            StreakAchievedUiModel(STREAK_7, false),
            StreakAchievedUiModel(STREAK_14, false),
            StreakAchievedUiModel(STREAK_30, false)
        )

        challenges.forEachIndexed { index, challenge ->
            if (streak >= challenge.streak) {
                challenges[index].achieved = true
            }
        }

        return StreakChallengeUiModel(
            currentChallenge = currentChallenge,
            dayOfCurrentChallenge = streak,
            endDayOfChallenge = currentChallenge,
            challengesAchieved = challenges
        )
    }

    companion object {
        private const val STREAK_3 = 3
        private const val STREAK_7 = 7
        private const val STREAK_14 = 14
        private const val STREAK_30 = 30
        private const val STREAK_3_LOWER_RANGE = 1
        private const val STREAK_7_LOWER_RANGE = 4
        private const val STREAK_14_LOWER_RANGE = 8
    }
}
