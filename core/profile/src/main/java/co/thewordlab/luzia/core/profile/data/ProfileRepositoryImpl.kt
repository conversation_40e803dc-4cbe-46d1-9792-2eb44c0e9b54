package co.thewordlab.luzia.core.profile.data

import co.thewordlab.fouundation.persistence.profile.ProfileDao
import co.thewordlab.luzia.core.profile.data.api.ProfileApi
import co.thewordlab.luzia.core.profile.data.api.model.SchoolMateDto
import co.thewordlab.luzia.core.profile.data.api.model.UserProfileDto
import co.thewordlab.luzia.core.profile.data.api.model.UserProfileUpdateRequest
import co.thewordlab.luzia.core.profile.domain.ProfileRepository
import co.thewordlab.luzia.core.profile.domain.model.School
import co.thewordlab.luzia.core.profile.domain.model.SchoolMate
import co.thewordlab.luzia.core.profile.domain.model.SchoolMates
import co.thewordlab.luzia.core.profile.domain.model.UserIntent
import co.thewordlab.luzia.core.profile.domain.model.UserProfile
import co.thewordlab.luzia.core.profile.domain.model.UserProfilePrivacy
import co.thewordlab.luzia.foundation.networking.model.AppErrors
import co.thewordlab.luzia.foundation.networking.model.Error
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.asFailure
import co.thewordlab.luzia.foundation.networking.model.asResult
import co.thewordlab.luzia.foundation.networking.model.asSuccess
import co.thewordlab.luzia.foundation.networking.model.getDataOrNull
import co.thewordlab.luzia.foundation.networking.model.mapTo
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import co.thewordlab.luzia.foundation.networking.session.UserType
import co.theworldlab.luzia.foundation.data.cache.store.disk.DiskCache
import co.theworldlab.luzia.foundation.data.cache.store.memory.inMemoryStore
import co.theworldlab.luzia.foundation.design.system.components.mate.UserMateUiModel
import co.theworldlab.luzia.foundation.design.system.components.profile.AvatarState
import com.slack.eithernet.ApiResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton
import javax.net.ssl.HttpsURLConnection.HTTP_CONFLICT

@Singleton
class ProfileRepositoryImpl @Inject constructor(
    private val profileApi: ProfileApi,
    private val userSessionManager: UserSessionManager,
    private val profileDao: ProfileDao,
    private val mapper: ProfileMapper
) : ProfileRepository {

    private val store = inMemoryStore(
        fetcher = { profileApi.getProfile().asResult().getDataOrNull() },
        diskCache = object : DiskCache<UserProfileDto?> {
            override fun getFromCache(): Flow<UserProfileDto?> {
                return userSessionManager.userSession.map { it?.masterUserId }.filterNotNull()
                    .flatMapLatest { profileDao.getProfile(it) }
                    .map { mapper.mapToDto(it) }
            }

            override suspend fun saveIntoCache(item: UserProfileDto?) {
                val masterUserId = userSessionManager.userSession.firstOrNull()?.masterUserId
                if (masterUserId != null) {
                    val entity = item?.let { mapper.mapToEntity(masterUserId, item) }
                    if (entity != null) profileDao.insertProfile(entity)
                }
            }
        }
    ).build()

    override fun getUserProfileAsFlow(): Flow<UserProfile?> = userSessionManager.userSession
        .map { it?.userType == UserType.FULL_USER }
        .flatMapLatest { isFullUser ->
            if (isFullUser) {
                store.flow()
            } else {
                flowOf(null)
            }
        }.map {
            it?.let { mapper.mapToDomain(it) }
        }

    override suspend fun getUserProfile(forceRefresh: Boolean): UserProfile? {
        return if (forceRefresh) {
            store.fetch()?.let { mapper.mapToDomain(it) }
        } else {
            store.get()?.let { mapper.mapToDomain(it) }
        }
    }

    override suspend fun getUserProfileById(masterUserId: String): ResultOf<UserProfile, Error> =
        profileApi.getProfileById(masterUserId).asResult().mapTo {
            mapper.mapToDomain(this)
        }

    override suspend fun updateUserProfile(request: UserProfileUpdateRequest): ResultOf<UserProfile, Error> {
        val result = profileApi.updateProfile(request).asResult()
        if (result is ResultOf.Success) store.update(result.data)
        return result.mapTo { mapper.mapToDomain(this) }
    }

    override suspend fun checkUsernameAvailable(username: String): ResultOf<Boolean, Error> {
        val response = profileApi.checkUsername(username)
        return when {
            response is ApiResult.Success -> ResultOf.Success(true)
            response is ApiResult.Failure.HttpFailure && response.code == HTTP_CONFLICT ->
                ResultOf.Success(false)

            else -> ResultOf.Failure(AppErrors.Unknown)
        }
    }

    override suspend fun getSchools(country: String, query: String): ResultOf<List<School>, Error> =
        profileApi.getSchools(country, query).asResult().mapTo {
            results.map {
                School(
                    id = it.id,
                    title = it.highlighted,
                    description = it.city
                )
            }
        }

    override suspend fun getSchoolMates(): ResultOf<SchoolMates, Error> =
        profileApi.getSchoolMates().asResult().mapTo {
            SchoolMates(
                school = school.map {
                    SchoolMate(
                        nickname = it.nickname.orEmpty(),
                        alias = it.username.orEmpty(),
                        avatarState = it.getAvatarState(),
                        bestiePoints = it.bestiePoints,
                        masterUserId = it.masterUserId.orEmpty(),
                        privacy = it.privacy ?: UserProfilePrivacy.PUBLIC
                    )
                }.sortedByDescending { it.bestiePoints }
            )
        }

    override suspend fun searchUsername(username: String): ResultOf<List<UserMateUiModel>, Error> {
        return profileApi.searchUsername(username).asResult().mapTo {
            data.map {
                UserMateUiModel(
                    masterUserId = it.masterUserId.orEmpty(),
                    name = it.nickname.orEmpty(),
                    username = it.username.orEmpty(),
                    bp = it.bestiePoints ?: 0,
                    isStudent = it.isStudent == true,
                    isPublic = it.privacy == UserProfilePrivacy.PUBLIC,
                    avatarState = it.avatarURL?.let { url -> AvatarState.ProfileImage(url) }
                        ?: AvatarState.Initials(it.nickname.orEmpty().take(1))
                )
            }
        }
    }

    private fun SchoolMateDto.getAvatarState(): AvatarState {
        return if (avatarURL.isNullOrEmpty()) {
            AvatarState.Initials(nickname.orEmpty().take(1))
        } else {
            AvatarState.ProfileImage(avatarURL)
        }
    }

    override suspend fun deleteProfileImage(): ResultOf<Unit, Error> {
        val response = profileApi.deleteProfileImage()
        return if (response.isSuccessful) {
            store.update { it?.copy(avatarURL = null) }
            ResultOf.Success(Unit)
        } else {
            ResultOf.Failure(AppErrors.Unknown)
        }
    }

    override suspend fun isSessionLocked(): Boolean {
        return when (val response = profileApi.getProfile()) {
            is ApiResult.Failure.HttpFailure -> response.code == HTTP_LOCKED
            else -> false
        }
    }

    override suspend fun deleteUser(): ResultOf<Unit, Error> {
        val response = profileApi.deleteUser()
        return if (response.isSuccessful) {
            Unit.asSuccess()
        } else {
            AppErrors.Unknown.asFailure()
        }
    }

    override suspend fun deleteUserToken(): ResultOf<Unit, Error> {
        val response = profileApi.deleteUserTokens()
        return if (response.isSuccessful) {
            Unit.asSuccess()
        } else {
            AppErrors.Unknown.asFailure()
        }
    }

    override suspend fun clearData(masterUserId: String) {
        profileDao.removeProfile(masterUserId)
    }

    override suspend fun getUserIntents(): ResultOf<List<UserIntent>, Error> =
        profileApi.getUserIntents().asResult().mapTo {
            data.map { UserIntent(it.onboardingIntentId, it.title) }
        }

    private companion object {
        const val HTTP_LOCKED = 423
    }
}
