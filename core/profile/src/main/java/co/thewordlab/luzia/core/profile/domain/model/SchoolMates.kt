package co.thewordlab.luzia.core.profile.domain.model

import co.theworldlab.luzia.foundation.design.system.components.profile.AvatarState

data class SchoolMates(
    val school: List<SchoolMate>
)

data class SchoolMate(
    val nickname: String,
    val avatarState: AvatarState? = null,
    val bestiePoints: Int,
    private val alias: String,
    val masterUserId: String,
    val privacy: UserProfilePrivacy
) {
    val username: String = "@$alias"
}
