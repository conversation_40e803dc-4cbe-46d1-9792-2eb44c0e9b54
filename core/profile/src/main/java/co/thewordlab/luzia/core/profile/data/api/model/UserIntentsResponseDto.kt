package co.thewordlab.luzia.core.profile.data.api.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class UserIntentsResponseDto(
    @Json(name = "data") val data: List<UserIntentDto>
)

@JsonClass(generateAdapter = true)
data class UserIntentDto(
    @Json(name = "onboardingIntentId") val onboardingIntentId: String,
    @Json(name = "title") val title: String
)
