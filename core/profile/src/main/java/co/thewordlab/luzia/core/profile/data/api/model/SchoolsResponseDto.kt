package co.thewordlab.luzia.core.profile.data.api.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class SchoolsResponseDto(
    @Json(name = "results")
    val results: List<SchoolDtp>
)

@JsonClass(generateAdapter = true)
data class SchoolDtp(
    @Json(name = "id")
    val id: String,
    @Json(name = "highlighted")
    val highlighted: String,
    @<PERSON>son(name = "address")
    val address: String,
    @<PERSON>son(name = "city")
    val city: String
)
