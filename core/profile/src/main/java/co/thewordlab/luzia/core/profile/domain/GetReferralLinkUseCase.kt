package co.thewordlab.luzia.core.profile.domain

import co.thewordlab.luzia.core.profile.domain.model.ReferralModel
import javax.inject.Inject

class GetReferralLinkUseCase @Inject constructor(
    private val profileRepository: ProfileRepository
) {

    suspend operator fun invoke(): ReferralModel? {
        return profileRepository.getUserProfile()
            ?.let { profile ->
                val link = validateLinkAndCode(profile.referralCode, profile.referralLink)
                ReferralModel(
                    link = link,
                    code = profile.referralCode.orEmpty()
                )
            }
    }

    private fun validateLinkAndCode(code: String?, link: String?): String {
        return if (code.isNullOrEmpty()) {
            PLAY_STORE_LINK
        } else {
            if (link.isNullOrEmpty()) {
                "$PLAY_STORE_LINK$REFERRAL_LINK$code"
            } else {
                link
            }
        }
    }

    private companion object {
        const val PLAY_STORE_LINK =
            "https://play.google.com/store/apps/details?id=co.thewordlab.luzia"
        const val REFERRAL_LINK = "&referrer=utm_source=app&referral_code="
    }
}
