package co.thewordlab.luzia.core.profile.domain

import co.thewordlab.luzia.core.profile.data.api.model.UserProfileUpdateRequest
import co.thewordlab.luzia.core.profile.domain.model.School
import co.thewordlab.luzia.core.profile.domain.model.SchoolMates
import co.thewordlab.luzia.core.profile.domain.model.UserIntent
import co.thewordlab.luzia.core.profile.domain.model.UserProfile
import co.thewordlab.luzia.foundation.networking.model.Error
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.theworldlab.luzia.foundation.design.system.components.mate.UserMateUiModel
import kotlinx.coroutines.flow.Flow

@Suppress("TooManyFunctions")
interface ProfileRepository {
    fun getUserProfileAsFlow(): Flow<UserProfile?>
    suspend fun getUserProfile(forceRefresh: Boolean = false): UserProfile?

    suspend fun getUserProfileById(masterUserId: String): ResultOf<UserProfile, Error>
    suspend fun updateUserProfile(request: UserProfileUpdateRequest): ResultOf<UserProfile, Error>
    suspend fun checkUsernameAvailable(username: String): ResultOf<Boolean, Error>
    suspend fun getSchools(country: String, query: String): ResultOf<List<School>, Error>
    suspend fun getSchoolMates(): ResultOf<SchoolMates, Error>
    suspend fun deleteProfileImage(): ResultOf<Unit, Error>
    suspend fun isSessionLocked(): Boolean
    suspend fun deleteUser(): ResultOf<Unit, Error>
    suspend fun deleteUserToken(): ResultOf<Unit, Error>
    suspend fun searchUsername(username: String): ResultOf<List<UserMateUiModel>, Error>
    suspend fun clearData(masterUserId: String)
    suspend fun getUserIntents(): ResultOf<List<UserIntent>, Error>
}
