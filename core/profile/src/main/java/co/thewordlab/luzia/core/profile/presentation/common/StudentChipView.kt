package co.thewordlab.luzia.core.profile.presentation.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun StudentChipView(modifier: Modifier = Modifier) {
    Row(
        modifier = modifier
            .clip(CircleShape)
            .background(LuziaTheme.palette.accents.green.green10)
            .padding(Spacing.X8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Spacing.X4.dp)
    ) {
        Image(
            modifier = Modifier.height(Spacing.X24.dp),
            painter = painterResource(R.drawable.im_friend_avatars),
            contentDescription = null
        )
        LuziaText(
            text = stringResource(co.thewordlab.luzia.foundation.localization.R.string.number_of_students),
            style = LuziaTheme.typography.body.semiBold.default,
            color = if (LuziaTheme.isDarkTheme) {
                LuziaTheme.palette.accents.green.green90
            } else {
                LuziaTheme.palette.accents.green.green30
            }
        )
    }
}
