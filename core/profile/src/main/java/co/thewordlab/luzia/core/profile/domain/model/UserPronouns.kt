package co.thewordlab.luzia.core.profile.domain.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = false)
enum class UserPronouns(val value: String) {

    @<PERSON><PERSON>(name = "he_him")
    HE_HIM("he_him"),

    @<PERSON><PERSON>(name = "she_her")
    SHE_HER("she_her"),

    @<PERSON><PERSON>(name = "they_them")
    THEY_THEM("they_them"),

    @<PERSON><PERSON>(name = "other")
    OTHER("other");

    companion object {
        fun getValue(value: String?) = entries.firstOrNull { it.value == value.orEmpty() }
    }
}
