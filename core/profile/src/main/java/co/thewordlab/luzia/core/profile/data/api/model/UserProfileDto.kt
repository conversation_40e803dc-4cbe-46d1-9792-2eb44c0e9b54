package co.thewordlab.luzia.core.profile.data.api.model

import co.thewordlab.luzia.core.profile.domain.model.UserProfilePrivacy
import co.thewordlab.luzia.core.profile.domain.model.UserPronouns
import co.thewordlab.luzia.core.profile.domain.model.UserType
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class UserProfileDto(
    @<PERSON><PERSON>(name = "preferredLanguage") val preferredLanguage: String? = null,
    @<PERSON><PERSON>(name = "nickname") val nickname: String? = null,
    @<PERSON><PERSON>(name = "username") val username: String? = null,
    @<PERSON><PERSON>(name = "avatarURL") val avatarURL: String? = null,
    @<PERSON><PERSON>(name = "backgroundURL") val backgroundURL: String? = null,
    @<PERSON><PERSON>(name = "phoneNumber") val phoneNumber: String?,
    @<PERSON>son(name = "email") val email: String?,
    @<PERSON>son(name = "utcOffset") val utcOffset: Int?,
    @<PERSON><PERSON>(name = "birthdate") val birthdate: String? = null,
    @<PERSON><PERSON>(name = "pronouns") val pronouns: UserPronouns? = null,
    @Json(name = "privacy") val privacy: UserProfilePrivacy? = null,
    @Json(name = "schoolName") val schoolName: String? = null,
    @Json(name = "schoolMates") val schoolMates: Int? = null,
    @Json(name = "isStudent") val isStudent: Boolean? = null,
    @Json(name = "userType") val userType: UserType? = null,
    @Json(name = "bestiePoints") val bestiePoints: Int? = null,
    @Json(name = "referralCode") val referralCode: String? = null,
    @Json(name = "referralURL") val referralURL: String? = null,
    @Json(name = "streamToken") val streamToken: String? = null,
    @Json(name = "streamUserId") val streamUserId: String? = null,
    @Json(name = "masterUserId") val masterUserId: String? = null,
    @Json(name = "country") val country: String? = null,
    @Json(name = "blockedByMe") val blockedByMe: Boolean? = null,
    @Json(name = "onboardingIntentId") val onboardingIntentId: String? = null,
    @Json(name = "selfDescription") val selfDescription: String? = null
)
