package co.thewordlab.luzia.core.profile.domain.model

import co.theworldlab.luzia.foundation.design.system.components.profile.AvatarState

data class UserProfile(
    val displayName: String? = null,
    val username: String? = null,
    val avatarURL: String? = null,
    val backgroundURL: String? = null,
    val language: String? = null,
    val utcOffset: Int? = null,
    val email: String? = null,
    val phone: String? = null,
    val birthdate: String? = null,
    val pronouns: UserPronouns? = null,
    val privacy: UserProfilePrivacy? = null,
    val schoolName: String? = null,
    val schoolMates: Int? = null,
    val isStudent: Boolean? = null,
    val userType: UserType? = null,
    val avatarState: AvatarState? = null,
    val bestiePoints: Int? = null,
    val referralCode: String? = null,
    val referralLink: String? = null,
    val streamToken: String? = null,
    val streamUserId: String? = null,
    val countryName: String? = null,
    val blockedByMe: Boolean = false,
    val onboardingIntentId: String? = null,
    val selfDescription: String? = null
)

fun UserProfile?.isProfileCompleted(): Boolean =
    this?.let {
        !it.username.isNullOrEmpty() &&
            !it.birthdate.isNullOrEmpty() &&
            it.privacy != null &&
            (it.isStudent != null && if (it.isStudent) !it.schoolName.isNullOrEmpty() else true)
    } ?: false
