package co.thewordlab.luzia.core.profile.domain

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject

class ClearUserCacheUseCase @Inject constructor(
    @ApplicationContext private val context: Context
) {

    operator fun invoke(fullCache: Boolean = false) {
        when (fullCache) {
            true -> clearFullInternalCache()
            false -> clearPartialInternalCache()
        }
    }

    private fun clearFullInternalCache() {
        val cacheDir = context.cacheDir
        if (cacheDir.exists()) {
            cacheDir.deleteRecursively()
        }
    }

    private fun clearPartialInternalCache() {
        val cacheDir = context.cacheDir
        if (cacheDir.exists()) {
            cacheDir.walkTopDown().forEach { file ->
                if (file.isFile &&
                    (file.name.startsWith(PREFIX_AUDIO) || file.name.startsWith(PREFIX_IMAGE))
                ) {
                    file.delete()
                }
            }
        }
    }

    private companion object {
        const val PREFIX_AUDIO = "Record_"
        const val PREFIX_IMAGE = "IMG_"
    }
}
