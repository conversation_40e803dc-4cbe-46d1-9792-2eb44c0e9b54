package co.thewordlab.luzia.core.profile.presentation.entrypoints.chat

import androidx.lifecycle.ViewModel
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class ProfileChatEntryPointViewModel @Inject constructor() :
    ViewModel(),
    ViewModelActions<ProfileChatEntryPointViewActions> {

    override fun onViewAction(action: ProfileChatEntryPointViewActions) {
        when (action) {
            is ProfileChatEntryPointViewActions.OnCreate -> {
            }
        }
    }
}
