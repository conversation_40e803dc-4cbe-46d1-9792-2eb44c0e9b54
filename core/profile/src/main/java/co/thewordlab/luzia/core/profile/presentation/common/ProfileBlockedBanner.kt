package co.thewordlab.luzia.core.profile.presentation.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun ProfileBlockedBanner(showDescription: Boolean = true) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(Spacing.X16.dp),
        verticalArrangement = Arrangement.spacedBy(Spacing.X16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            modifier = Modifier
                .clip(CircleShape)
                .background(LuziaTheme.palette.accents.red.error90)
                .padding(horizontal = Spacing.X12.dp, vertical = Spacing.X4.dp),
            horizontalArrangement = Arrangement.spacedBy(Spacing.X4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(designR.drawable.ic_block_20),
                contentDescription = null,
                tint = LuziaTheme.palette.accents.red.error10
            )
            LuziaText(
                text = stringResource(localizationR.string.you_blocked_this_user),
                style = LuziaTheme.typography.body.semiBold.small,
                color = LuziaTheme.palette.accents.red.error10
            )
        }
        if (showDescription) {
            LuziaText(
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                text = stringResource(localizationR.string.blocked_user_description),
                style = LuziaTheme.typography.body.regular.footnote,
                color = LuziaTheme.palette.text.secondary
            )
        }
    }
}
