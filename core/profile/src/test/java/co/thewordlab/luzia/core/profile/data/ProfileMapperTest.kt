package co.thewordlab.luzia.core.profile.data

import co.thewordlab.fouundation.persistence.profile.ProfileEntity
import co.thewordlab.fouundation.persistence.profile.ProfilePrivacy
import co.thewordlab.fouundation.persistence.profile.ProfileType
import co.thewordlab.fouundation.persistence.profile.Pronouns
import co.thewordlab.luzia.core.profile.data.api.model.UserProfileDto
import co.thewordlab.luzia.core.profile.domain.model.UserProfile
import co.thewordlab.luzia.core.profile.domain.model.UserProfilePrivacy
import co.thewordlab.luzia.core.profile.domain.model.UserPronouns
import co.thewordlab.luzia.core.profile.domain.model.UserType
import kotlinx.coroutines.test.runTest
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull

class ProfileMapperTest {

    private val sut: ProfileMapper = ProfileMapper()

    @Test
    fun `given profile entity when mapToDto then returns user profile dto`() = runTest {
        val entity = ProfileEntity(
            masterUserId = "masterUserId",
            nickname = "nickname",
            username = "username",
            avatarURL = "avatarURL",
            backgroundURL = "backgroundURL",
            preferredLanguage = "en",
            utcOffset = 1,
            email = "email",
            phoneNumber = "phoneNumber",
            birthdate = "birthdate",
            pronouns = Pronouns.HE_HIM,
            schoolName = "schoolName",
            schoolMates = 1,
            privacy = ProfilePrivacy.PUBLIC,
            isStudent = true,
            userType = ProfileType.FULL_USER,
            bestiePoints = 100,
            referralCode = "referralCode",
            referralURL = "referralURL",
            streamToken = "streamToken",
            streamUserId = "streamUserId",
            onboardingIntentId = "onboardingIntentId",
            selfDescription = "selfDescription"
        )

        val expectedResult = UserProfileDto(
            masterUserId = "masterUserId",
            nickname = "nickname",
            username = "username",
            avatarURL = "avatarURL",
            backgroundURL = "backgroundURL",
            preferredLanguage = "en",
            utcOffset = 1,
            email = "email",
            phoneNumber = "phoneNumber",
            birthdate = "birthdate",
            pronouns = UserPronouns.HE_HIM,
            schoolName = "schoolName",
            schoolMates = 1,
            privacy = UserProfilePrivacy.PUBLIC,
            isStudent = true,
            userType = UserType.FULL_USER,
            bestiePoints = 100,
            referralCode = "referralCode",
            referralURL = "referralURL",
            streamToken = "streamToken",
            streamUserId = "streamUserId",
            onboardingIntentId = "onboardingIntentId",
            selfDescription = "selfDescription"
        )

        val result = sut.mapToDto(entity)

        assertEquals(expectedResult, result)
    }

    @Test
    fun `given null profile entity when mapToDto then returns null`() = runTest {
        val result = sut.mapToDto(null)

        assertNull(result)
    }

    @Test
    fun `given user profile dto when mapToDomain then returns user profile`() = runTest {
        val dto = UserProfileDto(
            nickname = "nickname",
            username = "username",
            avatarURL = "avatarURL",
            backgroundURL = "backgroundURL",
            preferredLanguage = "en",
            utcOffset = 1,
            email = "email",
            phoneNumber = "phoneNumber",
            birthdate = "birthdate",
            pronouns = UserPronouns.THEY_THEM,
            schoolName = "schoolName",
            schoolMates = 1,
            privacy = UserProfilePrivacy.PRIVATE,
            isStudent = false,
            userType = UserType.GUEST_PLUS,
            bestiePoints = 200,
            referralCode = "referralCode",
            referralURL = "referralURL",
            streamToken = "streamToken",
            streamUserId = "streamUserId",
            onboardingIntentId = "onboardingIntentId",
            blockedByMe = true,
            country = "AR",
            selfDescription = "selfDescription"
        )

        val expectedResult = UserProfile(
            displayName = "nickname",
            username = "username",
            avatarURL = "avatarURL",
            backgroundURL = "backgroundURL",
            language = "en",
            utcOffset = 1,
            email = "email",
            phone = "phoneNumber",
            birthdate = "birthdate",
            pronouns = UserPronouns.THEY_THEM,
            schoolName = "schoolName",
            schoolMates = 1,
            privacy = UserProfilePrivacy.PRIVATE,
            isStudent = false,
            userType = UserType.GUEST_PLUS,
            bestiePoints = 200,
            referralCode = "referralCode",
            referralLink = "referralURL",
            streamToken = "streamToken",
            streamUserId = "streamUserId",
            onboardingIntentId = "onboardingIntentId",
            countryName = "Argentina",
            blockedByMe = true,
            selfDescription = "selfDescription"
        )

        val result = sut.mapToDomain(dto)

        assertEquals(expectedResult, result)
    }

    @Test
    fun `given user profile dto when mapToEntity then returns profile entity`() = runTest {
        val dto = UserProfileDto(
            nickname = "nickname",
            username = "username",
            avatarURL = "avatarURL",
            backgroundURL = "backgroundURL",
            preferredLanguage = "en",
            utcOffset = 1,
            email = "email",
            phoneNumber = "phoneNumber",
            birthdate = "birthdate",
            pronouns = UserPronouns.OTHER,
            schoolName = "schoolName",
            schoolMates = 1,
            privacy = UserProfilePrivacy.PRIVATE,
            isStudent = false,
            userType = UserType.GUEST,
            bestiePoints = 200,
            referralCode = "referralCode",
            referralURL = "referralURL",
            streamToken = "streamToken",
            streamUserId = "streamUserId",
            onboardingIntentId = "onboardingIntentId",
            selfDescription = "selfDescription"
        )
        val masterUserId = "masterUserId"

        val expectedResult = ProfileEntity(
            masterUserId = masterUserId,
            nickname = "nickname",
            username = "username",
            avatarURL = "avatarURL",
            backgroundURL = "backgroundURL",
            preferredLanguage = "en",
            utcOffset = 1,
            email = "email",
            phoneNumber = "phoneNumber",
            birthdate = "birthdate",
            pronouns = Pronouns.OTHER,
            schoolName = "schoolName",
            schoolMates = 1,
            privacy = ProfilePrivacy.PRIVATE,
            isStudent = false,
            userType = ProfileType.GUEST,
            bestiePoints = 200,
            referralCode = "referralCode",
            referralURL = "referralURL",
            streamToken = "streamToken",
            streamUserId = "streamUserId",
            onboardingIntentId = "onboardingIntentId",
            selfDescription = "selfDescription"
        )

        val result = sut.mapToEntity(masterUserId, dto)

        assertEquals(expectedResult, result)
    }
}
