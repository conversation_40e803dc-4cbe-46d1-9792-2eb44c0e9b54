package co.thewordlab.luzia.core.profile.data

import co.thewordlab.fouundation.persistence.profile.ProfileDao
import co.thewordlab.luzia.core.profile.data.ProfileMocks.givenUserGuestSession
import co.thewordlab.luzia.core.profile.data.ProfileMocks.givenUserProfileDto
import co.thewordlab.luzia.core.profile.data.api.ProfileApi
import co.thewordlab.luzia.core.profile.data.api.model.UserIntentDto
import co.thewordlab.luzia.core.profile.data.api.model.UserIntentsResponseDto
import co.thewordlab.luzia.core.profile.data.api.model.UserProfileUpdateRequest
import co.thewordlab.luzia.core.profile.domain.model.UserProfile
import co.thewordlab.luzia.foundation.networking.model.AppErrors
import co.thewordlab.luzia.foundation.networking.model.EmptyResponse
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.asResult
import co.thewordlab.luzia.foundation.networking.model.getDataOrNull
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import com.slack.eithernet.ApiResult
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.every
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

class ProfileRepositoryTest {

    private lateinit var sut: ProfileRepositoryImpl

    @MockK
    lateinit var profileApi: ProfileApi

    @MockK
    lateinit var userSessionManager: UserSessionManager

    @MockK
    lateinit var profileDao: ProfileDao

    @MockK
    lateinit var mapper: ProfileMapper

    @Before
    fun setUp() {
        MockKAnnotations.init(this, relaxed = true)
        sut = ProfileRepositoryImpl(profileApi, userSessionManager, profileDao, mapper)
    }

    @Test
    fun `given not full user session when getUserProfileAsFlow then returns null`() = runTest {
        every { userSessionManager.userSession } returns flowOf(givenUserGuestSession())

        val result = sut.getUserProfileAsFlow()

        result.collect { assert(it == null) }
    }

    @Test
    fun `given force refresh true when getUserProfile then returns user profile`() = runTest {
        val userProfileDto = givenUserProfileDto()
        coEvery { profileApi.getProfile() } returns ApiResult.success(userProfileDto)
        val userProfile = UserProfile(displayName = "nickname")
        every { mapper.mapToDomain(userProfileDto) } returns userProfile

        val result = sut.getUserProfile(true)

        assertEquals(userProfile.displayName, result?.displayName)
    }

    @Test
    fun `given force refresh false when getUserProfile then returns user profile`() = runTest {
        val userProfileDto = givenUserProfileDto()
        coEvery { profileApi.getProfile().asResult().getDataOrNull() } returns userProfileDto

        val userProfile = UserProfile(displayName = "nickname")
        every { mapper.mapToDomain(any()) } returns userProfile
        coEvery { profileApi.getProfile() } returns ApiResult.success(userProfileDto)

        val result = sut.getUserProfile(false)

        assertEquals(userProfile, result)
    }

    @Test
    fun `given master user id when getUserProfileById then returns user profile`() = runTest {
        val masterUserId = "masterUserId"
        val userProfileDto = givenUserProfileDto()
        coEvery { profileApi.getProfileById(masterUserId) } returns ApiResult.success(userProfileDto)
        val userProfile = UserProfile(displayName = "nickname")
        every { mapper.mapToDomain(userProfileDto) } returns userProfile

        val result = sut.getUserProfileById(masterUserId)

        assertEquals(userProfile, result.getDataOrNull())
    }

    @Test
    fun `given update request when updateUserProfile then returns user profile`() = runTest {
        val request = UserProfileUpdateRequest()
        val userProfileDto = givenUserProfileDto()
        coEvery { profileApi.updateProfile(request) } returns ApiResult.success(userProfileDto)
        val userProfile = UserProfile(displayName = "nickname")
        every { mapper.mapToDomain(userProfileDto) } returns userProfile

        val result = sut.updateUserProfile(request)

        assertEquals(userProfile.displayName, result.getDataOrNull()?.displayName)
    }

    @Test
    fun `given username available when checkUsernameAvailable then returns true`() = runTest {
        val username = "username"
        coEvery { profileApi.checkUsername(username) } returns ApiResult.success(EmptyResponse())

        val result = sut.checkUsernameAvailable(username)

        assertTrue(result.getDataOrNull() == true)
    }

    @Test
    fun `given username not available when checkUsernameAvailable then returns false`() = runTest {
        val username = "username"
        coEvery { profileApi.checkUsername(username) } returns ApiResult.apiFailure()

        val result = sut.checkUsernameAvailable(username)

        assertFalse(result.getDataOrNull() == true)
    }

    @Test
    fun `given unknown error when checkUsernameAvailable then returns failure`() = runTest {
        val username = "username"
        coEvery { profileApi.checkUsername(username) } returns ApiResult.apiFailure()

        val result = sut.checkUsernameAvailable(username)

        assertEquals(AppErrors.Unknown, (result as ResultOf.Failure).error)
    }

    @Test
    fun `given user when request user intents then returns user intents`() = runTest {
        coEvery { profileApi.getUserIntents() } returns ApiResult.success(
            UserIntentsResponseDto(data = listOf(UserIntentDto("id", "title")))
        )

        val result = sut.getUserIntents()

        assert(result.getDataOrNull() != null)
    }

    @Test
    fun `given user when request user intents then returns error`() = runTest {
        coEvery { profileApi.getUserIntents() } returns ApiResult.apiFailure()

        val result = sut.getUserIntents()

        assert(result.getDataOrNull() == null)
    }
}
