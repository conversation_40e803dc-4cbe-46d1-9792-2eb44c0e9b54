package co.thewordlab.luzia.core.navigation.common

import android.net.Uri
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.navigation.NavOptions
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING

val LocalNavigation = staticCompositionLocalOf<Navigation> {
    object : Navigation {
        override fun navigate(route: Route, options: NavOptions?) {
            DO_NOTHING
        }

        override fun goBack() {
            DO_NOTHING
        }

        override fun goBackTo(route: Route, inclusive: Boolean) {
            DO_NOTHING
        }

        override fun handleDeepLink(uri: Uri, options: NavOptions?) {
            DO_NOTHING
        }
    }
}
