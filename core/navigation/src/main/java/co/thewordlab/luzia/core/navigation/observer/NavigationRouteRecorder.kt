package co.thewordlab.luzia.core.navigation.observer

import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavController
import dagger.hilt.android.scopes.ActivityScoped
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import javax.inject.Inject

@ActivityScoped
class NavigationRouteRecorder @Inject constructor() {

    private val _navigationHistory = MutableSharedFlow<NavBackStackEntry>(
        replay = DEFAULT_REPLAY_CAPACITY,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )
    val navigationHistory: SharedFlow<NavBackStackEntry> = _navigationHistory.asSharedFlow()

    suspend fun listenToController(navController: NavController) {
        navController.currentBackStackEntryFlow.collect {
            _navigationHistory.emit(it)
        }
    }

    fun getRecentEntries(count: Int = 3): List<NavBackStackEntry> {
        return if (count <= 0) {
            emptyList()
        } else {
            _navigationHistory.replayCache.takeLast(count)
        }
    }

    companion object {
        private const val DEFAULT_REPLAY_CAPACITY = 3
    }
}
