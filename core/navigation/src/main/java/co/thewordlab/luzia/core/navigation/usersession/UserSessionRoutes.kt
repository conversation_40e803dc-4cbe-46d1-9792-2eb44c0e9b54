package co.thewordlab.luzia.core.navigation.usersession

import co.thewordlab.luzia.core.navigation.camera.CameraAnalysisModel
import co.thewordlab.luzia.core.navigation.common.Route
import co.thewordlab.luzia.core.navigation.usersession.model.GroupJoinSource
import co.thewordlab.luzia.core.navigation.usersession.model.NewChatPurpose
import co.thewordlab.luzia.core.navigation.usersession.model.NewGroupScreenSource
import co.thewordlab.luzia.core.navigation.usersession.model.ProfileFillPolicy
import co.thewordlab.luzia.core.navigation.usersession.model.SettingDestinations
import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreen
import co.theworldlab.luzia.foundation.design.system.components.bottombar.LandingDestinations
import kotlinx.serialization.Serializable

@Serializable
sealed class UserSessionRoutes : Route {

    @Serializable
    data class Landing(
        val destinations: LandingDestinations,
        val showAInAppReview: Boolean = false
    ) : UserSessionRoutes()

    @Serializable
    data object OnboardingChatDetail : UserSessionRoutes()

    @Serializable
    data class ChatDetail(
        val personalityId: String,
        val highlightId: Long? = null,
        val isCustomBestie: Boolean,
        val openKeyboardOnLaunch: Boolean = false
    ) : UserSessionRoutes()

    @Serializable
    data class ImagineTool(val imagineId: String? = null) : UserSessionRoutes()

    @Serializable
    data class ImagineToolResult(val imagineId: String, val prompt: String) : UserSessionRoutes()

    @Serializable
    data class Settings(val destinations: SettingDestinations = SettingDestinations.NONE) :
        UserSessionRoutes()

    @Serializable
    data object ThemeSelect : UserSessionRoutes()

    @Serializable
    data object LanguageSelect : UserSessionRoutes()

    @Serializable
    data object PrivacyPolicy : UserSessionRoutes()

    @Serializable
    data object PrivacyPolicyTerms : UserSessionRoutes()

    @Serializable
    data object Account : UserSessionRoutes()

    @Serializable
    data object ImagineGallery : UserSessionRoutes()

    @Serializable
    data class Vision(val openGallery: Boolean = false) : UserSessionRoutes()

    @Serializable
    data object DocumentTool : UserSessionRoutes()

    @Serializable
    data object MathTool : UserSessionRoutes()

    @Serializable
    data object BestiePoints : UserSessionRoutes()

    @Serializable
    data object Profile : UserSessionRoutes()

    @Serializable
    data object ProfileEdit : UserSessionRoutes()

    @Serializable
    data class ProfileEditItem(val screen: WizardScreen) : UserSessionRoutes()

    @Serializable
    data object SchoolMates : UserSessionRoutes()

    @Serializable
    data object ReferralCode : UserSessionRoutes()

    @Serializable
    data class NewChat(
        val purpose: NewChatPurpose,
        val messagesToShare: List<String> = emptyList()
    ) : UserSessionRoutes()

    @Serializable
    data class NewGroup(
        val screenSource: NewGroupScreenSource,
        val messagesToShare: List<String> = emptyList()
    ) : UserSessionRoutes()

    @Serializable
    data class Signup(val showDeleteConfirmation: Boolean = false) : UserSessionRoutes()

    @Serializable
    data class ExternalProfile(
        val userId: String
    ) : UserSessionRoutes()

    @Serializable
    data class ProactiveSharedMessage(val masterUserId: String) : UserSessionRoutes()

    @Serializable
    data class PersonalityProfile(val personalityId: String, val isCustomBestie: Boolean) :
        UserSessionRoutes()

    @Serializable
    data class Favorites(val personalityId: String, val isCustomBestie: Boolean) :
        UserSessionRoutes()

    @Serializable
    data object FavoritesSignupModal : UserSessionRoutes()

    @Serializable
    data class ResponseStyles(val personalityId: String) : UserSessionRoutes()

    @Serializable
    data class ResponseStylesModal(val personalityId: String) : UserSessionRoutes()

    @Serializable
    data object ResponseStylesSignupModal : UserSessionRoutes()

    @Serializable
    data class ProfileFill(val policy: ProfileFillPolicy) : UserSessionRoutes()

    @Serializable
    data class ImagineGalleryDetail(val index: Int) : UserSessionRoutes()

    @Serializable
    data class NotificationPermission(val openChat: Boolean) : UserSessionRoutes()

    @Serializable
    data class Web(val title: String, val url: String) : UserSessionRoutes()

    @Serializable
    data class Camera(
        val toolId: String?,
        val analysis: CameraAnalysisModel,
        val openGallery: Boolean = false
    ) : UserSessionRoutes()

    @Serializable
    data class GetStreamChatDetail(val channelId: String) : UserSessionRoutes()

    @Serializable
    data class DynamicTool(val toolId: String, val origin: String) : UserSessionRoutes()

    @Serializable
    data class DynamicToolResult(val configContent: String) : UserSessionRoutes()

    @Serializable
    data class GetStreamRenameGroup(val channelId: String, val name: String) : UserSessionRoutes()

    @Serializable
    data class GetStreamChangeImage(val channelId: String, val url: String) : UserSessionRoutes()

    @Serializable
    data class GetStreamAddFriends(
        val channelId: String,
        val members: List<String>
    ) : UserSessionRoutes()

    @Serializable
    data class Invite(
        val cid: String,
        val name: String,
        val memberCount: String,
        val image: String?,
        val source: GroupJoinSource
    ) : UserSessionRoutes()

    @Serializable
    data object CustomBestieSignupModal : UserSessionRoutes()

    @Serializable
    data object CustomBestieCreation : UserSessionRoutes()

    @Serializable
    data class CustomBestieEdition(val id: String) : UserSessionRoutes()

    @Serializable
    data class CustomBestieStepDetail(val id: String) : UserSessionRoutes()

    @Serializable
    data object CustomBestieCreationModal : UserSessionRoutes()

    @Serializable
    data object CustomBestieEditionModal : UserSessionRoutes()

    @Serializable
    data object Gamification : UserSessionRoutes()

    @Serializable
    data object OverlayLuziaInfo : UserSessionRoutes()

    @Serializable
    data class MajorReward(val rewardId: String) : UserSessionRoutes()

    @Serializable
    data class MessageSources(val payload: String) : UserSessionRoutes()
}
