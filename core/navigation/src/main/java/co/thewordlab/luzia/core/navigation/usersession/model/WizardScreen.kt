package co.thewordlab.luzia.core.navigation.usersession.model

import androidx.annotation.Keep
import co.thewordlab.luzia.foundation.analytics.AnalyticsScreens

@Keep
enum class WizardScreen(val canSkip: Boolean, val screenName: AnalyticsScreens) {
    GREETING(false, ProfileWizardStart),
    PROFILE_IMAGE(false, ProfileWizardPicture),
    PROFILE_BACKGROUND(false, ProfileWizardBackground),
    NAME(false, ProfileWizardNickname),
    USERNAME(false, ProfileWizardUsername),
    BIRTHDAY(false, ProfileWizardBirthday),
    PRONOUN(true, ProfileWizardPronouns),
    STUDENT(false, ProfileWizardStudentStatus),
    SCHOOL(true, ProfileWizardSchool),
    PRIVACY(false, ProfileWizardPrivacy),
    COMPLETED(false, ProfileWizardCompleted)
}

@Keep
enum class WizardScreenV2(val canSkip: <PERSON>olean, val screenName: AnalyticsScreens) {
    NAME(false, ProfileWizardV2Nickname),
    BIRTHDAY(false, ProfileWizardV2Birthday),
    INTENTS(false, ProfileWizardV2Intents),
    SELF_DESCRIPTION(false, ProfileWizardV2SelfDescription),
    PERMISSIONS(true, ProfileWizardV2Permissions)
}

data object ProfileWizardStart : AnalyticsScreens("profile_intro")
data object ProfileWizardPicture : AnalyticsScreens("profile_picture")
data object ProfileWizardBackground : AnalyticsScreens("profile_background")
data object ProfileWizardUsername : AnalyticsScreens("profile_username")
data object ProfileWizardNickname : AnalyticsScreens("profile_nickname")
data object ProfileWizardBirthday : AnalyticsScreens("profile_dob")
data object ProfileWizardPronouns : AnalyticsScreens("profile_pronouns")
data object ProfileWizardStudentStatus : AnalyticsScreens("profile_student_status")
data object ProfileWizardSchool : AnalyticsScreens("profile_school")
data object ProfileWizardPrivacy : AnalyticsScreens("profile_privacy")
data object ProfileWizardCompleted : AnalyticsScreens("profile_success")

data object ProfileWizardV2Nickname :
    AnalyticsScreens("onboarding_step", mapOf(ONBOARDING_STEP to "name"))

data object ProfileWizardV2Birthday :
    AnalyticsScreens("onboarding_step", mapOf(ONBOARDING_STEP to "dob"))

data object ProfileWizardV2Intents :
    AnalyticsScreens("onboarding_step", mapOf(ONBOARDING_STEP to "intents"))

data object ProfileWizardV2Permissions :
    AnalyticsScreens("onboarding_step", mapOf(ONBOARDING_STEP to "notifications"))

data object ProfileWizardV2SelfDescription :
    AnalyticsScreens("onboarding_step", mapOf(ONBOARDING_STEP to "self_description"))

private const val ONBOARDING_STEP = "onboarding_step"
