package co.thewordlab.luzia.core.navigation.common

import androidx.navigation.NavBackStackEntry
import androidx.navigation.toRoute
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.theworldlab.luzia.foundation.design.system.components.bottombar.LandingDestinations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Before
import org.junit.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class RouteCheckerTest {

    @Before
    fun setup() {
        mockkStatic("androidx.navigation.NavBackStackEntryKt")
    }

    @After
    fun tearDown() {
        unmockkStatic("androidx.navigation.NavBackStackEntryKt")
    }

    @Test
    fun `GIVEN entry with home landing destination WHEN isHomeRouteEntry THEN returns true`() {
        val mockEntry = mockk<NavBackStackEntry>()
        val landingRoute = UserSessionRoutes.Landing(destinations = LandingDestinations.HOME)
        every { mockEntry.toRoute<UserSessionRoutes.Landing>() } returns landingRoute
        val result = RouteChecker.isHomeRouteEntry(mockEntry)
        assertTrue(result)
    }

    @Test
    fun `GIVEN entry with non-home landing destination WHEN isHomeRouteEntry THEN returns false`() {
        val mockEntry = mockk<NavBackStackEntry>()
        val landingRoute = UserSessionRoutes.Landing(destinations = LandingDestinations.CHATS)
        every { mockEntry.toRoute<UserSessionRoutes.Landing>() } returns landingRoute
        val result = RouteChecker.isHomeRouteEntry(mockEntry)
        assertFalse(result)
    }

    @Test
    fun `GIVEN entry that throws exception WHEN isHomeRouteEntry THEN returns false`() {
        val mockEntry = mockk<NavBackStackEntry>()
        every { mockEntry.toRoute<UserSessionRoutes.Landing>() } throws RuntimeException("Invalid route")
        val result = RouteChecker.isHomeRouteEntry(mockEntry)
        assertFalse(result)
    }

    @Test
    fun `GIVEN entry with chat detail route WHEN isChatDetailRouteEntry THEN returns true`() {
        val mockEntry = mockk<NavBackStackEntry>()
        val chatDetailRoute = UserSessionRoutes.ChatDetail(
            personalityId = "test",
            highlightId = null,
            isCustomBestie = false
        )
        every { mockEntry.toRoute<UserSessionRoutes.ChatDetail>() } returns chatDetailRoute
        val result = RouteChecker.isChatDetailRouteEntry(mockEntry)
        assertTrue(result)
    }

    @Test
    fun `GIVEN entry that throws exception WHEN isChatDetailRouteEntry THEN returns false`() {
        val mockEntry = mockk<NavBackStackEntry>()
        every { mockEntry.toRoute<UserSessionRoutes.ChatDetail>() } throws RuntimeException("Invalid route")
        val result = RouteChecker.isChatDetailRouteEntry(mockEntry)
        assertFalse(result)
    }

    @Test
    fun `GIVEN navigation history shorter than pattern WHEN matchesEntryPattern THEN returns false`() {
        val entry1 = mockk<NavBackStackEntry>()
        val navigationHistory = listOf(entry1)
        val expectedPattern = listOf<(NavBackStackEntry) -> Boolean>(
            { true },
            { true },
            { true }
        )
        val result = RouteChecker.matchesEntryPattern(navigationHistory, expectedPattern)
        assertFalse(result)
    }

    @Test
    fun `GIVEN navigation history matches pattern exactly WHEN matchesEntryPattern THEN returns true`() {
        val entry1 = mockk<NavBackStackEntry>()
        val entry2 = mockk<NavBackStackEntry>()
        val entry3 = mockk<NavBackStackEntry>()
        val navigationHistory = listOf(entry1, entry2, entry3)
        val expectedPattern = listOf<(NavBackStackEntry) -> Boolean>(
            { it == entry1 },
            { it == entry2 },
            { it == entry3 }
        )
        val result = RouteChecker.matchesEntryPattern(navigationHistory, expectedPattern)
        assertTrue(result)
    }

    @Test
    fun `GIVEN history longer than pattern WHEN matchesEntryPattern THEN uses last entries and returns true`() {
        val entry1 = mockk<NavBackStackEntry>()
        val entry2 = mockk<NavBackStackEntry>()
        val entry3 = mockk<NavBackStackEntry>()
        val entry4 = mockk<NavBackStackEntry>()
        val navigationHistory = listOf(entry1, entry2, entry3, entry4)
        val expectedPattern = listOf<(NavBackStackEntry) -> Boolean>(
            { it == entry3 },
            { it == entry4 }
        )
        val result = RouteChecker.matchesEntryPattern(navigationHistory, expectedPattern)
        assertTrue(result)
    }

    @Test
    fun `GIVEN navigation history does not match pattern WHEN matchesEntryPattern THEN returns false`() {
        val entry1 = mockk<NavBackStackEntry>()
        val entry2 = mockk<NavBackStackEntry>()
        val navigationHistory = listOf(entry1, entry2)
        val expectedPattern = listOf<(NavBackStackEntry) -> Boolean>(
            { it == entry1 },
            { false } // This will always fail
        )
        val result = RouteChecker.matchesEntryPattern(navigationHistory, expectedPattern)
        assertFalse(result)
    }

    @Test
    fun `GIVEN empty navigation history WHEN matchesEntryPattern with non-empty pattern THEN returns false`() {
        val navigationHistory = emptyList<NavBackStackEntry>()
        val expectedPattern = listOf<(NavBackStackEntry) -> Boolean>({ true })
        val result = RouteChecker.matchesEntryPattern(navigationHistory, expectedPattern)
        assertFalse(result)
    }

    @Test
    fun `GIVEN empty pattern WHEN matchesEntryPattern THEN returns true`() {
        val entry1 = mockk<NavBackStackEntry>()
        val navigationHistory = listOf(entry1)
        val expectedPattern = emptyList<(NavBackStackEntry) -> Boolean>()
        val result = RouteChecker.matchesEntryPattern(navigationHistory, expectedPattern)
        assertTrue(result)
    }

    @Test
    fun `GIVEN realistic home to chat to home pattern WHEN matchesEntryPattern THEN returns true`() {
        val homeEntry1 = mockk<NavBackStackEntry>()
        val chatEntry = mockk<NavBackStackEntry>()
        val homeEntry2 = mockk<NavBackStackEntry>()
        val navigationHistory = listOf(homeEntry1, chatEntry, homeEntry2)
        every { homeEntry1.toRoute<UserSessionRoutes.Landing>() } returns
            UserSessionRoutes.Landing(destinations = LandingDestinations.HOME)
        every { homeEntry2.toRoute<UserSessionRoutes.Landing>() } returns
            UserSessionRoutes.Landing(destinations = LandingDestinations.HOME)
        every { chatEntry.toRoute<UserSessionRoutes.ChatDetail>() } returns
            UserSessionRoutes.ChatDetail("test", null, false)

        every { homeEntry1.toRoute<UserSessionRoutes.ChatDetail>() } throws RuntimeException()
        every { homeEntry2.toRoute<UserSessionRoutes.ChatDetail>() } throws RuntimeException()
        every { chatEntry.toRoute<UserSessionRoutes.Landing>() } throws RuntimeException()
        val expectedPattern = listOf(
            RouteChecker::isHomeRouteEntry,
            RouteChecker::isChatDetailRouteEntry,
            RouteChecker::isHomeRouteEntry
        )
        val result = RouteChecker.matchesEntryPattern(navigationHistory, expectedPattern)
        assertTrue(result)
    }

    @Test
    fun `GIVEN realistic home to chat pattern WHEN matchesEntryPattern THEN returns true`() {
        val homeEntry = mockk<NavBackStackEntry>()
        val chatEntry = mockk<NavBackStackEntry>()
        val navigationHistory = listOf(homeEntry, chatEntry)

        every { homeEntry.toRoute<UserSessionRoutes.Landing>() } returns
            UserSessionRoutes.Landing(destinations = LandingDestinations.HOME)
        every { chatEntry.toRoute<UserSessionRoutes.ChatDetail>() } returns
            UserSessionRoutes.ChatDetail("test", null, false)

        every { homeEntry.toRoute<UserSessionRoutes.ChatDetail>() } throws RuntimeException()
        every { chatEntry.toRoute<UserSessionRoutes.Landing>() } throws RuntimeException()

        val expectedPattern = listOf(
            RouteChecker::isHomeRouteEntry,
            RouteChecker::isChatDetailRouteEntry
        )
        val result = RouteChecker.matchesEntryPattern(navigationHistory, expectedPattern)
        assertTrue(result)
    }
}
