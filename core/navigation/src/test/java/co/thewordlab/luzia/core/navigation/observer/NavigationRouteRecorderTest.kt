package co.thewordlab.luzia.core.navigation.observer

import androidx.navigation.NavController
import co.thewordlab.luzia.foundation.testing.dispatchers.MainDispatcherRule
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@ExperimentalCoroutinesApi
class NavigationRouteRecorderTest {

    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()

    private lateinit var navigationRouteRecorder: NavigationRouteRecorder
    private lateinit var mockNavController: NavController

    @Before
    fun setUp() {
        navigationRouteRecorder = NavigationRouteRecorder()
        mockNavController = mockk {
            every { currentBackStackEntryFlow } returns emptyFlow()
        }
    }

    @Test
    fun `GIVEN new recorder WHEN created THEN navigation history is empty`() = runTest {
        val recentEntries = navigationRouteRecorder.getRecentEntries()

        assertTrue(recentEntries.isEmpty())
    }

    @Test
    fun `GIVEN recorder WHEN getRecentEntries with count THEN returns correct number`() = runTest {
        val recentEntries = navigationRouteRecorder.getRecentEntries(5)

        assertTrue(recentEntries.isEmpty())
        assertEquals(0, recentEntries.size)
    }

    @Test
    fun `GIVEN empty history WHEN getRecentEntries with large count THEN returns empty list`() = runTest {
        val recentEntries = navigationRouteRecorder.getRecentEntries(10)

        assertTrue(recentEntries.isEmpty())
    }

    @Test
    fun `GIVEN recorder WHEN getRecentEntries with zero count THEN returns empty list`() = runTest {
        val recentEntries = navigationRouteRecorder.getRecentEntries(0)

        assertTrue(recentEntries.isEmpty())
    }

    @Test
    fun `GIVEN recorder WHEN getRecentEntries with negative count THEN returns empty list`() = runTest {
        val recentEntries = navigationRouteRecorder.getRecentEntries(-1)

        assertTrue(recentEntries.isEmpty())
    }

    @Test
    fun `GIVEN recorder WHEN getRecentEntries with default count THEN returns empty list for new instance`() = runTest {
        val recentEntries = navigationRouteRecorder.getRecentEntries()

        assertEquals(0, recentEntries.size)
        assertTrue(recentEntries.isEmpty())
    }

    @Test
    fun `GIVEN NavigationRouteRecorder WHEN instantiated THEN has proper activity scope annotation`() {
        val recorder = NavigationRouteRecorder()

        val entries = recorder.getRecentEntries()

        assertTrue(entries.isEmpty())
    }
}
