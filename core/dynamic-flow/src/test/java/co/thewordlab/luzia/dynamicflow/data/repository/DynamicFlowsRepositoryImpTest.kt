package co.thewordlab.luzia.dynamicflow.data.repository

import co.thewordlab.luzia.dynamicflow.data.api.DynamicFlowsApi
import co.thewordlab.luzia.dynamicflow.data.models.AvailableDynamicFlowDTO
import co.thewordlab.luzia.dynamicflow.data.models.AvailableDynamicFlowsDTO
import co.thewordlab.luzia.dynamicflow.data.models.DynamicFlowDTO
import co.thewordlab.luzia.dynamicflow.domain.models.DynamicFlowsErrors
import co.thewordlab.luzia.dynamicflow.domain.models.mapToModel
import co.thewordlab.luzia.foundation.networking.model.CommonErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import com.slack.eithernet.ApiResult
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import java.io.IOException

@Suppress("MaxLineLength")
class DynamicFlowsRepositoryImpTest {

    @MockK
    private lateinit var dynamicFlowsApi: DynamicFlowsApi

    private lateinit var sut: DynamicFlowsRepositoryImp

    @Before
    fun setUp() {
        MockKAnnotations.init(this, relaxed = true)
        sut = DynamicFlowsRepositoryImp(dynamicFlowsApi)
    }

    @Test
    fun `given successful api call when getAvailableFlows then returns mapped list of flows`() = runTest {
        val flowId = "custom-besties"
        val mockDto = AvailableDynamicFlowsDTO(listOf(AvailableDynamicFlowDTO(flowId)))
        coEvery { dynamicFlowsApi.getAvailableFlows() } returns ApiResult.success(mockDto)

        val result = sut.getAvailableFlows()

        assertTrue(result is ResultOf.Success)
        val successResult = result as ResultOf.Success
        assertEquals(1, successResult.data.size)
        assertEquals(flowId, successResult.data[0])
    }

    @Test
    fun `given successful api call with null flowId when getAvailableFlows then returns mapped list with null`() = runTest {
        val mockDto = AvailableDynamicFlowsDTO(listOf(AvailableDynamicFlowDTO(null)))
        coEvery { dynamicFlowsApi.getAvailableFlows() } returns ApiResult.success(mockDto)

        val result = sut.getAvailableFlows()

        assertTrue(result is ResultOf.Success)
        val successResult = result as ResultOf.Success
        assertEquals(1, successResult.data.size)
        assertEquals(null, successResult.data[0])
    }

    @Test
    fun `given successful api call with empty list when getAvailableFlows then returns empty list`() = runTest {
        val mockDto = AvailableDynamicFlowsDTO(emptyList())
        coEvery { dynamicFlowsApi.getAvailableFlows() } returns ApiResult.success(mockDto)

        val result = sut.getAvailableFlows()

        assertTrue(result is ResultOf.Success)
        val successResult = result as ResultOf.Success
        assertEquals(0, successResult.data.size)
    }

    @Test
    fun `given network failure when getAvailableFlows then returns network error`() = runTest {
        coEvery { dynamicFlowsApi.getAvailableFlows() } returns ApiResult.networkFailure(IOException())

        val result = sut.getAvailableFlows()

        assertTrue(result is ResultOf.Failure)
        val failureResult = result as ResultOf.Failure
        assertTrue(failureResult.error is DynamicFlowsErrors.CommonError)
        assertEquals(CommonErrors.NetworkError, (failureResult.error as DynamicFlowsErrors.CommonError).error)
    }

    @Test
    fun `given api failure when getAvailableFlows then returns network error`() = runTest {
        coEvery { dynamicFlowsApi.getAvailableFlows() } returns ApiResult.apiFailure(null)

        val result = sut.getAvailableFlows()

        assertTrue(result is ResultOf.Failure)
        val failureResult = result as ResultOf.Failure
        assertTrue(failureResult.error is DynamicFlowsErrors.CommonError)
        assertEquals(CommonErrors.NetworkError, (failureResult.error as DynamicFlowsErrors.CommonError).error)
    }

    @Test
    fun `given http failure when getAvailableFlows then returns network error`() = runTest {
        coEvery { dynamicFlowsApi.getAvailableFlows() } returns ApiResult.httpFailure(404, null)

        val result = sut.getAvailableFlows()

        assertTrue(result is ResultOf.Failure)
        val failureResult = result as ResultOf.Failure
        assertTrue(failureResult.error is DynamicFlowsErrors.CommonError)
        assertEquals(CommonErrors.NetworkError, (failureResult.error as DynamicFlowsErrors.CommonError).error)
    }

    @Test
    fun `given unknown failure when getAvailableFlows then returns network error`() = runTest {
        coEvery { dynamicFlowsApi.getAvailableFlows() } returns ApiResult.unknownFailure(RuntimeException())

        val result = sut.getAvailableFlows()

        assertTrue(result is ResultOf.Failure)
        val failureResult = result as ResultOf.Failure
        assertTrue(failureResult.error is DynamicFlowsErrors.CommonError)
        assertEquals(CommonErrors.NetworkError, (failureResult.error as DynamicFlowsErrors.CommonError).error)
    }

    @Test
    fun `given successful api call when getFlow then returns mapped flow model`() = runTest {
        val flowId = "custom-besties"
        val mockDto = mockk<DynamicFlowDTO>(relaxed = true)
        val expectedModel = mockDto.mapToModel()
        coEvery { dynamicFlowsApi.getFlow(flowId) } returns ApiResult.success(mockDto)

        val result = sut.getFlow(flowId)

        assertTrue(result is ResultOf.Success)
        val successResult = result as ResultOf.Success
        assertEquals(expectedModel, successResult.data)
    }

    @Test
    fun `given successful api call with custom flow id when getFlow then returns mapped flow model`() = runTest {
        val flowId = "custom-besties-a"
        val mockDto = mockk<DynamicFlowDTO>(relaxed = true)
        val expectedModel = mockDto.mapToModel()
        coEvery { dynamicFlowsApi.getFlow(flowId) } returns ApiResult.success(mockDto)

        val result = sut.getFlow(flowId)

        assertTrue(result is ResultOf.Success)
        val successResult = result as ResultOf.Success
        assertEquals(expectedModel, successResult.data)
    }

    @Test
    fun `given network failure when getFlow then returns network error`() = runTest {
        val flowId = "custom-besties"
        coEvery { dynamicFlowsApi.getFlow(flowId) } returns ApiResult.networkFailure(IOException())

        val result = sut.getFlow(flowId)

        assertTrue(result is ResultOf.Failure)
        val failureResult = result as ResultOf.Failure
        assertTrue(failureResult.error is DynamicFlowsErrors.CommonError)
        assertEquals(CommonErrors.NetworkError, (failureResult.error as DynamicFlowsErrors.CommonError).error)
    }

    @Test
    fun `given api failure when getFlow then returns network error`() = runTest {
        val flowId = "custom-besties"
        coEvery { dynamicFlowsApi.getFlow(flowId) } returns ApiResult.apiFailure(null)

        val result = sut.getFlow(flowId)

        assertTrue(result is ResultOf.Failure)
        val failureResult = result as ResultOf.Failure
        assertTrue(failureResult.error is DynamicFlowsErrors.CommonError)
        assertEquals(CommonErrors.NetworkError, (failureResult.error as DynamicFlowsErrors.CommonError).error)
    }

    @Test
    fun `given http failure when getFlow then returns network error`() = runTest {
        val flowId = "custom-besties"
        coEvery { dynamicFlowsApi.getFlow(flowId) } returns ApiResult.httpFailure(404, null)

        val result = sut.getFlow(flowId)

        assertTrue(result is ResultOf.Failure)
        val failureResult = result as ResultOf.Failure
        assertTrue(failureResult.error is DynamicFlowsErrors.CommonError)
        assertEquals(CommonErrors.NetworkError, (failureResult.error as DynamicFlowsErrors.CommonError).error)
    }

    @Test
    fun `given unknown failure when getFlow then returns network error`() = runTest {
        val flowId = "custom-besties"
        coEvery { dynamicFlowsApi.getFlow(flowId) } returns ApiResult.unknownFailure(RuntimeException())

        val result = sut.getFlow(flowId)

        assertTrue(result is ResultOf.Failure)
        val failureResult = result as ResultOf.Failure
        assertTrue(failureResult.error is DynamicFlowsErrors.CommonError)
        assertEquals(CommonErrors.NetworkError, (failureResult.error as DynamicFlowsErrors.CommonError).error)
    }
}
