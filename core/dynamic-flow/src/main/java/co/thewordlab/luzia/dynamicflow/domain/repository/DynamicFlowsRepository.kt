package co.thewordlab.luzia.dynamicflow.domain.repository

import co.thewordlab.luzia.dynamicflow.domain.models.DynamicFlowsErrors
import co.thewordlab.luzia.dynamicflow.domain.models.DynamicFlowsModel
import co.thewordlab.luzia.foundation.networking.model.ResultOf

interface DynamicFlowsRepository {
    suspend fun getAvailableFlows(): ResultOf<List<String?>, DynamicFlowsErrors>
    suspend fun getFlow(id: String): ResultOf<DynamicFlowsModel?, DynamicFlowsErrors>
}
