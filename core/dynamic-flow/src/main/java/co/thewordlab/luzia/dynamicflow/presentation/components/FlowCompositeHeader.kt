package co.thewordlab.luzia.dynamicflow.presentation.components

import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.PreviewLightDark
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun FlowCompositeHeader(
    title: String,
    subtitle: String
) {
    LuziaText(
        modifier = Modifier.padding(horizontal = Spacing.X16.dp),
        text = title,
        style = LuziaTheme.typography.headlines.h4,
        color = LuziaTheme.palette.text.primary,
        textAlign = TextAlign.Center
    )
    Spacing.X8.Vertical()
    LuziaText(
        modifier = Modifier.padding(horizontal = Spacing.X16.dp),
        text = subtitle,
        style = LuziaTheme.typography.body.regular.small,
        color = LuziaTheme.palette.text.secondary,
        textAlign = TextAlign.Center
    )
}

@PreviewLightDark
@Composable
private fun Preview() {
    LuziaTheme {
        FlowCompositeHeader(
            title = "_Create your own Bestie",
            subtitle = "_Personalize a unique virtual friend and discover everything " +
                "they can do for you.",
        )
    }
}
