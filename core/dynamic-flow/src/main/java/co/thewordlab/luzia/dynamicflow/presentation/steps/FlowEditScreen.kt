package co.thewordlab.luzia.dynamicflow.presentation.steps

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import co.thewordlab.luzia.dynamicflow.presentation.factory.DynamicFlowViewModel
import co.thewordlab.luzia.dynamicflow.presentation.factory.FlowScreenConfig
import co.thewordlab.luzia.dynamicflow.presentation.factory.FlowStructure
import co.thewordlab.luzia.dynamicflow.presentation.factory.FlowViewActions
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme

@Composable
fun FlowEditScreen(
    editData: FlowScreenConfig.Summary?,
    onViewActions: (FlowViewActions) -> Unit
) {
    val viewModel: DynamicFlowViewModel = hiltViewModel()
    editData?.let {
        val screen = viewModel.dynamicFlowManager.buildScreenContent(it)
        ContentScreen(screen)
    } ?: onViewActions(FlowViewActions.NavigateBack)
}

@Composable
private fun ContentScreen(screen: FlowStructure) {
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        containerColor = LuziaTheme.palette.surface.background
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            screen.TopNavigation()
            Box(modifier = Modifier.weight(1f)) { screen.Content() }
            screen.BottomNavigation()
        }
    }
}
