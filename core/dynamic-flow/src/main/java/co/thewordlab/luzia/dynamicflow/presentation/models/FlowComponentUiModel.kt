package co.thewordlab.luzia.dynamicflow.presentation.models

private const val NO_ID = "no_id"

data class CommentUiModel(val id: String, val title: String, val description: String)
data class OptionModel(val id: String, val value: String)

sealed class FlowComponentUiModel(val id: String) {
    data class Header(val title: String) : FlowComponentUiModel(NO_ID)

    data class Image(val url: String) : FlowComponentUiModel(NO_ID)

    data class CompositeHeader(
        val title: String,
        val description: String
    ) : FlowComponentUiModel(NO_ID)

    data class FeatureIntro(
        val title: String,
        val subtitle: String,
        val imageUrl: String
    ) : FlowComponentUiModel(NO_ID)

    data class Input(
        val inputId: String,
        val hint: String,
        val maxLength: Int,
        val maxLines: Int
    ) : FlowComponentUiModel(inputId)

    data class SingleChip(
        val singleChipId: String,
        val title: String,
        val options: List<OptionModel>
    ) : FlowComponentUiModel(singleChipId)

    data class MultiChip(
        val multiChipId: String,
        val maxSelection: Int,
        val options: List<OptionModel>
    ) : FlowComponentUiModel(multiChipId)

    data class Avatar(
        val avatarId: String,
        val title: String,
        val description: String,
        val options: List<OptionModel>
    ) : FlowComponentUiModel(avatarId)

    data class SingleComment(
        val singleCommentId: String,
        val options: List<CommentUiModel>
    ) : FlowComponentUiModel(singleCommentId)
}
