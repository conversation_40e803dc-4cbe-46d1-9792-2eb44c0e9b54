package co.thewordlab.luzia.dynamicflow.presentation.factory

import co.thewordlab.luzia.dynamicflow.DynamicFlowBackClicked
import co.thewordlab.luzia.dynamicflow.DynamicFlowCancelClicked
import co.thewordlab.luzia.dynamicflow.DynamicFlowIntroCloseClicked
import co.thewordlab.luzia.dynamicflow.DynamicFlowNextClicked
import co.thewordlab.luzia.dynamicflow.DynamicFlowSkipClicked
import co.thewordlab.luzia.dynamicflow.DynamicFlowSubmitClicked
import co.thewordlab.luzia.dynamicflow.DynamicFlowSummaryOptionClicked
import co.thewordlab.luzia.dynamicflow.convertFlowStateToMap
import co.thewordlab.luzia.dynamicflow.domain.models.DynamicFlowComponentAction
import co.thewordlab.luzia.dynamicflow.domain.models.DynamicFlowComponentType
import co.thewordlab.luzia.dynamicflow.domain.models.DynamicFlowSummaryExtraData
import co.thewordlab.luzia.dynamicflow.domain.models.DynamicFlowsModel
import co.thewordlab.luzia.dynamicflow.domain.models.FlowComponent
import co.thewordlab.luzia.dynamicflow.domain.models.FlowComponentItem
import co.thewordlab.luzia.dynamicflow.domain.models.FlowHeader
import co.thewordlab.luzia.dynamicflow.domain.models.FlowProgress
import co.thewordlab.luzia.dynamicflow.domain.models.FlowStep
import co.thewordlab.luzia.dynamicflow.presentation.models.CommentUiModel
import co.thewordlab.luzia.dynamicflow.presentation.models.FlowComponentState
import co.thewordlab.luzia.dynamicflow.presentation.models.FlowComponentUiModel
import co.thewordlab.luzia.dynamicflow.presentation.models.FlowStepUiModel
import co.thewordlab.luzia.dynamicflow.presentation.models.FlowSummaryUiModel
import co.thewordlab.luzia.dynamicflow.presentation.models.OptionModel
import co.thewordlab.luzia.dynamicflow.presentation.steps.FlowStepScreen
import co.thewordlab.luzia.dynamicflow.presentation.steps.FlowSummaryScreen
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.ButtonState
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DynamicFlowManager @Inject constructor(
    private val analytics: Analytics
) {

    private var flowScreenConfig: List<FlowScreenConfig> = emptyList()

    fun buildData(
        flowType: String,
        data: DynamicFlowsModel?,
        extraData: DynamicFlowSummaryExtraData,
        onViewAction: (FlowViewActions) -> Unit
    ): List<FlowScreenConfig> {
        data ?: return emptyList()
        flowScreenConfig = data.steps.mapIndexed { index, step ->
            when {
                index >= data.steps.lastIndex -> buildSummaryData(
                    flowType,
                    step,
                    extraData,
                    onViewAction
                )

                else -> buildStepData(flowType, step, index, onViewAction)
            }
        }
        return flowScreenConfig
    }

    fun buildInitialLocalState(): List<FlowComponentState> {
        val items = mutableListOf<FlowComponentState>()
        flowScreenConfig.filterIsInstance<FlowScreenConfig.Step>().forEach { step ->
            step.content.components.forEach { component ->
                if (isValidComponentForLocalState(component)) {
                    items.add(
                        FlowComponentState(
                            stepId = step.content.id,
                            componentId = component.id,
                            componentType = mapComponentToType(component),
                            value = "",
                            valueId = "",
                            errorMessage = null,
                            needsValidation = step.topNavigation.skipAction == null &&
                                step.content.id != INITIAL_STEP_ID
                        )
                    )
                }
            }
        }
        return items
    }

    private fun isValidComponentForLocalState(component: FlowComponentUiModel) = when (component) {
        is FlowComponentUiModel.CompositeHeader,
        is FlowComponentUiModel.FeatureIntro,
        is FlowComponentUiModel.Header,
        is FlowComponentUiModel.Image -> false

        is FlowComponentUiModel.Avatar,
        is FlowComponentUiModel.Input,
        is FlowComponentUiModel.MultiChip,
        is FlowComponentUiModel.SingleChip,
        is FlowComponentUiModel.SingleComment -> true
    }

    private fun mapComponentToType(component: FlowComponentUiModel) = when (component) {
        is FlowComponentUiModel.CompositeHeader -> DynamicFlowComponentType.COMPOSITE_HEADER
        is FlowComponentUiModel.FeatureIntro -> DynamicFlowComponentType.FLOW_INTRO
        is FlowComponentUiModel.Header -> DynamicFlowComponentType.HEADER
        is FlowComponentUiModel.Image -> DynamicFlowComponentType.IMAGE
        is FlowComponentUiModel.Avatar -> DynamicFlowComponentType.CAROUSEL
        is FlowComponentUiModel.Input -> DynamicFlowComponentType.TEXT_INPUT
        is FlowComponentUiModel.MultiChip -> DynamicFlowComponentType.SELECTOR_BUTTONS
        is FlowComponentUiModel.SingleChip -> DynamicFlowComponentType.SELECTOR_BUTTONS
        is FlowComponentUiModel.SingleComment -> DynamicFlowComponentType.EXTENDED_SELECTOR_BUTTONS
    }

    fun getStepScreenData(stepId: String): FlowScreenConfig? =
        flowScreenConfig.filterIsInstance<FlowScreenConfig.Step>()
            .firstOrNull { if (it.data is FlowStepUiModel) it.data.id == stepId else false }

    fun getSummaryScreenData(): FlowScreenConfig.Summary? =
        flowScreenConfig.filterIsInstance<FlowScreenConfig.Summary>().firstOrNull()

    fun buildScreenContent(data: FlowScreenConfig): FlowStructure =
        when (data) {
            is FlowScreenConfig.Step -> FlowStepScreen(analytics, data)
            is FlowScreenConfig.Summary -> FlowSummaryScreen(analytics, data, flowScreenConfig)
        }

    private fun buildStepData(
        flowType: String,
        data: FlowStep,
        index: Int,
        onViewAction: (FlowViewActions) -> Unit
    ) = FlowScreenConfig.Step(
        top = buildTopNavigation(flowType, data.stepId, data.header, index == 0, onViewAction),
        bottom = buildBottomNavigation(
            flowType,
            data.stepId,
            data.progress,
            data.footer,
            onViewAction
        ),
        content = buildContentStepData(stepId = data.stepId, flowType = flowType, data = data.body)
    )

    private fun buildSummaryData(
        flowType: String,
        data: FlowStep,
        extraData: DynamicFlowSummaryExtraData,
        onViewAction: (FlowViewActions) -> Unit
    ) = FlowScreenConfig.Summary(
        top = buildTopNavigation(flowType, data.stepId, data.header, false, onViewAction),
        bottom = buildBottomNavigation(
            flowType,
            data.stepId,
            data.progress,
            data.footer,
            onViewAction
        ),
        content = buildContentSummaryData(flowType, extraData, data.body, onViewAction)
    )

    private fun buildTopNavigation(
        flowType: String,
        stepId: String,
        data: FlowHeader?,
        isFirstStep: Boolean,
        onViewAction: (FlowViewActions) -> Unit
    ) = NavigationTopData(
        title = data?.title.orEmpty(),
        cancelAction = {
            analytics.trackAction(DynamicFlowCancelClicked(stepId, flowType))
            onViewAction(FlowViewActions.OnCancelFlow)
        }.takeIf { data?.navigation?.cancelEnabled == true },
        backAction = {
            analytics.trackAction(DynamicFlowBackClicked(stepId, flowType))
            onViewAction(FlowViewActions.NavigateBack)
        }.takeIf { !isFirstStep && data?.navigation?.backEnabled == true },
        closeAction = {
            analytics.trackAction(DynamicFlowIntroCloseClicked(flowType))
            onViewAction(FlowViewActions.NavigateBack)
        }.takeIf { isFirstStep && data?.navigation?.backEnabled == true },
        skipAction = {
            analytics.trackAction(DynamicFlowSkipClicked(stepId, flowType))
            onViewAction(FlowViewActions.NavigateForward)
        }.takeIf { data?.navigation?.skipEnabled == true }
    )

    private fun buildBottomNavigation(
        flowType: String,
        stepId: String,
        flowProgress: FlowProgress?,
        components: List<FlowComponent>,
        onViewAction: (FlowViewActions) -> Unit
    ): NavigationBottomData {
        val button = components.filterIsInstance<FlowComponent.Button>().firstOrNull()
        return NavigationBottomData(
            progress = flowProgress?.let { progress ->
                progress.total.takeIf { total -> total != 0 }
                    ?.let { (progress.current * HUNDRED) / it }
            }?.toFloat(),
            buttonText = button?.title.orEmpty(),
            buttonEnabled = ButtonState.ENABLED,
            buttonAction = {
                if (button?.action == DynamicFlowComponentAction.SUBMIT) {
                    analytics.trackAction(DynamicFlowSubmitClicked(flowType))
                    onViewAction(FlowViewActions.SubmitFlow)
                } else {
                    analytics.trackAction(DynamicFlowNextClicked(stepId, flowType))
                    onViewAction(FlowViewActions.NavigateForward)
                }
            }
        )
    }

    private fun buildContentStepData(
        stepId: String,
        flowType: String,
        data: List<FlowComponent>
    ) = FlowStepUiModel(
        id = stepId,
        flowType = flowType,
        components = buildBodyComponent(data)
    )

    private fun buildContentSummaryData(
        flowType: String,
        extraData: DynamicFlowSummaryExtraData,
        data: List<FlowComponent>,
        onViewAction: (FlowViewActions) -> Unit
    ) =
        data.filterIsInstance<FlowComponent.SummarySelector>().map {
            FlowSummaryUiModel(
                flowType = flowType,
                extraData = extraData,
                data = it.items,
                onNavigateTo = { stepId, components ->
                    analytics.trackAction(
                        DynamicFlowSummaryOptionClicked(convertFlowStateToMap(components), flowType)
                    )
                    onViewAction(FlowViewActions.NavigateToStepDetail(stepId))
                },
            )
        }.first()

    private fun buildBodyComponent(data: List<FlowComponent>) =
        data.mapNotNull { component ->
            when (component) {
                is FlowComponent.TextInput ->
                    FlowComponentUiModel.Input(
                        inputId = component.id,
                        hint = component.placeholder,
                        maxLength = component.maxLength,
                        maxLines = if (data.size == 2) {
                            MAX_LINES_FOR_SINGLE_INPUT
                        } else {
                            1
                        }
                    )

                is FlowComponent.Carousel -> FlowComponentUiModel.Avatar(
                    avatarId = component.id,
                    title = component.title,
                    description = component.description,
                    options = component.items
                        .filterIsInstance<FlowComponentItem.CarouselItem>()
                        .map { OptionModel(it.id, it.imageUrl) },
                )

                is FlowComponent.CompositeHeader ->
                    FlowComponentUiModel.CompositeHeader(component.title, component.subTitle)

                is FlowComponent.ExtendedSelectorButtons ->
                    FlowComponentUiModel.SingleComment(
                        singleCommentId = component.id,
                        options = component.items
                            .filterIsInstance<FlowComponentItem.SelectorButton>()
                            .map { CommentUiModel(it.id, it.title, it.subtitle.orEmpty()) }
                    )

                is FlowComponent.Header ->
                    FlowComponentUiModel.Header(component.content)

                is FlowComponent.Image -> FlowComponentUiModel.Image(component.url)
                is FlowComponent.SelectorButtons ->
                    if (component.title.isNotEmpty()) {
                        FlowComponentUiModel.SingleChip(
                            singleChipId = component.id,
                            title = component.title,
                            options = component.items
                                .filterIsInstance<FlowComponentItem.SelectorButton>()
                                .map { OptionModel(it.id, it.title) }
                        )
                    } else {
                        FlowComponentUiModel.MultiChip(
                            multiChipId = component.id,
                            maxSelection = component.maxSelections,
                            options = component.items
                                .filterIsInstance<FlowComponentItem.SelectorButton>()
                                .map { OptionModel(it.id, it.title) }
                        )
                    }

                is FlowComponent.FeatureIntro ->
                    FlowComponentUiModel.FeatureIntro(
                        title = component.title,
                        subtitle = component.subheader,
                        imageUrl = component.mediaUrl
                    )

                is FlowComponent.SummarySelector,
                is FlowComponent.Button -> null
            }
        }

    private companion object {
        const val MAX_LINES_FOR_SINGLE_INPUT = 10
        const val HUNDRED = 100
        const val INITIAL_STEP_ID = "s0"
    }
}
