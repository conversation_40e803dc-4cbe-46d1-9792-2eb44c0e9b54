package co.thewordlab.luzia.dynamicflow.domain.models

data class DynamicFlowsModel(
    val flowId: String = "",
    val flowVersion: String = "",
    val steps: List<FlowStep> = emptyList(),
)

data class FlowStep(
    val stepId: String = "",
    val progress: FlowProgress? = null,
    val header: FlowHeader? = null,
    val body: List<FlowComponent> = emptyList(),
    val footer: List<FlowComponent> = emptyList(),
)

data class FlowProgress(
    val current: Int = 0,
    val total: Int = 0,
)

data class FlowHeader(
    val title: String = "",
    val navigation: FlowNavigation? = null,
)

data class FlowNavigation(
    val backEnabled: Boolean = false,
    val skipEnabled: Boolean = false,
    val cancelEnabled: Boolean = false,
)

sealed class FlowComponent {
    data class FeatureIntro(
        val title: String,
        val subheader: String,
        val mediaUrl: String
    ) : FlowComponent()

    data class Header(val content: String) : FlowComponent()
    data class CompositeHeader(
        val title: String,
        val subTitle: String
    ) : FlowComponent()

    data class TextInput(
        val id: String,
        val placeholder: String,
        val maxLength: Int
    ) : FlowComponent()

    data class SelectorButtons(
        val id: String,
        val title: String,
        val items: List<FlowComponentItem>,
        val maxSelections: Int
    ) : FlowComponent()

    data class ExtendedSelectorButtons(
        val id: String,
        val items: List<FlowComponentItem>
    ) : FlowComponent()

    data class Carousel(
        val id: String,
        val title: String,
        val description: String,
        val items: List<FlowComponentItem>,
        val maxSelections: Int
    ) : FlowComponent()

    data class Image(val url: String) : FlowComponent()

    data class Button(
        val id: String,
        val title: String,
        val action: DynamicFlowComponentAction? = null
    ) : FlowComponent()

    data class SummarySelector(
        val id: String,
        val items: List<String>
    ) : FlowComponent()
}

sealed class FlowComponentItem {
    data class SelectorButton(
        val id: String,
        val title: String,
        val subtitle: String? = null
    ) : FlowComponentItem()

    data class CarouselItem(val id: String, val imageUrl: String) : FlowComponentItem()
}
