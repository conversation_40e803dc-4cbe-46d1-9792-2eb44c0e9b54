package co.thewordlab.luzia.dynamicflow.presentation.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.PreviewLightDark
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import coil3.compose.AsyncImage

@Composable
fun FlowFeatureIntro(
    imageUrl: String,
    title: String,
    subtitle: String
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = Spacing.X16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        AsyncImage(
            modifier = Modifier
                .size(IconSizes.X200.dp)
                .aspectRatio(1f)
                .clip(CircleShape),
            model = imageUrl,
            contentScale = ContentScale.Crop,
            contentDescription = null
        )
        Spacing.X16.Vertical()
        LuziaText(
            text = title,
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary,
            textAlign = TextAlign.Center
        )
        Spacing.X16.Vertical()
        LuziaText(
            text = subtitle,
            style = LuziaTheme.typography.body.regular.default,
            color = LuziaTheme.palette.text.secondary,
            textAlign = TextAlign.Center
        )
    }
}

@PreviewLightDark
@Composable
private fun Preview() {
    LuziaTheme {
        FlowFeatureIntro(
            title = "_Create your own Bestie",
            subtitle = "_Personalize a unique virtual friend and discover everything " +
                "they can do for you.",
            imageUrl = "https://cdn.prod.website-files.com/64f85dc3e20068093f54e701/" +
                "6710f88e4201df8a7c12676b_luzia%20icon.png"
        )
    }
}
