package co.thewordlab.luzia.dynamicflow.presentation.steps

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.PreviewLightDark
import co.thewordlab.luzia.dynamicflow.presentation.factory.FlowStructure
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import co.thewordlab.luzia.foundation.localization.R as localizationR

class FlowLoadingScreen(
    @StringRes val loadingTextRes: Int,
    val lottieAnimRes: String
) : FlowStructure {

    @Composable
    override fun TopNavigation() {
        DO_NOTHING
    }

    @Composable
    override fun BottomNavigation() {
        DO_NOTHING
    }

    @Composable
    override fun Content() {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(Spacing.X16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            LuziaText(
                modifier = Modifier.fillMaxWidth().padding(top = Spacing.X128.dp),
                text = stringResource(loadingTextRes),
                style = LuziaTheme.typography.headlines.h4,
                color = LuziaTheme.palette.text.primary,
                textAlign = TextAlign.Center
            )
            Box(modifier = Modifier.fillMaxWidth()) {
                val composition by rememberLottieComposition(
                    LottieCompositionSpec.Asset(
                        lottieAnimRes
                    )
                )
                LottieAnimation(
                    modifier = Modifier.fillMaxWidth(),
                    contentScale = ContentScale.FillWidth,
                    composition = composition,
                    iterations = LottieConstants.IterateForever
                )
            }
        }
    }
}

@PreviewLightDark
@Composable
private fun Preview() {
    LuziaTheme {
        Column {
            val preview = FlowLoadingScreen(
                loadingTextRes = localizationR.string.custom_bestie_creating_title,
                lottieAnimRes = "custom_bestie_creation.lottie"
            )
            preview.TopNavigation()
            preview.Content()
            preview.BottomNavigation()
        }
    }
}
