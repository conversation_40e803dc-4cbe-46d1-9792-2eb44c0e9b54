package co.thewordlab.luzia.dynamicflow.presentation.steps

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import co.thewordlab.luzia.dynamicflow.presentation.factory.DynamicFlowViewModel
import co.thewordlab.luzia.dynamicflow.presentation.factory.FlowScreenConfig
import co.thewordlab.luzia.dynamicflow.presentation.factory.FlowStructure
import co.thewordlab.luzia.dynamicflow.presentation.factory.FlowViewActions
import co.thewordlab.luzia.dynamicflow.presentation.factory.NavigationBottomData
import co.thewordlab.luzia.dynamicflow.presentation.factory.NavigationTopData
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun FlowStepDetailScreen(
    stepId: String,
    onViewActions: (FlowViewActions) -> Unit
) {
    val viewModel: DynamicFlowViewModel = hiltViewModel()
    with(viewModel.dynamicFlowManager) {
        val stepData = getStepScreenData(stepId)
        if (stepData is FlowScreenConfig.Step) {
            val screen = buildScreenContent(
                stepData.copy(
                    top = NavigationTopData(backAction = { onViewActions(FlowViewActions.NavigateBack) }),
                    bottom = NavigationBottomData(
                        buttonText = stringResource(localizationR.string.tool_update_appstore_cta),
                        buttonAction = { onViewActions(FlowViewActions.NavigateBack) }
                    )
                )
            )
            ContentScreen(screen)
        } else {
            onViewActions(FlowViewActions.NavigateBack)
        }
    }
}

@Composable
private fun ContentScreen(screen: FlowStructure) {
    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .imePadding(),
        containerColor = LuziaTheme.palette.surface.background
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            screen.TopNavigation()
            Box(modifier = Modifier.weight(1f)) { screen.Content() }
            screen.BottomNavigation()
        }
    }
}
