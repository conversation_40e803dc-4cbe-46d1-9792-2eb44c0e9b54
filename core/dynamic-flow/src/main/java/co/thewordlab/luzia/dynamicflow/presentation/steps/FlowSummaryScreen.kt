package co.thewordlab.luzia.dynamicflow.presentation.steps

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import co.thewordlab.luzia.dynamicflow.DynamicFlowSummaryScreen
import co.thewordlab.luzia.dynamicflow.convertFlowStateToMap
import co.thewordlab.luzia.dynamicflow.domain.models.DynamicFlowComponentType
import co.thewordlab.luzia.dynamicflow.presentation.components.FlowBottomNavigation
import co.thewordlab.luzia.dynamicflow.presentation.components.FlowTopNavigation
import co.thewordlab.luzia.dynamicflow.presentation.factory.FlowScreenConfig
import co.thewordlab.luzia.dynamicflow.presentation.factory.FlowStructure
import co.thewordlab.luzia.dynamicflow.presentation.factory.LocalDynamicFlowState
import co.thewordlab.luzia.dynamicflow.presentation.factory.validateInput
import co.thewordlab.luzia.dynamicflow.presentation.models.FlowComponentState
import co.thewordlab.luzia.dynamicflow.presentation.models.FlowComponentUiModel
import co.thewordlab.luzia.dynamicflow.presentation.models.FlowSummaryUiModel
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import coil3.compose.AsyncImage
import co.thewordlab.luzia.foundation.design.system.R as designR

private const val EMPTY_ITEM = "-"

internal class FlowSummaryScreen(
    private val analytics: Analytics,
    private val data: FlowScreenConfig,
    private val flowScreenConfig: List<FlowScreenConfig>
) : FlowStructure {

    @Composable
    override fun TopNavigation() {
        validateInput<FlowSummaryUiModel>(
            data.data
        )?.let { dataModel ->
            FlowTopNavigation(
                if (dataModel.extraData.isEdition) {
                    data.topNavigation.copy(title = "", cancelAction = null)
                } else {
                    data.topNavigation
                }
            )
        }
    }

    @Composable
    override fun BottomNavigation() {
        validateInput<FlowSummaryUiModel>(
            data.data
        )?.let { dataModel ->
            FlowBottomNavigation(data.bottomNavigation.copy(buttonText = dataModel.extraData.buttonText))
        }
    }

    @Composable
    override fun Content() {
        val localState = LocalDynamicFlowState.current
        validateInput<FlowSummaryUiModel>(
            data.data
        )?.let { data ->
            LaunchedEffect(Unit) {
                analytics.trackScreen(
                    DynamicFlowSummaryScreen(
                        convertFlowStateToMap(localState.get()),
                        data.flowType
                    )
                )
            }
            val steps: Map<String, List<FlowComponentState>> =
                localState.get().groupBy { it.stepId }
            val sections = createStepIdToTitleMap(flowScreenConfig)
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(Spacing.X16.dp)
            ) {
                LuziaText(
                    modifier = Modifier.padding(horizontal = Spacing.X16.dp),
                    text = data.extraData.subtitle,
                    style = LuziaTheme.typography.headlines.h4,
                    color = LuziaTheme.palette.text.primary
                )
                Spacing.X24.Vertical()
                findCarouselImageUrl(steps)?.let {
                    Box(modifier = Modifier.padding(horizontal = Spacing.X16.dp)) {
                        AsyncImage(
                            modifier = Modifier
                                .size(IconSizes.X100.dp)
                                .clip(CircleShape)
                                .aspectRatio(1f),
                            contentScale = ContentScale.Crop,
                            model = it,
                            contentDescription = null
                        )
                    }
                    Spacing.X24.Vertical()
                }
                data.data.forEachIndexed { index, step ->
                    ItemRow(
                        id = step,
                        title = sections[step].orEmpty(),
                        description = findDescriptionValue(step, steps[step]),
                        errorMessage = findErrorMessage(step, steps[step])
                    ) { stepId -> data.onNavigateTo(stepId, localState.get()) }
                    if (index < data.data.lastIndex) {
                        HorizontalDivider()
                    }
                }
            }
        }
    }

    private fun findErrorMessage(
        step: String,
        flowComponentStates: List<FlowComponentState>?
    ): String? =
        flowComponentStates?.firstOrNull { it.stepId == step && it.errorMessage != null }?.errorMessage

    private fun findCarouselImageUrl(steps: Map<String, List<FlowComponentState>>): String? =
        steps.entries.toList().find { entry ->
            entry.value.any { item -> item.componentType == DynamicFlowComponentType.CAROUSEL }
        }?.value?.firstOrNull { it.componentType == DynamicFlowComponentType.CAROUSEL }?.value

    private fun createStepIdToTitleMap(flowScreenConfig: List<FlowScreenConfig>): Map<String, String> {
        return flowScreenConfig
            .filterIsInstance<FlowScreenConfig.Step>()
            .associate { step ->
                val title = step.content.components
                    .filterIsInstance<FlowComponentUiModel.CompositeHeader>()
                    .firstOrNull()
                    ?.title ?: ""

                step.content.id to title
            }
    }

    private fun findDescriptionValue(
        key: String,
        flowSelections: List<FlowComponentState>?,
    ): String {
        val items = flowSelections?.filter { it.stepId == key }
        return if (items.isNullOrEmpty()) {
            EMPTY_ITEM
        } else {
            items
                .filter { it.componentType.isValidTypeForDescription() && it.value.isNotEmpty() }
                .joinToString { it.value }
                .ifEmpty { EMPTY_ITEM }
        }
    }

    private fun DynamicFlowComponentType.isValidTypeForDescription() =
        this == DynamicFlowComponentType.TEXT_INPUT || this == DynamicFlowComponentType.SELECTOR_BUTTONS ||
            this == DynamicFlowComponentType.EXTENDED_SELECTOR_BUTTONS

    @Composable
    private fun ItemRow(
        id: String,
        title: String,
        description: String,
        errorMessage: String?,
        onItemNavigation: (id: String) -> Unit
    ) {
        val isInError = errorMessage != null
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .click { onItemNavigation(id) }
                .padding(Spacing.X16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(Spacing.X16.dp)
            ) {
                Column(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
                ) {
                    LuziaText(
                        text = title,
                        style = LuziaTheme.typography.body.regular.small,
                        color = LuziaTheme.palette.text.secondary
                    )
                    LuziaText(
                        text = description,
                        style = LuziaTheme.typography.body.semiBold.small,
                        color = if (isInError) {
                            LuziaTheme.palette.accents.red.error50
                        } else {
                            LuziaTheme.palette.text.primary
                        },
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                Icon(
                    modifier = Modifier.size(Spacing.X12.dp),
                    painter = painterResource(designR.drawable.ic_chevron_right),
                    tint = if (isInError) {
                        LuziaTheme.palette.accents.red.error50
                    } else {
                        LuziaTheme.palette.interactive.primary
                    },
                    contentDescription = null
                )
            }
            errorMessage?.let {
                Spacing.X8.Vertical()
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
                ) {
                    Icon(
                        painter = painterResource(designR.drawable.ic_info),
                        tint = LuziaTheme.palette.accents.red.error50,
                        contentDescription = null
                    )
                    LuziaText(
                        text = it,
                        style = LuziaTheme.typography.body.regular.small,
                        color = LuziaTheme.palette.accents.red.error50
                    )
                }
            }
        }
    }
}
