package co.thewordlab.luzia.dynamicflow.presentation.factory

import co.thewordlab.luzia.dynamicflow.presentation.models.FlowStepUiModel
import co.thewordlab.luzia.dynamicflow.presentation.models.FlowSummaryUiModel
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.ButtonState

sealed class FlowScreenConfig(
    val topNavigation: NavigationTopData,
    val bottomNavigation: NavigationBottomData,
    val data: Any?
) {
    data class Step(
        val top: NavigationTopData,
        val bottom: NavigationBottomData,
        val content: FlowStepUiModel
    ) : FlowScreenConfig(top, bottom, content)

    data class Summary(
        val top: NavigationTopData,
        val bottom: NavigationBottomData,
        val content: FlowSummaryUiModel
    ) : FlowScreenConfig(top, bottom, content)
}

data class NavigationTopData(
    val title: String = "",
    val backAction: (() -> Unit)? = null,
    val closeAction: (() -> Unit)? = null,
    val skipAction: (() -> Unit)? = null,
    val cancelAction: (() -> Unit)? = null,
)

data class NavigationBottomData(
    val progress: Float? = null,
    val buttonText: String = "",
    val buttonEnabled: ButtonState = ButtonState.ENABLED,
    val buttonAction: () -> Unit = { DO_NOTHING }
)
