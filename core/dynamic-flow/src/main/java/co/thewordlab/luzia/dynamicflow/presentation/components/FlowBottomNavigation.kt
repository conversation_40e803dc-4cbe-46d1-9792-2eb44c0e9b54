package co.thewordlab.luzia.dynamicflow.presentation.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import co.thewordlab.luzia.dynamicflow.presentation.factory.NavigationBottomData
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.LuziaPrimaryButton
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
internal fun FlowBottomNavigation(data: NavigationBottomData) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(LuziaTheme.palette.surface.content)
    ) {
        data.progress?.let { progress ->
            LinearProgressIndicator(
                progress = { progress / 100 },
                modifier = Modifier.fillMaxWidth(),
                color = LuziaTheme.palette.interactive.brand,
                trackColor = LuziaTheme.palette.interactive.brandLight,
            )
        }
        LuziaPrimaryButton(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.X16.dp),
            text = data.buttonText,
            state = data.buttonEnabled,
            onClick = data.buttonAction
        )
    }
}
