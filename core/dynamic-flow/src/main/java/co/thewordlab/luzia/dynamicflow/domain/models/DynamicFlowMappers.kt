package co.thewordlab.luzia.dynamicflow.domain.models

import co.thewordlab.luzia.dynamicflow.data.models.DynamicFlowDTO
import co.thewordlab.luzia.dynamicflow.data.models.FlowComponentDTO
import co.thewordlab.luzia.dynamicflow.data.models.FlowComponentItemDTO
import co.thewordlab.luzia.dynamicflow.data.models.FlowHeaderDTO
import co.thewordlab.luzia.dynamicflow.data.models.FlowNavigationDTO
import co.thewordlab.luzia.dynamicflow.data.models.FlowProgressDTO
import co.thewordlab.luzia.dynamicflow.data.models.FlowStepDTO

fun DynamicFlowDTO.mapToModel(): DynamicFlowsModel = DynamicFlowsModel(
    flowId = flowId,
    flowVersion = flowVersion,
    steps = steps.map { it.mapToModel() }
)

private fun FlowStepDTO.mapToModel(): FlowStep = FlowStep(
    stepId = stepId,
    progress = progress?.mapToModel(),
    header = header?.mapToModel(),
    body = body.mapNotNull { it.mapToModel() },
    footer = footer.mapNotNull { it.mapToModel() }
)

private fun FlowProgressDTO.mapToModel(): FlowProgress = FlowProgress(
    current = current,
    total = total
)

private fun FlowHeaderDTO.mapToModel(): FlowHeader = FlowHeader(
    title = title,
    navigation = navigation?.mapToModel()
)

private fun FlowNavigationDTO.mapToModel(): FlowNavigation = FlowNavigation(
    backEnabled = backEnabled,
    skipEnabled = skipEnabled,
    cancelEnabled = cancelEnabled
)

@Suppress("CyclomaticComplexMethod")
private fun FlowComponentDTO.mapToModel(): FlowComponent? = when (type) {
    DynamicFlowComponentType.FLOW_INTRO -> FlowComponent.FeatureIntro(
        title.orEmpty(),
        subheader.orEmpty(),
        mediaUrl.orEmpty()
    )

    DynamicFlowComponentType.HEADER -> FlowComponent.Header(content.orEmpty())
    DynamicFlowComponentType.COMPOSITE_HEADER -> FlowComponent.CompositeHeader(
        title = title.orEmpty(),
        subTitle = subheader.orEmpty()
    )

    DynamicFlowComponentType.TEXT_INPUT -> FlowComponent.TextInput(
        id = id.orEmpty(),
        placeholder = placeholder.orEmpty(),
        maxLength = maxLength ?: 0
    )

    DynamicFlowComponentType.SELECTOR_BUTTONS -> FlowComponent.SelectorButtons(
        id = id.orEmpty(),
        title = title.orEmpty(),
        items = items?.mapNotNull { it.mapToModel(DynamicFlowComponentType.SELECTOR_BUTTONS) }
            .orEmpty(),
        maxSelections ?: 0
    )

    DynamicFlowComponentType.EXTENDED_SELECTOR_BUTTONS -> FlowComponent.ExtendedSelectorButtons(
        id = id.orEmpty(),
        items = items?.mapNotNull {
            it.mapToModel(DynamicFlowComponentType.EXTENDED_SELECTOR_BUTTONS)
        }.orEmpty()
    )

    DynamicFlowComponentType.CAROUSEL -> FlowComponent.Carousel(
        id = id.orEmpty(),
        title = title.orEmpty(),
        description = subheader.orEmpty(),
        items = items?.mapNotNull { it.mapToModel(DynamicFlowComponentType.CAROUSEL) }.orEmpty(),
        maxSelections = maxSelections ?: 0
    )

    DynamicFlowComponentType.IMAGE -> FlowComponent.Image(mediaUrl.orEmpty())
    DynamicFlowComponentType.BUTTON -> FlowComponent.Button(
        id = id.orEmpty(),
        title = title.orEmpty(),
        action = action
    )

    DynamicFlowComponentType.SUMMARY_SELECTOR ->
        FlowComponent.SummarySelector(
            id = id.orEmpty(),
            items = stepIds.orEmpty()
        )

    DynamicFlowComponentType.OTHER -> null
}

private fun FlowComponentItemDTO.mapToModel(parentComponent: DynamicFlowComponentType): FlowComponentItem? =
    when (parentComponent) {
        DynamicFlowComponentType.EXTENDED_SELECTOR_BUTTONS,
        DynamicFlowComponentType.SELECTOR_BUTTONS ->
            FlowComponentItem.SelectorButton(id = id, title = title.orEmpty(), subtitle = subtitle)

        DynamicFlowComponentType.CAROUSEL ->
            FlowComponentItem.CarouselItem(
                id = id,
                imageUrl = imageUrl.orEmpty()
            )

        else -> null
    }
