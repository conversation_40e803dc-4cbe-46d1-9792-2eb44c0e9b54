package co.thewordlab.luzia.dynamicflow.presentation.factory

import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class FlowViewActions : ViewAction {
    data object NavigateBack : FlowViewActions()
    data object NavigateForward : FlowViewActions()
    data class NavigateToStepDetail(val id: String) : FlowViewActions()
    data object SubmitFlow : FlowViewActions()
    data object OnCancelFlow : FlowViewActions()
}
