package co.thewordlab.luzia.dynamicflow.presentation.components

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import co.thewordlab.luzia.dynamicflow.presentation.factory.NavigationTopData
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.LuziaTextButton
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
internal fun FlowTopNavigation(data: NavigationTopData) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(min = IconSizes.X80.dp)
            .padding(horizontal = Spacing.X16.dp, vertical = Spacing.X8.dp)
    ) {
        val (startIcon, titleText, endIcon) = createRefs()

        val startModifier = Modifier.constrainAs(startIcon) {
            start.linkTo(parent.start)
            top.linkTo(parent.top)
            bottom.linkTo(parent.bottom)
        }

        val hideRightActions = data.skipAction == null && data.cancelAction == null
        val endModifier = Modifier
            .constrainAs(endIcon) {
                end.linkTo(parent.end)
                top.linkTo(parent.top)
                bottom.linkTo(parent.bottom)
            }
            .graphicsLayer { if (hideRightActions) { alpha = 0f } }

        data.backAction?.let {
            NavigationIcon(modifier = startModifier, icon = designR.drawable.ic_back_arrow, it)
        } ?: run {
            data.closeAction?.let {
                NavigationIcon(modifier = startModifier, icon = designR.drawable.ic_close, it)
            }
        }

        if (data.title.isNotEmpty()) {
            LuziaText(
                modifier = Modifier
                    .constrainAs(titleText) {
                        start.linkTo(startIcon.end)
                        end.linkTo(endIcon.start)
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                        width = Dimension.fillToConstraints
                    },
                text = data.title,
                style = LuziaTheme.typography.body.semiBold.small,
                color = LuziaTheme.palette.text.primary,
                textAlign = TextAlign.Center
            )
        }

        LuziaTextButton(
            text = getButtonText(data.skipAction != null, data.cancelAction != null),
            onClick = getButtonAction(data.skipAction, data.cancelAction),
            modifier = endModifier
        )
    }
}

@Composable
private fun getButtonText(isSkip: Boolean, isCancel: Boolean): String = when {
    isSkip -> stringResource(localizationR.string.profile_skip)
    isCancel -> stringResource(localizationR.string.cancel)
    else -> ""
}

private fun getButtonAction(
    skipAction: (() -> Unit)? = null,
    cancelAction: (() -> Unit)? = null
) = when {
    skipAction != null -> skipAction
    cancelAction != null -> cancelAction
    else -> {
        { DO_NOTHING }
    }
}

@Composable
private fun NavigationIcon(modifier: Modifier, @DrawableRes icon: Int, onClick: () -> Unit) {
    Box(
        modifier = modifier
            .clip(CircleShape)
            .background(color = LuziaTheme.palette.interactive.contrast, shape = CircleShape)
            .size(IconSizes.X48.dp)
            .click(action = onClick),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            painter = painterResource(id = icon),
            contentDescription = null,
            modifier = Modifier.size(IconSizes.X32.dp),
            tint = LuziaTheme.palette.interactive.primary
        )
    }
}
