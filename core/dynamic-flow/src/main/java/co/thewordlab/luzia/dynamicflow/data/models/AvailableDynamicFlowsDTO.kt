package co.thewordlab.luzia.dynamicflow.data.models

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class AvailableDynamicFlowsDTO(
    @Json(name = "availableFlows") val availableFlows: List<AvailableDynamicFlowDTO>
)

@JsonClass(generateAdapter = true)
data class AvailableDynamicFlowDTO(
    @Json(name = "flowId") val flowId: String? = null
)
