package co.thewordlab.luzia.benchmark.baseline

import androidx.benchmark.macro.junit4.BaselineProfileRule
import co.thewordlab.luzia.benchmark.PACKAGE_NAME
import co.thewordlab.luzia.benchmark.StartupScenarios.launchStartupScenarios
import org.junit.Rule
import org.junit.Test

/**
 * Baseline Profile for app startup. This profile also enables using [Dex Layout Optimizations](https://developer.android.com/topic/performance/baselineprofiles/dex-layout-optimizations)
 * via the `includeInStartupProfile` parameter.
 */
class StartupBaselineProfile {
    @get:Rule
    val baselineProfileRule = BaselineProfileRule()

    @Test
    fun generate() = baselineProfileRule.collect(
        packageName = PACKAGE_NAME,
        includeInStartupProfile = true,
        profileBlock = {
            startActivityAndWait()
            launchStartupScenarios()
        }
    )
}