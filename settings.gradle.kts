pluginManagement {
	includeBuild("build-logic")
	repositories {
		google()
		mavenCentral()
		gradlePluginPortal()
		maven {
			url = uri("https://maven.pkg.github.com/The-Wordlab/compose-richtext")
			credentials {
				username = providers.gradleProperty("GITHUB_USERNAME").getOrElse("")
				password = providers.gradleProperty("GITHUB_TOKEN").getOrElse("")
			}
		}
		maven { url = uri("https://jitpack.io") }
	}
}

dependencyResolutionManagement {
	repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
	repositories {
		google()
		mavenCentral()
		maven {
			url = uri("https://maven.pkg.github.com/The-Wordlab/compose-richtext")
			credentials {
				username = providers.gradleProperty("GITHUB_USERNAME").getOrElse("")
				password = providers.gradleProperty("GITHUB_TOKEN").getOrElse("")
			}
		}
		maven { url = uri("https://jitpack.io") }
	}
}

rootProject.name = "luzia"
enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")
include(":app")
include(":core:bestiepoints")
include(":core:dynamic-flow")
include(":core:feedback")
include(":core:navigation")
include(":core:notifications")
include(":core:profile")
include(":core:signup")
include(":core:sharing")
include(":core:tools")
include(":core:web")
include(":dev-tools")
include(":features:chat")
include(":features:home")
include(":features:imagine")
include(":features:personality")
include(":features:proactive-messaging")
include(":features:profile")
include(":features:tools")
include(":features:signup")
include(":features:shortcuts")
include(":features:stream")
include(":features:settings")
include(":features:vision")
include(":foundation:analytics")
include(":foundation:architecture-system")
include(":foundation:common")
include(":foundation:config")
include(":foundation:design-system")
include(":foundation:files")
include(":foundation:localization")
include(":foundation:messages")
include(":foundation:networking")
include(":foundation:persistence")
include(":foundation:securelib")
include(":core:chat")
include(":core:ads")
include(":foundation:data-cache")
include(":benchmark")
include(":core:gamification")
include(":core:connectivity")
include(":features:gamification")
include(":foundation:testing")
include(":core:camera")
