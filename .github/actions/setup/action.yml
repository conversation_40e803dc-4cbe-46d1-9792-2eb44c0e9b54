name: Setup Development Environment
description: Sets up the development environment with Git checkout, JDK, and Gradle.
inputs:
  java-version:
    description: Java version to use.
    required: true
    default: '17'
runs:
  using: 'composite'
  steps:
    - name: Set up JDK
      uses: actions/setup-java@v4
      with:
        java-version: ${{ inputs.java-version }}
        distribution: temurin

    - name: Set up Gradle cache
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-${{ hashFiles('**/*.gradle*') }}-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}-${{ hashFiles('**/buildSrc/**/*.kt') }}

    - name: Make gradlew executable
      run: chmod +x ./gradlew
      shell: bash

    - name: Set up Android SDK
      uses: android-actions/setup-android@v3

    - name: Set up Git user
      shell: bash
      run: |
        git config --global user.name "GitHub Actions Bot"
        git config --global user.email "<EMAIL>"

    - name: Set up GitHub credentials
      shell: bash
      run: |
        chmod +x ./scripts/setup-github-credentials.sh
        ./scripts/setup-github-credentials.sh
