name: Check PR

on:
  # Triggers the workflow on every pull request to master branch
  pull_request:
    types: [ opened, synchronize, reopened ]
    branches:
      - latest
      - 'release/*'

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:
    inputs:
      logLevel:
        description: 'Log level'
        required: false
        default: summary
        type: choice
        options:
          - all
          - fail
          - summary
          - none

jobs:
  code_review:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Environment
        uses: ./.github/actions/setup
        env:
          GITHUB_TOKEN: ${{ secrets.PACKAGES_TOKEN }}

      - name: Run detekt
        run: ./gradlew detekt

      - name: Run unit tests
        run: ./gradlew testDevDebugUnitTest

      - name: Generate coverage reports
        run: ./gradlew createDevDebugCombinedCoverageReport

      - name: Cache SonarQube packages
        uses: actions/cache@v4
        with:
          path: ~/.sonar/cache
          key: ${{ runner.os }}-sonar
          restore-keys: ${{ runner.os }}-sonar

      - name: <PERSON><PERSON>radle packages
        uses: actions/cache@v4
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle') }}
          restore-keys: ${{ runner.os }}-gradle

      - name: Sonar Analysis
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: ./gradlew sonar -Dsonar.gradle.skipCompile=true --info
