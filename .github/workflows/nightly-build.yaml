name: Nightly Build Workflow

on:
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

  # Schedule the workflow to run nightly at 12:00 AM UTC
  schedule:
    - cron: '0 0 * * *'

jobs:
  update-translations:
    name: Update Phrase Translations
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout latest branch
        uses: actions/checkout@v4
        with:
          ref: latest
          ssh-key: ${{secrets.DEPLOY_KEY}}

      - name: Set up Git user
        shell: bash
        run: |
          git config --global user.name "GitHub Actions Bot"
          git config --global user.email "<EMAIL>"

      - name: Setup Phrase CLI
        run: |
          curl -sL https://github.com/phrase/phrase-cli/releases/download/2.7.0/phrase_linux_amd64.tar.gz | tar xz
          sudo mv phrase_linux_amd64 /usr/local/bin/phrase
          chmod +x /usr/local/bin/phrase

      - name: Pull translations from Phrase
        run: |
          bash ./scripts/updateTranslations.sh

      - name: Commit and push changes to latest branch
        run: |
          git add foundation/localization/src/main/res/values/
          git add foundation/localization/src/main/res/values-*/
          git diff --staged --quiet || git commit -m "CI: update translations from Phrase"
          git push origin latest

  tests:
    name: Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: latest
          ssh-key: ${{secrets.DEPLOY_KEY}}

      - name: Setup Environment
        uses: ./.github/actions/setup
        env:
          GITHUB_TOKEN: ${{ secrets.PACKAGES_TOKEN }}

      - name: Run detekt
        run: ./gradlew detekt

      - name: Run unit tests
        run: ./gradlew testDevDebugUnitTest

  deploy-to-firebase:
    name: Deploy To Firebase
    runs-on: ubuntu-latest
    needs: [tests]
    permissions:
      contents: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: latest
          ssh-key: ${{secrets.DEPLOY_KEY}}

      - name: Setup Environment
        uses: ./.github/actions/setup
        env:
          GITHUB_TOKEN: ${{ secrets.PACKAGES_TOKEN }}

      - name: Generate Release Notes
        id: generate_changelog
        run: |
          release_notes=$(bash ./scripts/generateChangelog.sh)
          echo "RELEASE_NOTES<<EOF"$'\n'"$release_notes"$'\n'EOF >> $GITHUB_OUTPUT

      - name: Expose version name
        id: versions
        uses: ltDino/android-get-version-action@v1.0
        with:
          gradlePath: app/build.gradle.kts

      - name: Decode Keystore
        env:
          ENCODED_STRING: ${{ secrets.KEYSTORE_BASE64 }}
          SIGNING_KEY_STORE_PATH: 'app/android_keystore.jks'
        run: |
          echo $ENCODED_STRING > keystore-b64.txt
          base64 -d keystore-b64.txt > $SIGNING_KEY_STORE_PATH

      - name: Build APK
        run: |
          ./gradlew assembleDevRelease
        env:
          BUILD_NUMBER: ${{ github.run_number }}
          KEYSTORE_PASSWORD: ${{ secrets.KEYSTORE_PASSWORD }}
          KEY_ALIAS: ${{ secrets.KEY_ALIAS }}
          KEY_PASSWORD: ${{ secrets.KEY_PASSWORD }}

      - name: Get apk info
        id: apk-info
        uses: hkusu/apk-info-action@v1
        with:
          apk-path: 'app/build/outputs/apk/dev/release/app-dev-release.apk'

      - name: Upload artifact to Firebase App Distribution
        id: firebase
        uses: wzieba/Firebase-Distribution-Github-Action@v1
        with:
          appId: ${{secrets.FIREBASE_APP_ID}}
          serviceCredentialsFileContent: ${{ secrets.FIREBASE_CREDENTIAL_FILE_CONTENT }}
          groups: luzia-team
          file: app/build/outputs/apk/dev/release/app-dev-release.apk
          releaseNotes: ${{ steps.generate_changelog.outputs.RELEASE_NOTES }}

      - name: Post release notes to Slack channel
        id: slack
        uses: slackapi/slack-github-action@v1.27.1
        with:
          channel-id: 'C09403F2QH4'
          payload: |
            {
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":firebase: Android Nightly Dev ${{steps.versions.outputs.versionName}}-(${{github.run_number}})",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "New version is available"
                  },
                  "accessory": {
                    "type": "button",
                    "text": {
                      "type": "plain_text",
                      "text": "Test link",
                      "emoji": true
                    },
                    "value": "click_me_123",
                    "url": "${{steps.firebase.outputs.TESTING_URI}}",
                    "action_id": "button-action"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "APK Size: ${{ steps.apk-info.outputs.readable-file-size }}"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "This version includes following changes:"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ${{ toJSON(steps.generate_changelog.outputs.RELEASE_NOTES) }}
                  }
                }
              ]
            }
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

      - name: Commit last commit hash
        run: |
          bash ./scripts/saveLastCommit.sh
