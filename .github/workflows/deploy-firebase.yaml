name: Deploy To Firebase App Distribution

on:
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  build_dev:
    name: Build Dev Apk File
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ssh-key: ${{secrets.DEPLOY_KEY}}

      - name: Setup Environment
        uses: ./.github/actions/setup
        env:
          GITHUB_TOKEN: ${{ secrets.PACKAGES_TOKEN }}

      - name: Generate Release Notes
        id: generate_changelog
        run: |
          release_notes=$(bash ./scripts/generateChangelog.sh)
          echo "RELEASE_NOTES<<EOF"$'\n'"$release_notes"$'\n'EOF >> $GITHUB_OUTPUT

      - name: Expose version name
        id: versions
        uses: ltDino/android-get-version-action@v1.0
        with:
          gradlePath: app/build.gradle.kts

      - name: Decode Keystore
        env:
          ENCODED_STRING: ${{ secrets.KEYSTORE_BASE64 }}
          SIGNING_KEY_STORE_PATH: 'app/android_keystore.jks'
        run: |
          echo $ENCODED_STRING > keystore-b64.txt
          base64 -d keystore-b64.txt > $SIGNING_KEY_STORE_PATH

      - name: Build APK
        run: |
          ./gradlew assembleDevRelease
        env:
          BUILD_NUMBER: ${{ github.run_number }}
          KEYSTORE_PASSWORD: ${{ secrets.KEYSTORE_PASSWORD }}
          KEY_ALIAS: ${{ secrets.KEY_ALIAS }}
          KEY_PASSWORD: ${{ secrets.KEY_PASSWORD }}

      - name: Upload APK
        uses: actions/upload-artifact@v4
        with:
          name: app-dev-release.apk
          path: app/build/outputs/apk/dev/release/app-dev-release.apk

      - name: Get apk info
        id: apk-info
        uses: hkusu/apk-info-action@v1
        with:
          apk-path: 'app/build/outputs/apk/dev/release/app-dev-release.apk'

      - name: Upload artifact to Firebase App Distribution
        id: firebase
        uses: wzieba/Firebase-Distribution-Github-Action@v1
        with:
          appId: ${{secrets.FIREBASE_APP_ID}}
          serviceCredentialsFileContent: ${{ secrets.FIREBASE_CREDENTIAL_FILE_CONTENT }}
          groups: luzia-team
          file: app/build/outputs/apk/dev/release/app-dev-release.apk
          releaseNotes: ${{ steps.generate_changelog.outputs.RELEASE_NOTES }}

      - name: Post release notes to Slack channel
        id: slack
        uses: slackapi/slack-github-action@v1.27.1
        with:
          channel-id: 'C06P2K80S4F'
          payload: |
            {
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":firebase: Firebase Android Dev ${{steps.versions.outputs.versionName}}-(${{github.run_number}})",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "New version is available"
                  },
                  "accessory": {
                    "type": "button",
                    "text": {
                      "type": "plain_text",
                      "text": "Test link",
                      "emoji": true
                    },
                    "value": "click_me_123",
                    "url": "${{steps.firebase.outputs.TESTING_URI}}",
                    "action_id": "button-action"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "APK Size: ${{ steps.apk-info.outputs.readable-file-size }}"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "This version includes following changes:"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ${{ toJSON(steps.generate_changelog.outputs.RELEASE_NOTES) }}
                  }
                }
              ]
            }
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

      - name: Commit last commit hash
        run: |
          bash ./scripts/saveLastCommit.sh
