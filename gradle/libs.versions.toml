[versions]
accompanist = "0.36.0"
amplitudeVersion = "1.19.3"
amplitudeExperimentVersion = "1.13.0"
androidDesugarJdkLibs = "2.1.5"
# AGP and tools should be updated together
androidGradlePlugin = "8.10.1"
androidTools = "31.10.1"
androidxActivity = "1.10.1"
androidxAppCompat = "1.7.1"
androidxBrowser = "1.8.0"
androidxComposeBom = "2025.06.00"
androidxComposeBeta = "1.8.2"
androidxComposeMaterial3Adaptive = "1.1.0"
androidxComposeMaterial3AdaptiveNavigationSuite = "1.3.2"
androidxComposeRuntimeTracing = "1.8.2"
androidxCore = "1.16.0"
androidxCoreSplashscreen = "1.0.1"
androidxCredential = "1.5.0"
androidxDataStore = "1.1.7"
androidxEspresso = "3.6.1"
androidxHiltNavigationCompose = "1.2.0"
androidxLifecycle = "2.9.1"
androidxMacroBenchmark = "1.3.4"
androidxMetrics = "1.0.0-beta02"
androidxNavigation = "2.9.0"
androidxTestCore = "1.6.1"
androidxTestExt = "1.2.1"
androidxTestRules = "1.6.1"
androidxTestRunner = "1.6.2"
androidxTracing = "1.3.0"
androidxUiAutomator = "2.3.0"
appsFlyer = "6.17.0"
appauth = "0.11.1"
brazeVersion =  "36.0.0"
cameraMlkitVision = "1.4.2"
cameraxVersion = "1.5.0-beta01"
chucker = "4.1.0"
coil = "3.2.0"
datadogVersion =  "2.20.0"
datadogPluginVersion = "1.15.0"
dependencyGuard = "0.5.0"
detekt = "1.23.8"
eithernet = "1.9.0"
exif = "1.3.7"
facebookVersion = "17.0.2"
firebaseBom = "33.14.0"
firebaseCrashlyticsPlugin = "3.0.4"
firebasePerfPlugin = "1.4.2"
firebaseMessagingVersion = "24.1.1"
gmsPlugin = "4.4.2"
googleIdentity = "1.1.1"
googleOss = "17.1.0"
googleOssPlugin = "0.10.6"
gson = "2.11.0"
guavaVersion = "33.4.0-android"
hilt = "2.55"
imageEditor = "4.6.0"
installreferrer = "2.2"
jacoco = "0.8.11"
jwtdecode = "2.0.2"
kotlin = "2.1.10"
kotlinxCoroutines = "1.10.1"
kotlinxDatetime = "0.6.2"
kotlinxSerializationJson = "1.8.0"
ksp = "2.1.10-1.0.31"
leakCanaryVersion = "2.14"
lottie = "6.6.6"
markdown = "0.0.2"
materialIconsExtended = "1.7.8"
mockk = "1.13.17"
moshi = "1.15.2"
okhttp = "4.12.0"
paging = "3.3.6"
playServiceAdId = "18.2.0"
playServiceAuth = "21.3.0"
playServicesMlkitTextRecognition = "19.0.1"
playServicesReview = "2.0.2"
retrofit = "2.11.0"
retrofitKotlinxSerializationJson = "1.0.0"
room = "2.6.1"
secrets = "2.0.1"
stream = "6.14.0"
streamPush = "1.3.1"
truth = "1.4.4"
turbine = "1.2.0"
kotlinVersion = "2.1.10"
junit = "4.13.2"
junitVersion = "1.2.1"
material = "1.12.0"
constraintlayout = "1.1.1"
playServicesBase = "18.7.0"
media3 = "1.5.1"
benchmarkMacroJunit4 = "1.3.4"
profileinstaller = "1.4.1"
sonarqube = "6.2.0.5505"
guava = "33.3.1-android"
admob = "24.3.0"
lifecycleService = "2.9.1"
koah = "0.0.5"

[libraries]
accompanist-permissions = { group = "com.google.accompanist", name = "accompanist-permissions", version.ref = "accompanist" }
accompanist-system = { group = "com.google.accompanist", name = "accompanist-systemuicontroller", version.ref = "accompanist" }
accompanist-webview = { group = "com.google.accompanist", name = "accompanist-webview", version.ref = "accompanist" }
android-desugarJdkLibs = { group = "com.android.tools", name = "desugar_jdk_libs", version.ref = "androidDesugarJdkLibs" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "androidxActivity" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "androidxAppCompat" }
androidx-browser = { group = "androidx.browser", name = "browser", version.ref = "androidxBrowser" }
androidx-camera-core = { group = "androidx.camera", name = "camera-core", version.ref = "cameraxVersion" }
androidx-camera-camera2 = { group = "androidx.camera", name = "camera-camera2", version.ref = "cameraxVersion" }
androidx-camera-mlkit-vision = { module = "androidx.camera:camera-mlkit-vision", version.ref = "cameraMlkitVision" }
androidx-camera-view = { group = "androidx.camera", name = "camera-view", version.ref = "cameraxVersion" }
androidx-camera-lifecycle = { group = "androidx.camera", name = "camera-lifecycle", version.ref = "cameraxVersion" }
androidx-camera-compose = { group = "androidx.camera", name = "camera-compose", version.ref = "cameraxVersion" }
androidx-camera-extensions = { group = "androidx.camera", name = "camera-extensions", version.ref = "cameraxVersion" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "androidxComposeBom" }
androidx-compose-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout-compose", version.ref = "constraintlayout" }
androidx-compose-foundation = { group = "androidx.compose.foundation", name = "foundation" }
androidx-compose-foundation-layout = { group = "androidx.compose.foundation", name = "foundation-layout" }
androidx-compose-markdown = { group = "com.github.the-wordlab.compose-richtext", name = "richtext-commonmark", version.ref = "markdown" }
androidx-compose-material-iconsExtended = { group = "androidx.compose.material", name = "material-icons-extended" }
androidx-compose-material3 = { group = "androidx.compose.material3", name = "material3" }
androidx-compose-material3-windowSizeClass = { group = "androidx.compose.material3", name = "material3-window-size-class" }
androidx-compose-material3-navigationSuite = { group = "androidx.compose.material3", name = "material3-adaptive-navigation-suite", version.ref = "androidxComposeMaterial3AdaptiveNavigationSuite" }
androidx-compose-material3-adaptive = { group = "androidx.compose.material3.adaptive", name = "adaptive", version.ref = "androidxComposeMaterial3Adaptive" }
androidx-compose-material3-adaptive-layout = { group = "androidx.compose.material3.adaptive", name = "adaptive-layout", version.ref = "androidxComposeMaterial3Adaptive" }
androidx-compose-material3-adaptive-navigation = { group = "androidx.compose.material3.adaptive", name = "adaptive-navigation", version.ref = "androidxComposeMaterial3Adaptive" }
androidx-compose-runtime = { group = "androidx.compose.runtime", name = "runtime" }
androidx-compose-runtime-tracing = { group = "androidx.compose.runtime", name = "runtime-tracing", version.ref = "androidxComposeRuntimeTracing" }
androidx-compose-ui = { group = "androidx.compose.ui", name = "ui", version.ref = "androidxComposeBeta" }
androidx-compose-ui-test = { group = "androidx.compose.ui", name = "ui-test-junit4", version.ref = "androidxComposeBeta" }
androidx-compose-ui-testManifest = { group = "androidx.compose.ui", name = "ui-test-manifest", version.ref = "androidxComposeBeta" }
androidx-compose-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling", version.ref = "androidxComposeBeta" }
androidx-compose-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview", version.ref = "androidxComposeBeta" }
androidx-compose-ui-util = { group = "androidx.compose.ui", name = "ui-util", version.ref = "androidxComposeBeta" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "androidxCore" }
androidx-core-splashscreen = { group = "androidx.core", name = "core-splashscreen", version.ref = "androidxCoreSplashscreen" }
androidx-dataStore-core = { group = "androidx.datastore", name = "datastore", version.ref = "androidxDataStore" }
androidx-dataStore-preferences = { group = "androidx.datastore", name = "datastore-preferences", version.ref = "androidxDataStore" }
androidx-hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "androidxHiltNavigationCompose" }
androidx-lifecycle-runtimeCompose = { group = "androidx.lifecycle", name = "lifecycle-runtime-compose", version.ref = "androidxLifecycle" }
androidx-lifecycle-runtimeTesting = { group = "androidx.lifecycle", name = "lifecycle-runtime-testing", version.ref = "androidxLifecycle" }
androidx-lifecycle-viewModelCompose = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "androidxLifecycle" }
androidx-material-icons-extended = { module = "androidx.compose.material:material-icons-extended", version.ref = "materialIconsExtended" }
androidx-media-exif = { group = "androidx.exifinterface", name = "exifinterface", version.ref = "exif" }
androidx-metrics = { group = "androidx.metrics", name = "metrics-performance", version.ref = "androidxMetrics" }
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "androidxNavigation" }
androidx-navigation-testing = { group = "androidx.navigation", name = "navigation-testing", version.ref = "androidxNavigation" }
androidx-paging = { group = "androidx.paging", name = "paging-common", version.ref = "paging" }
androidx-paging-compose = { group = "androidx.paging", name = "paging-compose", version.ref = "paging" }
androidx-test-core = { group = "androidx.test", name = "core", version.ref = "androidxTestCore" }
androidx-test-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "androidxEspresso" }
androidx-test-ext = { group = "androidx.test.ext", name = "junit-ktx", version.ref = "androidxTestExt" }
androidx-test-rules = { group = "androidx.test", name = "rules", version.ref = "androidxTestRules" }
androidx-test-runner = { group = "androidx.test", name = "runner", version.ref = "androidxTestRunner" }
androidx-test-uiautomator = { group = "androidx.test.uiautomator", name = "uiautomator", version.ref = "androidxUiAutomator" }
androidx-tracing-ktx = { group = "androidx.tracing", name = "tracing-ktx", version.ref = "androidxTracing" }
androidx-credentials = { group = "androidx.credentials", name = "credentials", version.ref = "androidxCredential" }
androidx-credentials-play-services = { group = "androidx.credentials", name = "credentials-play-services-auth", version.ref = "androidxCredential" }
appauth = { module = "net.openid:appauth", version.ref = "appauth" }
google_identity = { group = "com.google.android.libraries.identity.googleid", name = "googleid", version.ref = "googleIdentity" }
braze-compose = { group = "com.braze", name = "android-sdk-jetpack-compose", version.ref = "brazeVersion"}
braze-ui = { group = "com.braze", name = "android-sdk-ui", version.ref = "brazeVersion"}
chucker-main = { group = "com.github.chuckerteam.chucker", name = "library", version.ref = "chucker" }
chucker-none = { group = "com.github.chuckerteam.chucker", name = "library-no-op", version.ref = "chucker" }
coil-kt = { group = "io.coil-kt.coil3", name = "coil", version.ref = "coil" }
coil-kt-compose = { group = "io.coil-kt.coil3", name = "coil-compose", version.ref = "coil" }
coil-kt-network = { group = "io.coil-kt.coil3", name = "coil-network-okhttp", version.ref = "coil" }
coil-kt-svg = { group = "io.coil-kt.coil3", name = "coil-svg", version.ref = "coil" }
datadog-android-logs = { group = "com.datadoghq", name = "dd-sdk-android-logs", version.ref = "datadogVersion"}
datadog-android-rum = { group = "com.datadoghq",  name = "dd-sdk-android-rum", version.ref = "datadogVersion"}
detekt = { group = "io.gitlab.arturbosch.detekt", name = "detekt-gradle-plugin", version.ref = "detekt" }
detekt-formatting = { group = "io.gitlab.arturbosch.detekt", name = "detekt-formatting", version.ref = "detekt" }
facebook-analytics = { group = "com.facebook.android", name = "facebook-android-sdk", version.ref = "facebookVersion"}
firebase-analytics = { group = "com.google.firebase", name = "firebase-analytics-ktx" }
firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebaseBom" }
firebase-messaging = { group = "com.google.firebase", name = "firebase-messaging" }
firebase-cloud-messaging = { group = "com.google.firebase", name = "firebase-messaging-ktx", version.ref = "firebaseMessagingVersion" }
firebase-crashlytics = { group = "com.google.firebase", name = "firebase-crashlytics-ktx" }
firebase-performance = { group = "com.google.firebase", name = "firebase-perf-ktx" }
firebase-config = { group = "com.google.firebase", name = "firebase-config-ktx" }
firebase-inappmessaging = { group = "com.google.firebase", name = "firebase-inappmessaging-display" }
google-adid = { group = "com.google.android.gms", name = "play-services-ads-identifier", version.ref = "playServiceAdId" }
google-auth = { group = "com.google.android.gms", name = "play-services-auth", version.ref = "playServiceAuth" }
google-guava = { group = "com.google.guava", name = "guava", version.ref = "guavaVersion" }
google-oss-licenses = { group = "com.google.android.gms", name = "play-services-oss-licenses", version.ref = "googleOss" }
google-oss-licenses-plugin = { group = "com.google.android.gms", name = "oss-licenses-plugin", version.ref = "googleOssPlugin" }
google-review = { group = "com.google.android.play", name = "review", version.ref = "playServicesReview" }
google-review-ktx = { group = "com.google.android.play", name = "review-ktx", version.ref = "playServicesReview" }
gson = { group = "com.google.code.gson", name = "gson", version.ref = "gson" }
hilt-android = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
hilt-compiler = { group = "com.google.dagger", name = "hilt-android-compiler", version.ref = "hilt" }
image-editor = { group = "com.vanniktech", name = "android-image-cropper", version.ref = "imageEditor" }
installreferrer = { module = "com.android.installreferrer:installreferrer", version.ref = "installreferrer" }
javax-inject = { module = "javax.inject:javax.inject", version = "1" }
jwtdecode = { module = "com.auth0.android:jwtdecode", version.ref = "jwtdecode" }
kotlin-stdlib = { group = "org.jetbrains.kotlin", name = "kotlin-stdlib-jdk8", version.ref = "kotlin" }
kotlinx-coroutines-play = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-play-services", version.ref = "kotlinxCoroutines" }
kotlinx-coroutines-test = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-test", version.ref = "kotlinxCoroutines" }
kotlinx-datetime = { group = "org.jetbrains.kotlinx", name = "kotlinx-datetime", version.ref = "kotlinxDatetime" }
kotlinx-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "kotlinxSerializationJson" }
leak-canary = { group = "com.squareup.leakcanary", name = "leakcanary-android", version.ref = "leakCanaryVersion"}
lottie = { group = "com.airbnb.android", name = "lottie-compose", version.ref = "lottie" }
mockk = { group = "io.mockk", name = "mockk", version.ref = "mockk" }
moshi-core = { group = "com.squareup.moshi", name = "moshi", version.ref = "moshi" }
moshi-kotlin = { group = "com.squareup.moshi", name = "moshi-kotlin", version.ref = "moshi" }
moshi-adapters = { group = "com.squareup.moshi", name = "moshi-adapters", version.ref = "moshi" }
moshi-codegen = { group = "com.squareup.moshi", name = "moshi-kotlin-codegen", version.ref = "moshi" }
okhttp-logging = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp" }
play-services-mlkit-text-recognition = { module = "com.google.android.gms:play-services-mlkit-text-recognition", version.ref = "playServicesMlkitTextRecognition" }
retrofit-core = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
retrofit-moshi = { group = "com.squareup.retrofit2", name = "converter-moshi", version.ref = "retrofit" }
retrofit-kotlin-serialization = { group = "com.jakewharton.retrofit", name = "retrofit2-kotlinx-serialization-converter", version.ref = "retrofitKotlinxSerializationJson" }
room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
room-paging = { group = "androidx.room", name = "room-paging", version.ref = "room" }
slack-eithernet = { group = "com.slack.eithernet", name = "eithernet", version.ref = "eithernet" }
stream-chat-android-client = { module = "io.getstream:stream-chat-android-client", version.ref = "stream" }
stream-chat-android-compose = { module = "io.getstream:stream-chat-android-compose", version.ref = "stream" }
stream-chat-android-push = { module = "io.getstream:stream-android-push-firebase", version.ref = "streamPush" }
stream-chat-android-offline = { module = "io.getstream:stream-chat-android-offline", version.ref = "stream" }
truth = { group = "com.google.truth", name = "truth", version.ref = "truth" }
turbine = { group = "app.cash.turbine", name = "turbine", version.ref = "turbine" }
amplitude-analytics = { group = "com.amplitude", name = "analytics-android", version.ref = "amplitudeVersion" }
amplitude-analytics-experiments = { group = "com.amplitude", name = "experiment-android-client", version.ref = "amplitudeExperimentVersion" }
androidx-media3-exoplayer = { group = "androidx.media3", name = "media3-exoplayer", version.ref = "media3" }
androidx-media3-ui = { group = "androidx.media3", name = "media3-ui", version.ref = "media3" }
appsFlyer = { group = "com.appsflyer", name = "af-android-sdk", version.ref = "appsFlyer" }
guava = { group = "com.google.guava", name = "guava", version.ref = "guava" }
admob = { group = "com.google.android.gms", name = "play-services-ads", version.ref = "admob" }
koah = { group = "com.koahlabs", name = "koah-android", version.ref = "koah" }

# Dependencies of the included build-logic
android-gradlePlugin = { group = "com.android.tools.build", name = "gradle", version.ref = "androidGradlePlugin" }
android-tools-common = { group = "com.android.tools", name = "common", version.ref = "androidTools" }
firebase-crashlytics-gradlePlugin = { group = "com.google.firebase", name = "firebase-crashlytics-gradle", version.ref = "firebaseCrashlyticsPlugin" }
firebase-performance-gradlePlugin = { group = "com.google.firebase", name = "perf-plugin", version.ref = "firebasePerfPlugin" }
kotlin-gradlePlugin = { group = "org.jetbrains.kotlin", name = "kotlin-gradle-plugin", version.ref = "kotlin" }
ksp-gradlePlugin = { group = "com.google.devtools.ksp", name = "com.google.devtools.ksp.gradle.plugin", version.ref = "ksp" }
room-gradlePlugin = { group = "androidx.room", name = "room-gradle-plugin", version.ref = "room" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
play-services-base = { group = "com.google.android.gms", name = "play-services-base", version.ref = "playServicesBase" }
androidx-benchmark-macro-junit4 = { group = "androidx.benchmark", name = "benchmark-macro-junit4", version.ref = "benchmarkMacroJunit4" }
androidx-profileinstaller = { group = "androidx.profileinstaller", name = "profileinstaller", version.ref = "profileinstaller" }
androidx-lifecycle-service = { group = "androidx.lifecycle", name = "lifecycle-service", version.ref = "lifecycleService" }

[plugins]
android-application = { id = "com.android.application", version.ref = "androidGradlePlugin" }
android-library = { id = "com.android.library", version.ref = "androidGradlePlugin" }
android-test = { id = "com.android.test", version.ref = "androidGradlePlugin" }
baselineprofile = { id = "androidx.baselineprofile", version.ref = "androidxMacroBenchmark" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin"}
datadog = { id = "com.datadoghq.dd-sdk-android-gradle-plugin", version.ref = "datadogPluginVersion" }
dependencyGuard = { id = "com.dropbox.dependency-guard", version.ref = "dependencyGuard" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "firebaseCrashlyticsPlugin" }
firebase-perf = { id = "com.google.firebase.firebase-perf", version.ref = "firebasePerfPlugin" }
gms = { id = "com.google.gms.google-services", version.ref = "gmsPlugin" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
room = { id = "androidx.room", version.ref = "room" }
secrets = { id = "com.google.android.libraries.mapsplatform.secrets-gradle-plugin", version.ref = "secrets" }
detekt = { id = "io.gitlab.arturbosch.detekt", version.ref = "detekt" }
sonarqube = { id = "org.sonarqube", version.ref = "sonarqube" }


# Plugins defined by this project
luzia-android-application = { id = "luzia.android.application", version = "unspecified" }
luzia-android-application-compose = { id = "luzia.android.application.compose", version = "unspecified" }
luzia-android-application-firebase = { id = "luzia.android.application.firebase", version = "unspecified" }
luzia-android-application-flavors = { id = "luzia.android.application.flavors", version = "unspecified" }
luzia-android-application-jacoco = { id = "luzia.android.application.jacoco", version = "unspecified" }
luzia-android-feature = { id = "luzia.android.feature", version = "unspecified" }
luzia-android-hilt = { id = "luzia.android.hilt", version = "unspecified" }
luzia-android-library = { id = "luzia.android.library", version = "unspecified" }
luzia-android-library-compose = { id = "luzia.android.library.compose", version = "unspecified" }
luzia-android-library-jacoco = { id = "luzia.android.library.jacoco", version = "unspecified" }
luzia-android-lint = { id = "luzia.android.lint", version = "unspecified" }
luzia-android-room = { id = "luzia.android.room", version = "unspecified" }
luzia-android-test = { id = "luzia.android.test", version = "unspecified" }
luzia-jvm-library = { id = "luzia.jvm.library", version = "unspecified" }
jetbrains-kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlinVersion" }
