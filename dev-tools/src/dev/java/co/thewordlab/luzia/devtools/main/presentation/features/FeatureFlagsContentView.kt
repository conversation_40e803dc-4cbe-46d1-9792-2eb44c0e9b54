package co.thewordlab.luzia.devtools.main.presentation.features

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.RadioButton
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import co.thewordlab.luzia.devtools.main.presentation.DevToolsViewActions
import co.thewordlab.luzia.devtools.main.presentation.DevToolsViewState
import co.thewordlab.luzia.devtools.main.presentation.LocalFeatureFlag
import co.thewordlab.luzia.devtools.main.presentation.SwitchView
import co.thewordlab.luzia.foundation.config.FeatureFlagTreatment
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing


@Composable
fun FeatureFlagsContentView(
    viewState: DevToolsViewState,
    rippleColor: Color,
    onViewActions: (DevToolsViewActions) -> Unit,
) {
    FeatureFlagsBottomSheet(
        viewState = viewState,
        onViewActions = onViewActions,
        showFeatureFlags = viewState.showFeatureFlags,
        rippleColor = rippleColor
    )
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .navigationBarsPadding()
    ) {
        LuziaText(
            modifier = Modifier.padding(horizontal = Spacing.X16.dp),
            text = "Feature Flags",
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary
        )
        Spacer(
            Modifier
                .padding(horizontal = Spacing.X16.dp)
                .height(Spacing.X8.dp)
        )
        HorizontalDivider(modifier = Modifier.padding(horizontal = Spacing.X16.dp))
        SwitchView(
            title = "Use Local Feature Flags",
            enabled = viewState.isLocalConfigEnabled,
            rippleColor = rippleColor,
            onRowClicked = { onViewActions(DevToolsViewActions.OnShowFeatureFlags) },
            onValueChanged = { onViewActions(DevToolsViewActions.OnLocalConfigEnabled(it)) }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FeatureFlagsBottomSheet(
    viewState: DevToolsViewState,
    onViewActions: (DevToolsViewActions) -> Unit,
    showFeatureFlags: Boolean,
    rippleColor: Color
) {
    if (showFeatureFlags) {
        ModalBottomSheet(
            onDismissRequest = { onViewActions(DevToolsViewActions.OnDismissFeatureFlags) },
            containerColor = LuziaTheme.palette.surface.content
        ) {
            Column(modifier = Modifier.verticalScroll(rememberScrollState())) {
                viewState.featureFlags.forEach { flag ->
                    when (flag) {
                        is LocalFeatureFlag.BooleanFlag -> SwitchView(
                            title = flag.key,
                            enabled = flag.value,
                            rippleColor = rippleColor,
                            onRowClicked = {
                                onViewActions(
                                    DevToolsViewActions.OnBooleanFlagChanged(
                                        flag,
                                        !flag.value
                                    )
                                )
                            },
                            onValueChanged = {
                                onViewActions(
                                    DevToolsViewActions.OnBooleanFlagChanged(
                                        flag,
                                        !flag.value
                                    )
                                )
                            }
                        )

                        is LocalFeatureFlag.TreatmentFlag -> TreatmentSelectorView(
                            flag = flag,
                            rippleColor = rippleColor,
                            onValueChanged = {
                                onViewActions(
                                    DevToolsViewActions.OnTreatmentFlagChanged(
                                        flag,
                                        it
                                    )
                                )
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun TreatmentSelectorView(
    flag: LocalFeatureFlag.TreatmentFlag,
    rippleColor: Color,
    onValueChanged: (FeatureFlagTreatment) -> Unit
) {
    var treatmentSelectorVisible by remember { mutableStateOf(false) }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(Corners.X3.dp))
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = ripple(color = rippleColor),
                onClick = { treatmentSelectorVisible = !treatmentSelectorVisible }
            )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = Spacing.X16.dp, vertical = Spacing.X8.dp),
            horizontalArrangement = Arrangement.spacedBy(Spacing.X16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            LuziaText(
                modifier = Modifier.weight(0.5f),
                text = flag.key,
                style = LuziaTheme.typography.headlines.h4,
                color = LuziaTheme.palette.text.primary
            )
            LuziaText(
                text = flag.value.value,
                style = LuziaTheme.typography.headlines.h4,
                color = LuziaTheme.palette.text.primary
            )
        }
        AnimatedVisibility(
            visible = treatmentSelectorVisible
        ) {
            Column(
                modifier = Modifier
                    .wrapContentHeight()
                    .fillMaxWidth()
                    .background(LuziaTheme.palette.surface.alternative)
                    .padding(Spacing.X16.dp)
                    .padding(start = Spacing.X24.dp)
            ) {
                for (option in flag.options) {
                    ItemGroup(option.value == flag.value.value, option) {
                        treatmentSelectorVisible = false
                        onValueChanged(option)
                    }
                }
            }
        }
    }
}

@Composable
private fun ItemGroup(selected: Boolean, type: FeatureFlagTreatment, onItemSelected: () -> Unit) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .click { onItemSelected() },
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        LuziaText(
            text = type.value,
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary
        )
        RadioButton(
            selected = selected,
            onClick = onItemSelected,
            enabled = true
        )
    }
}
