package co.thewordlab.luzia.devtools.catalog.presentation

import co.thewordlab.luzia.devtools.catalog.presentation.components.DesignComponent
import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class CatalogViewEvents : ViewEvent {
    data object NavigateBack : CatalogViewEvents()
    data class NavigateToComponentDetails(val component: DesignComponent) : CatalogViewEvents()
}