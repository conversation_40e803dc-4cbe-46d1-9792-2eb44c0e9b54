package co.thewordlab.luzia.devtools.catalog.presentation

import co.thewordlab.luzia.devtools.catalog.presentation.components.DesignComponent
import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class CatalogViewActions : ViewAction {
    data object OnCreate : CatalogViewActions()
    data object OnBackClicked : CatalogViewActions()
    data class OnComponentClicked(val component: DesignComponent) : CatalogViewActions()
}