package co.thewordlab.luzia.devtools.main.data

import androidx.datastore.preferences.core.booleanPreferencesKey
import co.thewordlab.fouundation.persistence.LuziaDataStore
import javax.inject.Inject

class StrictModeRepository @Inject constructor(
    private val dataStore: LuziaDataStore
) {

    suspend fun saveStrictModeStatus(enabled: Boolean) =
        dataStore.saveData(strictModeEnabled, enabled)

    suspend fun isStrictModeEnabled(): Boolean =
        dataStore.getData(strictModeEnabled) ?: false

    companion object {
        private const val KEY_STRICT_MODE_ENABLED = "strict_mode_enabled"
        val strictModeEnabled = booleanPreferencesKey(KEY_STRICT_MODE_ENABLED)
    }
}
