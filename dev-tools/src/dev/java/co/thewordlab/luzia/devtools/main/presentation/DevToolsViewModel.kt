package co.thewordlab.luzia.devtools.main.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.devtools.DevToolsOrchestrator
import co.thewordlab.luzia.devtools.main.presentation.DevToolsViewEvents.NavigateBack
import co.thewordlab.luzia.devtools.main.presentation.DevToolsViewEvents.NavigateToCatalog
import co.thewordlab.luzia.devtools.main.presentation.DevToolsViewEvents.OpenChucker
import co.thewordlab.luzia.devtools.main.presentation.DevToolsViewEvents.OpenDeepLink
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagTreatment
import co.thewordlab.luzia.foundation.config.providers.local.LocalFeatureConfig
import co.thewordlab.luzia.foundation.networking.device.DeviceManagement
import co.thewordlab.luzia.foundation.networking.model.environments.DevEnvironment
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import co.thewordlab.luzia.foundation.networking.session.UserSessionMapper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class DevToolsViewModel @Inject constructor(
    private val deviceManagement: DeviceManagement,
    private val devToolsOrchestrator: DevToolsOrchestrator,
    private val localFeatureConfig: LocalFeatureConfig,
    private val sessionManager: UserSessionManager,
    private val userSessionMapper: UserSessionMapper
) : ViewModel(),
    ViewModelActions<DevToolsViewActions>,
    ViewModelEvents<DevToolsViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<DevToolsViewState> by ViewModelStatesImpl(DevToolsViewState()) {

    private var counter = 0

    override fun onViewAction(action: DevToolsViewActions) {
        when (action) {
            is DevToolsViewActions.OnCreate -> onCreate()
            is DevToolsViewActions.OnBackClicked -> sendEvent(NavigateBack)
            DevToolsViewActions.OnLogoClicked -> checkCounter()
            DevToolsViewActions.OnLogsClicked -> sendEvent(OpenChucker)
            DevToolsViewActions.OnCatalogClicked -> sendEvent(NavigateToCatalog)
            is DevToolsViewActions.OnLeakCanaryUpdated -> updateLeakCanary(action.isEnabled)
            is DevToolsViewActions.OnDeepLinkLaunched -> sendEvent(
                OpenDeepLink(action.deepLink)
            )

            is DevToolsViewActions.OnLocalConfigEnabled -> setLocalConfigEnabled(action.isEnabled)
            is DevToolsViewActions.OnBooleanFlagChanged -> setConfigValue(action.flag, action.value)
            is DevToolsViewActions.OnSaveSession -> saveSession(action.json)
            DevToolsViewActions.OnDismissFeatureFlags ->
                updateState { it.copy(showFeatureFlags = false) }

            DevToolsViewActions.OnShowFeatureFlags ->
                updateState { it.copy(showFeatureFlags = true) }

            is DevToolsViewActions.OnStrictModeUpdated -> updateStrictMode(action.isEnabled)
            is DevToolsViewActions.OnTreatmentFlagChanged ->
                setConfigValue(action.flag, action.value)


            DevToolsViewActions.OnShowEnvironments ->
                updateState { it.copy(showEnvironments = true) }

            DevToolsViewActions.OnHideEnvironments ->
                updateState { it.copy(showEnvironments = false) }

            is DevToolsViewActions.OnEnvironmentSelected -> {
                onEnvironmentSelected(action.environments)
            }
        }
    }

    private fun onEnvironmentSelected(action: Environments) = viewModelScope.launch {
        val environment = when (action) {
            Environments.AntonIA -> DevEnvironment.ANTONIA
            Environments.EugenIA -> DevEnvironment.EUGENIA
            Environments.MatIAs -> DevEnvironment.MATIAS
        }
        devToolsOrchestrator.saveEnvironment(environment)
        updateState {
            it.copy(
                showEnvironments = false,
                selectedEnvironment = action
            )
        }
    }

    private fun saveSession(json: String) {
        updateState { it.copy(session = json) }
        viewModelScope.launch {
            runCatching {
                sessionManager.saveRawSession(json)
            }
        }
    }

    private fun setLocalConfigEnabled(isEnabled: Boolean) {
        localFeatureConfig.setLocalConfigEnabled(isEnabled)
        updateState {
            it.copy(
                isLocalConfigEnabled = localFeatureConfig.isLocalConfigEnabled,
                showFeatureFlags = isEnabled
            )
        }
    }

    private fun setConfigValue(flag: LocalFeatureFlag.BooleanFlag, value: Boolean) {
        viewModelScope.launch {
            localFeatureConfig.save(flag.key, value)
            val flags = getLocalFeatureFlags()
            updateState { it.copy(featureFlags = flags) }
        }
    }

    private fun setConfigValue(flag: LocalFeatureFlag.TreatmentFlag, value: FeatureFlagTreatment) {
        viewModelScope.launch {
            localFeatureConfig.save(flag.key, value.value)
            val flags = getLocalFeatureFlags()
            updateState { it.copy(featureFlags = flags) }
        }
    }

    private fun updateLeakCanary(enabled: Boolean) = viewModelScope.launch {
        devToolsOrchestrator.saveLeakCanaryAvailability(enabled)
        updateState { viewState.value.copy(isLeakCanaryEnabled = enabled) }
    }

    private fun updateStrictMode(enabled: Boolean) = viewModelScope.launch {
        devToolsOrchestrator.saveStrictModeAvailability(enabled)
        updateState { viewState.value.copy(isStrictModeEnabled = enabled) }
    }

    private fun onCreate() = viewModelScope.launch {
        val isLeakCanaryEnabled = devToolsOrchestrator.isLeakCanaryEnabled()
        val isStrictModeEnabled = devToolsOrchestrator.isStrictModeEnabled()
        val isLocalConfigEnabled = localFeatureConfig.isLocalConfigEnabled
        val environment = mapEnvironment()
        val flags = getLocalFeatureFlags()
        updateState {
            DevToolsViewState(
                adId = deviceManagement.getAdvertisingId(),
                isLeakCanaryEnabled = isLeakCanaryEnabled,
                isLocalConfigEnabled = isLocalConfigEnabled,
                isStrictModeEnabled = isStrictModeEnabled,
                featureFlags = flags,
                selectedEnvironment = environment
            )
        }
        sessionManager.userSession
            .onEach { session ->
                updateState {
                    it.copy(
                        externalId = session?.deviceKey.orEmpty(),
                        session = session?.let { userSessionMapper.mapToJson(session) }.orEmpty()
                    )
                }
            }
            .launchIn(viewModelScope)
    }

    private suspend fun getLocalFeatureFlags(): List<LocalFeatureFlag> =
        FeatureFlag.entries.toList().mapNotNull { flag ->
            when (flag.defaultValue) {
                is Boolean -> LocalFeatureFlag.BooleanFlag(
                    key = flag.key,
                    value = localFeatureConfig.get<Boolean>(flag) ?: flag.defaultValue as Boolean
                )

                is String -> {
                    val value = localFeatureConfig.get<String>(flag) ?: flag.defaultValue as String
                    LocalFeatureFlag.TreatmentFlag(
                        key = flag.key,
                        value = FeatureFlagTreatment.getValue(value),
                        options = topOptionsList()
                    )
                }

                else -> null
            }
        }

    private fun topOptionsList(): List<FeatureFlagTreatment> {
        return listOf(
            FeatureFlagTreatment.CONTROL,
            FeatureFlagTreatment.TREATMENT_A,
            FeatureFlagTreatment.TREATMENT_B,
            FeatureFlagTreatment.TREATMENT_C
        )
    }

    private fun checkCounter() {
        counter++
        updateState { viewState.value.copy(showBackground = counter == LUZIA_COUNTER) }
    }

    private suspend fun mapEnvironment() =
        when (devToolsOrchestrator.getEnvironment()) {
            DevEnvironment.ANTONIA -> Environments.AntonIA
            DevEnvironment.MATIAS -> Environments.MatIAs
            DevEnvironment.EUGENIA -> Environments.EugenIA
        }

    private companion object {
        const val LUZIA_COUNTER = 7
    }
}
