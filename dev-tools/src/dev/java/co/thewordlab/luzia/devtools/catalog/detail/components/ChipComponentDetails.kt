package co.thewordlab.luzia.devtools.catalog.detail.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import co.thewordlab.luzia.devtools.catalog.presentation.components.ComponentTitle
import co.thewordlab.luzia.devtools.catalog.presentation.components.SectionTitle
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.lds.chips.ChipState
import co.theworldlab.luzia.foundation.design.system.components.lds.chips.LuziaChip
import co.theworldlab.luzia.foundation.design.system.components.lds.chips.LuziaChipComment
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing


@Composable
fun ChipComponentDetails() {

    val list = listOf(
        Pair("Enabled", ChipState.ENABLED),
        Pair("Disabled", ChipState.DISABLED),
        Pair("Selected", ChipState.SELECTED),
    )

    SectionTitle("Chips - Default")
    list.forEach { (title, state) ->
        ComponentTitle(title)
        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
            LuziaChip(text = "✉\uFE0FChip", state = state) { DO_NOTHING }
        }
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
    }
    Spacer(modifier = Modifier.height(Spacing.X16.dp))
    SectionTitle("Chips - Comments")
    list.forEach { (title, state) ->
        ComponentTitle(title)
        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
            LuziaChipComment(
                title = "✉\uFE0F Short & Sweet",
                subtitle = "K, see you there.",
                state = state
            ) { DO_NOTHING }
        }
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
    }
    Spacer(modifier = Modifier.height(Spacing.X16.dp))
}