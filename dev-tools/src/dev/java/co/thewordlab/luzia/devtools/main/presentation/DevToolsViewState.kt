package co.thewordlab.luzia.devtools.main.presentation

import androidx.annotation.DrawableRes
import co.thewordlab.luzia.dev_tools.R
import co.thewordlab.luzia.foundation.architecture.system.ViewState
import co.thewordlab.luzia.foundation.config.FeatureFlagTreatment


data class DevToolsViewState(
    val adId: String = "",
    val externalId: String = "",
    val session: String = "",
    val showBackground: Boolean = false,
    val isLeakCanaryEnabled: Boolean = false,
    val isStrictModeEnabled: Boolean = false,
    val isLocalConfigEnabled: Boolean = false,
    val showFeatureFlags: Boolean = false,
    val showEnvironments: Boolean = false,
    val featureFlags: List<LocalFeatureFlag> = emptyList(),
    val selectedEnvironment: Environments = Environments.AntonIA
) : ViewState

sealed class LocalFeatureFlag {
    data class BooleanFlag(val key: String, val value: Boolean) : LocalFeatureFlag()
    data class TreatmentFlag(
        val key: String,
        val value: FeatureFlagTreatment,
        val options: List<FeatureFlagTreatment> = listOf(
            FeatureFlagTreatment.CONTROL,
            FeatureFlagTreatment.TREATMENT_A,
            FeatureFlagTreatment.TREATMENT_B,
            FeatureFlagTreatment.TREATMENT_C
        )
    ) : LocalFeatureFlag()
}

sealed class Environments(val name: String, @DrawableRes val icon: Int) {
    data object EugenIA : Environments("EugenIA", R.drawable.im_eugenia_background)
    data object MatIAs : Environments("MatIAs", R.drawable.im_matias_background)
    data object AntonIA : Environments("AntonIA", R.drawable.im_antonia_background)

    companion object {
        fun getEnvironments() = listOf(EugenIA, MatIAs, AntonIA)
    }
}