package co.thewordlab.luzia.devtools.catalog.detail

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberTopAppBarState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.devtools.catalog.detail.components.ButtonsComponentDetails
import co.thewordlab.luzia.devtools.catalog.detail.components.CardsComponentDetails
import co.thewordlab.luzia.devtools.catalog.detail.components.ChipComponentDetails
import co.thewordlab.luzia.devtools.catalog.detail.components.FeedbackComponentDetails
import co.thewordlab.luzia.devtools.catalog.detail.components.ListItemsComponentDetails
import co.thewordlab.luzia.devtools.catalog.detail.components.SkeletonComponentDetails
import co.thewordlab.luzia.devtools.catalog.detail.components.TextInputComponentDetails
import co.thewordlab.luzia.devtools.catalog.presentation.components.DesignComponent
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.navbar.LuziaNavBarDefaults
import co.theworldlab.luzia.foundation.design.system.components.navbar.NavigationAction
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBar
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBarModel
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun ComponentDetailsScreen(
    componentId: String,
    onNavigateBack: () -> Unit
) {
    val viewModel: ComponentDetailsViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()

    OnCreate("ComponentDetailsScreen") {
        viewModel.onViewAction(ComponentDetailsViewActions.OnCreate(componentId))
    }

    ViewModelEventEffect(viewModel) {
        when (it) {
            ComponentDetailsViewEvents.NavigateBack -> onNavigateBack()
        }
    }

    ComponentDetailsContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ComponentDetailsContent(
    viewState: ComponentDetailsViewState,
    onViewActions: (ComponentDetailsViewActions) -> Unit
) {
    val scrollBehavior = TopAppBarDefaults.enterAlwaysScrollBehavior(rememberTopAppBarState())

    Scaffold(
        modifier = Modifier.nestedScroll(scrollBehavior.nestedScrollConnection),
        topBar = {
            Column {
                TopNavigationBar(
                    TopNavigationBarModel(
                        title = viewState.component?.displayName ?: "Component Details",
                        navigationAction = NavigationAction.Icon(R.drawable.ic_back_arrow) {
                            onViewActions(ComponentDetailsViewActions.OnBackClicked)
                        },
                        colors = LuziaNavBarDefaults.colors(
                            containerColor = LuziaTheme.palette.surface.background,
                            scrolledContainerColor = LuziaTheme.palette.surface.background
                        ),
                        scrollBehavior = scrollBehavior
                    )
                )
            }
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(LuziaTheme.palette.surface.background)
                .verticalScroll(rememberScrollState())
                .padding(innerPadding)
                .padding(horizontal = Spacing.X16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            viewState.component?.let { component ->
                when (component) {
                    DesignComponent.Buttons -> ButtonsComponentDetails()
                    DesignComponent.Chips -> ChipComponentDetails()
                    DesignComponent.TextInput -> TextInputComponentDetails()
                    DesignComponent.Cards -> CardsComponentDetails()
                    DesignComponent.ListItems -> ListItemsComponentDetails()
                    DesignComponent.Feedback -> FeedbackComponentDetails()
                    DesignComponent.Skeleton -> SkeletonComponentDetails()
                }
            }
        }
    }
}

@Preview
@Composable
private fun ComponentDetailsPreview() {
    LuziaTheme {
        ComponentDetailsContent(
            viewState = ComponentDetailsViewState(component = DesignComponent.Buttons),
            onViewActions = { DO_NOTHING }
        )
    }
} 