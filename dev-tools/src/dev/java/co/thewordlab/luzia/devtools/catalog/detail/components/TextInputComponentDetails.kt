package co.thewordlab.luzia.devtools.catalog.detail.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import co.thewordlab.luzia.devtools.catalog.presentation.components.ComponentTitle
import co.thewordlab.luzia.devtools.catalog.presentation.components.SectionTitle
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.lds.textinput.LuziaTextInput
import co.theworldlab.luzia.foundation.design.system.components.lds.textinput.TextInputState
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing


@Composable
fun TextInputComponentDetails() {
    val list = listOf(
        Triple("Enabled", TextInputState.ENABLED, -1),
        Triple("Disabled", TextInputState.DISABLED, -1),
        Triple("Filled", TextInputState.FILLED, 100),
        Triple("Error", TextInputState.ERROR, 100),
    )

    SectionTitle("Text Input - Default")
    list.forEach { (title, state, maxLength) ->
        ComponentTitle(title)
        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
            LuziaTextInput(
                value = title,
                hint = "Hint",
                state = state,
                maxLength = maxLength
            ) { DO_NOTHING }
        }
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
    }
    Spacer(modifier = Modifier.height(Spacing.X16.dp))
}