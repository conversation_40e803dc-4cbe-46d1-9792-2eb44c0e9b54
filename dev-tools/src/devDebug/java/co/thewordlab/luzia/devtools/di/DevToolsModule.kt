package co.thewordlab.luzia.devtools.di

import co.thewordlab.luzia.devtools.DevToolsOrchestrator
import co.thewordlab.luzia.devtools.DevToolsOrchestratorImp
import co.thewordlab.luzia.devtools.leakcanary.data.LeakCanaryRepository
import co.thewordlab.luzia.devtools.leakcanary.domain.SaveLeakCanaryAvailabilityUseCase
import co.thewordlab.luzia.devtools.main.data.StrictModeRepository
import co.thewordlab.luzia.devtools.main.domain.HandleEnvironmentUseCase
import co.thewordlab.luzia.devtools.main.domain.SaveStrictModeAvailabilityUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
object DevToolsModule {

    @Provides
    fun provideOrchestrator(
        leakCanaryRepository: LeakCanaryRepository,
        saveLeakCanaryAvailabilityUseCase: SaveLeakCanaryAvailabilityUseCase,
        saveStrictModeAvailabilityUseCase: SaveStrictModeAvailabilityUseCase,
        strictModeRepository: StrictModeRepository,
        handleEnvironmentUseCase: HandleEnvironmentUseCase
    ): DevToolsOrchestrator = DevToolsOrchestratorImp(
        leakCanaryRepo = leakCanaryRepository,
        strictModeRepo = strictModeRepository,
        saveLeakCanaryAvailabilityUseCase = saveLeakCanaryAvailabilityUseCase,
        saveStrictModeAvailabilityUseCase = saveStrictModeAvailabilityUseCase,
        handleEnvironmentUseCase = handleEnvironmentUseCase
    )
}