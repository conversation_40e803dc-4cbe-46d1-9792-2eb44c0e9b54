package co.thewordlab.luzia.devtools.catalog.presentation

import app.cash.turbine.test
import co.thewordlab.luzia.devtools.catalog.presentation.components.DesignComponent
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

class CatalogViewModelTest {

    private lateinit var viewModel: CatalogViewModel

    @Before
    fun setup() {
        viewModel = CatalogViewModel()
    }

    @Test
    fun `initial state has correct components list`() {
        val initialState = viewModel.viewState.value
        assertEquals(7, initialState.components.size)
        assertTrue(initialState.components.contains(DesignComponent.Buttons))
        assertTrue(initialState.components.contains(DesignComponent.Chips))
        assertTrue(initialState.components.contains(DesignComponent.TextInput))
        assertTrue(initialState.components.contains(DesignComponent.Cards))
        assertTrue(initialState.components.contains(DesignComponent.ListItems))
        assertTrue(initialState.components.contains(DesignComponent.Feedback))
        assertTrue(initialState.components.contains(DesignComponent.Skeleton))
        assertFalse(initialState.isLoading)
    }

    @Test
    fun `onCreate action does not change state`() {
        val initialState = viewModel.viewState.value
        viewModel.onViewAction(CatalogViewActions.OnCreate)
        assertEquals(initialState, viewModel.viewState.value)
    }

    @Test
    fun `onBackClicked action handles back navigation`() = runTest {
        viewModel.viewEvent.test {
            viewModel.onViewAction(CatalogViewActions.OnBackClicked)
            assertEquals(listOf(CatalogViewEvents.NavigateBack), listOf(awaitItem()))
        }
    }

    @Test
    fun `onComponentClicked action handles component navigation`() = runTest {
        val component = DesignComponent.ListItems
        viewModel.viewEvent.test {
            viewModel.onViewAction(CatalogViewActions.OnComponentClicked(component))
            assertEquals(
                listOf(CatalogViewEvents.NavigateToComponentDetails(component)),
                listOf(awaitItem())
            )
        }
    }
}
