package co.thewordlab.luzia.features.chat.presentation.details

import android.content.Context
import co.thewordlab.luzia.foundation.localization.R
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

@Suppress("MaxLineLength")
class ChatTooltipsTest {

    private lateinit var sut: ChatTooltips

    private val context: Context = mockk(relaxed = true)

    @Before
    fun setUp() {
        sut = ChatTooltips(context)
        every { context.getString(R.string.onboarding_icebreakers_tooltip) } returns "Icebreakers tooltip"
        every { context.getString(R.string.onboarding_send_file_tooltip) } returns "Send file tooltip"
        every { context.getString(R.string.onboarding_tools_tooltip) } returns "Tools tooltip"
        every { context.getString(R.string.onboarding_response_styles_tooltip) } returns "Response styles tooltip"
        every { context.getString(R.string.onboarding_web_tooltip) } returns "Web tooltip"
    }

    @Test
    fun `given includeWebSearch is false when getTooltips then returns base tooltips`() = runTest {
        val tooltips = sut.getTooltips(includeWebSearch = false, buttonAction = {})

        assertEquals(4, tooltips.size)
        assertTrue(tooltips.any { it.id == ChatTooltips.ID_ICEBREAKERS && it.totalSteps == 4 && it.visible })
        assertTrue(tooltips.any { it.id == ChatTooltips.ID_ADD_COMPOSER && it.totalSteps == 4 && !it.visible })
        assertTrue(tooltips.any { it.id == ChatTooltips.ID_TOOLS_COMPOSER && it.totalSteps == 4 && !it.visible })
        assertTrue(
            tooltips.any { it.id == ChatTooltips.ID_RESPONSE_STYLE_COMPOSER && it.totalSteps == 4 && !it.visible }
        )
        assertFalse(tooltips.any { it.id == ChatTooltips.ID_WEB_SEARCH_COMPOSER })
    }

    @Test
    fun `given includeWebSearch is true when getTooltips then returns all tooltips including web search`() = runTest {
        val tooltips = sut.getTooltips(includeWebSearch = true, buttonAction = {})

        assertEquals(5, tooltips.size)
        assertTrue(tooltips.any { it.id == ChatTooltips.ID_ICEBREAKERS && it.totalSteps == 5 && it.visible })
        assertTrue(tooltips.any { it.id == ChatTooltips.ID_ADD_COMPOSER && it.totalSteps == 5 && !it.visible })
        assertTrue(tooltips.any { it.id == ChatTooltips.ID_TOOLS_COMPOSER && it.totalSteps == 5 && !it.visible })
        assertTrue(
            tooltips.any { it.id == ChatTooltips.ID_RESPONSE_STYLE_COMPOSER && it.totalSteps == 5 && !it.visible }
        )
        assertTrue(tooltips.any { it.id == ChatTooltips.ID_WEB_SEARCH_COMPOSER && it.totalSteps == 5 && !it.visible })
    }

    @Test
    fun `given tooltips generated when buttonAction is invoked then it calls the provided lambda`() = runTest {
        val mockButtonAction = mockk<(String) -> Unit>(relaxed = true)
        val tooltips = sut.getTooltips(includeWebSearch = false, buttonAction = mockButtonAction)

        tooltips.find { it.id == ChatTooltips.ID_ICEBREAKERS }?.buttonAction?.invoke()
        verify { mockButtonAction(ChatTooltips.ID_ICEBREAKERS) }
    }

    @Test
    fun `given base tooltips when showNextTooltip with ID_ICEBREAKERS then returns tooltips with ID_ADD_COMPOSER visible`() =
        runTest {
            sut.getTooltips(includeWebSearch = false, buttonAction = {})
            val tooltips = sut.showNextTooltip(ChatTooltips.ID_ICEBREAKERS)

            assertEquals(4, tooltips.size)
            assertFalse(tooltips.first { it.id == ChatTooltips.ID_ICEBREAKERS }.visible)
            assertTrue(tooltips.first { it.id == ChatTooltips.ID_ADD_COMPOSER }.visible)
        }

    @Test
    fun `given base tooltips when showNextTooltip with ID_ADD_COMPOSER then returns tooltips with ID_TOOLS_COMPOSER visible`() =
        runTest {
            sut.getTooltips(includeWebSearch = false, buttonAction = {})
            sut.showNextTooltip(ChatTooltips.ID_ICEBREAKERS)
            val tooltips = sut.showNextTooltip(ChatTooltips.ID_ADD_COMPOSER)

            assertEquals(4, tooltips.size)
            assertFalse(tooltips.first { it.id == ChatTooltips.ID_ADD_COMPOSER }.visible)
            assertTrue(tooltips.first { it.id == ChatTooltips.ID_TOOLS_COMPOSER }.visible)
        }

    @Test
    fun `given base tooltips when showNextTooltip with ID_TOOLS_COMPOSER then returns tooltips with ID_RESPONSE_STYLE_COMPOSER visible`() =
        runTest {
            sut.getTooltips(includeWebSearch = false, buttonAction = {})
            sut.showNextTooltip(ChatTooltips.ID_ICEBREAKERS)
            sut.showNextTooltip(ChatTooltips.ID_ADD_COMPOSER)
            val tooltips = sut.showNextTooltip(ChatTooltips.ID_TOOLS_COMPOSER)

            assertEquals(4, tooltips.size)
            assertFalse(tooltips.first { it.id == ChatTooltips.ID_TOOLS_COMPOSER }.visible)
            assertTrue(tooltips.first { it.id == ChatTooltips.ID_RESPONSE_STYLE_COMPOSER }.visible)
        }

    @Test
    fun `given base tooltips when showNextTooltip with ID_RESPONSE_STYLE_COMPOSER then returns empty list`() =
        runTest {
            sut.getTooltips(includeWebSearch = false, buttonAction = {})
            sut.showNextTooltip(ChatTooltips.ID_ICEBREAKERS)
            sut.showNextTooltip(ChatTooltips.ID_ADD_COMPOSER)
            sut.showNextTooltip(ChatTooltips.ID_TOOLS_COMPOSER)
            val tooltips = sut.showNextTooltip(ChatTooltips.ID_RESPONSE_STYLE_COMPOSER)

            assertTrue(tooltips.isEmpty())
        }

    @Test
    fun `given tooltips with web search when showNextTooltip with ID_RESPONSE_STYLE_COMPOSER then returns tooltips with ID_WEB_SEARCH_COMPOSER visible`() =
        runTest {
            sut.getTooltips(includeWebSearch = true, buttonAction = {})
            sut.showNextTooltip(ChatTooltips.ID_ICEBREAKERS)
            sut.showNextTooltip(ChatTooltips.ID_ADD_COMPOSER)
            sut.showNextTooltip(ChatTooltips.ID_TOOLS_COMPOSER)
            val tooltips = sut.showNextTooltip(ChatTooltips.ID_RESPONSE_STYLE_COMPOSER)

            assertEquals(5, tooltips.size)
            assertFalse(tooltips.first { it.id == ChatTooltips.ID_RESPONSE_STYLE_COMPOSER }.visible)
            assertTrue(tooltips.first { it.id == ChatTooltips.ID_WEB_SEARCH_COMPOSER }.visible)
        }

    @Test
    fun `given tooltips with web search when showNextTooltip with ID_WEB_SEARCH_COMPOSER then returns empty list`() =
        runTest {
            sut.getTooltips(includeWebSearch = true, buttonAction = {})
            sut.showNextTooltip(ChatTooltips.ID_ICEBREAKERS)
            sut.showNextTooltip(ChatTooltips.ID_ADD_COMPOSER)
            sut.showNextTooltip(ChatTooltips.ID_TOOLS_COMPOSER)
            sut.showNextTooltip(ChatTooltips.ID_RESPONSE_STYLE_COMPOSER)
            val tooltips = sut.showNextTooltip(ChatTooltips.ID_WEB_SEARCH_COMPOSER)

            assertTrue(tooltips.isEmpty())
        }

    @Test
    fun `given no tooltips loaded when showNextTooltip then returns empty list`() = runTest {
        val tooltips = sut.showNextTooltip(ChatTooltips.ID_ICEBREAKERS)
        assertTrue(tooltips.isEmpty())
    }

    @Test
    fun `given unknown id when showNextTooltip then returns current tooltips without changes`() = runTest {
        sut.getTooltips(includeWebSearch = false, buttonAction = {})
        val tooltips = sut.showNextTooltip("unknown_id")

        assertEquals(0, tooltips.size)
    }
}
