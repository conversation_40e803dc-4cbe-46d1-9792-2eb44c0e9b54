package co.thewordlab.luzia.features.chat.presentation.responses.signup

import androidx.lifecycle.ViewModel
import co.thewordlab.luzia.features.chat.ORIGIN
import co.thewordlab.luzia.features.chat.ORIGIN_CHAT
import co.thewordlab.luzia.features.chat.ORIGIN_LUZIA_PROFILE
import co.thewordlab.luzia.features.chat.ResponseStyleSignupClicked
import co.thewordlab.luzia.features.chat.ResponseStyleSignupScreen
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class ResponseStylesSignupViewModel @Inject constructor(
    private val analytics: Analytics
) :
    ViewModel(),
    ViewModelActions<ResponseStylesSignupViewActions> {

    private var openFromChat: Boolean = false

    override fun onViewAction(action: ResponseStylesSignupViewActions) {
        when (action) {
            is ResponseStylesSignupViewActions.OnCreate -> {
                openFromChat = action.openFromChat
                analytics.logScreenWithProps(ResponseStyleSignupScreen, buildAttributes())
            }

            ResponseStylesSignupViewActions.OnNavigateToSignupClicked ->
                analytics.logActionWithProps(ResponseStyleSignupClicked, buildAttributes())
        }
    }

    private fun buildAttributes() =
        mapOf(ORIGIN to if (openFromChat) ORIGIN_CHAT else ORIGIN_LUZIA_PROFILE)
}
