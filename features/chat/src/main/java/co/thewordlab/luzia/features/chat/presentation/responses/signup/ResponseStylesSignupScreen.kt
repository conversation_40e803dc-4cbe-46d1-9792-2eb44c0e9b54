package co.thewordlab.luzia.features.chat.presentation.responses.signup

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonFilled
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import kotlinx.coroutines.launch
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ResponseStyleModalInView(
    onSignup: () -> Unit,
    onDismiss: () -> Unit,
) {
    val bottomSheetState = rememberModalBottomSheetState(true, confirmValueChange = { false })
    val coroutineScope = rememberCoroutineScope()
    fun closeSheet() {
        coroutineScope.launch {
            bottomSheetState.hide()
            onDismiss()
        }
    }
    BackHandler { onDismiss() }
    ModalBottomSheet(
        containerColor = LuziaTheme.palette.surface.background,
        dragHandle = null,
        onDismissRequest = { closeSheet() }
    ) {
        ResponseStylesSignupModalContent(
            cancelableOutside = false,
            openFromChat = false,
            onNavigateToSignup = onSignup,
            onClose = { closeSheet() }
        )
    }
}

@Composable
fun ResponseStylesSignupModal(onNavigateToSignup: () -> Unit) {
    ResponseStylesSignupModalContent(
        cancelableOutside = true,
        openFromChat = true,
        onNavigateToSignup = onNavigateToSignup,
        onClose = { DO_NOTHING }
    )
}

@Composable
private fun ResponseStylesSignupModalContent(
    cancelableOutside: Boolean,
    openFromChat: Boolean,
    onNavigateToSignup: () -> Unit,
    onClose: () -> Unit
) {
    val viewModel: ResponseStylesSignupViewModel = hiltViewModel()
    OnCreate("ResponseStylesSignupModalContent") {
        viewModel.onViewAction(ResponseStylesSignupViewActions.OnCreate(openFromChat))
    }

    val (mainColor, secondaryColor) = listOf(
        LuziaTheme.palette.accents.blue.blue10,
        LuziaTheme.palette.accents.blue.blue90
    )
    Column(modifier = Modifier.padding(Spacing.X16.dp)) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier
                    .background(color = secondaryColor, shape = CircleShape)
                    .padding(vertical = Spacing.X8.dp, horizontal = Spacing.X12.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
            ) {
                Icon(
                    modifier = Modifier
                        .size(IconSizes.X24.dp)
                        .background(mainColor, shape = CircleShape),
                    painter = painterResource(id = designR.drawable.ic_pencil_32),
                    contentDescription = null,
                    tint = secondaryColor
                )
                LuziaText(
                    text = stringResource(id = localizationR.string.response_style_text),
                    style = LuziaTheme.typography.body.semiBold.small,
                    color = mainColor
                )
            }
            if (!cancelableOutside) {
                IconButton(
                    modifier = Modifier
                        .padding(Spacing.X8.dp)
                        .size(IconSizes.X32.dp),
                    onClick = onClose
                ) {
                    Icon(
                        modifier = Modifier.size(IconSizes.X24.dp),
                        painter = painterResource(id = R.drawable.ic_close),
                        contentDescription = null,
                        tint = LuziaTheme.palette.text.primary
                    )
                }
            }
        }
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
        LuziaText(
            text = stringResource(id = localizationR.string.response_style_signup_title),
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary
        )
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
        LuziaText(
            text = stringResource(id = localizationR.string.response_style_signup_desc),
            style = LuziaTheme.typography.body.semiBold.small,
            color = LuziaTheme.palette.text.secondary
        )
        Spacer(modifier = Modifier.height(Spacing.X8.dp))
        Row(verticalAlignment = Alignment.CenterVertically) {
            Icon(
                modifier = Modifier.size(IconSizes.X16.dp),
                painter = painterResource(id = R.drawable.ic_time),
                contentDescription = null,
                tint = LuziaTheme.palette.text.helper
            )
            Spacer(modifier = Modifier.width(Spacing.X16.dp))
            LuziaText(
                text = stringResource(id = localizationR.string.group_chat_mins),
                style = LuziaTheme.typography.body.regular.footnote,
                color = LuziaTheme.palette.text.helper
            )
        }
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
        ButtonFilled(
            modifier = Modifier.fillMaxWidth(),
            onClick = {
                onNavigateToSignup()
                viewModel.onViewAction(ResponseStylesSignupViewActions.OnNavigateToSignupClicked)
            },
            buttonText = stringResource(id = localizationR.string.profile_signup_title_cta)
        )
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        ResponseStylesSignupModalContent(
            cancelableOutside = false,
            openFromChat = false,
            onNavigateToSignup = { DO_NOTHING },
            onClose = { DO_NOTHING }
        )
    }
}
