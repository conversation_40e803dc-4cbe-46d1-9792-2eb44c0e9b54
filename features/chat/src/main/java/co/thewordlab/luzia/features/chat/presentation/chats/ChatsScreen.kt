package co.thewordlab.luzia.features.chat.presentation.chats

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarResult
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.fouundation.persistence.chat.ChatView
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.features.personality.domain.LUZIA_ID_PERSONALITY
import co.thewordlab.luzia.features.personality.presentation.custombestie.models.AddCustomBestiePosition
import co.thewordlab.luzia.features.personality.presentation.custombestie.models.PersonalitiesViewState
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.date.formatDateForChats
import co.theworldlab.luzia.foundation.design.system.components.item.SwipeActionContent
import co.theworldlab.luzia.foundation.design.system.components.item.SwipeToActions
import co.theworldlab.luzia.foundation.design.system.components.item.SwipeToItem
import co.theworldlab.luzia.foundation.design.system.components.item.SwipeToModel
import co.theworldlab.luzia.foundation.design.system.components.message.banner.BannerType
import co.theworldlab.luzia.foundation.design.system.components.message.banner.BannerUiModel
import co.theworldlab.luzia.foundation.design.system.components.message.banner.BannerView
import co.theworldlab.luzia.foundation.design.system.components.message.banner.EndIconType
import co.theworldlab.luzia.foundation.design.system.components.navbar.SectionHeader
import co.theworldlab.luzia.foundation.design.system.components.personality.AddPersonalityView
import co.theworldlab.luzia.foundation.design.system.components.personality.PersonalityView
import co.theworldlab.luzia.foundation.design.system.components.scaffold.LocalAppState
import co.theworldlab.luzia.foundation.design.system.helpers.addShadow
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.legacy.composables.LuziaCardDefaults
import co.theworldlab.luzia.foundation.design.system.model.UiImage
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import java.util.Calendar
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun ChatsScreen() {
    val chatsViewModel: ChatsViewModel = hiltViewModel()
    val viewState by chatsViewModel.viewState.collectAsStateWithLifecycle()
    val appState = LocalAppState.current
    val context = LocalContext.current
    val navigation = LocalNavigation.current

    ViewModelEventEffect(chatsViewModel) { event ->
        when (event) {
            is ChatViewEvents.ShowDeleteConfirmation -> {
                appState.showSnackBarWithAction(
                    message = context.getString(
                        localizationR.string.chat_deleted,
                        event.chat.name.orEmpty()
                    ),
                    actionLabel = context.getString(localizationR.string.delete_multiple_messages_undo),
                    duration = SnackbarDuration.Short,
                    onActionReceived = { action ->
                        when (action) {
                            SnackbarResult.Dismissed -> chatsViewModel.onViewAction(
                                ChatViewActions.OnDeleteConfirmed
                            )

                            SnackbarResult.ActionPerformed -> chatsViewModel.onViewAction(
                                ChatViewActions.OnDeleteCancelled
                            )
                        }
                    }
                )
            }

            ChatViewEvents.NavigateToCustomBestie ->
                navigation.navigate(UserSessionRoutes.CustomBestieCreation)

            ChatViewEvents.NavigateToCustomBestieCreationNotAllowed ->
                navigation.navigate(UserSessionRoutes.CustomBestieCreationModal)

            ChatViewEvents.NavigateToCustomBestieSignup ->
                navigation.navigate(UserSessionRoutes.CustomBestieSignupModal)

            is ChatViewEvents.NavigateToPersonality ->
                navigation.navigate(
                    UserSessionRoutes.ChatDetail(
                        personalityId = event.personalityId,
                        isCustomBestie = event.isCustomBestie
                    )
                )
        }
    }

    OnCreate("ChatsScreen") { chatsViewModel.onViewAction(ChatViewActions.OnCreate) }
    ChatContent(
        viewState,
        chatsViewModel::onViewAction
    )
}

@Composable
private fun ChatContent(
    viewState: ChatViewState,
    onViewAction: (ChatViewActions) -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(LuziaTheme.palette.surface.background)
            .verticalScroll(rememberScrollState())
            .testTag("containerChatList"),
    ) {
        if (viewState.showBestiePointsBanner) {
            Spacer(modifier = Modifier.height(Spacing.X16.dp))
            BannerView(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = Spacing.X16.dp),
                model = BannerUiModel(
                    title = stringResource(id = localizationR.string.streaks_bp_challenge_chat_with_me),
                    onAction = { onViewAction(ChatViewActions.OnBannerCloseClicked) },
                    endIconType = EndIconType.CLOSE,
                    type = BannerType.Thunder
                )
            )
        }

        val finalChats =
            if (viewState.chats.firstOrNull { it.chatId == LUZIA_ID_PERSONALITY } == null) {
                val luzia =
                    viewState.personalitiesViewState.personalities.firstOrNull {
                        it.personalityId == LUZIA_ID_PERSONALITY
                    }
                val chatView = ChatView(
                    chatId = luzia?.personalityId,
                    text = luzia?.welcomeMessage,
                    name = luzia?.name,
                    iconResourceId = if (luzia?.avatar is UiImage.Resource) {
                        (luzia.avatar as UiImage.Resource).resourceId
                    } else {
                        null
                    },
                    thumbnail = null,
                    timeStamp = null,
                    isRead = true,
                    proactivePayload = null,
                    isCustomBestie = false
                )
                viewState.chats + chatView
            } else {
                viewState.chats
            }
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
        ActiveChatsContent(finalChats, onViewAction)
        Spacer(modifier = Modifier.height(Spacing.X12.dp))
        if (viewState.personalitiesViewState.personalities.isNotEmpty()) {
            NonActiveChatsContent(
                viewState.personalitiesViewState,
                viewState.chats,
                onViewAction
            )
        }
    }
}

@Composable
private fun ActiveChatsContent(
    chats: List<ChatView>,
    onViewAction: (ChatViewActions) -> Unit
) {
    Box(modifier = Modifier.padding(vertical = Spacing.X8.dp, horizontal = Spacing.X16.dp)) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .addShadow(),
            colors = LuziaCardDefaults.cardColors(),
        ) {
            Spacer(modifier = Modifier.height(Spacing.X8.dp))
            SectionHeader(
                modifier = Modifier.padding(horizontal = Spacing.X16.dp, vertical = Spacing.X8.dp),
                title = stringResource(id = localizationR.string.your_chats)
            )
            chats.forEachIndexed { index, chat ->
                DismissiblePersonalityItem(chat, onViewAction)
                if (index != chats.lastIndex && chats.size != 1) {
                    HorizontalDivider(
                        modifier = Modifier.padding(horizontal = Spacing.X16.dp),
                        color = LuziaTheme.palette.surface.background
                    )
                }
            }
            Spacer(modifier = Modifier.height(Spacing.X8.dp))
        }
    }
}

@Composable
private fun DismissiblePersonalityItem(
    chat: ChatView,
    onViewAction: (ChatViewActions) -> Unit
) {
    val context = LocalContext.current
    SwipeToItem(
        model = SwipeToModel(
            data = chat,
            endContent = SwipeActionContent(
                enableSwipe = chat.timeStamp != null,
                enableConfirmation = true,
                icon = designR.drawable.ic_delete_24,
                confirmationTitle = stringResource(id = localizationR.string.delete_this_chat_confirmation_title),
                confirmationDesc = stringResource(id = localizationR.string.delete_this_chat_confirmation_subtitle),
                confirmationButton = stringResource(id = localizationR.string.delete),
            ),
            content = {
                PersonalityView(
                    modifier = Modifier
                        .background(LuziaTheme.palette.surface.content)
                        .click {
                            onViewAction(
                                ChatViewActions.OnNavigateToPersonality(
                                    chat.chatId.orEmpty(),
                                    chat.isCustomBestie
                                )
                            )
                        }
                        .padding(horizontal = Spacing.X16.dp)
                        .testTag("item/${chat.chatId.orEmpty()}"),
                    title = chat.name.orEmpty(),
                    description = chat.text.orEmpty(),
                    image = chat.iconResourceId?.let { UiImage.Resource(it) }
                        ?: UiImage.Plain(chat.thumbnail.orEmpty()),
                    avatarSize = IconSizes.X48.dp,
                    showUnreadBadge = chat.isRead == false,
                    lastMessageDate = chat.timeStamp?.let {
                        formatDateForChats(
                            context = context,
                            calendar = Calendar.getInstance().apply {
                                timeInMillis = chat.timeStamp ?: 0
                            }
                        )
                    } ?: ""
                )
            },
            onAction = { action ->
                when (action) {
                    is SwipeToActions.OnEndActionCancelled ->
                        onViewAction(ChatViewActions.OnDeleteCancelled)

                    is SwipeToActions.OnStartEndAction ->
                        onViewAction(ChatViewActions.OnStartDeleting(action.data))

                    is SwipeToActions.OnSwiped ->
                        onViewAction(ChatViewActions.OnPersonalitySwiped(action.data))
                }
            }
        )
    )
}

@Composable
private fun NonActiveChatsContent(
    personalitiesViewState: PersonalitiesViewState,
    chats: List<ChatView>,
    onViewAction: (ChatViewActions) -> Unit
) {
    Box(modifier = Modifier.padding(vertical = Spacing.X8.dp, horizontal = Spacing.X16.dp)) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .addShadow(),
            colors = LuziaCardDefaults.cardColors(),
        ) {
            Spacer(modifier = Modifier.height(Spacing.X8.dp))
            SectionHeader(
                modifier = Modifier.padding(horizontal = Spacing.X16.dp, vertical = Spacing.X8.dp),
                title = stringResource(id = localizationR.string.common_characters)
            )
            if (personalitiesViewState.addCustomBestiePosition == AddCustomBestiePosition.START) {
                AddPersonalityView { onViewAction(ChatViewActions.OnAddCustomBestieClicked) }
                HorizontalDivider(
                    modifier = Modifier.padding(horizontal = Spacing.X16.dp),
                    color = LuziaTheme.palette.surface.background
                )
            }
            val filteredPersonalities = personalitiesViewState.personalities
                .filter { personality ->
                    chats.none {
                        it.chatId == personality.personalityId
                    } && personality.personalityId != LUZIA_ID_PERSONALITY
                }
            filteredPersonalities
                .forEachIndexed { index, personality ->
                    PersonalityView(
                        modifier = Modifier
                            .clip(RoundedCornerShape(Corners.X4.dp))
                            .background(LuziaTheme.palette.surface.content)
                            .click {
                                onViewAction(
                                    ChatViewActions.OnNavigateToPersonality(
                                        personality.personalityId,
                                        personality.isCustomBestie
                                    )
                                )
                            }
                            .padding(horizontal = Spacing.X16.dp)
                            .testTag("item/${personality.personalityId}"),
                        title = personality.name,
                        description = personality.description,
                        image = personality.avatar,
                        avatarSize = IconSizes.X48.dp,
                        showUnreadBadge = false
                    )
                    if (index != filteredPersonalities.lastIndex) {
                        HorizontalDivider(
                            modifier = Modifier.padding(horizontal = Spacing.X16.dp),
                            color = LuziaTheme.palette.surface.background
                        )
                    }
                }
            if (personalitiesViewState.addCustomBestiePosition == AddCustomBestiePosition.END) {
                HorizontalDivider(
                    modifier = Modifier.padding(horizontal = Spacing.X16.dp),
                    color = LuziaTheme.palette.surface.background
                )
                AddPersonalityView { onViewAction(ChatViewActions.OnAddCustomBestieClicked) }
            }
            Spacer(modifier = Modifier.height(Spacing.X8.dp))
        }
    }
}
