package co.thewordlab.luzia.features.chat.presentation.chats

import co.thewordlab.fouundation.persistence.chat.ChatView
import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class ChatViewActions : ViewAction {
    data object OnCreate : ChatViewActions()
    data object OnBannerCloseClicked : ChatViewActions()
    data class OnStartDeleting(val chat: ChatView) : ChatViewActions()
    data object OnDeleteConfirmed : ChatViewActions()
    data object OnDeleteCancelled : ChatViewActions()
    data class OnPersonalitySwiped(val chat: ChatView) : ChatViewActions()
    data object OnAddCustomBestieClicked : ChatViewActions()
    data class OnNavigateToPersonality(
        val personalityId: String,
        val isCustomBestie: Boolean
    ) : ChatViewActions()
}
