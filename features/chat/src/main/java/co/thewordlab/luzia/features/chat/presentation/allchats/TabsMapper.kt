package co.thewordlab.luzia.features.chat.presentation.allchats

import android.content.Context
import co.thewordlab.luzia.foundation.localization.R
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject

class TabsMapper @Inject constructor(@ApplicationContext private val context: Context) {

    fun buildFrom(): List<TabModel> =
        listOfNotNull(
            TabModel(
                AllChatsTabs.LUZIA_CHATS,
                context.getString(R.string.group_chat_luzia_chats_button)
            )
        )
}
