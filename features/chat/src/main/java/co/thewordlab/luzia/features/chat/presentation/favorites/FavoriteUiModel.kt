package co.thewordlab.luzia.features.chat.presentation.favorites

import androidx.compose.runtime.Stable
import co.theworldlab.luzia.foundation.design.system.components.item.SelectedItemContext
import co.theworldlab.luzia.foundation.design.system.model.UiImage

@Stable
data class FavoriteUiModel(
    val messageId: Long,
    val personalityUrl: UiImage?,
    val title: String,
    val text: String,
    val timeStamp: Long,
)

object FavoriteSelectedItemContext : SelectedItemContext<FavoriteUiModel> {
    override fun isValid(item: FavoriteUiModel): Boolean = true
}
