package co.thewordlab.luzia.features.chat.presentation.details

import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class ChatDetailViewEvents : ViewEvent {
    data class NavigateToPersonality(val personalityId: String, val isCustomBestie: Boolean) :
        ChatDetailViewEvents()

    data object ShowLikedFeedbackMessage : ChatDetailViewEvents()
    data object ShowDislikedFeedbackMessage : ChatDetailViewEvents()
    data object ScrollToBottom : ChatDetailViewEvents()
    data class NavigateToResponseStyle(val personalityId: String) : ChatDetailViewEvents()
    data class NavigateToCamera(val openGallery: Boolean) : ChatDetailViewEvents()
    data object ShowFileSelection : ChatDetailViewEvents()
    data object ShowFileTooBig : ChatDetailViewEvents()
}
