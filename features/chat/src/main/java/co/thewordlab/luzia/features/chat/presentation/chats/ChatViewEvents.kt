package co.thewordlab.luzia.features.chat.presentation.chats

import co.thewordlab.fouundation.persistence.chat.ChatView
import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class ChatViewEvents : ViewEvent {
    data class ShowDeleteConfirmation(val chat: ChatView) : ChatViewEvents()
    data object NavigateToCustomBestie : ChatViewEvents()
    data object NavigateToCustomBestieSignup : ChatViewEvents()
    data object NavigateToCustomBestieCreationNotAllowed : ChatViewEvents()
    data class NavigateToPersonality(
        val personalityId: String,
        val isCustomBestie: Boolean
    ) : ChatViewEvents()
}
