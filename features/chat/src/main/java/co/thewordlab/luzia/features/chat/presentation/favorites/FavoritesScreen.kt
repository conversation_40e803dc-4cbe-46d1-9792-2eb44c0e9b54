package co.thewordlab.luzia.features.chat.presentation.favorites

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.common.onLinkClicked
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.date.formatDateForChats
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.item.SelectedItemState
import co.theworldlab.luzia.foundation.design.system.components.item.rememberSelectedItems
import co.theworldlab.luzia.foundation.design.system.components.message.MarkdownTextView
import co.theworldlab.luzia.foundation.design.system.components.message.rememberRichTextStyle
import co.theworldlab.luzia.foundation.design.system.components.navbar.LuziaNavBarDefaults
import co.theworldlab.luzia.foundation.design.system.components.navbar.NavigationAction
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBar
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBarModel
import co.theworldlab.luzia.foundation.design.system.components.selection.LuziaRadioIcon
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.model.UiImage
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import coil3.compose.AsyncImage
import com.halilibo.richtext.ui.RichTextThemeProvider
import java.util.Calendar
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun FavoritesScreen(
    personalityId: String,
    isCustomBestie: Boolean,
    onNavigateToMessage: (Long) -> Unit
) {
    val viewModel: FavoritesViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val favorites = viewModel.favorites.collectAsLazyPagingItems()
    val navigation = LocalNavigation.current
    val context = LocalContext.current

    ViewModelEventEffect(viewModel) {
        when (it) {
            FavoritesViewEvents.NavigateBack ->
                navigation.goBack()

            is FavoritesViewEvents.NavigateToMessage -> onNavigateToMessage(it.messageId)
            FavoritesViewEvents.ShowSignupModal ->
                navigation.navigate(UserSessionRoutes.FavoritesSignupModal)

            is FavoritesViewEvents.OpenLink ->
                context.onLinkClicked(navigation, it.url)
        }
    }

    OnCreate("FavoritesScreen") {
        viewModel.onViewAction(
            FavoritesViewActions.OnCreate(personalityId, isCustomBestie)
        )
    }

    FavoritesContent(
        viewState = viewState,
        favorites = favorites,
        onViewActions = viewModel::onViewAction
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun FavoritesContent(
    viewState: FavoritesViewState,
    favorites: LazyPagingItems<FavoriteUiModel>,
    onViewActions: (FavoritesViewActions) -> Unit,
) {
    val selectedItemState = rememberSelectedItems<FavoriteUiModel> { item, other ->
        item.messageId == other.messageId
    }
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            TopNavigationBar(
                TopNavigationBarModel(
                    title = stringResource(id = localizationR.string.favorites_title),
                    navigationAction = NavigationAction.Icon(R.drawable.ic_back_arrow) {
                        onViewActions(FavoritesViewActions.OnBackClicked)
                    },
                    colors = LuziaNavBarDefaults.colors(
                        containerColor = LuziaTheme.palette.surface.background,
                        scrolledContainerColor = LuziaTheme.palette.surface.background
                    ),
                    actions = if (favorites.itemCount > 0) {
                        if (viewState.editionActive) {
                            listOf(
                                NavigationAction.Text(stringResource(id = localizationR.string.signup_done_button)) {
                                    onViewActions(
                                        FavoritesViewActions.OnDoneEditClicked(selectedItemState.getItems().value)
                                    )
                                    selectedItemState.clear()
                                }
                            )
                        } else {
                            listOf(
                                NavigationAction.Icon(designR.drawable.ic_edit_24) {
                                    onViewActions(FavoritesViewActions.OnEditClicked)
                                }
                            )
                        }
                    } else {
                        emptyList()
                    }
                )
            )
        },
        containerColor = LuziaTheme.palette.surface.background
    ) { innerPadding ->
        if (favorites.itemCount == 0) {
            EmptyScreen(modifier = Modifier.padding(innerPadding))
        } else {
            FavoritesList(
                modifier = Modifier.padding(innerPadding),
                viewState = viewState,
                selectedItemState = selectedItemState,
                favorites = favorites,
                onViewActions = onViewActions
            )
        }
    }
}

@Composable
private fun FavoritesList(
    modifier: Modifier,
    viewState: FavoritesViewState,
    selectedItemState: SelectedItemState<FavoriteUiModel>,
    favorites: LazyPagingItems<FavoriteUiModel>,
    onViewActions: (FavoritesViewActions) -> Unit,
) {
    Column(modifier = modifier.fillMaxSize()) {
        LazyColumn(
            modifier = Modifier.weight(1f),
            state = rememberLazyListState()
        ) {
            items(favorites.itemCount) { index ->
                favorites[index]?.let {
                    FavoritesItem(
                        editionActive = viewState.editionActive,
                        favorite = it,
                        selectedItemState = selectedItemState,
                        onViewActions = onViewActions
                    )
                }
                if (index != favorites.itemCount - 1 && favorites.itemCount != 1) {
                    HorizontalDivider(
                        modifier = Modifier.padding(horizontal = Spacing.X16.dp),
                        color = LuziaTheme.palette.border.primary
                    )
                }
            }
        }
        AnimatedVisibility(visible = viewState.editionActive) {
            FavoritesBottomBar(selectedItemState = selectedItemState, onViewActions = onViewActions)
        }
    }
}

@Composable
private fun FavoritesBottomBar(
    modifier: Modifier = Modifier,
    selectedItemState: SelectedItemState<FavoriteUiModel>,
    onViewActions: (FavoritesViewActions) -> Unit,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .background(LuziaTheme.palette.surface.content)
            .padding(Spacing.X16.dp),
    ) {
        IconButton(
            modifier = Modifier
                .size(IconSizes.X40.dp)
                .background(color = LuziaTheme.palette.interactive.brandLight, shape = CircleShape)
                .align(Alignment.CenterStart),
            onClick = {
                onViewActions(FavoritesViewActions.OnUnFavoriteClicked(selectedItemState.getItems().value))
                selectedItemState.clear()
            }
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_unstar_24),
                contentDescription = null,
                tint = LuziaTheme.palette.interactive.brand
            )
        }
        if (selectedItemState.getItems().value.isNotEmpty()) {
            LuziaText(
                modifier = Modifier
                    .wrapContentWidth()
                    .align(Alignment.Center),
                text = stringResource(
                    id = localizationR.string.group_chat_selected_message,
                    selectedItemState.getItems().value.size
                ),
                style = LuziaTheme.typography.body.regular.default,
                color = LuziaTheme.palette.text.secondary
            )
        }
    }
}

@Composable
private fun FavoritesItem(
    favorite: FavoriteUiModel,
    editionActive: Boolean,
    selectedItemState: SelectedItemState<FavoriteUiModel>,
    onViewActions: (FavoritesViewActions) -> Unit,
) {
    val context = LocalContext.current
    var isSelected = selectedItemState.isSelected(favorite)

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
    ) {
        AnimatedVisibility(editionActive) {
            LuziaRadioIcon(
                modifier = Modifier.padding(start = Spacing.X16.dp),
                selected = isSelected,
                onClick = {
                    when {
                        editionActive && isSelected -> selectedItemState.removeItem(favorite)
                        editionActive -> {
                            onViewActions(FavoritesViewActions.OnUnFavoriteMessageSelected(favorite.messageId))
                            selectedItemState.addItem(FavoriteSelectedItemContext, favorite)
                        }
                    }
                    isSelected = selectedItemState.isSelected(favorite)
                }
            )
        }
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .click { onViewActions(FavoritesViewActions.OnItemClick(favorite.messageId)) }
                .padding(Spacing.X16.dp),
            verticalAlignment = Alignment.Top,
            horizontalArrangement = Arrangement.spacedBy(Spacing.X16.dp)
        ) {
            IconPersonality(personalityUrl = favorite.personalityUrl)
            Column {
                Row(
                    modifier = Modifier.padding(top = Spacing.X4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    LuziaText(
                        text = favorite.title,
                        style = LuziaTheme.typography.body.semiBold.default,
                        color = LuziaTheme.palette.text.primary
                    )
                    Spacer(modifier = Modifier.weight(1f))
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Icon(
                            painter = painterResource(id = designR.drawable.ic_star_12),
                            contentDescription = null,
                            tint = Color.Unspecified
                        )
                        Spacer(modifier = Modifier.width(Spacing.X4.dp))
                        LuziaText(
                            text = formatDateForChats(
                                context = context,
                                calendar = Calendar.getInstance().apply {
                                    timeInMillis = favorite.timeStamp
                                }
                            ),
                            style = LuziaTheme.typography.body.regular.footnote,
                            color = LuziaTheme.palette.text.helper
                        )
                        Spacer(modifier = Modifier.width(Spacing.X4.dp))
                        Icon(
                            modifier = Modifier.size(IconSizes.X12.dp),
                            painter = painterResource(id = designR.drawable.ic_chevron_right),
                            contentDescription = null,
                            tint = LuziaTheme.palette.text.helper
                        )
                    }
                }
                Spacer(modifier = Modifier.height(Spacing.X8.dp))
                RichTextThemeProvider(
                    textStyleProvider = { LuziaTheme.typography.body.regular.default },
                    contentColorProvider = { LuziaTheme.palette.text.primary }
                ) {
                    MarkdownTextView(
                        text = favorite.text,
                        style = rememberRichTextStyle(),
                        onLinkClicked = { onViewActions(FavoritesViewActions.OnLinkClicked(it)) }
                    )
                }
            }
        }
    }
}

@Composable
private fun IconPersonality(personalityUrl: UiImage?) {
    personalityUrl?.let {
        AsyncImage(
            modifier = Modifier
                .clip(CircleShape)
                .size(IconSizes.X32.dp),
            model = when (it) {
                is UiImage.Resource -> it.resourceId
                is UiImage.Plain -> it.url
            },
            contentDescription = null
        )
    } ?: Box(
        modifier = Modifier
            .size(IconSizes.X32.dp)
            .background(
                color = LuziaTheme.palette.primitives.neutral.neutral10,
                shape = CircleShape
            ),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            painter = painterResource(id = designR.drawable.ic_user_16),
            contentDescription = null,
            tint = LuziaTheme.palette.accents.green.green30
        )
    }
}

@Composable
private fun EmptyScreen(modifier: Modifier) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(Spacing.X16.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            modifier = Modifier.size(IconSizes.X48.dp),
            painter = painterResource(id = designR.drawable.ic_star_12),
            contentDescription = null,
            tint = Color.Unspecified
        )
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
        LuziaText(
            text = stringResource(id = localizationR.string.favorites_empty_title),
            textAlign = TextAlign.Center,
            style = LuziaTheme.typography.body.semiBold.default,
            color = LuziaTheme.palette.text.primary
        )
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
        LuziaText(
            text = stringResource(id = localizationR.string.favorites_empty_desc),
            textAlign = TextAlign.Center,
            style = LuziaTheme.typography.body.regular.default,
            color = LuziaTheme.palette.text.secondary
        )
    }
}
