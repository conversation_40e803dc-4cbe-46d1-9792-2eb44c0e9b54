package co.thewordlab.luzia.features.chat.presentation.favorites

import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class FavoritesViewEvents : ViewEvent {
    data class NavigateToMessage(val messageId: Long) : FavoritesViewEvents()
    data class OpenLink(val url: String) : FavoritesViewEvents()
    data object NavigateBack : FavoritesViewEvents()
    data object ShowSignupModal : FavoritesViewEvents()
}
