package co.thewordlab.luzia.features.chat.presentation.details

import co.thewordlab.luzia.foundation.architecture.system.ViewAction
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageModel

sealed class ChatDetailViewActions : ViewAction {

    data class OnCreate(val comesFromOnboarding: Boolean) : ChatDetailViewActions()
    data object OnResume : ChatDetailViewActions()
    data object OnStop : ChatDetailViewActions()
    data object OnPersonalityIconClicked : ChatDetailViewActions()
    data object OnSearchClicked : ChatDetailViewActions()
    data object OnSearchCancelled : ChatDetailViewActions()
    data object OnSearchCancelledClicked : ChatDetailViewActions()
    data object OnClearSearchClicked : ChatDetailViewActions()
    data class OnQueryChanged(val query: String) : ChatDetailViewActions()
    data class OnDeleteCancelled(val entityToDelete: List<MessageModel>?) : ChatDetailViewActions()
    data class OnSearchNavigateForward(val positionSelected: Int, val searchResultsSize: Int) :
        ChatDetailViewActions()

    data class OnSearchNavigateBackward(val positionSelected: Int, val searchResultsSize: Int) :
        ChatDetailViewActions()

    data object OnMaxPromptLengthMessageShown : ChatDetailViewActions()
    data object OnResponseStyleClicked : ChatDetailViewActions()
    data class OnCameraClick(val openGallery: Boolean) : ChatDetailViewActions()
    data object OnChatToolIconClicked : ChatDetailViewActions()
    data object OnChatPlusIconClicked : ChatDetailViewActions()
    data object OnFileUploadClicked : ChatDetailViewActions()
    data class OnDocumentReceived(val docUri: String, val name: String, val size: Long) :
        ChatDetailViewActions()
    data class OnInputChanged(val text: String) : ChatDetailViewActions()
    data object OnToggleWebSearch : ChatDetailViewActions()
}
