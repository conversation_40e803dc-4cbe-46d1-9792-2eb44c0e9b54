package co.thewordlab.luzia.features.chat.presentation.details

import android.net.Uri
import androidx.core.net.toUri
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import co.thewordlab.fouundation.persistence.chat.ChatMetadata
import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.fouundation.persistence.chat.MessageType
import co.thewordlab.luzia.core.ads.AdHandler
import co.thewordlab.luzia.core.ads.analytics.AdDisplayed
import co.thewordlab.luzia.core.ads.controller.AdController
import co.thewordlab.luzia.core.ads.model.AdViewData
import co.thewordlab.luzia.core.chat.analytics.ChatAnalytics
import co.thewordlab.luzia.core.chat.analytics.STEP_ICEBREAKER
import co.thewordlab.luzia.core.chat.analytics.STEP_PLUS_ICON
import co.thewordlab.luzia.core.chat.analytics.STEP_RESPONSE_STYLE_ICON
import co.thewordlab.luzia.core.chat.analytics.STEP_TASK_ICON
import co.thewordlab.luzia.core.chat.analytics.STEP_WEB_ICON
import co.thewordlab.luzia.core.chat.analytics.SendMessageEventHelper
import co.thewordlab.luzia.core.chat.data.MessagesInChatHandler
import co.thewordlab.luzia.core.chat.domain.usecases.GetResponseStyleFirstTimeUseCase
import co.thewordlab.luzia.core.chat.list.domain.AdSupportedChatController
import co.thewordlab.luzia.core.chat.presentation.files.FileViewState
import co.thewordlab.luzia.core.chat.presentation.search.SearchViewActions
import co.thewordlab.luzia.core.chat.presentation.search.SearchViewState
import co.thewordlab.luzia.core.chat.presentation.search.getSearchQuery
import co.thewordlab.luzia.core.chat.presentation.search.isSearching
import co.thewordlab.luzia.core.feedback.domain.FeedbackSource
import co.thewordlab.luzia.core.tools.ChatComposerToolClicked
import co.thewordlab.luzia.core.tools.domain.model.ToolSupportedFile
import co.thewordlab.luzia.features.chat.ATTACHMENT_TYPE_CAMERA
import co.thewordlab.luzia.features.chat.ATTACHMENT_TYPE_FILE
import co.thewordlab.luzia.features.chat.ATTACHMENT_TYPE_GALLERY
import co.thewordlab.luzia.features.chat.ChatComposerAttachFileClicked
import co.thewordlab.luzia.features.chat.ChatComposerFileAttached
import co.thewordlab.luzia.features.chat.ChatComposerFileDeleted
import co.thewordlab.luzia.features.chat.ChatComposerPlusClicked
import co.thewordlab.luzia.features.chat.ChatScreen
import co.thewordlab.luzia.features.chat.DeleteMessagesCancelled
import co.thewordlab.luzia.features.chat.MESSAGE_DELETE_SELECTOR
import co.thewordlab.luzia.features.chat.MaxPromptLimitReached
import co.thewordlab.luzia.features.chat.ORIGIN_CHAT
import co.thewordlab.luzia.features.chat.PERSONALITY
import co.thewordlab.luzia.features.chat.ResponseStyleClicked
import co.thewordlab.luzia.features.chat.SearchCancelled
import co.thewordlab.luzia.features.chat.SearchCleared
import co.thewordlab.luzia.features.chat.SearchClicked
import co.thewordlab.luzia.features.chat.SearchNavigateNext
import co.thewordlab.luzia.features.chat.SearchNavigatePrevious
import co.thewordlab.luzia.features.chat.SearchQuerySubmitted
import co.thewordlab.luzia.features.chat.analytics.PersonalityClick
import co.thewordlab.luzia.features.chat.analytics.SendVoiceChat
import co.thewordlab.luzia.features.chat.buildMessageAttributes
import co.thewordlab.luzia.features.chat.buildResponseStyleAttributes
import co.thewordlab.luzia.features.chat.presentation.details.ChatTooltips.Companion.ID_ADD_COMPOSER
import co.thewordlab.luzia.features.chat.presentation.details.ChatTooltips.Companion.ID_ICEBREAKERS
import co.thewordlab.luzia.features.chat.presentation.details.ChatTooltips.Companion.ID_RESPONSE_STYLE_COMPOSER
import co.thewordlab.luzia.features.chat.presentation.details.ChatTooltips.Companion.ID_TOOLS_COMPOSER
import co.thewordlab.luzia.features.chat.presentation.details.ChatTooltips.Companion.ID_WEB_SEARCH_COMPOSER
import co.thewordlab.luzia.features.personality.data.repository.PersonalityRepository
import co.thewordlab.luzia.features.personality.domain.LUZIA_ID_PERSONALITY
import co.thewordlab.luzia.features.personality.domain.models.PersonalityModel
import co.thewordlab.luzia.features.personality.domain.models.custombestie.mapToPersonalityModel
import co.thewordlab.luzia.features.personality.domain.repository.CustomBestieRepository
import co.thewordlab.luzia.features.personality.domain.repository.DraftsRepository
import co.thewordlab.luzia.features.personality.domain.usecases.GetSelectedPersonalityResponseStyle
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.analytics.helpers.ConnectivityObserver
import co.thewordlab.luzia.foundation.analytics.providers.CrashlyticsAnalyticsProvider
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.common.haptic.HapticFeedbackManager
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.foundation.files.importing.FileImporter
import co.thewordlab.luzia.foundation.files.importing.TextImporter
import co.thewordlab.luzia.foundation.messages.data.model.asEvent
import co.thewordlab.luzia.foundation.messages.data.repository.TextMessageResultWrapper
import co.thewordlab.luzia.foundation.networking.model.AppErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import co.thewordlab.luzia.foundation.networking.session.UserType
import co.theworldlab.luzia.foundation.design.system.components.lds.tooltip.Tooltip
import co.theworldlab.luzia.foundation.design.system.events.AppEventContainer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject
import co.thewordlab.luzia.foundation.common.flows.combine as combineMulti

@Suppress("LongParameterList", "TooManyFunctions", "LargeClass")
@HiltViewModel
@OptIn(ExperimentalCoroutinesApi::class)
class ChatDetailViewModel @Inject constructor(
    private val messagesHandler: MessagesInChatHandler,
    private val personalityRepository: PersonalityRepository,
    private val userSessionManager: UserSessionManager,
    private val fileImporter: FileImporter,
    private val textImporter: TextImporter,
    private val hapticFeedbackManager: HapticFeedbackManager,
    private val analytics: Analytics,
    private val appEventContainer: AppEventContainer,
    private val coroutineScope: CoroutineScope,
    private val getResponseStyleFirstTimeUseCase: GetResponseStyleFirstTimeUseCase,
    private val getSelectedPersonalityResponseStyle: GetSelectedPersonalityResponseStyle,
    private val featureFlagManager: FeatureFlagManager,
    private val sendMessageEventHelper: SendMessageEventHelper,
    private val customBestieRepository: CustomBestieRepository,
    private val draftsRepository: DraftsRepository,
    private val connectivityObserver: ConnectivityObserver,
    private val adHandler: AdHandler,
    private val crashlytics: CrashlyticsAnalyticsProvider,
    private val chatTooltips: ChatTooltips,
    savedStateHandle: SavedStateHandle,
) : ViewModel(),
    AdSupportedChatController,
    ViewModelStates<ChatDetailViewState> by ViewModelStatesImpl(ChatDetailViewState()),
    ViewModelActions<ChatDetailViewActions>,
    ViewModelEvents<ChatDetailViewEvents> by ViewModelEventsImpl(),
    SendMessageEventHelper by sendMessageEventHelper,
    AdController {

    private val personalityId = savedStateHandle.getStateFlow(ARG_PERSONALITY, LUZIA_ID_PERSONALITY)
    private var isCustomBestie = savedStateHandle.getStateFlow(ARG_CUSTOM_BESTIE, false)
    private val personality = MutableStateFlow<PersonalityModel?>(null)
    private val isAiResponding = MutableStateFlow(false)
    private val searchStatus = MutableStateFlow<SearchViewActions>(SearchViewActions.Idle)
    private val adDataState = MutableStateFlow<AdViewData?>(null)
    private val messages =
        personalityId.flatMapLatest {
            messagesHandler.getMessages(it)
        }.cachedIn(viewModelScope)

    private val draftState = personalityId.flatMapLatest { draftsRepository.getDraft(it) }
    private var comesFromOnboarding: Boolean = false

    override val searchState: Flow<SearchViewState<MessageEntity>> = searchStatus.mapLatest {
        SearchViewState(
            isSearching = isSearching(it),
            query = getSearchQuery(it),
            searchResults = if (it is SearchViewActions.SearchResults<*>) {
                it.results as List<MessageEntity>
            } else {
                null
            }
        )
    }

    override val adController: AdController = this

    init {
        listenAuthStatusForResend()
        readAllMessages()
        observeChatDetailChanges()
        listenForQueryChanges()
        observePersonalityChanges()
    }

    private fun observeChatDetailChanges() = viewModelScope.launch {
        val textToSpeechEnabled: Boolean = featureFlagManager.get(FeatureFlag.TextToSpeechEnabled)
        val tooltips = getTooltips()
        combineMulti(
            personality.filterNotNull(),
            isAiResponding,
            searchStatus,
            messagesHandler.getLastUserMessageMaxPromptLength(personalityId.value),
            personalityRepository.isPersonalityProfileShown(),
            draftState,
            connectivityObserver.observe()
        ) { personality, isAiResponding, searchAction, maxPromptLength,
            personalityProfileShown, draft, connectionState ->
            val notShownResponseStyles = getResponseStyleFirstTimeUseCase.isFirstTime() &&
                personalityId.value == LUZIA_ID_PERSONALITY
            val webSearchState = getWebSearchState()
            updateState {
                it.copy(
                    personalityModel = personality,
                    isSendEnabled = !isAiResponding,
                    actionsEnabled = connectionState.isConnected,
                    isCameraEnabled = connectionState.isConnected,
                    actionSink = ::onAction,
                    searchState = SearchViewState(
                        isSearching = isSearching(searchAction),
                        query = getSearchQuery(searchAction),
                        searchResults = if (searchAction is SearchViewActions.SearchResults<*>) {
                            searchAction.results as List<MessageEntity>
                        } else {
                            null
                        }
                    ),
                    maxPromptLength = maxPromptLength,
                    showRedDotInPersonalityProfile = personalityProfileShown != true || notShownResponseStyles,
                    isResponseStylesEnabled = checkResponseStylesEnabled(personality),
                    shouldAnimateResponseStyles = notShownResponseStyles,
                    isTextToSpeechEnabled = textToSpeechEnabled,
                    isCustomBestie = isCustomBestie.value,
                    draft = draft.orEmpty(),
                    webSearch = webSearchState,
                    tooltips = tooltips
                )
            }
        }.launchIn(viewModelScope)
    }

    private suspend fun getTooltips(): List<Tooltip> {
        return when {
            comesFromOnboarding -> {
                val tooltips = chatTooltips.getTooltips(
                    includeWebSearch = featureFlagManager.get(FeatureFlag.WebSearchEnabled),
                    buttonAction = { onNextTooltipClicked(it) }
                )
                tooltips.firstOrNull()?.let {
                    analytics.trackEvent(ChatAnalytics.ChatTooltipShown(it.id))
                }
                tooltips
            }

            else -> emptyList()
        }
    }

    private fun onNextTooltipClicked(id: String) {
        val step = when (id) {
            ID_ICEBREAKERS -> STEP_ICEBREAKER
            ID_ADD_COMPOSER -> STEP_PLUS_ICON
            ID_TOOLS_COMPOSER -> STEP_TASK_ICON
            ID_RESPONSE_STYLE_COMPOSER -> STEP_RESPONSE_STYLE_ICON
            ID_WEB_SEARCH_COMPOSER -> STEP_WEB_ICON
            else -> null
        }
        step?.let { analytics.trackEvent(ChatAnalytics.ChatTooltipShown(it)) }
        updateState { it.copy(tooltips = chatTooltips.showNextTooltip(id)) }
    }

    private suspend fun getWebSearchState(): WebSearchViewState? {
        return takeIf { featureFlagManager.get(FeatureFlag.WebSearchEnabled) }?.let {
            WebSearchViewState()
        }
    }

    private fun observePersonalityChanges() {
        combine(isCustomBestie, personalityId) { isCustom, id -> Pair(isCustom, id) }
            .flatMapLatest { (isCustom, id) ->
                if (isCustom) {
                    customBestieRepository.getCustomBestie(id)
                        .filterNotNull()
                        .map { it.mapToPersonalityModel() }
                } else {
                    personalityRepository.getPersonality(id).filterNotNull()
                }
            }
            .filterNotNull()
            .distinctUntilChanged()
            .onEach { item -> personality.update { item } }
            .launchIn(viewModelScope)
    }

    override fun getChatMessages(): Flow<PagingData<MessageEntity>> {
        return messages
    }

    override fun getChatMetadata(): Flow<ChatMetadata?> {
        return personalityId.flatMapLatest { messagesHandler.getChatMetadata(it) }
    }

    override fun hasUnreadMessages(): Flow<Boolean> {
        return personalityId.flatMapLatest {
            messagesHandler.getUnreadMessageCount(it)
        }.distinctUntilChanged().map { it > 0L }
    }

    override suspend fun getIceBreakers(): Flow<List<String>> =
        personality.filterNotNull().map { it.iceBreakers }

    override suspend fun dismissFeedback(feedbackId: String) {
        messagesHandler.dismissFeedback(feedbackId)
    }

    @OptIn(FlowPreview::class)
    private fun listenForQueryChanges() {
        searchStatus.map { it }
            .distinctUntilChanged()
            .debounce(DEBOUNCE_TYPING)
            .onEach { manageQueryChanges(it) }
            .launchIn(viewModelScope)
    }

    private fun manageQueryChanges(searchAction: SearchViewActions) {
        if (searchAction is SearchViewActions.SearchResults<*> && searchAction.query.isNotEmpty()) {
            analytics.logAction(
                SearchQuerySubmitted,
                mapOf(
                    Parameter.Personality to personalityId.value,
                    Parameter.SearchQueryLength to searchAction.query.length,
                    Parameter.SearchQueryMatches to searchAction.results.size
                )
            )
        }
        if (searchAction is SearchViewActions.Search) {
            findMessages(searchAction.query)
        }
    }

    @Suppress("CyclomaticComplexMethod", "LongMethod")
    override fun onViewAction(action: ChatDetailViewActions) {
        when (action) {
            ChatDetailViewActions.OnResume ->
                getImportedFiles()

            ChatDetailViewActions.OnStop -> onStop()

            ChatDetailViewActions.OnPersonalityIconClicked -> {
                analytics.logAction(
                    PersonalityClick,
                    mapOf(Parameter.Personality to personalityId.value)
                )
                sendEvent(
                    ChatDetailViewEvents.NavigateToPersonality(
                        personalityId.value,
                        isCustomBestie.value
                    )
                )
            }

            is ChatDetailViewActions.OnQueryChanged -> {
                searchStatus.value = SearchViewActions.Search(action.query)
            }

            ChatDetailViewActions.OnSearchCancelled -> {
                searchStatus.value = SearchViewActions.Dismiss
            }

            ChatDetailViewActions.OnSearchCancelledClicked -> {
                searchStatus.value = SearchViewActions.Dismiss
                analytics.logAction(
                    SearchCancelled,
                    mapOf(Parameter.Personality to personalityId.value)
                )
            }

            ChatDetailViewActions.OnSearchClicked -> {
                analytics.logAction(
                    SearchClicked,
                    mapOf(Parameter.Personality to personalityId.value)
                )
                searchStatus.value = SearchViewActions.Search("")
            }

            ChatDetailViewActions.OnClearSearchClicked -> {
                searchStatus.value = SearchViewActions.Clear
                analytics.logAction(
                    SearchCleared,
                    mapOf(Parameter.Personality to personalityId.value)
                )
            }

            is ChatDetailViewActions.OnCreate -> {
                comesFromOnboarding = action.comesFromOnboarding
                sendScreenEvent()
            }

            is ChatDetailViewActions.OnDeleteCancelled ->
                with(action.entityToDelete?.firstOrNull()) {
                    analytics.logAction(
                        DeleteMessagesCancelled,
                        buildMessageAttributes(
                            this?.messagePersonalityId.orEmpty(),
                            this?.messageType?.property.orEmpty(),
                            this?.messageId.toString()
                        ).apply {
                            put(Parameter.Source, MESSAGE_DELETE_SELECTOR)
                        }
                    )
                }

            is ChatDetailViewActions.OnSearchNavigateBackward ->
                analytics.logAction(
                    SearchNavigatePrevious,
                    mapOf(
                        Parameter.Personality to personalityId.value,
                        Parameter.SearchMatchIndex to action.positionSelected,
                        Parameter.SearchMatchTotal to action.searchResultsSize
                    )
                )

            is ChatDetailViewActions.OnSearchNavigateForward ->
                analytics.logAction(
                    SearchNavigateNext,
                    mapOf(
                        Parameter.Personality to personalityId.value,
                        Parameter.SearchMatchIndex to action.positionSelected,
                        Parameter.SearchMatchTotal to action.searchResultsSize
                    )
                )

            ChatDetailViewActions.OnMaxPromptLengthMessageShown ->
                analytics.logEvent(
                    MaxPromptLimitReached,
                    mapOf(Parameter.Personality to personalityId.value, Parameter.Tool to TOOL)
                )

            ChatDetailViewActions.OnResponseStyleClicked -> onResponseStyleClicked()
            is ChatDetailViewActions.OnCameraClick -> {
                analytics.trackAction(
                    ChatComposerAttachFileClicked(
                        personalityId.value,
                        if (action.openGallery) ATTACHMENT_TYPE_GALLERY else ATTACHMENT_TYPE_CAMERA
                    )
                )
                sendEvent(ChatDetailViewEvents.NavigateToCamera(action.openGallery))
            }

            ChatDetailViewActions.OnChatToolIconClicked -> {
                analytics.logActionWithProps(
                    ChatComposerToolClicked,
                    mapOf(PERSONALITY to personalityId.value)
                )
            }

            ChatDetailViewActions.OnChatPlusIconClicked -> {
                analytics.logActionWithProps(
                    ChatComposerPlusClicked,
                    mapOf(PERSONALITY to personalityId.value)
                )
            }

            ChatDetailViewActions.OnFileUploadClicked -> {
                analytics.trackAction(
                    ChatComposerAttachFileClicked(
                        personalityId.value,
                        ATTACHMENT_TYPE_FILE
                    )
                )
                sendEvent(ChatDetailViewEvents.ShowFileSelection)
            }

            is ChatDetailViewActions.OnDocumentReceived -> {
                analytics.trackEvent(ChatComposerFileAttached(personalityId.value))
                handleFileUri(action.docUri, action.name, action.size, ToolSupportedFile.PDF)
            }

            is ChatDetailViewActions.OnInputChanged -> {
                updateState { it.copy(input = action.text) }
            }

            ChatDetailViewActions.OnToggleWebSearch -> onToggleWebSearch()
        }
    }

    private fun onStop() {
        sendLastMessageMetrics()
        val state = viewState.value
        val id = state.personalityModel?.personalityId.orEmpty()
        viewModelScope.launch {
            if (state.input.isNotEmpty()) {
                draftsRepository.saveDraft(id, state.input)
            }
        }
    }

    private fun onToggleWebSearch() {
        updateState {
            it.copy(webSearch = it.webSearch?.copy(isActive = !it.webSearch.isActive))
        }
    }

    private fun toggleNeutralWebSearchState() {
        updateState {
            it.copy(webSearch = it.webSearch?.copy(isActive = false, isEnabled = true))
        }
    }

    private fun handleFileUri(
        fileUri: String,
        fileName: String,
        size: Long,
        fileInfo: ToolSupportedFile
    ) = viewModelScope.launch {
        val file = fileImporter.resolveFile(fileUri.toUri(), "File_", ".pdf")
        if (file != null) {
            val isFileSizeAcceptable = size <= MAX_FILE_SIZE_ALLOWED
            if (isFileSizeAcceptable) {
                sendDocument(file, fileInfo, fileName)
            } else {
                sendEvent(ChatDetailViewEvents.ShowFileTooBig)
            }
        }
    }

    private suspend fun sendDocument(file: File, fileInfo: ToolSupportedFile, fileName: String) {
        updateFileState(
            fileState = FileViewState.Loading(fileName, fileInfo.getFileTypeText()),
            sendEnabled = false
        )

        val messageCount = messagesHandler.countAllUserMessagesWithLuzia() + 1
        adHandler.checkAndMarkPendingAd(personalityId.value, messageCount)

        val properties = mutableMapOf<Parameter, Any>(
            Parameter.Type to ATTACHMENT_TYPE_FILE,
            Parameter.Personality to personalityId.value
        )
        sendMessageMetrics(properties)
        val result = messagesHandler.sendDocument(personalityId.value, file)
        result.asEvent()?.let(appEventContainer::sendEvent)
        when (result) {
            is ResultOf.Failure ->
                updateFileState(
                    fileState = FileViewState.Error(
                        name = fileName,
                        type = fileInfo.getFileTypeText(),
                        onRetry = {
                            viewModelScope.launch {
                                sendDocument(
                                    file,
                                    fileInfo,
                                    fileName
                                )
                            }
                        },
                        onCancel = ::clearFile
                    ),
                    sendEnabled = false
                )

            is ResultOf.Success -> updateFileState(
                fileState = FileViewState.Uploaded(
                    name = fileName,
                    type = fileInfo.getFileTypeText(),
                    onDelete = ::clearFile
                ),
                sendEnabled = true
            )
        }
    }

    private fun clearFile() {
        analytics.trackAction(ChatComposerFileDeleted(personalityId.value))
        messagesHandler.clearDocument()
        updateFileState(fileState = FileViewState.None, sendEnabled = true)
    }

    private fun updateFileState(fileState: FileViewState, sendEnabled: Boolean) {
        when (fileState) {
            FileViewState.None -> updateState {
                it.copy(
                    fileState = fileState,
                    isSendEnabled = sendEnabled,
                    webSearch = it.webSearch?.copy(isActive = false, isEnabled = true)
                )
            }

            is FileViewState.Uploaded -> updateState {
                it.copy(
                    webSearch = it.webSearch?.copy(isActive = false, isEnabled = false),
                    fileState = fileState,
                    isSendEnabled = sendEnabled
                )
            }

            else -> updateState {
                it.copy(
                    fileState = fileState,
                    isSendEnabled = sendEnabled
                )
            }
        }
    }

    private fun ToolSupportedFile.getFileTypeText() =
        this.extension.removePrefix(".").uppercase()

    private fun onResponseStyleClicked() = viewModelScope.launch {
        analytics.logActionWithProps(
            ResponseStyleClicked,
            buildResponseStyleAttributes(
                ORIGIN_CHAT,
                userSessionManager.userSession.firstOrNull()?.userType == UserType.FULL_USER,
                getSelectedPersonalityResponseStyle(personalityId.value)?.id.orEmpty()
            )
        )
        sendEvent(ChatDetailViewEvents.NavigateToResponseStyle(personalityId.value))
    }

    private fun sendScreenEvent() = viewModelScope.launch {
        val chats = messagesHandler.getChats().firstOrNull()?.map { it.chatId }.toString()
        analytics.logScreen(
            ChatScreen,
            mapOf(Parameter.Personality to personalityId.value, Parameter.ActiveChats to chats)
        )
    }

    private fun findMessages(query: String) = viewModelScope.launch {
        searchStatus.value = SearchViewActions.SearchResults(
            query = query,
            results = messagesHandler.searchMessages(personalityId.value, query)
        )
    }

    private fun getImportedFiles() = viewModelScope.launch {
        val personalityId = personalityId.value
        messagesHandler.clearFailedUserMessages(personalityId)
        sendImportedAudio(personalityId)
        getImportedText(personalityId)
    }

    private fun sendImportedAudio(personalityId: String) {
        coroutineScope.launch {
            fileImporter.getStream(personalityId).firstOrNull()?.let { imported ->
                sendAudioImported(imported.file)
            }
        }
    }

    private fun getImportedText(personalityId: String) {
        viewModelScope.launch {
            textImporter.getStream(personalityId).firstOrNull()?.let { imported ->
                updateState { it.copy(input = imported.text) }
            }
        }
    }

    private fun readAllMessages() {
        viewModelScope.launch {
            messagesHandler.readAllMessages(personalityId.value)
        }
    }

    private fun listenAuthStatusForResend() {
        userSessionManager.userSession
            .filter { it?.userType == UserType.FULL_USER }
            .distinctUntilChanged()
            .onEach { messagesHandler.resendAllSignupRequiredMessages() }
            .launchIn(viewModelScope)
    }

    private fun onAction(action: ChatActions) {
        when (action) {
            is ChatActions.SendTextMessage -> sendTextMessage(action)
            is ChatActions.SendAudioRecording -> sendAudioRecording(action)
            is ChatActions.PerformHaptic -> performFeedback()
        }
    }

    private fun sendTextMessage(action: ChatActions.SendTextMessage) {
        if (action.imageUri != null) {
            sendVisionImage(action.imageUri, action.text)
        } else {
            sendTextMessageWithAdCheck(action)
        }
    }

    private fun sendTextMessageWithAdCheck(action: ChatActions.SendTextMessage) {
        viewModelScope.launch {
            val messageCount = messagesHandler.countAllUserMessagesWithLuzia() + 1

            adDataState.value?.let {
                adDataState.value = null
                adHandler.destroyAd(it)
            }

            var adViewData = adHandler.getPendingAdData(
                personalityId = personalityId.value,
                text = action.text,
                messageCount = messageCount
            )

            if (adViewData == null) {
                adViewData = adHandler.getAdData(
                    personalityId = personalityId.value,
                    text = action.text,
                    messageCount = messageCount
                )
            }

            proceedWithTextMessage(action.text, adViewData, messageCount)
        }
    }

    fun sendVisionImage(imageUri: Uri, text: String) {
        viewModelScope.launch {
            isAiResponding.update { true }
            val messageCount = messagesHandler.countAllUserMessagesWithLuzia() + 1

            adHandler.checkAndMarkPendingAd(personalityId.value, messageCount)

            val properties = mutableMapOf<Parameter, Any>(
                Parameter.Type to TYPE_VISION,
                Parameter.Personality to personalityId.value
            )
            sendMessageMetrics(properties)
            val file = fileImporter.resolveFile(imageUri, "File_", ".jpg")
            if (file != null) {
                val result =
                    messagesHandler.sendVisionImage(file, text, personalityId.value)
                result.asEvent()?.let(appEventContainer::sendEvent)
            }
            isAiResponding.update { false }
        }
    }

    override suspend fun sendTextMessage(text: String) {
        proceedWithTextMessage(text, null, 0)
    }

    private fun proceedWithTextMessage(text: String, adViewData: AdViewData?, messageCount: Int) {
        viewModelScope.launch {
            draftsRepository.saveDraft(personalityId.value, "")
            updateState { it.copy(fileState = FileViewState.None) }
            isAiResponding.update { true }

            val properties = mutableMapOf<Parameter, Any>(
                Parameter.Type to MessageType.Text.property,
                Parameter.Personality to personalityId.value
            )
            sendMessageMetrics(properties)

            val adContentForRepo = adHandler.mapToAdContent(adViewData)

            val result: ResultOf<TextMessageResultWrapper, AppErrors> =
                messagesHandler.sendTextMessageAndSignalAd(
                    personalityId = personalityId.value,
                    text = text,
                    adContent = adContentForRepo,
                    isWebSearchEnabled = viewState.value.webSearch?.isActive == true
                )
            toggleNeutralWebSearchState()
            when (result) {
                is ResultOf.Success -> {
                    if (adViewData != null) {
                        adDataState.value = adHandler.updateAdData(
                            adViewData = adViewData,
                            requestId = result.data.baseResult.metadata.requestId,
                            content = result.data.baseResult.content,
                            modifiedAdDescription = result.data.modifiedAdDescription
                        )
                        adHandler.recordAdImpression(personalityId.value, messageCount)
                        analytics.trackEvent(AdDisplayed(personalityId.value, messageCount))
                    }
                }

                is ResultOf.Failure -> {
                    if (adViewData != null) {
                        adHandler.destroyAd(adViewData)
                        // Log ad backend failure to Firebase
                        crashlytics.reportException(
                            "Ad backend failed after successful ad load - personality: ${personalityId.value}, " +
                                "messageCount: $messageCount",
                            Exception("Ad impression wasted due to backend failure: ${result.error}")
                        )
                    }
                    result.asEvent()?.let(appEventContainer::sendEvent)
                }
            }
            isAiResponding.update { false }
        }
    }

    override suspend fun resendMessage(message: MessageEntity) {
        isAiResponding.update { true }
        val properties =
            mutableMapOf<Parameter, Any>(
                Parameter.Type to message.messageType.property,
                Parameter.Personality to personalityId.value
            )
        sendMessageMetrics(properties)
        val isWebSearchEnabled = viewState.value.webSearch?.isActive == true
        val result = messagesHandler.resendMessage(message, isWebSearchEnabled)
        toggleNeutralWebSearchState()
        result.asEvent()?.let(appEventContainer::sendEvent)
        isAiResponding.update { false }
    }

    override suspend fun updateMessage(messageId: Long, text: String) {
        val isWebSearchEnabled = viewState.value.webSearch?.isActive == true
        messagesHandler.updateMessage(messageId, text, isWebSearchEnabled)
        toggleNeutralWebSearchState()
    }

    override fun getFeedbackSource(): FeedbackSource {
        return FeedbackSource.Chat(viewState.value.personalityModel?.personalityId.orEmpty())
    }

    private fun sendAudioRecording(action: ChatActions.SendAudioRecording) {
        viewModelScope.launch {
            isAiResponding.update { true }
            updateState { it.copy(fileState = FileViewState.None) }

            val messageCount = messagesHandler.countAllUserMessagesWithLuzia() + 1
            adHandler.checkAndMarkPendingAd(personalityId.value, messageCount)

            val properties =
                mutableMapOf<Parameter, Any>(
                    Parameter.Type to MessageType.AudioRecord.property,
                    Parameter.Personality to personalityId.value
                )
            sendMessageMetrics(properties)
            analytics.logEvent(SendVoiceChat, properties)
            val result = messagesHandler.sendAudioRecording(personalityId.value, action.file)
            result.asEvent()?.let(appEventContainer::sendEvent)
            isAiResponding.update { false }
        }
    }

    private suspend fun sendAudioImported(file: File) {
        isAiResponding.update { true }

        val messageCount = messagesHandler.countAllUserMessagesWithLuzia() + 1
        adHandler.checkAndMarkPendingAd(personalityId.value, messageCount)

        val properties =
            mutableMapOf<Parameter, Any>(
                Parameter.Type to MessageType.AudioImport.property,
                Parameter.Personality to personalityId.value
            )
        sendMessageMetrics(properties)
        val result = messagesHandler.sendAudioImported(personalityId.value, file)
        result.asEvent()?.let(appEventContainer::sendEvent)
        isAiResponding.update { false }
    }

    private fun performFeedback() {
        viewModelScope.launch { hapticFeedbackManager.shot() }
    }

    override suspend fun textToSpeech(text: String): ResultOf<Uri, AppErrors> =
        messagesHandler.textToSpeech(text)

    override fun getAdDataState(): StateFlow<AdViewData?> = adDataState.asStateFlow()

    private fun checkResponseStylesEnabled(personality: PersonalityModel): Boolean =
        if (viewState.value.isResponseStylesEnabled) {
            true
        } else {
            personality.personalityId == LUZIA_ID_PERSONALITY && personality.responseStyles.isNotEmpty()
        }

    override fun onCleared() {
        adHandler.destroyAd(adDataState.value)
        adDataState.value = null
        super.onCleared()
    }

    private companion object {
        const val ARG_PERSONALITY = "personalityId"
        const val ARG_CUSTOM_BESTIE = "isCustomBestie"
        const val TYPE_VISION = "vision"
        const val TOOL = "chat"
        const val DEBOUNCE_TYPING = 500L
        const val MAX_FILE_SIZE_ALLOWED = 20 * 1024 * 1024
    }
}
