package co.theworldlab.luzia.features.vision.screens

import android.content.Context
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import co.thewordlab.luzia.core.camera.advanced.AdvancedCameraScreen
import co.thewordlab.luzia.core.navigation.camera.CameraAnalysisModel
import co.theworldlab.luzia.features.vision.model.VisionAction
import co.theworldlab.luzia.features.vision.screens.common.PromptSelectionView
import co.theworldlab.luzia.foundation.design.system.components.input.model.CameraResult
import java.util.Locale

@Composable
fun VisionScreen(
    openGallery: <PERSON>olean,
    onSubmit: (CameraResult) -> Unit
) {
    var prompt: String? by remember { mutableStateOf(null) }
    val context = LocalContext.current
    Column(Modifier.fillMaxSize()) {
        AdvancedCameraScreen(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth(),
            startWithGallery = openGallery,
            analysisResultFor = CameraAnalysisModel.Vision,
            onFinalOutput = { output ->
                onSubmit(output.copy(prompt = prompt.orEmpty()))
            }
        )
        PromptSelectionView(
            onPromptChanged = { action ->
                prompt = action.getPrompt(context)
            }
        )
    }
}

private fun VisionAction.getPrompt(context: Context): String {
    return promptRes?.let { res ->
        if (this == VisionAction.TRANSLATE) {
            context.getString(res, Locale.getDefault().displayLanguage)
        } else {
            context.getString(res)
        }
    }.orEmpty()
}
