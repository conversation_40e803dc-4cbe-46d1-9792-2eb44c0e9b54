package co.theworldlab.luzia.features.vision.screens

import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.theworldlab.luzia.foundation.design.system.components.input.model.CameraResult

fun NavGraphBuilder.vision(navController: NavController) {
    composable<UserSessionRoutes.Vision> { backstackEntry ->
        val route = backstackEntry.toRoute<UserSessionRoutes.Vision>()
        VisionScreen(
            openGallery = route.openGallery,
            onSubmit = { result ->
                navController.run {
                    popBackStack(route, true)
                    currentBackStackEntry?.savedStateHandle?.set(CameraResult.KEY, result)
                }
            }
        )
    }
}
