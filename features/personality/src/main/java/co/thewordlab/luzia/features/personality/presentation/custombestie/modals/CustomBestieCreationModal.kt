package co.thewordlab.luzia.features.personality.presentation.custombestie.modals

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.PreviewLightDark
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.LuziaPrimaryButton
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun CustomBestieCreationModal() {
    val navigation = LocalNavigation.current
    Column(
        modifier = Modifier.padding(
            horizontal = Spacing.X24.dp,
            vertical = Spacing.X16.dp
        ),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        LuziaText(
            text = "\uD83D\uDED1",
            style = LuziaTheme.typography.headlines.h1,
            color = LuziaTheme.palette.text.primary
        )
        Spacer(modifier = Modifier.height(Spacing.X8.dp))
        LuziaText(
            text = stringResource(id = localizationR.string.custom_bestie_one_limit_title),
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary
        )
        Spacer(modifier = Modifier.height(Spacing.X8.dp))
        LuziaText(
            text = stringResource(id = localizationR.string.custom_bestie_one_limit_desc),
            style = LuziaTheme.typography.body.regular.small,
            color = LuziaTheme.palette.text.secondary
        )
        Spacer(modifier = Modifier.height(Spacing.X24.dp))
        LuziaPrimaryButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(id = localizationR.string.got_it_text),
            onClick = { navigation.goBack() }
        )
    }
}

@PreviewLightDark
@Composable
private fun Preview() {
    LuziaTheme {
        CustomBestieCreationModal()
    }
}
