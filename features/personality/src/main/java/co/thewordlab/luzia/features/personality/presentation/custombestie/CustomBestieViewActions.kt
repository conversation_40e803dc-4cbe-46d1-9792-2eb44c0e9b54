package co.thewordlab.luzia.features.personality.presentation.custombestie

import co.thewordlab.luzia.dynamicflow.presentation.models.FlowComponentState
import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class CustomBestieViewActions : ViewAction {
    data object OnCreate : CustomBestieViewActions()
    data object NavigatePreviousPage : CustomBestieViewActions()
    data object OnCancelFlowConfirmed : CustomBestieViewActions()
    data object OnRetryClicked : CustomBestieViewActions()
    data class CreateCustomBestie(val flowState: List<FlowComponentState>) : CustomBestieViewActions()
}
