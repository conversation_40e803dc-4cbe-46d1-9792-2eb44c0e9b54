package co.thewordlab.luzia.features.personality.di

import co.thewordlab.luzia.features.personality.data.api.CustomBestiesApi
import co.thewordlab.luzia.features.personality.data.api.PersonalityApi
import co.thewordlab.luzia.features.personality.data.repository.CustomBestieRepositoryImp
import co.thewordlab.luzia.features.personality.data.repository.DraftsRepositoryImpl
import co.thewordlab.luzia.features.personality.domain.repository.CustomBestieRepository
import co.thewordlab.luzia.features.personality.domain.repository.DraftsRepository
import co.thewordlab.luzia.foundation.networking.di.BaseHost
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit

@Module
@InstallIn(SingletonComponent::class)
object PersonalityModule {
    @Provides
    fun provideRetrofit(@BaseHost retrofit: Retrofit): PersonalityApi {
        return retrofit.create(PersonalityApi::class.java)
    }

    @Provides
    fun provideCustomBestieRetrofit(@BaseHost retrofit: Retrofit): CustomBestiesApi =
        retrofit.create(CustomBestiesApi::class.java)

    @Provides
    fun provideCustomBestieRepository(
        impl: CustomBestieRepositoryImp
    ): CustomBestieRepository = impl

    @Provides
    fun provideDraftsRepository(impl: DraftsRepositoryImpl): DraftsRepository = impl
}
