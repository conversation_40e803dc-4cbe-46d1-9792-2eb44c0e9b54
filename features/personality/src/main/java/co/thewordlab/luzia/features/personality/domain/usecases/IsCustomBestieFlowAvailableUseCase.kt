package co.thewordlab.luzia.features.personality.domain.usecases

import co.thewordlab.luzia.core.gamification.domain.GamificationRepository
import co.thewordlab.luzia.dynamicflow.domain.repository.DynamicFlowsRepository
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import co.thewordlab.luzia.foundation.networking.session.UserType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class IsCustomBestieFlowAvailableUseCase @Inject constructor(
    private val dynamicFlowRepository: DynamicFlowsRepository,
    private val gamificationRepository: GamificationRepository,
    private val featureFlagManager: FeatureFlagManager,
    private val userSessionManager: UserSessionManager
) {

    operator fun invoke(): Flow<Boolean> {
        return gamificationRepository.getPendingReward(CP_REWARD).map { reward ->
            val userSession = userSessionManager.getUserSessionImmediate()
            if (userSession?.userType != UserType.FULL_USER) {
                return@map false
            }
            val isGamificationEnabled: Boolean =
                featureFlagManager.get(FeatureFlag.GamificationEnabled)
            val isRewardClaimed = reward?.isClaimed == true
            val canProceed = if (isGamificationEnabled) {
                isRewardClaimed && checkFlowAvailability()
            } else {
                checkFlowAvailability()
            }
            canProceed
        }
    }

    private suspend fun checkFlowAvailability(): Boolean {
        return when (val result = dynamicFlowRepository.getAvailableFlows()) {
            is ResultOf.Failure -> false
            is ResultOf.Success -> {
                result.data
                    .filterNotNull()
                    .any { it.contains(CP_PREFIX) }
            }
        }
    }

    private companion object {
        const val CP_REWARD = "custom-besties-1"
        const val CP_PREFIX = "custom-bestie"
    }
}
