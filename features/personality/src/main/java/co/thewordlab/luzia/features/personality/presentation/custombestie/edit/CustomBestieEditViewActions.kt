package co.thewordlab.luzia.features.personality.presentation.custombestie.edit

import co.thewordlab.luzia.dynamicflow.presentation.models.FlowComponentState
import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class CustomBestieEditViewActions : ViewAction {
    data class OnCreate(val customBestieId: String) : CustomBestieEditViewActions()
    data class EditCustomBestie(val flowState: List<FlowComponentState>) : CustomBestieEditViewActions()
    data object OnCloseClicked : CustomBestieEditViewActions()
    data object OnRetryClicked : CustomBestieEditViewActions()
}
