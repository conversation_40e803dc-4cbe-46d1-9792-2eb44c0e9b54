package co.thewordlab.luzia.features.personality.data.repository

import androidx.datastore.preferences.core.booleanPreferencesKey
import co.thewordlab.fouundation.persistence.LuziaDataStore
import co.thewordlab.fouundation.persistence.chat.ChatDao
import co.thewordlab.fouundation.persistence.personality.PersonalityDao
import co.thewordlab.fouundation.persistence.personality.PersonalityEntity
import co.thewordlab.fouundation.persistence.personality.getPersonalityResponseStyle
import co.thewordlab.fouundation.persistence.personality.savePersonalityResponseStyle
import co.thewordlab.luzia.features.personality.data.api.PersonalityApi
import co.thewordlab.luzia.features.personality.data.models.PersonalityDTO
import co.thewordlab.luzia.features.personality.domain.LUZIA_ID_PERSONALITY
import co.thewordlab.luzia.features.personality.domain.asWelcomeMessage
import co.thewordlab.luzia.features.personality.domain.mapAsEntity
import co.thewordlab.luzia.features.personality.domain.mapAsModel
import co.thewordlab.luzia.features.personality.domain.models.PersonalityModel
import co.thewordlab.luzia.features.shortcuts.Shortcut
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.foundation.design.system.R
import co.thewordlab.luzia.foundation.networking.model.AppErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.asResult
import co.thewordlab.luzia.foundation.networking.model.getDataOrNull
import co.theworldlab.luzia.foundation.data.cache.store.disk.DiskCache
import co.theworldlab.luzia.foundation.data.cache.store.memory.inMemoryStore
import co.theworldlab.luzia.foundation.design.system.model.UiImage
import co.theworldlab.luzia.foundation.localization.TextProvider
import com.squareup.moshi.Moshi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.time.Duration.Companion.minutes
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Singleton
@Suppress("TooManyFunctions")
class PersonalityRepository @Inject constructor(
    private val personalityApi: PersonalityApi,
    private val textProvider: TextProvider,
    private val chatDao: ChatDao,
    private val personalityDao: PersonalityDao,
    private val luziaDataStore: LuziaDataStore,
    private val moshi: Moshi,
    private val featureFlagManager: FeatureFlagManager
) {

    private val personalitiesStore = inMemoryStore(
        fetcher = {
            val sortingEnabled =
                featureFlagManager.get<Boolean>(FeatureFlag.ToolsCharactersSortingEnabled)
            val response = personalityApi.getPersonalities(sortingEnabled).asResult()
            response.getDataOrNull()?.data.orEmpty().mapIndexed { index, dTO ->
                dTO.mapAsEntity(index, moshi)
            }
        },
        diskCache = object : DiskCache<List<PersonalityEntity>> {
            override fun getFromCache(): Flow<List<PersonalityEntity>> {
                return personalityDao.getAllPersonalities()
            }

            override suspend fun saveIntoCache(item: List<PersonalityEntity>) {
                val cached = personalityDao.getAllPersonalities().firstOrNull().orEmpty()
                val newList = item.mapIndexed { index, entity ->
                    val oldOrder = cached.find { it.personalityId == entity.personalityId }?.order
                    val order = if (entity.personalityId == LUZIA_ID_PERSONALITY) -1 else oldOrder
                    entity.copy(order = order ?: index)
                }
                personalityDao.hideAll()
                personalityDao.insertPersonalities(newList)
            }
        }
    ).withExpiration(CACHE_EXPIRY).build()

    private val luziaPersonality =
        PersonalityModel(
            personalityId = LUZIA_ID_PERSONALITY,
            name = "Luzia",
            description = textProvider.getString(localizationR.string.luzia_intro),
            avatar = UiImage.Resource(R.drawable.luzia_with_background),
            tag = "",
            tagDescription = "",
            welcomeMessage = textProvider.getString(localizationR.string.luzia_intro)
        )

    suspend fun getPersonality(id: String): Flow<PersonalityModel?> {
        val personalityFlow = personalityDao.getPersonality(id)
        val personality = personalityFlow.firstOrNull()
        return flow {
            if (personality == null && id == LUZIA_ID_PERSONALITY) {
                emit(luziaPersonality)
            } else {
                emit(personality?.mapAsModel(textProvider, moshi))
            }
            val personalityResult = fetchPersonalityResult(id)
            persistPersonalityResult(personalityResult)
            addWelcomeMessageIfNeeded(id, personalityResult)
            emitAll(personalityFlow.map { it?.mapAsModel(textProvider, moshi) })
        }
    }

    fun getAllPersonalities(): Flow<List<PersonalityModel>> = personalitiesStore.flow()
        .map { list -> list?.map { it.mapAsModel(textProvider, moshi) }.orEmpty() }

    suspend fun getAllShortcuts(): List<Shortcut> {
        val list = personalityDao.getAllPersonalities().first()
        return list.map { Shortcut(it.personalityId, it.name, it.thumbnail) }
    }

    fun isPersonalityProfileShown(): Flow<Boolean?> =
        luziaDataStore.getDataFlow(personalityProfileShown)

    suspend fun markPersonalityProfileAsShown() =
        luziaDataStore.saveData(personalityProfileShown, true)

    suspend fun savePersonalityResponseStyle(
        personalityId: String,
        responseStyleId: String
    ) {
        luziaDataStore.savePersonalityResponseStyle(personalityId, responseStyleId, moshi)
    }

    suspend fun getPersonalityResponseStyle(personalityId: String): String? =
        luziaDataStore.getPersonalityResponseStyle(personalityId, moshi)

    private suspend fun fetchPersonalityResult(id: String): ResultOf<PersonalityDTO, AppErrors> =
        personalityApi.getPersonality(id).asResult()

    private suspend fun <T> persistPersonalityResult(result: ResultOf<T, AppErrors>) {
        (result as? ResultOf.Success)?.let { successResult ->
            val entity = when (val data = successResult.data) {
                is PersonalityDTO -> {
                    val index =
                        personalityDao.getPersonality(data.personalityId).firstOrNull()?.order ?: 0
                    val order = if (data.personalityId == LUZIA_ID_PERSONALITY) -1 else index
                    data.mapAsEntity(order, moshi)
                }

                else -> null
            }
            entity?.let { personalityDao.insertPersonality(it) }
        }
    }

    private suspend fun <T> addWelcomeMessageIfNeeded(
        id: String,
        result: ResultOf<T, AppErrors>
    ) {
        if (chatDao.hasWelcomeMessage(id) > 0) return
        val message = when (result) {
            is ResultOf.Success -> {
                when (val data = result.data) {
                    is PersonalityDTO -> data.asWelcomeMessage(textProvider)
                    else -> null
                }
            }

            is ResultOf.Failure -> {
                val personality = personalityDao.getPersonality(id).firstOrNull()
                personality?.asWelcomeMessage(textProvider)
            }
        }
        message?.let { chatDao.insertMessage(it) }
    }

    private companion object {
        val CACHE_EXPIRY = 5.minutes
        const val KEY_PERSONALITY_PROFILE_SHOWN = "personality_profile_shown"
        val personalityProfileShown = booleanPreferencesKey(KEY_PERSONALITY_PROFILE_SHOWN)
    }
}
