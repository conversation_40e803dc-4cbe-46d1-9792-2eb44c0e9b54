package co.thewordlab.luzia.features.personality.domain.usecases

import co.thewordlab.luzia.features.personality.domain.repository.CustomBestieRepository
import kotlinx.coroutines.flow.firstOrNull
import javax.inject.Inject

class IsCustomBestieCreatedUseCase @Inject constructor(
    private val customBestieRepository: CustomBestieRepository
) {

    suspend operator fun invoke(): Boolean =
        !customBestieRepository.getAllCustomBesties(forceSync = false).firstOrNull().isNullOrEmpty()
}
