package co.thewordlab.luzia.features.personality.presentation.personalityprofile

import co.thewordlab.luzia.features.profile.presentation.profilefill.pronouns.models.PronounsUiModel
import co.thewordlab.luzia.foundation.architecture.system.ViewState
import co.theworldlab.luzia.foundation.design.system.model.UiImage

data class PersonalityProfileViewState(
    val personalityName: String = "",
    val personalityLabel: String = "",
    val avatarState: UiImage? = null,
    val avatarAnimationId: String? = null,
    val bannerImageUrl: String = "",
    val pronouns: PronounsUiModel? = null,
    val isFullUser: Boolean = false,
    val responseStylesEnabled: Boolean = false,
    val responseStyleSelected: String = "",
    val showEditProfile: Boolean = false
) : ViewState
