package co.thewordlab.luzia.features.personality.domain.models.custombestie

import co.thewordlab.fouundation.persistence.custombestie.CustomBestieEntity
import co.thewordlab.luzia.core.profile.domain.model.UserPronouns
import co.thewordlab.luzia.features.personality.data.models.custombestie.CustomBestieDTO
import co.thewordlab.luzia.features.personality.data.models.custombestie.CustomBestieOptionDTO
import co.thewordlab.luzia.features.personality.domain.models.PersonalityModel
import co.theworldlab.luzia.foundation.design.system.model.UiImage
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types.newParameterizedType

fun CustomBestieDTO.mapToEntity(order: Int, moshi: Moshi) =
    CustomBestieEntity(
        customBestieId = customBestieId,
        name = name,
        description = description,
        imageURL = imageURL?.mapToJson(moshi),
        backgroundURL = backgroundURL,
        vibe = vibe.mapTo<PERSON>son(moshi),
        responseStyle = responseStyle?.mapTo<PERSON>son(moshi),
        humour = humour?.mapTo<PERSON>son(moshi),
        freeText = freeText,
        pronouns = pronouns?.mapToJson(moshi),
        order = order
    )

fun CustomBestieEntity.mapToModel(moshi: Moshi) = CustomBestieModel(
    id = customBestieId,
    name = name,
    description = description,
    imageUrl = imageURL.mapToCustomBestieOption(moshi),
    backgroundURL = backgroundURL,
    vibe = vibe.mapToCustomBestieOptionList(moshi),
    freeText = freeText.orEmpty(),
    pronouns = pronouns.mapToCustomBestieOption(moshi),
    humour = humour.mapToCustomBestieOption(moshi),
    responseStyle = responseStyle.mapToCustomBestieOption(moshi),
)

private fun CustomBestieOptionDTO.mapToJson(moshi: Moshi): String =
    moshi.adapter(CustomBestieOptionDTO::class.java).toJson(this)

private fun List<CustomBestieOptionDTO>?.mapToJson(moshi: Moshi): String? =
    this?.let {
        val listType = newParameterizedType(List::class.java, CustomBestieOptionDTO::class.java)
        val adapter: JsonAdapter<List<CustomBestieOptionDTO>> = moshi.adapter(listType)
        adapter.toJson(it)
    }

fun String?.mapToCustomBestieOption(moshi: Moshi): CustomBestieOption? =
    this?.let { moshi.adapter(CustomBestieOptionDTO::class.java).fromJson(it) }?.mapToModel()

private fun String?.mapToCustomBestieOptionList(moshi: Moshi): List<CustomBestieOption> =
    this?.let {
        val listType = newParameterizedType(List::class.java, CustomBestieOptionDTO::class.java)
        val adapter: JsonAdapter<List<CustomBestieOptionDTO>> = moshi.adapter(listType)
        val items = adapter.fromJson(it) ?: emptyList()
        items.map { item -> item.mapToModel() }
    } ?: emptyList()

private fun CustomBestieOptionDTO.mapToModel() = CustomBestieOption(
    id = key,
    value = value.orEmpty()
)

fun CustomBestieModel.mapToPersonalityModel() = PersonalityModel(
    personalityId = id,
    name = name,
    description = description,
    avatar = UiImage.Plain(imageUrl?.value.orEmpty()),
    pronouns = UserPronouns.getValue(pronouns?.value.orEmpty()),
    backgroundURL = backgroundURL,
    isCustomBestie = true
)
