package co.thewordlab.luzia.features.personality.presentation.custombestie

import co.thewordlab.luzia.dynamicflow.presentation.models.FlowComponentState
import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class CustomBestieViewEvents : ViewEvent {
    data object NavigateBack : CustomBestieViewEvents()
    data class NavigateToStepDetail(val id: String) : CustomBestieViewEvents()
    data object ShowCancelConfirmation : CustomBestieViewEvents()
    data object CloseCreationFlow : CustomBestieViewEvents()
    data object CollectFlowData : CustomBestieViewEvents()
    data class NavigateToChatDetail(val customBestieId: String) : CustomBestieViewEvents()
    data object ShowGenericErrorMessage : CustomBestieViewEvents()
    data object ShowCreationNotAllowed : CustomBestieViewEvents()
    data class PushLocalState(val components: List<FlowComponentState>) : CustomBestieViewEvents()
}
