package co.thewordlab.luzia.features.personality.presentation.personalityprofile

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.core.profile.presentation.common.StudentChipView
import co.thewordlab.luzia.core.profile.presentation.entrypoints.chat.DurationView
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.OnStart
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonFilled
import co.theworldlab.luzia.foundation.design.system.model.UiImage
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import coil3.compose.AsyncImage
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

private const val ASPECT_RATIO_PROFILE_BANNER = 360f / 220
private const val BACKGROUND_PROFILE_BANNER = 0xFFE8E8FF
private const val SIZE_PROFILE_IMAGE = 114

@Composable
fun PersonalityProfileScreen(
    personalityId: String,
    isCustomBestie: Boolean
) {
    val navigation = LocalNavigation.current
    val viewModel: PersonalityProfileViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()

    OnCreate("PersonalityProfileScreen") {
        viewModel.onViewAction(
            PersonalityProfileViewActions.OnCreate(
                personalityId,
                isCustomBestie
            )
        )
    }

    OnStart { viewModel.onViewAction(PersonalityProfileViewActions.OnStart) }

    ViewModelEventEffect(viewModel) {
        when (it) {
            PersonalityProfileViewEvents.NavigateBack ->
                navigation.goBack()

            PersonalityProfileViewEvents.OnNavigateToFavorites ->
                navigation.navigate(UserSessionRoutes.Favorites(personalityId, isCustomBestie))

            PersonalityProfileViewEvents.OnNavigateToSignup ->
                navigation.navigate(UserSessionRoutes.Signup())

            is PersonalityProfileViewEvents.OnNavigateToResponseStyles ->
                navigation.navigate(UserSessionRoutes.ResponseStyles(it.personalityId))

            is PersonalityProfileViewEvents.OnNavigateToProfileEdit ->
                navigation.navigate(UserSessionRoutes.CustomBestieEdition(it.personalityId))
        }
    }
    PersonalityProfileContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@Composable
private fun PersonalityProfileContent(
    viewState: PersonalityProfileViewState,
    onViewActions: (PersonalityProfileViewActions) -> Unit
) {
    Scaffold(
        containerColor = LuziaTheme.palette.surface.content,
        contentWindowInsets = WindowInsets.navigationBars
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            Column {
                ProfileBannerView(viewState = viewState, onViewActions = onViewActions)
                Spacing.X64.Vertical()
                ProfileBasicInformation(viewState = viewState)
                Spacing.X8.Vertical()
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(rememberScrollState())
                        .padding(horizontal = Spacing.X16.dp)
                ) {
                    if (viewState.responseStylesEnabled) {
                        ResponseStylesButton(onViewActions, viewState.responseStyleSelected)
                        Spacing.X8.Vertical()
                    }
                    FavoritesButton(onViewActions)
                    if (viewState.isFullUser) {
                        FullUserContent()
                    } else {
                        GuestUserContent(onViewActions)
                    }
                }
            }
            NavigationContent(onViewActions)
        }
    }
}

@Composable
private fun GuestUserContent(onViewActions: (PersonalityProfileViewActions) -> Unit) {
    Spacer(modifier = Modifier.height(Spacing.X8.dp))
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(Corners.X4.dp))
            .border(
                width = 1.dp,
                color = LuziaTheme.palette.border.primary,
                shape = RoundedCornerShape(Corners.X4.dp)
            )
            .background(Color.Transparent)
            .padding(Spacing.X16.dp)
    ) {
        LuziaText(
            text = stringResource(id = localizationR.string.connect_with_friends_cta),
            style = LuziaTheme.typography.body.semiBold.default,
            color = LuziaTheme.palette.text.primary
        )
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
        StudentChipView()
        Spacer(modifier = Modifier.height(Spacing.X8.dp))
        LuziaText(
            text = stringResource(id = localizationR.string.luzia_profile_join_community_title),
            style = LuziaTheme.typography.body.semiBold.small,
            color = LuziaTheme.palette.text.secondary
        )
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
        DurationView(text = stringResource(id = localizationR.string.group_chat_mins))
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
        ButtonFilled(
            modifier = Modifier.fillMaxWidth().testTag("buttonSignup"),
            buttonText = stringResource(id = localizationR.string.profile_signup_title_cta),
            onClick = { onViewActions(PersonalityProfileViewActions.OnSignupClicked) }
        )
    }
}

@Composable
private fun ColumnScope.FullUserContent() {
    Spacer(modifier = Modifier.weight(1f))
    LuziaText(
        modifier = Modifier
            .padding(horizontal = Spacing.X16.dp)
            .align(Alignment.CenterHorizontally),
        text = stringResource(id = localizationR.string.luzia_profile_enjoy_community),
        style = LuziaTheme.typography.body.semiBold.default,
        color = LuziaTheme.palette.text.primary
    )
    Spacer(modifier = Modifier.height(Spacing.X8.dp))
    Box(
        modifier = Modifier
            .wrapContentSize()
            .align(Alignment.CenterHorizontally)
            .background(LuziaTheme.palette.accents.green.green10, CircleShape)
            .padding(vertical = Spacing.X4.dp, horizontal = Spacing.X8.dp),
        contentAlignment = Alignment.Center
    ) {
        LuziaText(
            text = stringResource(id = localizationR.string.number_of_students),
            style = LuziaTheme.typography.body.semiBold.small,
            color = LuziaTheme.palette.accents.green.green90
        )
    }
    Spacer(modifier = Modifier.height(Spacing.X16.dp))
}

@Composable
private fun ResponseStylesButton(
    onViewActions: (PersonalityProfileViewActions) -> Unit,
    responseStyleSelected: String
) {
    ButtonContent(
        modifier = Modifier.testTag("buttonResponseStyles"),
        iconRes = designR.drawable.ic_pencil_32,
        iconTint = LuziaTheme.palette.accents.blue.blue90,
        iconBackground = LuziaTheme.palette.accents.blue.blue10,
        iconPadding = 0.dp,
        textRes = localizationR.string.response_style_responses_text,
        subtitleText = responseStyleSelected,
        onClick = { onViewActions(PersonalityProfileViewActions.OnResponseStylesClicked) }
    )
}

@Composable
private fun FavoritesButton(onViewActions: (PersonalityProfileViewActions) -> Unit) {
    ButtonContent(
        modifier = Modifier.testTag("buttonFavorites"),
        iconRes = designR.drawable.ic_star_12,
        iconTint = LuziaTheme.palette.accents.yellow.yellow50,
        iconBackground = LuziaTheme.palette.accents.yellow.yellow10,
        textRes = localizationR.string.signup_favorite_messages_tag,
        onClick = { onViewActions(PersonalityProfileViewActions.OnFavoritesClicked) }
    )
}

@Composable
private fun ButtonContent(
    modifier: Modifier,
    @DrawableRes iconRes: Int,
    iconTint: Color,
    iconBackground: Color,
    iconPadding: Dp = Spacing.X8.dp,
    @StringRes textRes: Int,
    subtitleText: String = "",
    onClick: () -> Unit
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(Corners.X4.dp))
            .click { onClick() }
            .border(
                width = 1.dp,
                color = LuziaTheme.palette.border.primary,
                shape = RoundedCornerShape(Corners.X4.dp)
            )
            .background(Color.Transparent)
            .padding(Spacing.X16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            modifier = Modifier
                .size(IconSizes.X40.dp)
                .background(iconBackground, shape = CircleShape)
                .padding(iconPadding),
            painter = painterResource(id = iconRes),
            contentDescription = null,
            tint = iconTint
        )
        Spacer(modifier = Modifier.width(Spacing.X8.dp))
        Column(modifier = Modifier.weight(1f)) {
            LuziaText(
                text = stringResource(id = textRes),
                style = LuziaTheme.typography.body.semiBold.default,
                color = LuziaTheme.palette.text.secondary
            )
            if (subtitleText.isNotEmpty()) {
                LuziaText(
                    text = subtitleText,
                    style = LuziaTheme.typography.body.regular.default,
                    color = LuziaTheme.palette.text.secondary
                )
            }
        }
        Spacer(modifier = Modifier.width(Spacing.X8.dp))
        Icon(
            modifier = Modifier.size(Spacing.X12.dp),
            painter = painterResource(designR.drawable.ic_chevron_right),
            tint = LuziaTheme.palette.text.secondary,
            contentDescription = null
        )
    }
}

@Composable
private fun NavigationContent(onViewActions: (PersonalityProfileViewActions) -> Unit) {
    Column {
        Spacer(modifier = Modifier.statusBarsPadding())
        Box(
            modifier = Modifier
                .padding(Spacing.X8.dp)
                .fillMaxWidth()
        ) {
            IconButton(
                modifier = Modifier
                    .background(LuziaTheme.palette.surface.content, CircleShape)
                    .align(Alignment.TopStart)
                    .testTag("buttonBack"),
                onClick = { onViewActions(PersonalityProfileViewActions.OnBackClicked) }
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_back_arrow),
                    tint = LuziaTheme.palette.text.primary,
                    contentDescription = null
                )
            }
        }
    }
}

@Composable
private fun ProfileBannerView(
    viewState: PersonalityProfileViewState,
    onViewActions: (PersonalityProfileViewActions) -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(ASPECT_RATIO_PROFILE_BANNER)
            .background(Color(BACKGROUND_PROFILE_BANNER))
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentDescription = null,
            contentScale = ContentScale.Crop,
            painter = painterResource(R.drawable.im_background_placeholder)
        )
        AsyncImage(
            modifier = Modifier.fillMaxSize(),
            model = viewState.bannerImageUrl,
            contentDescription = null,
            contentScale = ContentScale.Crop
        )
        Box(
            modifier = Modifier
                .padding(start = Spacing.X16.dp)
                .align(Alignment.BottomStart)
                .offset(y = (SIZE_PROFILE_IMAGE / 2).dp)
        ) {
            viewState.avatarAnimationId?.let { anim ->
                val composition by rememberLottieComposition(LottieCompositionSpec.Asset(anim))
                LottieAnimation(
                    modifier = Modifier
                        .clip(CircleShape)
                        .size(SIZE_PROFILE_IMAGE.dp),
                    contentScale = ContentScale.FillWidth,
                    composition = composition
                )
            } ?: run {
                AsyncImage(
                    modifier = Modifier
                        .clip(CircleShape)
                        .size(SIZE_PROFILE_IMAGE.dp),
                    model = when (val image = viewState.avatarState) {
                        is UiImage.Resource -> image.resourceId
                        is UiImage.Plain -> image.url
                        null -> DO_NOTHING
                    },
                    contentDescription = null
                )
            }
            if (viewState.showEditProfile) {
                IconButton(
                    modifier = Modifier
                        .background(LuziaTheme.palette.surface.content, CircleShape)
                        .align(Alignment.BottomEnd),
                    onClick = { onViewActions(PersonalityProfileViewActions.OnProfileEditClicked) }
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_edit_24),
                        tint = LuziaTheme.palette.interactive.primary,
                        contentDescription = null
                    )
                }
            }
        }
    }
}

@Composable
private fun ProfileBasicInformation(viewState: PersonalityProfileViewState) {
    Column(
        modifier = Modifier
            .padding(bottom = Spacing.X16.dp)
            .padding(horizontal = Spacing.X16.dp),
        verticalArrangement = Arrangement.spacedBy(2.dp)
    ) {
        LuziaText(
            text = viewState.personalityName,
            style = LuziaTheme.typography.headlines.h2,
            color = LuziaTheme.palette.text.primary
        )
        viewState.pronouns?.let {
            LuziaText(
                text = stringResource(id = it.label),
                style = LuziaTheme.typography.body.regular.footnote,
                color = LuziaTheme.palette.text.helper
            )
        }
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
        LuziaText(
            text = viewState.personalityLabel,
            style = LuziaTheme.typography.body.regular.default,
            color = LuziaTheme.palette.text.secondary
        )
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        PersonalityProfileContent(
            viewState = PersonalityProfileViewState(),
            onViewActions = {}
        )
    }
}
