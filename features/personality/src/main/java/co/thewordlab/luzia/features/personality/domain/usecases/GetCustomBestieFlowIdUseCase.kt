package co.thewordlab.luzia.features.personality.domain.usecases

import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.foundation.config.FeatureFlagTreatment
import javax.inject.Inject

class GetCustomBestieFlowIdUseCase @Inject constructor(
    private val featureFlagManager: FeatureFlagManager
) {

    operator fun invoke(): String {
        val featureValue = featureFlagManager.getImmediate<String>(FeatureFlag.CustomBestieFlowId)
        return when (FeatureFlagTreatment.getValue(featureValue)) {
            FeatureFlagTreatment.TREATMENT_A -> CB_A
            FeatureFlagTreatment.TREATMENT_B -> CB_B
            FeatureFlagTreatment.TREATMENT_C -> CB_C
            FeatureFlagTreatment.CONTROL -> CB_CONTROL
        }
    }

    private companion object {
        const val CB_CONTROL = "custom-besties"
        const val CB_A = "custom-besties-a"
        const val CB_B = "custom-besties-b"
        const val CB_C = "custom-besties-c"
    }
}
