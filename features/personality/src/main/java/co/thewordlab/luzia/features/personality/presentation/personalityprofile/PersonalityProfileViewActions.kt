package co.thewordlab.luzia.features.personality.presentation.personalityprofile

import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class PersonalityProfileViewActions : ViewAction {
    data class OnCreate(val personalityId: String, val isCustomBestie: Boolean) : PersonalityProfileViewActions()
    data object OnStart : PersonalityProfileViewActions()
    data object OnFavoritesClicked : PersonalityProfileViewActions()
    data object OnBackClicked : PersonalityProfileViewActions()
    data object OnSignupClicked : PersonalityProfileViewActions()
    data object OnResponseStylesClicked : PersonalityProfileViewActions()
    data object OnProfileEditClicked : PersonalityProfileViewActions()
}
