package co.thewordlab.luzia.features.personality.domain

@Suppress("CyclomaticComplexMethod")
fun findPersonalityLottie(personalityId: String): String? =
    when (personalityId) {
        "luzia_bro" -> "personality_elias.lottie"
        "luzia_games" -> "personality_games.lottie"
        "LuzIA" -> "personality_luzia.lottie"
        "luzia_advent" -> "personality_advent.lottie"
        "luzia_intimate" -> "personality_intima.lottie"
        "luzia_lawyer" -> "personality_lawyer.lottie"
        "luzia_surprise" -> "personality_surpise.lottie"
        "professor" -> "personality_teacher.lottie"
        "luzia_language_teacher" -> "personality_languages.lottie"
        "marketing" -> "personality_marketeer.lottie"
        "luzia_horoscope" -> "personality_astrologer.lottie"
        "luzia_real_time" -> "personality_info_luzia_now.lottie"
        "journaling" -> "personality_your_friend.lottie"
        else -> null
    }
