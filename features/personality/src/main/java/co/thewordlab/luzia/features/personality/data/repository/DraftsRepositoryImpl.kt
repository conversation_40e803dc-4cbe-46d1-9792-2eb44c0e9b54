package co.thewordlab.luzia.features.personality.data.repository

import co.thewordlab.fouundation.persistence.drafts.DraftEntity
import co.thewordlab.fouundation.persistence.drafts.DraftsDao
import co.thewordlab.luzia.features.personality.domain.repository.DraftsRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class DraftsRepositoryImpl @Inject constructor(
    private val draftsDao: DraftsDao
) : DraftsRepository {
    override suspend fun saveDraft(id: String, text: String) {
        val item = DraftEntity(id, text)
        draftsDao.saveDraft(item)
    }

    override suspend fun getDraft(id: String): Flow<String?> {
        return draftsDao.getDraft(id)
            .map { it?.text }
            .distinctUntilChanged()
    }

    override suspend fun deleteAll() {
        draftsDao.deleteAll()
    }
}
