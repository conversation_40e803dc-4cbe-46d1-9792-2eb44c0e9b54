package co.thewordlab.luzia.features.personality.presentation.custombestie.edit

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.dynamicflow.presentation.factory.LocalDynamicFlowState
import co.thewordlab.luzia.dynamicflow.presentation.steps.FlowEditScreen
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.error.ErrorViewContent
import co.theworldlab.luzia.foundation.design.system.components.scaffold.LocalAppState
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.legacy.composables.Loading
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun CustomBestieEditScreen(customBestieId: String) {
    val viewModel: CustomBestieEditViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val localState = LocalDynamicFlowState.current
    val navigation = LocalNavigation.current
    val appState = LocalAppState.current
    val context = LocalContext.current

    OnCreate("CustomBestieScreen") {
        viewModel.onViewAction(CustomBestieEditViewActions.OnCreate(customBestieId))
    }

    ViewModelEventEffect(viewModel) {
        when (it) {
            CustomBestieEditViewEvents.NavigateBack -> navigation.goBack()
            is CustomBestieEditViewEvents.NavigateToStepDetail ->
                navigation.navigate(UserSessionRoutes.CustomBestieStepDetail(it.id))

            CustomBestieEditViewEvents.CollectFlowData ->
                viewModel.onViewAction(CustomBestieEditViewActions.EditCustomBestie(localState.get()))

            CustomBestieEditViewEvents.ShowEditionNotAllowed ->
                navigation.navigate(UserSessionRoutes.CustomBestieEditionModal)

            CustomBestieEditViewEvents.ShowGenericErrorMessage ->
                appState.showSnackBar(context.getString(localizationR.string.generic_error))

            CustomBestieEditViewEvents.CleanLocalData -> localState.clear()
            is CustomBestieEditViewEvents.UpdateLocalData -> localState.plusAll(it.customBestieData)
        }
    }

    when {
        viewState.isError -> ErrorScreenContent(
            modifier = Modifier,
            errorMessage = viewState.errorMessage,
            onViewActions = viewModel::onViewAction
        )

        viewState.isLoading -> Loading(Modifier, showTranslucentBackground = true)
        else -> {
            viewState.screenConfigData?.let {
                FlowEditScreen(
                    editData = it,
                    onViewActions = viewModel::onFlowViewAction
                )
            }
        }
    }
}

@Composable
private fun ErrorScreenContent(
    modifier: Modifier,
    errorMessage: String,
    onViewActions: (CustomBestieEditViewActions) -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(Spacing.X16.dp)
    ) {
        NavigationIcon(
            modifier = Modifier,
            icon = R.drawable.ic_close,
            onClick = { onViewActions(CustomBestieEditViewActions.OnCloseClicked) }
        )
        ErrorViewContent(
            errorMessage = errorMessage,
            onRetryClicked = { onViewActions(CustomBestieEditViewActions.OnRetryClicked) }
        )
    }
}

@Composable
private fun NavigationIcon(modifier: Modifier, @DrawableRes icon: Int, onClick: () -> Unit) {
    Box(
        modifier = modifier
            .clip(CircleShape)
            .background(color = LuziaTheme.palette.interactive.contrast, shape = CircleShape)
            .size(IconSizes.X48.dp)
            .click(action = onClick),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            painter = painterResource(id = icon),
            contentDescription = null,
            modifier = Modifier.size(IconSizes.X32.dp),
            tint = LuziaTheme.palette.interactive.primary
        )
    }
}
