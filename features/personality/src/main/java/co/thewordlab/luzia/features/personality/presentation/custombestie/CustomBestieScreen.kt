package co.thewordlab.luzia.features.personality.presentation.custombestie

import androidx.activity.compose.BackHandler
import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Scaffold
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.navOptions
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.dynamicflow.presentation.factory.DynamicFlowManager
import co.thewordlab.luzia.dynamicflow.presentation.factory.FlowStructure
import co.thewordlab.luzia.dynamicflow.presentation.factory.LocalDynamicFlowState
import co.thewordlab.luzia.dynamicflow.presentation.steps.FlowLoadingScreen
import co.thewordlab.luzia.foundation.analytics.AnalyticsMultiplexer
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.localization.R
import co.theworldlab.luzia.foundation.design.system.components.error.ErrorViewContent
import co.theworldlab.luzia.foundation.design.system.components.scaffold.LocalAppState
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.legacy.composables.Loading
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialog
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogButton
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogTextDefaults
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Suppress("CyclomaticComplexMethod")
@Composable
fun CustomBestieScreen() {
    val viewModel: CustomBestieViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    var exitConfirmationVisible by remember { mutableStateOf(false) }
    val localState = LocalDynamicFlowState.current
    val navigation = LocalNavigation.current
    val appState = LocalAppState.current
    val context = LocalContext.current

    OnCreate("CustomBestieScreen") {
        viewModel.onViewAction(CustomBestieViewActions.OnCreate)
    }

    BackHandler { viewModel.onViewAction(CustomBestieViewActions.NavigatePreviousPage) }

    ViewModelEventEffect(viewModel) { event ->
        when (event) {
            CustomBestieViewEvents.NavigateBack -> navigation.goBack()
            is CustomBestieViewEvents.NavigateToStepDetail ->
                navigation.navigate(UserSessionRoutes.CustomBestieStepDetail(event.id))
            CustomBestieViewEvents.CloseCreationFlow ->
                navigation.goBackTo(
                    UserSessionRoutes.CustomBestieCreation,
                    inclusive = true
                )

            CustomBestieViewEvents.ShowCancelConfirmation -> {
                exitConfirmationVisible = true
            }

            CustomBestieViewEvents.CollectFlowData ->
                viewModel.onViewAction(CustomBestieViewActions.CreateCustomBestie(localState.get()))

            is CustomBestieViewEvents.NavigateToChatDetail ->
                navigation.navigate(
                    UserSessionRoutes.ChatDetail(
                        personalityId = event.customBestieId,
                        isCustomBestie = true
                    ),
                    navOptions { popUpTo(UserSessionRoutes.CustomBestieCreation) { inclusive = true } }
                )

            CustomBestieViewEvents.ShowCreationNotAllowed ->
                navigation.navigate(UserSessionRoutes.CustomBestieCreationModal)

            CustomBestieViewEvents.ShowGenericErrorMessage ->
                appState.showSnackBar(context.getString(localizationR.string.generic_error))

            is CustomBestieViewEvents.PushLocalState -> {
                localState.clear()
                localState.plusAll(event.components)
            }
        }
    }

    if (viewState.screensData.isNotEmpty() || viewState.isLoading || viewState.isError) {
        Scaffold(
            containerColor = LuziaTheme.palette.surface.background
        ) { innerPadding ->
            when {
                viewState.isError -> ErrorScreenContent(
                    modifier = Modifier.padding(innerPadding),
                    errorMessage = viewState.errorMessage,
                    onViewActions = viewModel::onViewAction
                )

                viewState.isLoading -> Loading(Modifier.padding(innerPadding))
                viewState.screensData.isNotEmpty() ->
                    CustomBestieContent(
                        modifier = Modifier.padding(innerPadding),
                        viewState = viewState,
                        dynamicFlowManager = viewModel.dynamicFlowManager
                    )
            }
        }
    }

    if (viewState.isLoadingFinalStep) {
        LoadingFinalStepContent()
    }

    if (exitConfirmationVisible) {
        ExitAlertDialog(
            onConfirm = {
                exitConfirmationVisible = false
                viewModel.onViewAction(CustomBestieViewActions.OnCancelFlowConfirmed)
            },
            onDismiss = { exitConfirmationVisible = false }
        )
    }
}

@Composable
private fun ErrorScreenContent(
    modifier: Modifier,
    errorMessage: String,
    onViewActions: (CustomBestieViewActions) -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(Spacing.X16.dp)
    ) {
        NavigationIcon(
            modifier = Modifier,
            icon = designR.drawable.ic_close,
            onClick = { onViewActions(CustomBestieViewActions.NavigatePreviousPage) }
        )
        ErrorViewContent(
            errorMessage = errorMessage,
            onRetryClicked = { onViewActions(CustomBestieViewActions.OnRetryClicked) }
        )
    }
}

@Composable
private fun NavigationIcon(modifier: Modifier, @DrawableRes icon: Int, onClick: () -> Unit) {
    Box(
        modifier = modifier
            .clip(CircleShape)
            .background(color = LuziaTheme.palette.interactive.contrast, shape = CircleShape)
            .size(IconSizes.X48.dp)
            .click(action = onClick),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            painter = painterResource(id = icon),
            contentDescription = null,
            modifier = Modifier.size(IconSizes.X32.dp),
            tint = LuziaTheme.palette.interactive.primary
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun LoadingFinalStepContent() {
    val bottomSheetState = rememberModalBottomSheetState(true, confirmValueChange = { false })
    ModalBottomSheet(
        sheetState = bottomSheetState,
        containerColor = LuziaTheme.palette.surface.background,
        dragHandle = null,
        onDismissRequest = { DO_NOTHING }
    ) {
        FlowLoadingScreen(
            loadingTextRes = localizationR.string.custom_bestie_creating_title,
            lottieAnimRes = "custom_bestie_creation.lottie"
        ).Content()
    }
}

@Composable
private fun ExitAlertDialog(onConfirm: () -> Unit, onDismiss: () -> Unit) {
    LuziaAlertDialog(
        confirmButton = LuziaAlertDialogButton(
            stringResource(id = R.string.yes_text),
            action = onConfirm,
            isPrimaryAction = true
        ),
        dismissButton = LuziaAlertDialogButton(
            stringResource(id = R.string.cancel),
            action = onDismiss
        ),
        title = LuziaAlertDialogText(
            stringResource(id = R.string.custom_bestie_exit_confirmation_title),
            LuziaAlertDialogTextDefaults.Title()
        ),
        text = LuziaAlertDialogText(
            stringResource(id = R.string.custom_bestie_exit_confirmation_desc),
            LuziaAlertDialogTextDefaults.Description()
        )
    )
}

@Composable
private fun CustomBestieContent(
    modifier: Modifier,
    viewState: CustomBestieViewState,
    dynamicFlowManager: DynamicFlowManager
) {
    val pagerState = rememberPagerState(pageCount = { viewState.screensData.size })
    val currentScreen = remember { mutableStateOf<FlowStructure?>(null) }

    LaunchedEffect(viewState.currentPage) {
        currentScreen.value =
            dynamicFlowManager.buildScreenContent(viewState.screensData[viewState.currentPage])
        pagerState.animateScrollToPage(viewState.currentPage)
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(LuziaTheme.palette.surface.background)
            .imePadding()
    ) {
        currentScreen.value?.TopNavigation()
        HorizontalPager(
            state = pagerState,
            modifier = Modifier
                .weight(1f)
                .fillMaxSize(),
            userScrollEnabled = false
        ) { currentScreen.value?.Content() }
        currentScreen.value?.BottomNavigation()
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        CustomBestieContent(
            modifier = Modifier,
            viewState = CustomBestieViewState(),
            dynamicFlowManager = DynamicFlowManager(AnalyticsMultiplexer(emptyList()))
        )
    }
}
