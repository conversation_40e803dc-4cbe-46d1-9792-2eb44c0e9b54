package co.thewordlab.luzia.features.personality.data.models.custombestie

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class CustomBestiesDTO(
    @Json(name = "data")
    val data: List<CustomBestieDTO>
)

@JsonClass(generateAdapter = true)
data class CustomBestieDTO(
    @Json(name = "customBestieId") val customBestieId: String = "",
    @<PERSON><PERSON>(name = "name") val name: String = "",
    @<PERSON><PERSON>(name = "description") val description: String = "",
    @<PERSON><PERSON>(name = "imageURL") val imageURL: CustomBestieOptionDTO? = null,
    @<PERSON><PERSON>(name = "pronouns") val pronouns: CustomBestieOptionDTO? = null,
    @<PERSON><PERSON>(name = "backgroundURL") val backgroundURL: String? = null,
    @<PERSON><PERSON>(name = "responseStyle") val responseStyle: CustomBestieOptionDTO? = null,
    @<PERSON><PERSON>(name = "humour") val humour: CustomBestieOptionDTO? = null,
    @<PERSON><PERSON>(name = "vibe") val vibe: List<CustomBestieOptionDTO>? = null,
    @Json(name = "freeText") val freeText: String? = null
)

@JsonClass(generateAdapter = true)
data class CustomBestieOptionDTO(
    @Json(name = "key") val key: String = "",
    @Json(name = "value") val value: String? = null,
)
