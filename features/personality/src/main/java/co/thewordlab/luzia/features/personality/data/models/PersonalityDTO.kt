package co.thewordlab.luzia.features.personality.data.models

import co.thewordlab.luzia.core.profile.domain.model.UserPronouns
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class PersonalityDTO(
    @<PERSON><PERSON>(name = "personalityId") val personalityId: String = "",
    @<PERSON><PERSON>(name = "name") val name: String = "",
    @<PERSON><PERSON>(name = "description") val description: String = "",
    @<PERSON><PERSON>(name = "thumbnailURI") val thumbnailURI: String = "",
    @<PERSON><PERSON>(name = "tag") val tag: String = "",
    @<PERSON><PERSON>(name = "tagDescription") val tagDescription: String = "",
    @<PERSON><PERSON>(name = "welcomeMessage") val welcomeMessage: String? = null,
    @<PERSON><PERSON>(name = "iceBreakers") val iceBreakers: List<String> = emptyList(),
    @<PERSON><PERSON>(name = "pronouns") val pronouns: UserPronouns? = null,
    @<PERSON><PERSON>(name = "backgroundURL") val backgroundURL: String? = null,
    @<PERSON><PERSON>(name = "responseStyles") val responseStyles: List<ResponseStyleDTO>? = null
)
