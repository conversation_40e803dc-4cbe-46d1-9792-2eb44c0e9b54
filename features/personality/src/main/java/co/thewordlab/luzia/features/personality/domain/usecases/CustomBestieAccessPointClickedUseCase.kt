package co.thewordlab.luzia.features.personality.domain.usecases

import androidx.datastore.preferences.core.intPreferencesKey
import co.thewordlab.fouundation.persistence.LuziaDataStore
import javax.inject.Inject

class CustomBestieAccessPointClickedUseCase @Inject constructor(
    private val luziaDataStore: LuziaDataStore
) {

    suspend fun getCounter(): Int =
        luziaDataStore.getData(customBestieAccessPointCount) ?: 0

    suspend fun increaseCounter() {
        val count = getCounter()
        luziaDataStore.saveData(customBestieAccessPointCount, count + 1)
    }

    private companion object {
        const val CUSTOM_BESTIE_ACCESS_POINT = "custom_bestie_access_point_count"
        val customBestieAccessPointCount = intPreferencesKey(CUSTOM_BESTIE_ACCESS_POINT)
    }
}
