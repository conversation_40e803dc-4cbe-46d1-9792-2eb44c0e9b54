package co.thewordlab.luzia.features.personality.presentation.personalityprofile

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes

fun NavGraphBuilder.personality() {
    composable<UserSessionRoutes.PersonalityProfile> { backstackEntry ->
        val data = backstackEntry.toRoute<UserSessionRoutes.PersonalityProfile>()
        PersonalityProfileScreen(
            personalityId = data.personalityId,
            isCustomBestie = data.isCustomBestie
        )
    }
}
