package co.thewordlab.luzia.features.personality.data.repository

import androidx.datastore.preferences.core.booleanPreferencesKey
import app.cash.turbine.test
import co.thewordlab.fouundation.persistence.LuziaDataStore
import co.thewordlab.fouundation.persistence.custombestie.CustomBestieDao
import co.thewordlab.fouundation.persistence.custombestie.CustomBestieEntity
import co.thewordlab.luzia.features.personality.data.api.CustomBestiesApi
import co.thewordlab.luzia.features.personality.data.models.custombestie.CreateBestieResponse
import co.thewordlab.luzia.features.personality.data.models.custombestie.CustomBestieDTO
import co.thewordlab.luzia.features.personality.data.models.custombestie.CustomBestiesDTO
import co.thewordlab.luzia.features.personality.domain.models.custombestie.CustomBestieErrors
import co.thewordlab.luzia.features.personality.domain.models.custombestie.CustomBestieModel
import co.thewordlab.luzia.foundation.networking.model.EmptyResponse
import co.thewordlab.luzia.foundation.networking.model.ErrorDetailDto
import co.thewordlab.luzia.foundation.networking.model.ErrorDto
import co.thewordlab.luzia.foundation.networking.model.ExtraDataDto
import co.thewordlab.luzia.foundation.networking.model.ExtraErrorDetailDto
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import com.slack.eithernet.ApiResult
import com.squareup.moshi.Moshi
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import java.io.IOException

class CustomBestieRepositoryImpTest {

    private val customBestieApi: CustomBestiesApi = mockk(relaxed = true)
    private val customBestieDao: CustomBestieDao = mockk(relaxed = true)
    private val luziaDataStore: LuziaDataStore = mockk(relaxed = true)
    private val moshi = Moshi.Builder().build()

    private val repository = CustomBestieRepositoryImp(
        customBestieApi = customBestieApi,
        customBestieDao = customBestieDao,
        luziaDataStore = luziaDataStore,
        moshi = moshi
    )

    @Before
    fun setup() {
        every { customBestieDao.getAllCustomBesties() } returns flowOf(listOf(testCustomBestieEntity))
        every { customBestieDao.getCustomBestie(BESTIE_ID) } returns flowOf(testCustomBestieEntity)

        coEvery { customBestieApi.getCustomBesties() } returns ApiResult.success(
            CustomBestiesDTO(data = listOf(CustomBestieDTO(customBestieId = BESTIE_ID)))
        )

        coEvery {
            customBestieApi.getCustomBestie(BESTIE_ID)
        } returns ApiResult.success(testCustomBestieDTO)
    }

    @Test
    fun `getAllCustomBesties should return flow of custom besties`() = runTest {
        repository.getAllCustomBesties().test {
            val items = awaitItem()
            assertEquals(1, items.size)
            assertEquals(BESTIE_ID, items[0].id)
            assertEquals(NAME, items[0].name)
            assertEquals(DESCRIPTION, items[0].description)
            cancelAndIgnoreRemainingEvents()
        }
    }

    @Test
    fun `getAllCustomBesties should return empty when api returns empty`() = runTest {
        coEvery { customBestieApi.getCustomBesties() } returns ApiResult.success(
            CustomBestiesDTO(data = emptyList())
        )
        repository.getAllCustomBesties().test {
            val items = awaitItem()
            assertEquals(0, items.size)
            cancelAndIgnoreRemainingEvents()
        }
    }

    @Test
    fun `getAllCustomBesties should return empty when api returns failure`() = runTest {
        coEvery { customBestieApi.getCustomBesties() } returns ApiResult.networkFailure(
            IOException("Network error")
        )
        repository.getAllCustomBesties().test {
            val items = awaitItem()
            assertEquals(0, items.size)
            cancelAndIgnoreRemainingEvents()
        }
    }

    @Test
    fun `getCustomBestie should return flow of custom bestie`() = runTest {
        repository.getCustomBestie(BESTIE_ID).test {
            val item = awaitItem()
            assertEquals(BESTIE_ID, item?.id)
            assertEquals(NAME, item?.name)
            assertEquals(DESCRIPTION, item?.description)
            cancelAndIgnoreRemainingEvents()
        }

        coVerify { customBestieApi.getCustomBestie(BESTIE_ID) }
    }

    @Test
    fun `isCustomBestieProfileShown should return flow from datastore`() = runTest {
        val testKey = booleanPreferencesKey("custom_bestie_profile_shown")
        every { luziaDataStore.getDataFlow(testKey) } returns flowOf(true)

        repository.isCustomBestieProfileShown().test {
            val result = awaitItem()
            assertEquals(true, result)
            awaitComplete()
        }
    }

    @Test
    fun `markCustomBestieProfileAsShown should save to datastore`() = runTest {
        coEvery { luziaDataStore.saveData(any(), any()) } returns Unit

        repository.markCustomBestieProfileAsShown()

        coVerify { luziaDataStore.saveData(any(), true) }
    }

    @Test
    fun `createCustomBestie should return success with id when api call succeeds`() = runTest {
        coEvery {
            customBestieApi.createCustomBestie(any())
        } returns ApiResult.success(CreateBestieResponse(customBestieId = BESTIE_ID))

        val result = repository.createCustomBestie(testCustomBestieModel)

        assertTrue(result is ResultOf.Success)
        assertEquals(BESTIE_ID, (result as ResultOf.Success).data)
        coVerify { customBestieApi.createCustomBestie(any()) }
        coVerify { customBestieApi.getCustomBesties() }
    }

    @Test
    fun `createCustomBestie should return failure when api call fails`() = runTest {
        val errorDetail =
            ErrorDetailDto(code = "429", message = "Too Many Requests", extraData = null)
        coEvery {
            customBestieApi.createCustomBestie(any())
        } returns ApiResult.httpFailure(429, ErrorDto(error = errorDetail))

        val result = repository.createCustomBestie(testCustomBestieModel)

        assertTrue(result is ResultOf.Failure)
        assertTrue((result as ResultOf.Failure).error is CustomBestieErrors.ActionNotAllowed)
    }

    @Test
    fun `updateCustomBestie should return success when api call succeeds`() = runTest {
        coEvery {
            customBestieApi.updateCustomBestie(any())
        } returns ApiResult.success(EmptyResponse())

        val result = repository.updateCustomBestie(testCustomBestieModel)

        assertTrue(result is ResultOf.Success)
        coVerify { customBestieApi.updateCustomBestie(any()) }
        coVerify { customBestieApi.getCustomBestie(BESTIE_ID) }
    }

    @Test
    fun `updateCustomBestie should return failure when api call fails`() = runTest {
        val errorDetail =
            ErrorDetailDto(code = "429", message = "Too Many Requests", extraData = null)
        coEvery {
            customBestieApi.updateCustomBestie(any())
        } returns ApiResult.httpFailure(429, ErrorDto(error = errorDetail))

        val result = repository.updateCustomBestie(testCustomBestieModel)

        assertTrue(result is ResultOf.Failure)
        assertTrue((result as ResultOf.Failure).error is CustomBestieErrors.ActionNotAllowed)
    }

    @Test
    fun `updateCustomBestie should return failure when bestie not found`() = runTest {
        every { customBestieDao.getCustomBestie(BESTIE_ID) } returns flowOf(null)

        val result = repository.updateCustomBestie(testCustomBestieModel)

        assertTrue(result is ResultOf.Failure)
        assertTrue((result as ResultOf.Failure).error is CustomBestieErrors.EntryNotFound)
    }

    @Test
    fun `clearSession should call dao clear`() = runTest {
        coEvery { customBestieDao.clear() } returns Unit

        repository.clearSession()

        coVerify { customBestieDao.clear() }
    }

    @Test
    fun `getCustomBestie should handle null entity`() = runTest {
        every { customBestieDao.getCustomBestie(BESTIE_ID) } returns flowOf(null)

        repository.getCustomBestie(BESTIE_ID).test {
            val item = awaitItem()
            assertNull(item)
            awaitComplete()
        }
    }

    @Test
    fun `createCustomBestie should handle validation errors`() = runTest {
        val extraData = ExtraDataDto(
            dataErrors = listOf(
                ExtraErrorDetailDto(key = "name", message = "Name is required", status = 400)
            )
        )
        val errorDetail = ErrorDetailDto(
            code = "400",
            message = "Bad Request",
            extraData = extraData
        )
        val errorDto = ErrorDto(error = errorDetail)

        coEvery {
            customBestieApi.createCustomBestie(any())
        } returns ApiResult.httpFailure(400, errorDto)

        val result = repository.createCustomBestie(testCustomBestieModel)

        assertTrue(result is ResultOf.Failure)
        assertTrue((result as ResultOf.Failure).error is CustomBestieErrors.InvalidCustomBestie)
    }

    @Test
    fun `updateCustomBestie should handle validation errors`() = runTest {
        val extraData = ExtraDataDto(
            dataErrors = listOf(
                ExtraErrorDetailDto(key = "name", message = "Name is required", status = 400)
            )
        )
        val errorDetail = ErrorDetailDto(
            code = "400",
            message = "Bad Request",
            extraData = extraData
        )
        val errorDto = ErrorDto(error = errorDetail)

        coEvery {
            customBestieApi.updateCustomBestie(any())
        } returns ApiResult.httpFailure(400, errorDto)

        val result = repository.updateCustomBestie(testCustomBestieModel)

        assertTrue(result is ResultOf.Failure)
        assertTrue((result as ResultOf.Failure).error is CustomBestieErrors.InvalidCustomBestie)
    }

    @Test
    fun `createCustomBestie should handle network errors`() = runTest {
        coEvery {
            customBestieApi.createCustomBestie(any())
        } returns ApiResult.networkFailure(IOException("Network error"))

        val result = repository.createCustomBestie(testCustomBestieModel)

        assertTrue(result is ResultOf.Failure)
        assertTrue((result as ResultOf.Failure).error is CustomBestieErrors.CommonError)
    }

    @Test
    fun `updateCustomBestie should handle network errors`() = runTest {
        coEvery {
            customBestieApi.updateCustomBestie(any())
        } returns ApiResult.networkFailure(IOException("Network error"))

        val result = repository.updateCustomBestie(testCustomBestieModel)

        assertTrue(result is ResultOf.Failure)
        assertTrue((result as ResultOf.Failure).error is CustomBestieErrors.CommonError)
    }

    private companion object {
        const val BESTIE_ID = "test-id"
        const val NAME = "Test Bestie"
        const val DESCRIPTION = "Test Description"

        private val testCustomBestieEntity = CustomBestieEntity(
            customBestieId = BESTIE_ID,
            name = NAME,
            description = DESCRIPTION,
            order = 0
        )

        private val testCustomBestieDTO = CustomBestieDTO(
            customBestieId = BESTIE_ID,
            name = NAME,
            description = DESCRIPTION
        )

        private val testCustomBestieModel = CustomBestieModel(
            id = BESTIE_ID,
            name = NAME,
            description = DESCRIPTION
        )
    }
}
