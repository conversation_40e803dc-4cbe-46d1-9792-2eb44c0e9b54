package co.thewordlab.luzia.features.personality.domain.usecases

import co.thewordlab.luzia.core.gamification.domain.GamificationRepository
import co.thewordlab.luzia.core.gamification.domain.model.PendingReward
import co.thewordlab.luzia.dynamicflow.domain.models.DynamicFlowsErrors
import co.thewordlab.luzia.dynamicflow.domain.repository.DynamicFlowsRepository
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.foundation.networking.model.CommonErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.session.UserSession
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import co.thewordlab.luzia.foundation.networking.session.UserType
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

class IsCustomBestieFlowAvailableUseCaseTest {

    private val dynamicFlowRepository: DynamicFlowsRepository = mockk()
    private val gamificationRepository: GamificationRepository = mockk()
    private val featureFlagManager: FeatureFlagManager = mockk()
    private val userSessionManager: UserSessionManager = mockk()

    private val useCase = IsCustomBestieFlowAvailableUseCase(
        dynamicFlowRepository = dynamicFlowRepository,
        gamificationRepository = gamificationRepository,
        featureFlagManager = featureFlagManager,
        userSessionManager = userSessionManager
    )

    @Before
    fun setup() {
        every { userSessionManager.getUserSessionImmediate() } returns SESSION_FULL_USER
    }

    @Test
    fun `invoke returns true when gamification is enabled with custom-bestie prefix`() = runTest {
        every {
            gamificationRepository.getPendingReward("custom-besties-1")
        } returns flowOf(pendingReward)
        coEvery { featureFlagManager.get<Boolean>(FeatureFlag.GamificationEnabled) } returns true
        coEvery { dynamicFlowRepository.getAvailableFlows() } returns ResultOf.Success(
            listOf("custom-besties", "custom-besties-a", "other-flow")
        )

        val result = useCase().first()

        assertTrue(result)
    }

    @Test
    fun `invoke returns true when gamification is disabled  with custom-bestie prefix`() = runTest {
        every {
            gamificationRepository.getPendingReward("custom-besties-1")
        } returns flowOf(pendingReward)
        coEvery { featureFlagManager.get<Boolean>(FeatureFlag.GamificationEnabled) } returns false
        coEvery { dynamicFlowRepository.getAvailableFlows() } returns ResultOf.Success(
            listOf("custom-besties", "custom-besties-a", "other-flow")
        )

        val result = useCase().first()

        assertTrue(result)
    }

    @Test
    fun `invoke returns true when gamification is enabled, flow is available`() = runTest {
        every {
            gamificationRepository.getPendingReward("custom-besties-1")
        } returns flowOf(pendingReward)
        coEvery { featureFlagManager.get<Boolean>(FeatureFlag.GamificationEnabled) } returns true
        coEvery { dynamicFlowRepository.getAvailableFlows() } returns ResultOf.Success(
            listOf("custom-besties", "custom-besties-a", "other-flow")
        )

        val result = useCase().first()

        assertTrue(result)
    }

    @Test
    fun `invoke returns false when flow is not available with custom-bestie prefix`() = runTest {
        every {
            gamificationRepository.getPendingReward("custom-besties-1")
        } returns flowOf(pendingReward)
        coEvery { featureFlagManager.get<Boolean>(FeatureFlag.GamificationEnabled) } returns true
        coEvery { dynamicFlowRepository.getAvailableFlows() } returns ResultOf.Success(
            listOf("other-flow-1", "other-flow-2")
        )

        val result = useCase().first()

        assertFalse(result)
    }

    @Test
    fun `invoke returns false when flow availability check fails`() = runTest {
        every {
            gamificationRepository.getPendingReward("custom-besties-1")
        } returns flowOf(pendingReward)
        coEvery { featureFlagManager.get<Boolean>(FeatureFlag.GamificationEnabled) } returns true
        coEvery { dynamicFlowRepository.getAvailableFlows() } returns ResultOf.Failure(
            DynamicFlowsErrors.CommonError(CommonErrors.NetworkError)
        )

        val result = useCase().first()

        assertFalse(result)
    }

    @Test
    fun `invoke returns false when flow list contains null values`() = runTest {
        every {
            gamificationRepository.getPendingReward("custom-besties-1")
        } returns flowOf(pendingReward)
        coEvery { featureFlagManager.get<Boolean>(FeatureFlag.GamificationEnabled) } returns true
        coEvery { dynamicFlowRepository.getAvailableFlows() } returns ResultOf.Success(
            listOf(null, "other-flow")
        )

        val result = useCase().first()

        assertFalse(result)
    }

    @Test
    fun `invoke returns false when session is for guest user`() = runTest {
        every { userSessionManager.getUserSessionImmediate() } returns SESSION_GUEST_USER
        every {
            gamificationRepository.getPendingReward("custom-besties-1")
        } returns flowOf(pendingReward)
        val result = useCase().first()
        assertFalse(result)
    }

    private companion object {
        val SESSION_FULL_USER = UserSession(
            accessToken = "",
            refreshToken = "",
            deviceKey = "",
            sessionId = "",
            masterUserId = "",
            userType = UserType.FULL_USER
        )
        val SESSION_GUEST_USER = UserSession(
            accessToken = "",
            refreshToken = "",
            deviceKey = "",
            sessionId = "",
            masterUserId = "",
            userType = UserType.GUEST
        )
        val pendingReward = PendingReward(
            id = "custom-besties-1",
            level = 1,
            earnedPoints = 100,
            isClaimed = true
        )
    }
}
