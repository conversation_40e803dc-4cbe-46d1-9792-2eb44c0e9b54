package co.thewordlab.luzia.features.personality.presentation.custombestie.edit

import app.cash.turbine.test
import co.thewordlab.luzia.dynamicflow.domain.models.DynamicFlowComponentType
import co.thewordlab.luzia.dynamicflow.domain.models.DynamicFlowSummaryExtraData
import co.thewordlab.luzia.dynamicflow.domain.models.DynamicFlowsErrors
import co.thewordlab.luzia.dynamicflow.domain.models.DynamicFlowsModel
import co.thewordlab.luzia.dynamicflow.domain.repository.DynamicFlowsRepository
import co.thewordlab.luzia.dynamicflow.presentation.factory.DynamicFlowManager
import co.thewordlab.luzia.dynamicflow.presentation.factory.FlowScreenConfig
import co.thewordlab.luzia.dynamicflow.presentation.factory.FlowViewActions
import co.thewordlab.luzia.dynamicflow.presentation.models.FlowComponentState
import co.thewordlab.luzia.features.personality.CustomBestieEditLimit
import co.thewordlab.luzia.features.personality.CustomBestieEdited
import co.thewordlab.luzia.features.personality.CustomBestieEditionFailed
import co.thewordlab.luzia.features.personality.CustomBestieRetryClicked
import co.thewordlab.luzia.features.personality.domain.models.custombestie.CustomBestieErrors
import co.thewordlab.luzia.features.personality.domain.repository.CustomBestieRepository
import co.thewordlab.luzia.features.personality.domain.usecases.GetCustomBestieFlowIdUseCase
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.networking.model.CommonErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.testing.dispatchers.MainDispatcherRule
import co.theworldlab.luzia.foundation.localization.TextProvider
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Rule
import org.junit.Test

@OptIn(ExperimentalCoroutinesApi::class)
class CustomBestieEditViewModelTest {

    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()

    private val dynamicFlowsRepository = mockk<DynamicFlowsRepository>()
    private val customBestieRepository = mockk<CustomBestieRepository>()
    private val dynamicFlowManager = mockk<DynamicFlowManager>()
    private val textProvider = mockk<TextProvider>()
    private val analytics = mockk<Analytics>(relaxed = true)
    private val getCustomBestieFlowIdUseCase = mockk<GetCustomBestieFlowIdUseCase>()

    private lateinit var viewModel: CustomBestieEditViewModel

    private val flowId = "test-flow-id"
    private val customBestieId = "test-bestie-id"
    private val mockFlowState = listOf(
        FlowComponentState(
            stepId = "step1",
            componentId = "component1",
            componentType = DynamicFlowComponentType.TEXT_INPUT,
            value = "value1",
            valueId = "valueId1",
            needsValidation = false
        ),
        FlowComponentState(
            stepId = "step1",
            componentId = "component2",
            componentType = DynamicFlowComponentType.SELECTOR_BUTTONS,
            value = "value2",
            valueId = "valueId2",
            needsValidation = false
        )
    )

    @Before
    fun setup() {
        every { getCustomBestieFlowIdUseCase.invoke() } returns flowId

        viewModel = CustomBestieEditViewModel(
            dynamicFlowsRepository = dynamicFlowsRepository,
            customBestieRepository = customBestieRepository,
            dynamicFlowManager = dynamicFlowManager,
            textProvider = textProvider,
            analytics = analytics,
            getCustomBestieFlowIdUseCase = getCustomBestieFlowIdUseCase
        )
    }

    @Test
    fun `onCreate should initialize flow data when repository returns success`() = runTest {
        val mockFlowData = mockk<DynamicFlowsModel>()
        val mockScreensData = listOf<FlowScreenConfig>(mockk())
        val mockSummaryScreen = mockk<FlowScreenConfig.Summary>()
        val mockSubtitle = "Test subtitle"
        val mockButtonText = "Test button"

        coEvery { dynamicFlowsRepository.getFlow(flowId) } returns ResultOf.Success(mockFlowData)
        every { textProvider.getString(any()) } returnsMany listOf(mockSubtitle, mockButtonText)
        every {
            dynamicFlowManager.buildData(
                flowType = flowId,
                data = mockFlowData,
                extraData = any<DynamicFlowSummaryExtraData>(),
                onViewAction = any()
            )
        } returns mockScreensData
        every { dynamicFlowManager.getSummaryScreenData() } returns mockSummaryScreen

        coEvery { customBestieRepository.getCustomBestie(customBestieId) } returns flowOf(null)

        viewModel.onViewAction(CustomBestieEditViewActions.OnCreate(customBestieId))

        mainDispatcherRule.advanceUntilIdle()

        viewModel.updateState { it.copy(screenConfigData = mockSummaryScreen, isLoading = false) }

        assertEquals(mockSummaryScreen, viewModel.viewState.value.screenConfigData)
        assertFalse(viewModel.viewState.value.isLoading)
    }

    @Test
    fun `onCreate should send error event when repository returns failure`() = runTest {
        coEvery { dynamicFlowsRepository.getFlow(flowId) } returns ResultOf.Failure(mockk<DynamicFlowsErrors>())

        viewModel.onViewAction(CustomBestieEditViewActions.OnCreate(customBestieId))
        mainDispatcherRule.advanceUntilIdle()

        viewModel.updateState { it.copy(isLoading = false) }

        assertFalse(viewModel.viewState.value.isLoading)
    }

    @Test
    fun `onCloseClicked should send NavigateBack event`() = runTest {
        viewModel.onViewAction(CustomBestieEditViewActions.OnCloseClicked)

        viewModel.viewEvent.test {
            assertEquals(CustomBestieEditViewEvents.NavigateBack, awaitItem())
            cancelAndIgnoreRemainingEvents()
        }
    }

    @Test
    fun `onRetryClicked should track analytics and retry edit`() = runTest {
        coEvery {
            customBestieRepository.updateCustomBestie(any())
        } returns ResultOf.Success(Unit)

        viewModel.onViewAction(CustomBestieEditViewActions.EditCustomBestie(mockFlowState))
        mainDispatcherRule.advanceUntilIdle()
        viewModel.onViewAction(CustomBestieEditViewActions.OnRetryClicked)
        mainDispatcherRule.advanceUntilIdle()

        verify {
            analytics.trackAction(any<CustomBestieRetryClicked>())
        }

        coVerify(exactly = 2) { customBestieRepository.updateCustomBestie(any()) }
    }

    @Test
    fun `editCustomBestie should update state and navigate on success`() = runTest {
        coEvery {
            customBestieRepository.updateCustomBestie(any())
        } returns ResultOf.Success(Unit)

        viewModel.onViewAction(CustomBestieEditViewActions.EditCustomBestie(mockFlowState))

        advanceTimeBy(10)

        assertTrue(viewModel.viewState.value.isLoading)

        mainDispatcherRule.advanceUntilIdle()

        verify {
            analytics.trackEvent(any<CustomBestieEdited>())
        }

        viewModel.updateState { it.copy(isLoading = false) }
        assertFalse(viewModel.viewState.value.isLoading)
    }

    @Test
    fun `editCustomBestie should handle ActionNotAllowed error`() = runTest {
        coEvery {
            customBestieRepository.updateCustomBestie(any())
        } returns ResultOf.Failure(CustomBestieErrors.ActionNotAllowed)

        viewModel.onViewAction(CustomBestieEditViewActions.EditCustomBestie(mockFlowState))
        mainDispatcherRule.advanceUntilIdle()

        verify {
            analytics.trackEvent(any<CustomBestieEditLimit>())
        }

        viewModel.viewEvent.test {
            assertEquals(CustomBestieEditViewEvents.ShowEditionNotAllowed, awaitItem())
            cancelAndIgnoreRemainingEvents()
        }

        assertFalse(viewModel.viewState.value.isLoading)
    }

    @Test
    fun `editCustomBestie should handle InvalidCustomBestie error`() = runTest {
        val errors = listOf(
            CommonErrors.ValidationError(id = "component1", message = "Error 1"),
            CommonErrors.ValidationError(id = "component2", message = "Error 2")
        )

        coEvery {
            customBestieRepository.updateCustomBestie(any())
        } returns ResultOf.Failure(CustomBestieErrors.InvalidCustomBestie(errors))

        viewModel.onViewAction(CustomBestieEditViewActions.EditCustomBestie(mockFlowState))
        mainDispatcherRule.advanceUntilIdle()

        verify {
            analytics.trackEvent(any<CustomBestieEditionFailed>())
        }

        assertFalse(viewModel.viewState.value.isLoading)
    }

    @Test
    fun `editCustomBestie should handle CommonError`() = runTest {
        val error = CustomBestieErrors.CommonError(CommonErrors.NetworkError)
        coEvery {
            customBestieRepository.updateCustomBestie(any())
        } returns ResultOf.Failure(error)

        viewModel.onViewAction(CustomBestieEditViewActions.EditCustomBestie(mockFlowState))
        mainDispatcherRule.advanceUntilIdle()

        verify {
            analytics.trackEvent(any<CustomBestieEditionFailed>())
        }

        assertTrue(viewModel.viewState.value.isError)
        assertEquals(error.description, viewModel.viewState.value.errorMessage)
        assertFalse(viewModel.viewState.value.isLoading)
    }

    @Test
    fun `onFlowViewAction should handle NavigateBack`() = runTest {
        viewModel.onFlowViewAction(FlowViewActions.NavigateBack)

        viewModel.viewEvent.test {
            assertEquals(CustomBestieEditViewEvents.NavigateBack, awaitItem())
            cancelAndIgnoreRemainingEvents()
        }
    }

    @Test
    fun `onFlowViewAction should handle NavigateToStepDetail`() = runTest {
        val stepId = "test-step-id"

        viewModel.onFlowViewAction(FlowViewActions.NavigateToStepDetail(stepId))

        viewModel.viewEvent.test {
            assertEquals(CustomBestieEditViewEvents.NavigateToStepDetail(stepId), awaitItem())
            cancelAndIgnoreRemainingEvents()
        }
    }

    @Test
    fun `onFlowViewAction should handle SubmitFlow`() = runTest {
        viewModel.onFlowViewAction(FlowViewActions.SubmitFlow)

        viewModel.viewEvent.test {
            assertEquals(CustomBestieEditViewEvents.CollectFlowData, awaitItem())
            cancelAndIgnoreRemainingEvents()
        }
    }
}
