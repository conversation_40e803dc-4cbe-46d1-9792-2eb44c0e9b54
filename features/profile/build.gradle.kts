plugins {
    alias(libs.plugins.luzia.android.feature)
}

android {
    namespace = "co.thewordlab.luzia.features.profile"
}

dependencies {
    implementation(libs.androidx.compose.constraintlayout)
    implementation(projects.core.bestiepoints)
    implementation(projects.core.notifications)
    implementation(projects.core.profile)
    implementation(projects.core.signup)
    implementation(projects.core.sharing)
    implementation(projects.features.shortcuts)
    implementation(projects.features.proactiveMessaging)
    implementation(projects.foundation.analytics)
    implementation(projects.foundation.common)
    implementation(projects.foundation.files)
    implementation(projects.foundation.networking)
    implementation(projects.foundation.persistence)
    implementation(projects.core.navigation)

    testImplementation(projects.foundation.testing)
}
