package co.thewordlab.luzia.features.profile.presentation.profilefill.permissions

import androidx.lifecycle.ViewModel
import co.thewordlab.luzia.features.profile.presentation.ProfileWizardV2NotificationAccepted
import co.thewordlab.luzia.features.profile.presentation.ProfileWizardV2NotificationDismiss
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class ProfileNotificationPermissionViewModel @Inject constructor(
    private val analytics: Analytics
) :
    ViewModel(),
    ViewModelActions<ProfileNotificationPermissionViewActions>,
    ViewModelEvents<ProfileNotificationPermissionViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<ProfileNotificationPermissionViewState> by ViewModelStatesImpl(
        ProfileNotificationPermissionViewState()
    ) {

    private var permissionRequestedFromButton = false

    override fun onViewAction(action: ProfileNotificationPermissionViewActions) {
        when (action) {
            ProfileNotificationPermissionViewActions.OnAllow -> {
                if (permissionRequestedFromButton) {
                    analytics.trackAction(ProfileWizardV2NotificationAccepted)
                    sendEvent(ProfileNotificationPermissionViewEvents.Dismiss)
                }
            }

            ProfileNotificationPermissionViewActions.OnDismiss -> {
                if (permissionRequestedFromButton) {
                    analytics.trackAction(ProfileWizardV2NotificationDismiss)
                    sendEvent(ProfileNotificationPermissionViewEvents.Dismiss)
                }
            }

            ProfileNotificationPermissionViewActions.OnCreate -> {
                presentButtonState()
            }
        }
    }

    private fun presentButtonState() {
        updateState {
            it.copy(
                buttonState = it.buttonState.copy(
                    isEnabled = true,
                    isLoading = false,
                    onClick = {
                        permissionRequestedFromButton = true
                        sendEvent(ProfileNotificationPermissionViewEvents.AskPermission)
                    }
                )
            )
        }
    }
}
