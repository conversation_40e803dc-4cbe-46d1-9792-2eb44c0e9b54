package co.thewordlab.luzia.features.profile.presentation.profile

import androidx.annotation.StringRes
import co.thewordlab.luzia.core.profile.presentation.ProfilePrompt
import co.thewordlab.luzia.features.profile.presentation.profilefill.pronouns.models.PronounsUiModel
import co.thewordlab.luzia.foundation.architecture.system.ViewState
import co.theworldlab.luzia.foundation.design.system.components.profile.AvatarState
import co.thewordlab.luzia.foundation.localization.R as localizationR

data class ProfileScreenViewState(
    val displayName: String = "",
    val avatarState: AvatarState? = null,
    val bannerImageUrl: String = "",
    val basicInfo: BasicInformation? = null,
    val school: SchoolInformation? = null,
    val socialMetrics: SocialMetricsInformation? = null,
    val ctaButton: CTAButtonType? = null,
    val prompt: ProfilePrompt? = null,
    val isExternalUser: Boolean = false,
    val ctaLoading: Boolean = false,
    val countryName: String? = null,
    val hideProfileReason: HideProfileReason? = null
) : ViewState

enum class HideProfileReason {
    BLOCKED_BY_USER,
    PRIVATE_PROFILE
}

enum class CTAButtonType(@StringRes val textRes: Int) {
    EDIT(localizationR.string.common_edit),
    MESSAGE(localizationR.string.input_chat_placeholder)
}

data class SocialMetricsInformation(
    val bestiePoints: Int,
    val totalFriends: Int
)

data class SchoolInformation(
    val name: String?,
    val isStudent: Boolean?
)

data class BasicInformation(
    val username: String,
    val pronouns: PronounsUiModel?
)
