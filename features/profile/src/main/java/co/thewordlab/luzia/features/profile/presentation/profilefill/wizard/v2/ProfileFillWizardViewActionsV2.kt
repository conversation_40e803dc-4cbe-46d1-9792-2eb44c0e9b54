package co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.v2

import co.thewordlab.luzia.core.navigation.usersession.model.ProfileFillPolicy
import co.thewordlab.luzia.foundation.analytics.AnalyticsScreens
import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class ProfileFillWizardViewActionsV2 : ViewAction {
    data class OnCreate(val policy: ProfileFillPolicy) : ProfileFillWizardViewActionsV2()
    data object OnNextClicked : ProfileFillWizardViewActionsV2()
    data object OnBackClicked : ProfileFillWizardViewActionsV2()
    data class OnScreenLoaded(val screen: AnalyticsScreens) : ProfileFillWizardViewActionsV2()
    data object OnSkipClicked : ProfileFillWizardViewActionsV2()
}
