package co.thewordlab.luzia.features.profile.presentation.edit

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreen
import co.thewordlab.luzia.core.profile.data.api.model.UserProfileUpdateRequest
import co.thewordlab.luzia.core.profile.domain.GetUserProfileUseCase
import co.thewordlab.luzia.core.profile.domain.ProfileRepository
import co.thewordlab.luzia.core.profile.domain.model.UserProfile
import co.thewordlab.luzia.features.profile.presentation.ProfileEdition
import co.thewordlab.luzia.features.profile.presentation.profilefill.background.backgrounds
import co.thewordlab.luzia.features.profile.presentation.profilefill.privacy.ProfilePrivacyType
import co.thewordlab.luzia.features.profile.presentation.profilefill.privacy.mapToDomain
import co.thewordlab.luzia.features.profile.presentation.profilefill.privacy.mapToModel
import co.thewordlab.luzia.features.profile.presentation.profilefill.pronouns.models.parsePronouns
import co.thewordlab.luzia.features.profile.presentation.profilefill.student.StudentUiModel
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.common.date.DateFormatter
import co.thewordlab.luzia.foundation.common.date.DatePattern
import co.thewordlab.luzia.foundation.common.extensions.capitalizeWords
import co.theworldlab.luzia.foundation.localization.TextProvider
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ProfileEditViewModel @Inject constructor(
    private val profileRepository: ProfileRepository,
    private val getUserProfileUseCase: GetUserProfileUseCase,
    private val dateFormatter: DateFormatter,
    private val textProvider: TextProvider,
    private val analytics: Analytics
) : ViewModel(),
    ViewModelActions<ProfileEditViewActions>,
    ViewModelEvents<ProfileEditViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<ProfileEditViewState> by ViewModelStatesImpl(ProfileEditViewState()) {

    override fun onViewAction(action: ProfileEditViewActions) {
        when (action) {
            is ProfileEditViewActions.OnCreate -> {
                analytics.logScreen(ProfileEdition)
                listenToProfile()
            }

            ProfileEditViewActions.OnDismiss -> {
                sendEvent(ProfileEditViewEvents.Dismiss)
            }

            is ProfileEditViewActions.OnItemClicked -> {
                sendEvent(ProfileEditViewEvents.OpenScreen(action.item.wizardScreen))
            }

            ProfileEditViewActions.OnChangeBannerImage -> {
                sendEvent(ProfileEditViewEvents.OpenScreen(WizardScreen.PROFILE_BACKGROUND))
            }

            ProfileEditViewActions.OnChangeProfileImage ->
                sendEvent(ProfileEditViewEvents.OpenScreen(WizardScreen.PROFILE_IMAGE))

            is ProfileEditViewActions.OnPrivacySelected -> updatePrivacyState(action.option)
        }
    }

    private fun listenToProfile() {
        getUserProfileUseCase()
            .filterNotNull()
            .onEach(::updateProfile)
            .launchIn(viewModelScope)
    }

    private fun updateProfile(userProfile: UserProfile) {
        val birthDate = dateFormatter.formatServerDate(userProfile.birthdate, DatePattern.SLASHES)
        updateState {
            it.copy(
                displayName = userProfile.displayName.orEmpty(),
                avatarState = userProfile.avatarState,
                bannerImageUrl = userProfile.backgroundURL ?: backgrounds[0],
                itemsBasic = mutableListOf(
                    ProfileEditItem(WizardScreen.NAME, userProfile.displayName),
                    ProfileEditItem(WizardScreen.USERNAME, userProfile.username),
                    ProfileEditItem(WizardScreen.PRONOUN, userProfile.getPronoun()),
                    ProfileEditItem(WizardScreen.BIRTHDAY, birthDate)
                ),
                itemsEducation = buildProfileEducationOptions(userProfile),
                privacyType = userProfile.privacy?.mapToModel()
            )
        }
    }

    private fun buildProfileEducationOptions(
        userProfile: UserProfile
    ): List<ProfileEditItem> {
        val items = mutableListOf<ProfileEditItem>()
        val isStudent = userProfile.isStudent ?: false
        items.add(ProfileEditItem(WizardScreen.STUDENT, isStudent.getStudentLabel()))
        if (isStudent) {
            items.add(
                ProfileEditItem(
                    WizardScreen.SCHOOL,
                    userProfile.schoolName?.capitalizeWords()
                )
            )
        }

        return items
    }

    private fun UserProfile.getPronoun(): String? {
        val pronoun = parsePronouns(pronouns)
        return pronoun?.let { textProvider.getString(it.label) }
    }

    private fun Boolean?.getStudentLabel(): String? =
        when (this) {
            true -> textProvider.getString(StudentUiModel.Student.label)
            false -> textProvider.getString(StudentUiModel.NotStudent.label)
            else -> null
        }

    private fun updatePrivacyState(type: ProfilePrivacyType) {
        updateState { it.copy(privacyType = type) }
        viewModelScope.launch {
            profileRepository.updateUserProfile(UserProfileUpdateRequest(privacy = type.mapToDomain()))
        }
    }
}
