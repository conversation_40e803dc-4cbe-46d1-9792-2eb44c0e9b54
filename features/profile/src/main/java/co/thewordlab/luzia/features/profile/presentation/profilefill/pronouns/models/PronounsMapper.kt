package co.thewordlab.luzia.features.profile.presentation.profilefill.pronouns.models

import co.thewordlab.luzia.core.profile.domain.model.UserPronouns

fun parsePronouns(pronoun: UserPronouns?) = when (pronoun) {
    UserPronouns.HE_HIM -> PronounsUiModel.HeHim
    UserPronouns.SHE_HER -> PronounsUiModel.SheHer
    UserPronouns.THEY_THEM -> PronounsUiModel.TheyThem
    UserPronouns.OTHER -> PronounsUiModel.Other
    else -> null
}

fun mapToDomain(pronoun: PronounsUiModel) = when (pronoun) {
    PronounsUiModel.HeHim -> UserPronouns.HE_HIM
    PronounsUiModel.SheHer -> UserPronouns.SHE_HER
    PronounsUiModel.TheyThem -> UserPronouns.THEY_THEM
    PronounsUiModel.Other -> UserPronouns.OTHER
}
