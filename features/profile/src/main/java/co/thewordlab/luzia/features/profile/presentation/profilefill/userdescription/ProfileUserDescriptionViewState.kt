package co.thewordlab.luzia.features.profile.presentation.profilefill.userdescription

import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.SubmitButtonState
import co.thewordlab.luzia.foundation.architecture.system.ViewState

data class ProfileUserDescriptionViewState(
    val prompt: String = "",
    val inputNotValid: Boolean = false,
    val buttonState: SubmitButtonState = SubmitButtonState()
) : ViewState
