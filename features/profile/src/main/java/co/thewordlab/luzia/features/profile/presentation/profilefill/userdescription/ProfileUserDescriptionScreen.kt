package co.thewordlab.luzia.features.profile.presentation.profilefill.userdescription

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreenV2
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.v2.LocalSubmitButtonContainerV2
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.lds.textinput.LuziaTextInput
import co.theworldlab.luzia.foundation.design.system.components.lds.textinput.TextInputState
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

private const val MAX_CHARACTERS = 100
private val MIN_HEIGHT_FOR_EXTENDED_INPUT = 200.dp
private const val MAX_LINES_FOR_SINGLE_INPUT = 10

@Composable
fun ProfileUserDescriptionScreen() {
    val viewModel: ProfileUserDescriptionViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val submitButtonState = LocalSubmitButtonContainerV2.current
    LaunchedEffect(viewState.buttonState) {
        submitButtonState.bindButtonStates(
            wizardScreen = WizardScreenV2.SELF_DESCRIPTION,
            buttonState = viewState.buttonState.copy(
                text = localizationR.string.button_continue
            )
        )
    }
    OnCreate("ProfileUserDescriptionScreen") {
        viewModel.onViewAction(ProfileUserDescriptionViewActions.OnCreate)
    }
    ViewModelEventEffect(viewModel) {
        when (it) {
            ProfileUserDescriptionViewEvents.NavigateBack -> submitButtonState.submitScreen()
        }
    }
    ProfileUserDescriptionContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@Composable
private fun ProfileUserDescriptionContent(
    viewState: ProfileUserDescriptionViewState,
    onViewActions: (ProfileUserDescriptionViewActions) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .imePadding()
            .verticalScroll(rememberScrollState())
            .padding(Spacing.X16.dp),
        verticalArrangement = Arrangement.spacedBy(Spacing.X20.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Spacing.X12.dp)
        ) {
            Icon(
                modifier = Modifier.size(IconSizes.X80.dp),
                painter = painterResource(designR.drawable.luzia_with_background),
                tint = Color.Unspecified,
                contentDescription = null
            )
            LuziaText(
                text = stringResource(localizationR.string.onboarding_user_info_title),
                color = LuziaTheme.palette.text.primary,
                style = LuziaTheme.typography.headlines.h3
            )
        }

        LuziaTextInput(
            modifier = Modifier
                .fillMaxWidth()
                .sizeIn(minHeight = MIN_HEIGHT_FOR_EXTENDED_INPUT),
            hint = stringResource(localizationR.string.onboarding_user_info_placeholder),
            value = viewState.prompt,
            state = if (viewState.inputNotValid) {
                TextInputState.ERROR
            } else {
                TextInputState.FILLED
            },
            maxLines = MAX_LINES_FOR_SINGLE_INPUT,
            maxLength = MAX_CHARACTERS,
            errorText = stringResource(localizationR.string.onboarding_user_info_error)
        ) { onViewActions(ProfileUserDescriptionViewActions.OnTextChanged(it)) }
    }
}

@PreviewLightDark
@Composable
private fun Preview() {
    LuziaTheme {
        ProfileUserDescriptionContent(
            viewState = ProfileUserDescriptionViewState().copy(prompt = "text"),
            onViewActions = { DO_NOTHING }
        )
    }
}
