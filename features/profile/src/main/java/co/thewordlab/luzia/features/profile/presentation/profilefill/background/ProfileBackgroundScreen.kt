package co.thewordlab.luzia.features.profile.presentation.profilefill.background

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.LocalSubmitButtonContainer
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.localization.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import coil3.compose.AsyncImage
import co.thewordlab.luzia.foundation.design.system.R as designR

@Composable
fun ProfileBackgroundScreen() {
    val viewModel: ProfileBackgroundViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()

    val submitButtonState = LocalSubmitButtonContainer.current
    LaunchedEffect(viewState.buttonState) {
        submitButtonState.bindButtonStates(
            wizardScreen = WizardScreen.PROFILE_BACKGROUND,
            buttonState = viewState.buttonState
        )
    }

    OnCreate("ProfileBackgroundScreen") {
        viewModel.onViewAction(ProfileBackgroundViewActions.OnCreate)
    }
    ViewModelEventEffect(viewModel) {
        when (it) {
            ProfileBackgroundViewEvents.OnBackgroundSaved -> submitButtonState.submitScreen()
        }
    }
    ProfileBackgroundContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@Composable
private fun ProfileBackgroundContent(
    viewState: ProfileBackgroundViewState,
    onViewActions: (ProfileBackgroundViewActions) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(Spacing.X16.dp),
        verticalArrangement = Arrangement.spacedBy(Spacing.X12.dp)
    ) {
        LuziaText(
            stringResource(R.string.profile_background_title),
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary
        )
        LuziaText(
            stringResource(R.string.profile_background_desc),
            style = LuziaTheme.typography.body.regular.default,
            color = LuziaTheme.palette.text.secondary
        )
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
        LazyVerticalGrid(
            modifier = Modifier.fillMaxSize(),
            columns = GridCells.Fixed(2),
            verticalArrangement = Arrangement.spacedBy(Spacing.X8.dp),
            horizontalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
        ) {
            items(viewState.backgroundUrls.size) { index ->
                val isSelected = index == viewState.backgroundSelected
                Box {
                    AsyncImage(
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(Corners.X4.dp))
                            .border(
                                width = if (isSelected) 2.dp else 0.dp,
                                color = if (isSelected) LuziaTheme.palette.border.brand else Color.Transparent,
                                shape = RoundedCornerShape(Corners.X4.dp)
                            )
                            .click {
                                onViewActions(
                                    ProfileBackgroundViewActions.OnBackgroundSelected(
                                        index
                                    )
                                )
                            },
                        model = viewState.backgroundUrls[index],
                        clipToBounds = true,
                        contentDescription = null
                    )

                    if (isSelected) {
                        Icon(
                            modifier = Modifier
                                .padding(end = Spacing.X8.dp, top = Spacing.X8.dp)
                                .size(IconSizes.X24.dp)
                                .border(
                                    color = Color.White.copy(alpha = 0.5f),
                                    width = 1.dp,
                                    shape = CircleShape
                                )
                                .background(
                                    color = LuziaTheme.palette.interactive.brand,
                                    CircleShape
                                )
                                .padding(Spacing.X4.dp)
                                .align(Alignment.TopEnd),
                            painter = painterResource(designR.drawable.ic_check),
                            contentDescription = null,
                            tint = Color.White
                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        ProfileBackgroundContent(
            viewState = ProfileBackgroundViewState(),
            onViewActions = {}
        )
    }
}
