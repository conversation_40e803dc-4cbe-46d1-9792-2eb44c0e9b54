package co.thewordlab.luzia.features.profile.presentation.profilefill.completed.v2

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.PreviewLightDark
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun ProfileCompletedScreenV2() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(Spacing.X16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        val composition by rememberLottieComposition(
            LottieCompositionSpec.RawRes(R.raw.luzia_onboarding_complete_loading)
        )
        LottieAnimation(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(1f),
            contentScale = ContentScale.FillWidth,
            composition = composition,
            iterations = LottieConstants.IterateForever
        )
        Spacing.X64.Vertical()
        LuziaText(
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center,
            text = stringResource(localizationR.string.onboarding_loading_title),
            style = LuziaTheme.typography.headlines.h3,
            color = LuziaTheme.palette.text.primary
        )
        Spacing.X8.Vertical()
        LuziaText(
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center,
            text = stringResource(localizationR.string.onboarding_loading_desc),
            style = LuziaTheme.typography.body.regular.small,
            color = LuziaTheme.palette.text.secondary
        )
    }
}

@PreviewLightDark
@Composable
private fun Preview() {
    LuziaTheme {
        ProfileCompletedScreenV2()
    }
}
