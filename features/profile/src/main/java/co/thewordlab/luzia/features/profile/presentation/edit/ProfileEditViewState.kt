package co.thewordlab.luzia.features.profile.presentation.edit

import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.privacy.ProfilePrivacyType
import co.thewordlab.luzia.foundation.architecture.system.ViewState
import co.theworldlab.luzia.foundation.design.system.components.profile.AvatarState

data class ProfileEditViewState(
    val displayName: String = "",
    val avatarState: AvatarState? = null,
    val bannerImageUrl: String = "",
    val itemsBasic: List<ProfileEditItem> = emptyList(),
    val itemsEducation: List<ProfileEditItem> = emptyList(),
    val privacyType: ProfilePrivacyType? = null
) : ViewState

data class ProfileEditItem(
    val wizardScreen: WizardScreen,
    val value: String?
)
