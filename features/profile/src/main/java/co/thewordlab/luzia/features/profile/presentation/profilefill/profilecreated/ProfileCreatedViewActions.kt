package co.thewordlab.luzia.features.profile.presentation.profilefill.profilecreated

import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class ProfileCreatedViewActions : ViewAction {
    data object OnDismiss : ProfileCreatedViewActions()
    data object OnStartChat : ProfileCreatedViewActions()
    data object OnNotificationPermissionAsk : ProfileCreatedViewActions()
    data class OnCreate(val isStudent: Boolean) : ProfileCreatedViewActions()
}
