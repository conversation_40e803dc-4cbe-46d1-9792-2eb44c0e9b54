package co.thewordlab.luzia.features.profile.presentation.profilefill.avatar

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.profile.domain.AvatarRepository
import co.thewordlab.luzia.core.profile.domain.ProfileRepository
import co.thewordlab.luzia.features.profile.presentation.GenerateAvatarSuccess
import co.thewordlab.luzia.features.profile.presentation.ProfileAvatarSaved
import co.thewordlab.luzia.features.profile.presentation.ProfileWizardAvatar
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ProfileAvatarViewModel @Inject constructor(
    private val avatarRepository: AvatarRepository,
    private val profileRepository: ProfileRepository,
    private val analytics: Analytics
) :
    ViewModel(),
    ViewModelActions<ProfileAvatarViewActions>,
    ViewModelEvents<ProfileAvatarViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<ProfileAvatarViewState> by ViewModelStatesImpl(ProfileAvatarViewState()) {

    override fun onViewAction(action: ProfileAvatarViewActions) {
        when (action) {
            is ProfileAvatarViewActions.OnCreate -> {
                analytics.logScreen(ProfileWizardAvatar)
                loadProfile()
                presentButtonState()
            }

            ProfileAvatarViewActions.OnSaveClicked -> {
                analytics.logAction(ProfileAvatarSaved)
                sendEvent(ProfileAvatarViewEvents.NavigateBack)
            }
        }
    }

    private suspend fun generateAvatar(url: String) {
        when (val result = avatarRepository.generateAvatar(url)) {
            is ResultOf.Failure -> {
                sendEvent(ProfileAvatarViewEvents.ImageDownloadFailed)
                sendEvent(ProfileAvatarViewEvents.NavigateBack)
            }

            is ResultOf.Success -> {
                analytics.logEvent(GenerateAvatarSuccess)
                updateState {
                    it.copy(
                        isAvatarGenerated = true,
                        avatarPath = result.data
                    )
                }
            }
        }
    }

    private fun loadProfile() = viewModelScope.launch {
        profileRepository.getUserProfile()?.let { profile ->
            updateState { it.copy(profileImageUrl = profile.avatarURL) }
            profile.avatarURL?.let { generateAvatar(it) }
        }
    }

    private fun presentButtonState() {
        updateState {
            it.copy(
                buttonState = it.buttonState.copy(
                    isEnabled = false,
                    isLoading = false,
                    onClick = { sendEvent(ProfileAvatarViewEvents.NavigateBack) }
                )
            )
        }
    }
}
