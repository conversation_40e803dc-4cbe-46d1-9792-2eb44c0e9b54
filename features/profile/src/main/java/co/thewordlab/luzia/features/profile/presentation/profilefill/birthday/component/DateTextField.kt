@file:Suppress("MagicNumber")

package co.thewordlab.luzia.features.profile.presentation.profilefill.birthday.component

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationVector1D
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.keyframes
import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusGroup
import androidx.compose.foundation.focusable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.input.key.onKeyEvent
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalTextInputService
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.BackspaceCommand
import androidx.compose.ui.text.input.CommitTextCommand
import androidx.compose.ui.text.input.DeleteSurroundingTextCommand
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.ImeOptions
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.TextInputSession
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import co.thewordlab.luzia.features.profile.presentation.profilefill.birthday.component.DateUtils.localDateToFieldMap
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import java.time.LocalDate
import kotlin.jvm.optionals.getOrElse

private const val SPLIT_CHAR = '/'
private val SPLIT_CHAR_SPACING = 10.dp
private val CURSOR_STROKE = 4.dp

@Composable
fun DateTextField(
    modifier: Modifier = Modifier,
    initialValue: LocalDate? = null,
    onValueChanged: (LocalDate?) -> Unit = {},
    datePattern: DatePattern = DatePattern.DD_MM_YYYY,
    minDate: LocalDate = defaultInitialDate(),
    maxDate: LocalDate = defaultEndDate()
) {
    DateValidator.isDateTextFieldInputsValid(initialValue, minDate, maxDate)

    val dateFormat by remember {
        val factory = DateFormat.Factory()
        factory.minDate = minDate.atTime(23, 59)
        factory.maxDate = maxDate.atTime(23, 59)
        mutableStateOf(
            factory.createSpecificFormat(datePattern).getOrElse {
                factory.createDefaultFormat()
            }
        )
    }

    val textInputService = LocalTextInputService.current
    val focusManager = LocalFocusManager.current

    var inputSession by remember { mutableStateOf<TextInputSession?>(null) }

    val state = remember(initialValue) {
        DateTextFieldState(
            dateFormat,
            localDateToFieldMap(initialValue),
            onValueChanged
        )
    }

    val keyboardActionRunner = remember { KeyboardActionRunner(KeyboardActions(), focusManager) }

    LaunchedEffect(state.hasFocus) {
        if (state.hasFocus) {
            inputSession = textInputService?.startInput(
                value = TextFieldValue(),
                imeOptions = KeyboardOptions(
                    autoCorrectEnabled = false,
                    platformImeOptions = null,
                    showKeyboardOnFocus = true,
                    hintLocales = null,
                    capitalization = KeyboardCapitalization.None,
                    keyboardType = KeyboardType.Number,
                    imeAction = ImeAction.Done,
                ).toImeOptions(),
                onEditCommand = { operations ->
                    operations.forEach { operation ->
                        when (operation) {
                            is DeleteSurroundingTextCommand,
                            is BackspaceCommand -> state.onBackspace()

                            is CommitTextCommand -> operation.text.forEach(state::onEnterDigit)
                        }
                    }
                },
                onImeActionPerformed = keyboardActionRunner::runAction
            )
            keyboardActionRunner.inputSession = inputSession
        } else {
            inputSession?.let { textInputService?.stopInput(it) }
        }
    }

    RowDateContent(modifier, state, dateFormat, inputSession)
}

@Composable
private fun RowDateContent(
    modifier: Modifier,
    state: DateTextFieldState,
    dateFormat: DateFormat,
    inputSession: TextInputSession?
) {
    Row(
        modifier = modifier
            .focusGroup()
            .onKeyEvent { event ->
                state.onKeyEvent(event)
                false
            }
    ) {
        val cursorAlpha = cursorAlphaAnimatable(state)
        dateFormat.fields.forEachIndexed { index, field ->
            state.fieldsState[field]?.let {
                Box(
                    modifier = Modifier
                        .alignByBaseline()
                        .focusRequester(it.focusRequester)
                        .onFocusChanged { focusState ->
                            if (focusState.isFocused) {
                                state.focusedField = field
                            } else if (state.focusedField == field) {
                                state.focusedField = null
                            }
                        }
                        .focusable()
                        .noRippleClickable(enabled = true) {
                            it.focusRequester.requestFocus()
                            inputSession?.showSoftwareKeyboard()
                        },
                    contentAlignment = Alignment.BottomStart
                ) {
                    Row {
                        val fieldText = state.valueForField(field)
                        for (position in 0 until field.length) {
                            val char = fieldText.getOrNull(position)
                            Box(
                                modifier = Modifier
                                    .alignByBaseline()
                                    .cursor(
                                        state,
                                        field,
                                        SolidColor(LuziaTheme.palette.interactive.brand),
                                        { cursorAlpha.value },
                                        position
                                    ),
                                contentAlignment = Alignment.BottomStart
                            ) {
                                LuziaText(
                                    text = stringResource(id = field.placeholder),
                                    style = LuziaTheme.typography.headlines.h3.copy(fontWeight = FontWeight.Normal),
                                    modifier = Modifier.alpha(if (char == null) 1f else 0f),
                                    color = LuziaTheme.palette.text.helper
                                )

                                char?.let { char ->
                                    LuziaText(
                                        text = char.toString(),
                                        style = LuziaTheme.typography.headlines.h3,
                                        modifier = Modifier.alpha(1f),
                                        color = LuziaTheme.palette.text.helper
                                    )
                                }
                            }
                        }
                    }
                }
            }

            if (index < dateFormat.fields.size - 1) {
                LuziaText(
                    modifier = Modifier
                        .padding(horizontal = SPLIT_CHAR_SPACING)
                        .alignByBaseline(),
                    text = SPLIT_CHAR.toString(),
                    style = LuziaTheme.typography.headlines.h3,
                    color = LuziaTheme.palette.text.helper
                )
            }
        }
    }
}

@Composable
private fun cursorAlphaAnimatable(state: DateTextFieldState): Animatable<Float, AnimationVector1D> {
    val cursorAlpha = remember { Animatable(1f) }
    LaunchedEffect(
        state.valueForField(DateField.Day),
        state.valueForField(DateField.Month),
        state.valueForField(DateField.Year),
        state.focusedField
    ) {
        cursorAlpha.snapTo(1f)
        cursorAlpha.animateTo(
            0f,
            infiniteRepeatable(
                animation = keyframes {
                    durationMillis = 1000
                    1f at 0
                    1f at 499
                    0f at 500
                    0f at 999
                }
            )
        )
    }
    return cursorAlpha
}

private fun Modifier.cursor(
    state: DateTextFieldState,
    field: DateField,
    brush: Brush,
    alphaProvider: () -> Float,
    position: Int
): Modifier {
    val fieldText = state.valueForField(field)
    val isFocused = state.focusedField == field
    return drawWithContent {
        drawContent()
        if (isFocused && fieldText.length == position) {
            drawLine(
                brush = brush,
                start = Offset(0f, 0f),
                end = Offset(0f, size.height),
                strokeWidth = CURSOR_STROKE.toPx(),
                alpha = alphaProvider()
            )
        } else if (isFocused && fieldText.length == field.length && position == field.length - 1) {
            drawLine(
                brush = brush,
                start = Offset(size.width, 0f),
                end = Offset(size.width, size.height),
                strokeWidth = CURSOR_STROKE.toPx(),
                alpha = alphaProvider()
            )
        }
    }
}

private fun defaultInitialDate(): LocalDate =
    LocalDate.of(1900, 1, 1)

private fun defaultEndDate(): LocalDate =
    LocalDate.now()

private fun Modifier.noRippleClickable(
    enabled: Boolean = true,
    onClick: () -> Unit
) = clickable(MutableInteractionSource(), indication = null, enabled = enabled, onClick = onClick)

private fun KeyboardOptions.toImeOptions() =
    ImeOptions(
        singleLine = true,
        capitalization = capitalization,
        autoCorrect = autoCorrectEnabled == true,
        keyboardType = keyboardType,
        imeAction = imeAction
    )

@PreviewLightDark
@Composable
private fun Preview() {
    LuziaTheme {
        DateTextField()
    }
}
