package co.thewordlab.luzia.features.profile.presentation.profilefill.privacy

import co.thewordlab.luzia.core.profile.domain.model.UserProfilePrivacy

fun UserProfilePrivacy?.mapToModel() =
    when (this) {
        UserProfilePrivacy.PRIVATE -> ProfilePrivacyType.PRIVATE
        else -> ProfilePrivacyType.PUBLIC
    }

fun ProfilePrivacyType?.mapToDomain() =
    when (this) {
        ProfilePrivacyType.PUBLIC -> UserProfilePrivacy.PUBLIC
        ProfilePrivacyType.PRIVATE -> UserProfilePrivacy.PRIVATE
        else -> UserProfilePrivacy.PUBLIC
    }
