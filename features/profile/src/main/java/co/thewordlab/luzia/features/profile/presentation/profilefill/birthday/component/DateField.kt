package co.thewordlab.luzia.features.profile.presentation.profilefill.birthday.component

import co.thewordlab.luzia.foundation.localization.R as localizationR

sealed class DateField {
    abstract val length: Int
    abstract val placeholder: Int

    data object Day : DateField() {
        override val length = 2
        override val placeholder = localizationR.string.day_short_label
    }
    data object Month : DateField() {
        override val length = 2
        override val placeholder = localizationR.string.month_short_label
    }
    data object Year : DateField() {
        override val length = 4
        override val placeholder = localizationR.string.year_short_label
    }
}
