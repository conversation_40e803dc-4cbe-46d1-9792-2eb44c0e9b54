package co.thewordlab.luzia.features.profile.presentation.profile.external

import androidx.navigation.NavGraphBuilder
import androidx.navigation.toRoute
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.common.bottomSheet
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme

fun NavGraphBuilder.externalUser() {
    bottomSheet<UserSessionRoutes.ExternalProfile>(
        showHandle = false,
        backgroundColor = { LuziaTheme.palette.surface.background }
    ) {
        val navigation = LocalNavigation.current
        val route = it.backstackEntry.toRoute<UserSessionRoutes.ExternalProfile>()
        ProfileExternalUserScreen(
            route.userId,
            onNavigateBack = { navigation.goBack() },
            onNavigateToChat = { id -> navigation.navigate(UserSessionRoutes.GetStreamChatDetail(id)) }
        )
    }
}
