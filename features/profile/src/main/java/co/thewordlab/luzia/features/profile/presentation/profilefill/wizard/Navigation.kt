package co.thewordlab.luzia.features.profile.presentation.profilefill.wizard

import android.app.Activity
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavOptions
import androidx.navigation.compose.composable
import androidx.navigation.navOptions
import androidx.navigation.toRoute
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.common.Navigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.foundation.common.extensions.findActivity
import co.thewordlab.luzia.foundation.common.permission.PermissionState
import co.thewordlab.luzia.foundation.common.permission.notificationPermissionState
import co.theworldlab.luzia.foundation.design.system.components.bottombar.LandingDestinations

fun NavGraphBuilder.profileFill() {
    composable<UserSessionRoutes.ProfileFill> { backstackEntry ->
        val navigation = LocalNavigation.current
        val policy = backstackEntry.toRoute<UserSessionRoutes.ProfileFill>().policy
        val activity = LocalContext.current.findActivity()
        ProfileFillWizardScreen(
            policy = policy,
            onClose = {
                navigation.navigateToNotificationPermission(activity, false)
            },
            onGoToProfile = {
                val navOptions = navOptions {
                    launchSingleTop = true
                    popUpTo(UserSessionRoutes.Landing(LandingDestinations.HOME))
                }
                navigation.navigate(UserSessionRoutes.Profile, navOptions)
            },
            onProfileCreated = {
                navigation.goBack()
            }
        )
    }
}

private fun Navigation.navigateToNotificationPermission(
    activity: Activity,
    openChat: Boolean,
) {
    val permissionState = activity.notificationPermissionState()
    permissionState.run {
        if (this is PermissionState.Denied && !this.redirectToSettings) {
            val options = NavOptions.Builder()
                .setPopUpTo(UserSessionRoutes.Landing(LandingDestinations.HOME), false)
                .build()
            navigate(UserSessionRoutes.NotificationPermission(openChat), options)
        } else {
            goBackTo(UserSessionRoutes.Landing(LandingDestinations.HOME))
        }
    }
}
