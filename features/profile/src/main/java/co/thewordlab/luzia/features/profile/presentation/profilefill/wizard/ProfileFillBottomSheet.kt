package co.thewordlab.luzia.features.profile.presentation.profilefill.wizard

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.avatar.ProfileAvatarScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.background.ProfileBackgroundScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.birthday.ProfileBirthdayScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.greeting.ProfileGreetingScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.name.ProfileNameScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.picselection.ProfilePicSelectionScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.privacy.ProfilePrivacyScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.pronouns.ProfilePronounScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.school.ProfileSchoolScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.student.ProfileStudentScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.username.ProfileUsernameScreen
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonFilled
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import kotlinx.coroutines.launch
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun ProfileFillScreen(wizardScreen: WizardScreen, onDismiss: () -> Unit) {
    var showAvatarSheet by remember { mutableStateOf(false) }
    SubmitButtonContainer(wizardScreen, onDismiss) { buttonState ->
        Column(modifier = Modifier.fillMaxWidth()) {
            Spacer(Modifier.height(Spacing.X8.dp))
            CloseIcon(onClose = { onDismiss() })
            Spacer(Modifier.height(Spacing.X16.dp))
            Content(
                modifier = Modifier.weight(1f),
                wizardScreen = wizardScreen,
                onStudentChanged = { DO_NOTHING },
                onAvatarClicked = { showAvatarSheet = true },
            )
            if (buttonState != null) BottomSection(buttonState)
        }
        if (showAvatarSheet) {
            ProfileFillAvatarBottomSheet(
                onDismiss = { showAvatarSheet = false },
                onClosed = {
                    showAvatarSheet = false
                }
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileFillAvatarBottomSheet(
    onDismiss: () -> Unit,
    onClosed: (isAvatarGenerated: Boolean) -> Unit
) {
    val bottomSheetState = rememberModalBottomSheetState(true)
    val coroutineScope = rememberCoroutineScope()
    var isAvatarGenerated by remember { mutableStateOf(false) }
    fun closeSheet() {
        coroutineScope.launch {
            bottomSheetState.hide()
            onClosed(isAvatarGenerated)
        }
    }
    ModalBottomSheet(
        modifier = Modifier.statusBarsPadding(),
        onDismissRequest = onDismiss,
        containerColor = LuziaTheme.palette.surface.content,
        dragHandle = null,
        sheetState = bottomSheetState
    ) {
        SubmitButtonContainer(WizardScreen.PROFILE_IMAGE, onDismiss) { buttonState ->
            Column(modifier = Modifier.fillMaxWidth()) {
                Spacer(Modifier.height(Spacing.X8.dp))
                CloseIcon(onClose = ::closeSheet)
                Spacer(Modifier.height(Spacing.X16.dp))
                ProfileAvatarScreen { isAvatarGenerated = true }
                if (buttonState != null) BottomSection(buttonState)
            }
        }
    }
}

@Composable
private fun BottomSection(submitButtonState: SubmitButtonState) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.End,
        verticalAlignment = Alignment.CenterVertically
    ) {
        ButtonFilled(
            modifier = Modifier.padding(Spacing.X20.dp),
            onClick = submitButtonState.onClick,
            enabled = submitButtonState.isEnabled,
            isLoading = submitButtonState.isLoading,
            buttonText = stringResource(localizationR.string.save)
        )
    }
}

@Composable
private fun CloseIcon(onClose: () -> Unit) {
    IconButton(
        modifier = Modifier.padding(Spacing.X2.dp),
        onClick = onClose,
        content = {
            Icon(
                painter = painterResource(R.drawable.ic_close),
                contentDescription = null,
                tint = LuziaTheme.palette.text.primary
            )
        }
    )
}

@Composable
private fun Content(
    modifier: Modifier,
    wizardScreen: WizardScreen,
    onStudentChanged: (Boolean) -> Unit,
    onAvatarClicked: () -> Unit
) {
    Box(modifier.fillMaxWidth()) {
        when (wizardScreen) {
            WizardScreen.GREETING -> ProfileGreetingScreen()
            WizardScreen.PROFILE_IMAGE -> ProfilePicSelectionScreen(
                editionMode = true,
                onAvatarClicked = onAvatarClicked
            )

            WizardScreen.USERNAME -> ProfileUsernameScreen()
            WizardScreen.BIRTHDAY -> ProfileBirthdayScreen()
            WizardScreen.PRONOUN -> ProfilePronounScreen()
            WizardScreen.SCHOOL -> ProfileSchoolScreen(WizardScreen.SCHOOL)
            WizardScreen.PRIVACY -> ProfilePrivacyScreen()
            WizardScreen.STUDENT -> ProfileStudentScreen(onStudentChanged)
            WizardScreen.NAME -> ProfileNameScreen()
            WizardScreen.COMPLETED -> DO_NOTHING
            WizardScreen.PROFILE_BACKGROUND -> ProfileBackgroundScreen()
        }
    }
}
