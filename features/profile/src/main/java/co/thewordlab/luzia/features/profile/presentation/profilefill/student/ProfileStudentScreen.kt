package co.thewordlab.luzia.features.profile.presentation.profilefill.student

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.LocalSubmitButtonContainer
import co.thewordlab.luzia.foundation.architecture.system.OnStart
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.localization.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR

@Composable
fun ProfileStudentScreen(onStudentChanged: (Boolean) -> Unit) {
    val viewModel: ProfileStudentViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val submitButtonState = LocalSubmitButtonContainer.current
    LaunchedEffect(viewState.buttonState) {
        submitButtonState.bindButtonStates(
            wizardScreen = WizardScreen.STUDENT,
            buttonState = viewState.buttonState
        )
    }
    OnStart { viewModel.onViewAction(ProfileStudentViewActions.OnStart) }
    ViewModelEventEffect(viewModel) {
        when (it) {
            is ProfileStudentViewEvents.SubmitScreen -> submitButtonState.submitScreen()
            is ProfileStudentViewEvents.ChangeStudentState -> onStudentChanged(it.isStudent)
        }
    }
    ProfileStudentContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@Composable
private fun ProfileStudentContent(
    viewState: ProfileStudentViewState,
    onViewActions: (ProfileStudentViewActions) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(Spacing.X16.dp),
        verticalArrangement = Arrangement.spacedBy(Spacing.X12.dp)
    ) {
        LuziaText(
            text = stringResource(R.string.profile_student_title),
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary
        )
        LuziaText(
            text = stringResource(R.string.profile_student_desc),
            style = LuziaTheme.typography.body.regular.default,
            color = LuziaTheme.palette.text.secondary
        )
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
        StudentOption(
            title = stringResource(R.string.profile_student_yes_title),
            desc = stringResource(R.string.profile_student_yes_desc),
            iconRes = designR.drawable.ic_student_yes_24,
            isSelected = viewState.isStudent == true,
            onClick = { onViewActions(ProfileStudentViewActions.OnSelection(true)) }
        )
        StudentOption(
            title = stringResource(R.string.profile_student_no_title),
            desc = stringResource(R.string.profile_student_no_desc),
            iconRes = designR.drawable.ic_student_no_24,
            isSelected = viewState.isStudent == false,
            onClick = { onViewActions(ProfileStudentViewActions.OnSelection(false)) }
        )
        Spacer(Modifier.weight(1f))
        StudentNumberView(Modifier.align(Alignment.CenterHorizontally))
    }
}

@Composable
private fun StudentNumberView(modifier: Modifier) {
    Column(
        modifier = modifier.padding(bottom = Spacing.X40.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
    ) {
        LuziaText(
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center,
            text = stringResource(R.string.join_community_title),
            style = LuziaTheme.typography.body.semiBold.small,
            color = LuziaTheme.palette.text.primary
        )
        Row(
            modifier = Modifier
                .clip(CircleShape)
                .background(LuziaTheme.palette.accents.green.green10)
                .padding(vertical = Spacing.X4.dp, horizontal = Spacing.X12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Spacing.X4.dp)
        ) {
            LuziaText(
                text = stringResource(R.string.join_community_number),
                style = LuziaTheme.typography.body.regular.small,
                color = LuziaTheme.palette.accents.green.green30
            )
        }
    }
}

@Composable
private fun StudentOption(
    title: String,
    desc: String,
    @DrawableRes iconRes: Int,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(Corners.X4.dp))
            .border(
                width = if (isSelected) 2.dp else 1.dp,
                color = if (isSelected) LuziaTheme.palette.border.brand else LuziaTheme.palette.border.primary,
                shape = RoundedCornerShape(Corners.X4.dp)
            )
            .click(action = onClick)
            .padding(Spacing.X16.dp),
    ) {
        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
            LuziaText(
                title,
                style = LuziaTheme.typography.body.semiBold.small,
                color = LuziaTheme.palette.text.primary
            )
            Icon(
                painter = painterResource(id = iconRes),
                contentDescription = null,
                tint = LuziaTheme.palette.text.secondary
            )
        }
        Spacer(Modifier.height(Spacing.X4.dp))
        LuziaText(
            desc,
            style = LuziaTheme.typography.body.semiBold.small,
            color = LuziaTheme.palette.text.secondary
        )
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        ProfileStudentContent(
            viewState = ProfileStudentViewState(),
            onViewActions = {}
        )
    }
}
