package co.thewordlab.luzia.features.profile.presentation.referral

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.material3.rememberTopAppBarState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.ClipboardManager
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.shareText
import co.theworldlab.luzia.foundation.design.system.components.navbar.LuziaNavBarDefaults
import co.theworldlab.luzia.foundation.design.system.components.navbar.NavigationAction
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBar
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBarModel
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonFilled
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import kotlinx.coroutines.launch
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Suppress("LongMethod")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReferralScreen(model: ReferralUIModel, onNavigateBack: () -> Unit) {
    val viewModel: ReferralViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val snackBarHostState = remember { SnackbarHostState() }
    val context = LocalContext.current
    val scrollBehavior = TopAppBarDefaults.enterAlwaysScrollBehavior(rememberTopAppBarState())
    val bottomSheetState = rememberModalBottomSheetState(true)
    val coroutineScope = rememberCoroutineScope()
    val clipboardManager: ClipboardManager = LocalClipboardManager.current

    fun closeSheet() {
        coroutineScope.launch {
            bottomSheetState.hide()
            onNavigateBack()
        }
    }

    OnCreate("ReferralScreen") { viewModel.onViewAction(ReferralViewActions.OnCreate(model.referralActivated)) }
    ViewModelEventEffect(viewModel) { event ->
        when (event) {
            ReferralViewEvents.NavigateBack -> if (model.showAsModal) {
                closeSheet()
            } else {
                onNavigateBack()
            }

            is ReferralViewEvents.OpenNativeShare -> {
                val textToShare =
                    String.format(
                        context.getString(localizationR.string.referral_use_my_code),
                        event.code,
                        event.link
                    )
                context.shareText(textToShare)
            }

            ReferralViewEvents.ShowError ->
                snackBarHostState.showSnackbar(
                    context.getString(localizationR.string.generic_error)
                )

            is ReferralViewEvents.CopyShareText -> {
                val textToShare =
                    String.format(
                        context.getString(localizationR.string.referral_use_my_code),
                        event.code,
                        event.link
                    )
                clipboardManager.setText(AnnotatedString(textToShare))
            }
        }
    }

    if (!model.showAsModal) {
        Scaffold(
            modifier = Modifier.nestedScroll(scrollBehavior.nestedScrollConnection),
            topBar = {
                TopNavigationBar(
                    TopNavigationBarModel(
                        title = stringResource(id = localizationR.string.referral_invite_title),
                        navigationAction = NavigationAction.Icon(designR.drawable.ic_back_arrow) {
                            viewModel.onViewAction(ReferralViewActions.OnBackClicked)
                        },
                        colors = LuziaNavBarDefaults.colors(
                            containerColor = LuziaTheme.palette.surface.background,
                            scrolledContainerColor = LuziaTheme.palette.surface.background
                        ),
                        scrollBehavior = scrollBehavior
                    )
                )
            },
            containerColor = LuziaTheme.palette.surface.background,
            snackbarHost = { SnackbarHost(hostState = snackBarHostState) }
        ) { padding ->
            ReferralContent(
                modifier = Modifier.padding(padding),
                viewState = viewState,
                data = model,
                onViewActions = viewModel::onViewAction
            )
        }
    } else {
        ModalBottomSheet(
            modifier = Modifier.statusBarsPadding(),
            sheetState = rememberModalBottomSheetState(true),
            containerColor = LuziaTheme.palette.surface.background,
            dragHandle = null,
            onDismissRequest = { viewModel.onViewAction(ReferralViewActions.OnDismiss) }
        ) {
            Column(modifier = Modifier.fillMaxWidth()) {
                Spacer(Modifier.height(Spacing.X8.dp))
                Box(modifier = Modifier.fillMaxWidth()) {
                    CloseIcon(onClose = { viewModel.onViewAction(ReferralViewActions.OnDismiss) })
                    LuziaText(
                        modifier = Modifier.fillMaxWidth().padding(top = Spacing.X16.dp),
                        text = stringResource(localizationR.string.referral_invite_title),
                        style = LuziaTheme.typography.headlines.h4,
                        color = LuziaTheme.palette.text.primary,
                        textAlign = TextAlign.Center
                    )
                }
                Spacer(Modifier.height(Spacing.X16.dp))
                ReferralContent(
                    modifier = Modifier,
                    viewState = viewState,
                    data = model,
                    onViewActions = viewModel::onViewAction
                )
            }
        }
    }
}

@Composable
private fun CloseIcon(onClose: () -> Unit) {
    IconButton(
        onClick = onClose,
        content = {
            Icon(
                painter = painterResource(designR.drawable.ic_close),
                contentDescription = null,
                tint = LuziaTheme.palette.text.primary
            )
        }
    )
}

@Composable
private fun ReferralContent(
    modifier: Modifier,
    viewState: ReferralViewState,
    data: ReferralUIModel,
    onViewActions: (ReferralViewActions) -> Unit
) {
    Box(modifier = modifier.fillMaxWidth().padding(Spacing.X16.dp)) {
        Column(
            modifier = Modifier.fillMaxWidth()
                .testTag("referralContainer")
                .clip(RoundedCornerShape(Corners.X4.dp))
                .background(
                    color = if (LuziaTheme.isDarkTheme) {
                        LuziaTheme.palette.interactive.brandLight
                    } else {
                        LuziaTheme.palette.surface.content
                    },
                    shape = RoundedCornerShape(Corners.X4.dp)
                )
                .padding(Spacing.X16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                modifier = Modifier.size(IconSizes.X128.dp),
                painter = painterResource(designR.drawable.im_friends),
                contentDescription = null,
                tint = Color.Unspecified
            )
            Spacer(modifier = Modifier.height(Spacing.X16.dp))
            LuziaText(
                text = stringResource(data.title),
                style = LuziaTheme.typography.body.semiBold.default,
                color = LuziaTheme.palette.text.primary,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(Spacing.X16.dp))
            LuziaText(
                text = stringResource(data.description),
                style = LuziaTheme.typography.body.regular.small,
                color = LuziaTheme.palette.text.secondary
            )
            if (data.referralActivated) {
                Spacer(modifier = Modifier.height(Spacing.X24.dp))
                CopyButton(viewState.referralCode, onViewActions)
                Spacer(modifier = Modifier.height(Spacing.X16.dp))
                ButtonFilled(
                    modifier = Modifier.fillMaxWidth(),
                    onClick = { onViewActions(ReferralViewActions.OnReferralLinkClicked) },
                    buttonText = stringResource(localizationR.string.referral_share_link),
                    iconPainter = painterResource(id = designR.drawable.ic_share_arrow),
                    iconContentDescription = stringResource(localizationR.string.referral_share_link)
                )
            }
        }
    }
}

@Composable
private fun CopyButton(link: String, onViewActions: (ReferralViewActions) -> Unit) {
    ConstraintLayout(
        modifier = Modifier.fillMaxWidth()
            .clip(RoundedCornerShape(Corners.X4.dp))
            .background(
                color = LuziaTheme.palette.surface.background,
                shape = RoundedCornerShape(Corners.X4.dp)
            )
            .click {
                onViewActions(ReferralViewActions.OnReferralCodeCopyClicked)
            }
            .padding(Spacing.X16.dp)
    ) {
        val (titleRef, iconRef) = createRefs()
        LuziaText(
            modifier = Modifier.constrainAs(titleRef) {
                top.linkTo(parent.top)
                bottom.linkTo(parent.bottom)
                start.linkTo(parent.start)
                end.linkTo(iconRef.start, margin = Spacing.X8.dp)
                width = Dimension.fillToConstraints
            },
            text = link,
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
        Icon(
            modifier = Modifier
                .size(IconSizes.X32.dp)
                .background(
                    color = LuziaTheme.palette.interactive.brand,
                    shape = CircleShape
                )
                .padding(Spacing.X6.dp)
                .constrainAs(iconRef) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    end.linkTo(parent.end)
                },
            painter = painterResource(designR.drawable.ic_copy),
            contentDescription = null,
            tint = Color.White
        )
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        ReferralContent(
            modifier = Modifier,
            data = ReferralUIModel.ReferralBase,
            viewState = ReferralViewState(
                referralLink = "https://play.google.com/store/apps/details?id=co.thewordlab.luzia"
            ),
            onViewActions = {}
        )
    }
}
