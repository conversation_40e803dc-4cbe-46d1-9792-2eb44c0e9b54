package co.thewordlab.luzia.features.profile.presentation.profilefill.profilecreated

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.profile.presentation.common.StudentChipView
import co.thewordlab.luzia.features.profile.R
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.findActivity
import co.thewordlab.luzia.foundation.common.permission.notificationPermissionState
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonFilled
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec.RawRes
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

private const val SCALE_LOTTIE = 3f

@Composable
fun ProfileCreatedScreen(
    isStudent: Boolean,
    onDismiss: () -> Unit,
    onChat: () -> Unit,
    onNotificationPermission: () -> Unit
) {
    val viewModel: ProfileCreatedViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    OnCreate("ProfileCreatedScreen") {
        viewModel.onViewAction(ProfileCreatedViewActions.OnCreate(isStudent))
    }
    ViewModelEventEffect(viewModel) {
        when (it) {
            ProfileCreatedViewEvents.NavigateBack -> onDismiss()
            ProfileCreatedViewEvents.StartChat -> onChat()
            ProfileCreatedViewEvents.OpenNotificationPermission -> onNotificationPermission()
        }
    }
    ProfileCreatedContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction,
        showCloseIcon = true,
        showSubmitButton = true
    )
}

@Composable
fun ProfileCreatedContent(
    viewState: ProfileCreatedViewState,
    onViewActions: (ProfileCreatedViewActions) -> Unit,
    showCloseIcon: Boolean,
    showSubmitButton: Boolean,
) {
    val activity = LocalContext.current.findActivity()
    Scaffold(
        containerColor = LuziaTheme.palette.surface.content,
        contentWindowInsets = WindowInsets(0, 0, 0, 0)
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .padding(innerPadding)
                .fillMaxSize()
        ) {
            MiddleContent(
                modifier = Modifier
                    .align(Alignment.Center)
                    .fillMaxWidth(),
                viewState = viewState
            )
            if (showSubmitButton) {
                ButtonFilled(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(Spacing.X16.dp)
                        .fillMaxWidth()
                        .navigationBarsPadding(),
                    buttonText = stringResource(localizationR.string.start_chatting),
                    isLoading = viewState.isLoading,
                    onClick = {
                        if (activity.notificationPermissionState().isGranted) {
                            onViewActions(ProfileCreatedViewActions.OnStartChat)
                        } else {
                            onViewActions(ProfileCreatedViewActions.OnNotificationPermissionAsk)
                        }
                    }
                )
            }
            if (viewState.isStudent) {
                StudentNumberView(modifier = Modifier.align(Alignment.TopCenter))
            }
            if (showCloseIcon) {
                CloseIcon(onViewActions)
            }
        }
    }
}

@Composable
private fun StudentNumberView(modifier: Modifier) {
    Column(
        modifier = modifier.padding(top = Spacing.X64.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
    ) {
        StudentChipView()
        LuziaText(
            text = stringResource(localizationR.string.number_of_students_desc),
            style = LuziaTheme.typography.body.semiBold.default,
            color = LuziaTheme.palette.text.primary
        )
    }
}

@Composable
private fun MiddleContent(modifier: Modifier, viewState: ProfileCreatedViewState) {
    Column(
        modifier = modifier.padding(horizontal = Spacing.X32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        val composition by rememberLottieComposition(RawRes(R.raw.confetti_anim))
        val messageResId = if (viewState.isStudent) {
            localizationR.string.profile_created_desc_student
        } else {
            localizationR.string.profile_created_desc_no_student
        }
        LottieAnimation(
            modifier = Modifier
                .fillMaxWidth()
                .height(Spacing.X256.dp)
                .padding(horizontal = Spacing.X64.dp)
                .graphicsLayer {
                    scaleX = SCALE_LOTTIE
                    scaleY = SCALE_LOTTIE
                },
            contentScale = ContentScale.FillWidth,
            composition = composition,
            iterations = LottieConstants.IterateForever
        )
        Spacer(Modifier.height(Spacing.X16.dp))
        LuziaText(
            text = stringResource(localizationR.string.profile_created_title),
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary,
            textAlign = TextAlign.Center
        )
        Spacer(Modifier.height(Spacing.X12.dp))
        LuziaText(
            text = stringResource(messageResId),
            style = LuziaTheme.typography.body.regular.default,
            color = LuziaTheme.palette.text.secondary,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun CloseIcon(
    onViewActions: (ProfileCreatedViewActions) -> Unit
) {
    IconButton(
        modifier = Modifier
            .statusBarsPadding()
            .padding(Spacing.X16.dp),
        onClick = {
            onViewActions(ProfileCreatedViewActions.OnDismiss)
        },
        content = {
            Icon(
                painter = painterResource(designR.drawable.ic_close),
                contentDescription = null,
                tint = LuziaTheme.palette.text.primary
            )
        }
    )
}

@Preview
@Composable
private fun PreviewStudent() {
    LuziaTheme {
        ProfileCreatedContent(
            viewState = ProfileCreatedViewState(isStudent = true),
            onViewActions = {},
            showCloseIcon = true,
            showSubmitButton = true
        )
    }
}

@Preview
@Composable
private fun PreviewNoStudent() {
    LuziaTheme {
        ProfileCreatedContent(
            viewState = ProfileCreatedViewState(isStudent = false),
            onViewActions = {},
            showCloseIcon = true,
            showSubmitButton = true
        )
    }
}
