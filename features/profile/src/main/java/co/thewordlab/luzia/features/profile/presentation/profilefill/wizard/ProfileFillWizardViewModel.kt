package co.thewordlab.luzia.features.profile.presentation.profilefill.wizard

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.navigation.usersession.model.ProfileFillPolicy
import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreen
import co.thewordlab.luzia.core.profile.domain.GetUserProfileUseCase
import co.thewordlab.luzia.core.profile.domain.model.UserProfile
import co.thewordlab.luzia.core.profile.domain.model.isProfileCompleted
import co.thewordlab.luzia.features.profile.presentation.DismissSkipSchoolPopup
import co.thewordlab.luzia.features.profile.presentation.LOADED
import co.thewordlab.luzia.features.profile.presentation.LOADING
import co.thewordlab.luzia.features.profile.presentation.ProfileAvatarClosed
import co.thewordlab.luzia.features.profile.presentation.ProfileWizardSkipPronouns
import co.thewordlab.luzia.features.profile.presentation.SubmitSkipSchoolPopup
import co.thewordlab.luzia.features.profile.presentation.profilefill.school.ProfileSchoolSkipReason
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.LuziaIcon.LARGE
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.LuziaIcon.SMALL
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.profile.AvatarState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ProfileFillWizardViewModel @Inject constructor(
    private val analytics: Analytics,
    private val getUserProfileUseCase: GetUserProfileUseCase
) :
    ViewModel(),
    ViewModelActions<ProfileFillWizardViewActions>,
    ViewModelEvents<ProfileFillWizardViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<ProfileFillWizardViewState> by ViewModelStatesImpl(ProfileFillWizardViewState()) {

    private var profile: UserProfile? = null
    private var policy = ProfileFillPolicy.PROFILE_FILL

    override fun onViewAction(action: ProfileFillWizardViewActions) = when (action) {
        is ProfileFillWizardViewActions.OnCreate -> createScreens(action.policy)
        ProfileFillWizardViewActions.OnBackClicked -> changeScreen { it - 1 }
        ProfileFillWizardViewActions.OnNextClicked -> changeScreen { it + 1 }
        ProfileFillWizardViewActions.OnCloseClicked -> sendEvent(ProfileFillWizardViewEvents.Close)
        ProfileFillWizardViewActions.OnSkip -> attemptSkip()
        is ProfileFillWizardViewActions.OnStudentChanged -> changeWizardList(action.isStudent)
        is ProfileFillWizardViewActions.OnScreenLoaded -> analytics.logScreen(action.screen)
        ProfileFillWizardViewActions.OnAvatarClicked -> sendEvent(ProfileFillWizardViewEvents.ShowAvatarGenerator)
        is ProfileFillWizardViewActions.OnAvatarClosed -> trackAvatarClosed(action.isAvatarGenerated)
        ProfileFillWizardViewActions.OnDismissSchoolSkipDialog -> {
            analytics.logAction(DismissSkipSchoolPopup)
            updateState { it.copy(showSchoolSkipDialog = false) }
        }

        is ProfileFillWizardViewActions.OnSubmitSkipReason -> {
            val properties = mutableMapOf(Parameter.SelectedOption to action.reason.optionName)
            if (action.reason is ProfileSchoolSkipReason.OtherSpecify) {
                properties[Parameter.OtherText] = action.reason.reasonText
            }
            analytics.logAction(SubmitSkipSchoolPopup, properties)
            updateState { it.copy(showSchoolSkipDialog = false) }
            skip()
        }
    }

    private fun attemptSkip() {
        skip()
    }

    private fun skip() {
        changeScreen {
            trackSkip(it)
            it + 1
        }
    }

    private fun trackSkip(index: Int) {
        when (index) {
            viewState.value.screens.indexOf(WizardScreen.PRONOUN) -> ProfileWizardSkipPronouns
            else -> null
        }?.run { analytics.logAction(this) }
    }

    @Suppress("CyclomaticComplexMethod")
    private fun createScreens(policy: ProfileFillPolicy) {
        this.policy = policy
        when (policy) {
            ProfileFillPolicy.PROFILE_FILL -> createScreensForProfileFill()
            ProfileFillPolicy.AFTER_SIGN_UP -> createScreensForAfterSignup()
            ProfileFillPolicy.GUEST_FILL -> DO_NOTHING
        }
    }

    private fun createScreensForAfterSignup() {
        viewModelScope.launch {
            profile = getUserProfileUseCase().firstOrNull()
            if (profile.isProfileCompleted() || profile?.userType == null) {
                sendEvent(ProfileFillWizardViewEvents.Close)
            } else {
                val screens = mutableListOf(
                    WizardScreen.NAME,
                    WizardScreen.STUDENT
                )
                profile?.run {
                    if (isStudent != null) screens.remove(WizardScreen.STUDENT)
                }
                updateState { it.copy(canLoadScreen = true, screens = screens).evaluateState(0) }
            }
        }
    }

    private fun createScreensForProfileFill() {
        viewModelScope.launch {
            profile = getUserProfileUseCase().firstOrNull()
            if (profile.isProfileCompleted() || profile?.userType == null) {
                sendEvent(ProfileFillWizardViewEvents.Close)
            } else {
                val exclusions = profile?.run {
                    setOfNotNull(
                        WizardScreen.GREETING,
                        WizardScreen.USERNAME,
                        WizardScreen.SCHOOL,
                        if (shouldExcludeAvatarScreen(this)) WizardScreen.PROFILE_IMAGE else null,
                        birthdate?.let { WizardScreen.BIRTHDAY },
                        pronouns?.let { WizardScreen.PRONOUN },
                        backgroundURL?.let { WizardScreen.PROFILE_BACKGROUND }
                    )
                }.orEmpty().toMutableSet()
                profile?.isStudent?.let { student ->
                    if (!student) {
                        exclusions.addAll(listOf(WizardScreen.STUDENT))
                    } else {
                        exclusions.add(WizardScreen.STUDENT)
                    }
                }
                exclusions.add(WizardScreen.NAME)
                val screens = WizardScreen.entries.minus(exclusions)
                updateState { it.copy(canLoadScreen = true, screens = screens).evaluateState(0) }
            }
        }
    }

    private fun trackAvatarClosed(isAvatarGenerated: Boolean) {
        analytics.logAction(
            ProfileAvatarClosed,
            mapOf(
                Parameter.Type to when (isAvatarGenerated) {
                    true -> LOADED
                    false -> LOADING
                }
            )
        )
    }

    private fun shouldExcludeAvatarScreen(profile: UserProfile): Boolean =
        profile.avatarState is AvatarState.AiAvatar

    private fun changeWizardList(isStudent: Boolean) {
        updateState { state ->
            val screens = state.screens.toMutableList()
            val studentScreenIndex = screens.indexOf(WizardScreen.STUDENT)
            state.copy(screens = screens, isStudent = isStudent).evaluateState(studentScreenIndex)
        }
    }

    private fun changeScreen(newIndex: (Int) -> Int) {
        sendEvent(ProfileFillWizardViewEvents.HideKeyboard)
        with(viewState.value) {
            val index = screens.indexOf(currentScreen)
            val newIndexPosition = newIndex(index).coerceAtLeast(0)
            val hasCompleted = newIndexPosition >= screens.size
            when {
                hasCompleted && policy == ProfileFillPolicy.PROFILE_FILL -> {
                    sendEvent(ProfileFillWizardViewEvents.GoToProfile)
                }

                hasCompleted && policy == ProfileFillPolicy.AFTER_SIGN_UP -> {
                    viewModelScope.launch {
                        profile = getUserProfileUseCase().firstOrNull()
                        val isStudent = profile?.isStudent == true
                        sendEvent(ProfileFillWizardViewEvents.ProfileCreated(isStudent))
                    }
                }

                else -> updateState { it.evaluateState(newIndexPosition) }
            }
        }
    }

    private fun ProfileFillWizardViewState.evaluateState(index: Int): ProfileFillWizardViewState =
        copy(
            currentScreen = screens[index],
            luziaIcon = getLuziaIconStyle(screens[index]),
            contentCentered = screens[index] == WizardScreen.COMPLETED,
            canGoBack = getCanGoBack(screens, index),
            topIcon = getTopIcon(screens, index, policy),
            progress = (index + 1) / screens.size.toFloat()
        )

    private fun getLuziaIconStyle(newScreen: WizardScreen) =
        if (newScreen == WizardScreen.GREETING || newScreen == WizardScreen.COMPLETED) LARGE else SMALL

    private fun getCanGoBack(
        screens: List<WizardScreen>,
        newIndex: Int,
    ): Boolean {
        return when (policy) {
            ProfileFillPolicy.PROFILE_FILL -> !screens.isLastOrFirst(newIndex)
            ProfileFillPolicy.AFTER_SIGN_UP -> true
            ProfileFillPolicy.GUEST_FILL -> true
        }
    }

    private fun getTopIcon(
        screens: List<WizardScreen>,
        newIndex: Int,
        policy: ProfileFillPolicy,
    ): TopIcon? {
        return when (policy) {
            ProfileFillPolicy.PROFILE_FILL -> {
                if (screens.isLastOrFirst(newIndex)) TopIcon.CLOSE else TopIcon.BACK
            }

            ProfileFillPolicy.AFTER_SIGN_UP -> {
                if (newIndex == 0) null else TopIcon.BACK
            }

            ProfileFillPolicy.GUEST_FILL -> null
        }
    }

    private fun <T> List<T>.isLastOrFirst(index: Int): Boolean {
        return index == 0 || index == lastIndex
    }
}
