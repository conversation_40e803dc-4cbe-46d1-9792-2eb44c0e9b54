package co.thewordlab.luzia.features.profile.presentation.profilefill.birthday

import co.thewordlab.luzia.features.profile.presentation.profilefill.birthday.component.DatePattern
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.SubmitButtonState
import co.thewordlab.luzia.foundation.architecture.system.ViewState
import java.time.LocalDate

internal data class ProfileBirthdayViewState(
    val birthday: LocalDate? = null,
    val isValidDate: Boolean = true,
    val datePattern: DatePattern = DatePattern.DD_MM_YYYY,
    val buttonState: SubmitButtonState = SubmitButtonState()
) : ViewState
