package co.thewordlab.luzia.features.profile.presentation.profilefill.name

import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.SubmitButtonState
import co.thewordlab.luzia.foundation.architecture.system.ViewState

data class ProfileNameViewState(
    val name: String = "",
    val showError: Boolean = false,
    val buttonState: SubmitButtonState = SubmitButtonState()
) : ViewState
