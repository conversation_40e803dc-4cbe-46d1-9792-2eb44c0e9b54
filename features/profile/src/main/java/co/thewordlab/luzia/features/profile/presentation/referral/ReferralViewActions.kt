package co.thewordlab.luzia.features.profile.presentation.referral

import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class ReferralViewActions : ViewAction {
    data class OnCreate(val referralActivated: Boolean) : ReferralViewActions()
    data object OnBackClicked : ReferralViewActions()
    data object OnReferralLinkClicked : ReferralViewActions()
    data object OnReferralCodeCopyClicked : ReferralViewActions()
    data object OnDismiss : ReferralViewActions()
}
