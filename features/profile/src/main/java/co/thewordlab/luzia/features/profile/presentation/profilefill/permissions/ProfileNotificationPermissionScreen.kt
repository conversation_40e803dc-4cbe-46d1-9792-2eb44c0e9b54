package co.thewordlab.luzia.features.profile.presentation.profilefill.permissions

import android.Manifest.permission.POST_NOTIFICATIONS
import android.annotation.SuppressLint
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreenV2
import co.thewordlab.luzia.core.notifications.presentation.v2.NotificationPermissionV2Content
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.v2.LocalSubmitButtonContainerV2
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.common.extensions.findActivity
import co.thewordlab.luzia.foundation.common.permission.PermissionState
import co.thewordlab.luzia.foundation.common.permission.notificationPermissionState
import co.thewordlab.luzia.foundation.common.permission.openNotificationSettings
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.PermissionStatus
import com.google.accompanist.permissions.rememberPermissionState
import co.thewordlab.luzia.foundation.localization.R as localizationR

@SuppressLint("InlinedApi")
@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun NotificationPermissionScreen() {
    val notificationPermissionState = rememberPermissionState(permission = POST_NOTIFICATIONS)
    val activity = LocalContext.current.findActivity()
    val viewModel: ProfileNotificationPermissionViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val submitButtonState = LocalSubmitButtonContainerV2.current
    LaunchedEffect(viewState.buttonState) {
        submitButtonState.bindButtonStates(
            wizardScreen = WizardScreenV2.PERMISSIONS,
            buttonState = viewState.buttonState.copy(text = localizationR.string.button_continue)
        )
    }

    OnCreate("NotificationPermissionScreen") {
        viewModel.onViewAction(ProfileNotificationPermissionViewActions.OnCreate)
    }
    ViewModelEventEffect(viewModel) {
        when (it) {
            ProfileNotificationPermissionViewEvents.AskPermission -> {
                when (val state = activity.notificationPermissionState()) {
                    PermissionState.Granted -> {
                        viewModel.onViewAction(ProfileNotificationPermissionViewActions.OnAllow)
                    }

                    is PermissionState.Denied -> {
                        if (state.redirectToSettings) {
                            activity.openNotificationSettings()
                        } else {
                            notificationPermissionState.launchPermissionRequest()
                        }
                    }
                }
            }

            ProfileNotificationPermissionViewEvents.Dismiss ->
                submitButtonState.submitScreen()
        }
    }
    LaunchedEffect(notificationPermissionState.status) {
        val state = notificationPermissionState.status
        when {
            state is PermissionStatus.Denied && state.shouldShowRationale -> {
                viewModel.onViewAction(ProfileNotificationPermissionViewActions.OnDismiss)
            }

            state is PermissionStatus.Granted -> {
                viewModel.onViewAction(ProfileNotificationPermissionViewActions.OnAllow)
            }

            else -> DO_NOTHING
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .verticalScroll(rememberScrollState())
            .padding(Spacing.X16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        NotificationPermissionV2Content()
    }
}

@Composable
@PreviewLightDark
private fun Preview() {
    NotificationPermissionScreen()
}
