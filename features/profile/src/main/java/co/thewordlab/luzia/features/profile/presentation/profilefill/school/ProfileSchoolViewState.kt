package co.thewordlab.luzia.features.profile.presentation.profilefill.school

import androidx.annotation.StringRes
import co.thewordlab.luzia.core.profile.domain.model.School
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.SubmitButtonState
import co.thewordlab.luzia.foundation.architecture.system.ViewState
import co.thewordlab.luzia.foundation.localization.R as localizationR

data class ProfileSchoolViewState(
    val query: String = "",
    val selectedSchoolId: String? = null,
    val isLoading: Boolean = false,
    val schools: List<School> = emptyList(),
    val buttonState: SubmitButtonState = SubmitButtonState()
) : ViewState

sealed class ProfileSchoolSkipReason(
    val optionName: String,
    @StringRes val resId: Int
) {
    data object Later : ProfileSchoolSkipReason(
        optionName = "add_school_later",
        resId = localizationR.string.skip_form_reason_later
    )

    data object NotSchoolStudent : ProfileSchoolSkipReason(
        optionName = "not_school_student",
        resId = localizationR.string.skip_form_reason_not_school_student
    )

    data object NoNeedToConnect : ProfileSchoolSkipReason(
        optionName = "no_need_to_connect",
        resId = localizationR.string.skip_form_reason_no_need
    )

    data object SchoolNotFound : ProfileSchoolSkipReason(
        optionName = "school_not_on_list",
        resId = localizationR.string.skip_form_reason_not_list
    )

    data class OtherSpecify(val reasonText: String) : ProfileSchoolSkipReason(
        optionName = "other_specify",
        resId = localizationR.string.skip_form_other
    )

    companion object {
        fun getList(): List<ProfileSchoolSkipReason> {
            return listOf(Later, NotSchoolStudent, NoNeedToConnect, SchoolNotFound).shuffled()
                .plus(OtherSpecify(""))
        }
    }
}
