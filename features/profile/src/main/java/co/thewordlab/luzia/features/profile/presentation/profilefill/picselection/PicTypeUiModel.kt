package co.thewordlab.luzia.features.profile.presentation.profilefill.picselection

import androidx.annotation.StringRes
import co.thewordlab.luzia.foundation.localization.R as localizationR

sealed class PicTypeUiModel(
    @StringRes val title: Int,
    @StringRes val description: Int,
    @StringRes val hint: Int? = null,
    val isEnabled: Boolean = true
) {

    data class Photo(val url: String, val enabled: Boolean) :
        PicTypeUiModel(
            title = localizationR.string.profile_image_selection_photo_title,
            description = localizationR.string.profile_image_selection_photo_desc,
            hint = localizationR.string.profile_image_selection_photo_hint,
            isEnabled = enabled
        )

    data class Avatar(val path: String?, val enabled: Boolean) :
        PicTypeUiModel(
            title = localizationR.string.profile_image_selection_avatar_title,
            description = localizationR.string.profile_image_selection_avatar_desc,
            isEnabled = enabled
        )

    data class Hidden(val name: String) :
        PicTypeUiModel(
            title = localizationR.string.profile_image_selection_hidden_title,
            description = localizationR.string.profile_image_selection_hidden_desc,
            isEnabled = true
        )
}
