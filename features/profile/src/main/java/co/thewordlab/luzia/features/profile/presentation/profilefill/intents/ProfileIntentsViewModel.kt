package co.thewordlab.luzia.features.profile.presentation.profilefill.intents

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.profile.data.api.model.UserProfileUpdateRequest
import co.thewordlab.luzia.core.profile.domain.ProfileRepository
import co.thewordlab.luzia.features.profile.presentation.ProfileWizardV2IntentsError
import co.thewordlab.luzia.features.profile.presentation.STATUS_EMPTY
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ProfileIntentsViewModel @Inject constructor(
    private val profileRepository: ProfileRepository,
    private val analytics: Analytics
) :
    ViewModel(),
    ViewModelActions<ProfileIntentsViewActions>,
    ViewModelEvents<ProfileIntentsViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<ProfileIntentsViewState> by ViewModelStatesImpl(ProfileIntentsViewState()) {

    override fun onViewAction(action: ProfileIntentsViewActions) {
        when (action) {
            ProfileIntentsViewActions.OnCreate -> {
                presentButtonState()
                getIntents()
            }

            is ProfileIntentsViewActions.OnIntentSelected -> onIntentSelected(action.intentId)
        }
    }

    private fun getIntents() = viewModelScope.launch {
        when (val result = profileRepository.getUserIntents()) {
            is ResultOf.Failure -> {
                analytics.trackEvent(ProfileWizardV2IntentsError)
                updateState { it.copy(isLoading = false) }
                sendEvent(ProfileIntentsViewEvents.NavigateBack)
            }

            is ResultOf.Success -> updateState { it.copy(intents = result.data, isLoading = false) }
        }
    }

    private fun presentButtonState() {
        updateState {
            it.copy(
                buttonState = it.buttonState.copy(
                    isEnabled = false,
                    isLoading = false,
                    onClick = ::saveIntent
                )
            )
        }
    }

    private fun onIntentSelected(intentId: String) {
        updateState {
            it.copy(
                selectedIntentId = intentId,
                buttonState = it.buttonState.copy(isEnabled = true)
            )
        }
    }

    private fun saveIntent() = viewModelScope.launch {
        setButtonLoading(true)
        val request =
            UserProfileUpdateRequest(onboardingIntentId = viewState.value.selectedIntentId)
        val result = profileRepository.updateUserProfile(request)
        setButtonLoading(false)
        when (result) {
            is ResultOf.Failure -> {
                analytics.trackEvent(ProfileWizardV2IntentsError)
                sendEvent(ProfileIntentsViewEvents.NavigateBack)
            }

            is ResultOf.Success -> {
                analytics.setUserProperties(
                    mapOf(Parameter.UserIntent to (viewState.value.selectedIntentId ?: STATUS_EMPTY))
                )
                sendEvent(ProfileIntentsViewEvents.NavigateBack)
            }
        }
    }

    private fun setButtonLoading(isLoading: Boolean) {
        updateState { it.copy(buttonState = it.buttonState.copy(isLoading = isLoading)) }
    }
}
