package co.thewordlab.luzia.features.profile.presentation.profilefill.background

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.profile.data.api.model.UserProfileUpdateRequest
import co.thewordlab.luzia.core.profile.domain.GetUserProfileUseCase
import co.thewordlab.luzia.core.profile.domain.ProfileRepository
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ProfileBackgroundViewModel @Inject constructor(
    private val profileRepository: ProfileRepository,
    private val getUserProfileUseCase: GetUserProfileUseCase,
) : ViewModel(),
    ViewModelActions<ProfileBackgroundViewActions>,
    ViewModelEvents<ProfileBackgroundViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<ProfileBackgroundViewState> by ViewModelStatesImpl(ProfileBackgroundViewState()) {

    override fun onViewAction(action: ProfileBackgroundViewActions) {
        when (action) {
            is ProfileBackgroundViewActions.OnCreate -> {
                loadProfile()
                initialButtonState()
            }

            is ProfileBackgroundViewActions.OnBackgroundSelected -> onBackgroundSelected(action.index)
        }
    }

    private fun loadProfile() =
        getUserProfileUseCase()
            .onEach { profile ->
                updateState {
                    it.copy(
                        backgroundSelected = it.backgroundUrls.indexOfFirst { background ->
                            profile?.backgroundURL == background
                        }.coerceAtLeast(0),
                        buttonState = it.buttonState.copy(isEnabled = true)
                    )
                }
            }
            .launchIn(viewModelScope)

    private fun initialButtonState() {
        updateState {
            it.copy(
                buttonState = it.buttonState.copy(
                    isEnabled = false,
                    isLoading = false,
                    onClick = ::saveBackground
                )
            )
        }
    }

    private fun onBackgroundSelected(index: Int) {
        updateState {
            it.copy(
                backgroundSelected = index,
                buttonState = it.buttonState.copy(isEnabled = true)
            )
        }
    }

    private fun saveBackground() = viewModelScope.launch {
        with(viewState.value) {
            setButtonLoading(true)
            val request =
                UserProfileUpdateRequest(backgroundURL = backgroundUrls[backgroundSelected])
            val response = profileRepository.updateUserProfile(request)
            setButtonLoading(false)
            if (response is ResultOf.Success) {
                sendEvent(ProfileBackgroundViewEvents.OnBackgroundSaved)
            }
        }
    }

    private fun setButtonLoading(isLoading: Boolean) {
        updateState { it.copy(buttonState = it.buttonState.copy(isLoading = isLoading)) }
    }
}
