package co.thewordlab.luzia.features.profile.presentation.profile.external

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.profile.domain.ExternalUserSendMessageHandler
import co.thewordlab.luzia.core.profile.domain.GetProfileExternalUserUseCase
import co.thewordlab.luzia.core.profile.domain.GetUserProfileUseCase
import co.thewordlab.luzia.core.profile.domain.model.UserProfile
import co.thewordlab.luzia.core.profile.domain.model.UserProfilePrivacy
import co.thewordlab.luzia.core.sharing.BlockReportEntryPoint
import co.thewordlab.luzia.core.sharing.ReportUser
import co.thewordlab.luzia.core.sharing.UnBlockUser
import co.thewordlab.luzia.core.sharing.domain.usecase.BlockUserUseCase
import co.thewordlab.luzia.core.sharing.domain.usecase.ReportUserUseCase
import co.thewordlab.luzia.features.profile.presentation.ProfileDetail
import co.thewordlab.luzia.features.profile.presentation.ProfileMessageClick
import co.thewordlab.luzia.features.profile.presentation.UsernameCopy
import co.thewordlab.luzia.features.profile.presentation.profile.BasicInformation
import co.thewordlab.luzia.features.profile.presentation.profile.CTAButtonType
import co.thewordlab.luzia.features.profile.presentation.profile.HideProfileReason
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewActions
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewEvents
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewEvents.CopyUsername
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewState
import co.thewordlab.luzia.features.profile.presentation.profile.SchoolInformation
import co.thewordlab.luzia.features.profile.presentation.profile.SocialMetricsInformation
import co.thewordlab.luzia.features.profile.presentation.profilefill.background.backgrounds
import co.thewordlab.luzia.features.profile.presentation.profilefill.pronouns.models.parsePronouns
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ProfileExternalUserScreenViewModel @Inject constructor(
    private val getUserProfileUseCase: GetUserProfileUseCase,
    private val analytics: Analytics,
    private val getProfileExternalUserUseCase: GetProfileExternalUserUseCase,
    private val featureFlagManager: FeatureFlagManager,
    private val externalUserSendMessageHandler: ExternalUserSendMessageHandler,
    private val blockUserUseCase: BlockUserUseCase,
    private val reportUserUseCase: ReportUserUseCase
) :
    ViewModel(),
    ViewModelActions<ProfileScreenViewActions>,
    ViewModelEvents<ProfileScreenViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<ProfileScreenViewState> by ViewModelStatesImpl(
        ProfileScreenViewState().copy(isExternalUser = true)
    ) {

    private var currentUserId: String = ""

    override fun onViewAction(action: ProfileScreenViewActions) {
        when (action) {
            is ProfileScreenViewActions.OnProfileIdLoaded ->
                getUserProfile(action.userId)

            ProfileScreenViewActions.OnReportUser -> onReportUser()
            ProfileScreenViewActions.OnUnblockUser -> onUnblockUser()
            ProfileScreenViewActions.OnCloseNavigation -> sendEvent(ProfileScreenViewEvents.NavigateBack)
            ProfileScreenViewActions.OnSendMessage -> onSendMessage()
            ProfileScreenViewActions.OnUsernameCopyClicked -> onUsernameCopyClicked()
            else -> DO_NOTHING
        }
    }

    private fun onReportUser() = viewModelScope.launch {
        analytics.trackAction(ReportUser(BlockReportEntryPoint.PROFILE))
        reportUserUseCase(currentUserId, null)
    }

    private fun onUnblockUser() = viewModelScope.launch {
        analytics.trackAction(UnBlockUser(BlockReportEntryPoint.PROFILE))
        blockUserUseCase(false, currentUserId)
        getUserProfile(currentUserId)
    }

    private fun onUsernameCopyClicked() {
        analytics.logActionWithProps(UsernameCopy)
        sendEvent(CopyUsername)
    }

    private fun logAnalyticsScreen(id: String) = viewModelScope.launch {
        val isMyProfile = getUserProfileUseCase().firstOrNull()?.streamUserId == id
        analytics.logScreen(ProfileDetail, mapOf(Parameter.Myself to isMyProfile))
    }

    private fun onSendMessage() {
        viewModelScope.launch {
            analytics.logAction(ProfileMessageClick)
            updateState { it.copy(ctaLoading = true) }
            val channelId = externalUserSendMessageHandler.createChat(currentUserId)
            updateState { it.copy(ctaLoading = false) }
            if (channelId != null) {
                sendEvent(ProfileScreenViewEvents.NavigateChatChannel(channelId))
            }
        }
    }

    private fun getUserProfile(userId: String) = viewModelScope.launch {
        logAnalyticsScreen(userId)
        if (userId.isNotEmpty()) {
            currentUserId = userId
            when (val result = getProfileExternalUserUseCase(userId)) {
                is ResultOf.Failure -> sendEvent(ProfileScreenViewEvents.NavigateBack)
                is ResultOf.Success -> updateProfile(result.data)
            }
        } else {
            sendEvent(ProfileScreenViewEvents.NavigateBack)
        }
    }

    private fun updateProfile(userProfile: UserProfile) = viewModelScope.launch {
        val groupChatsEnabled: Boolean = featureFlagManager.get(FeatureFlag.GroupChatsEnabled)
        val hideReason = when {
            userProfile.blockedByMe == true -> HideProfileReason.BLOCKED_BY_USER
            userProfile.privacy == UserProfilePrivacy.PRIVATE -> HideProfileReason.PRIVATE_PROFILE
            else -> null
        }
        updateState { state ->
            state.copy(
                displayName = userProfile.displayName.orEmpty(),
                avatarState = userProfile.avatarState,
                bannerImageUrl = userProfile.backgroundURL ?: backgrounds[0],
                basicInfo = userProfile.username?.let { username ->
                    BasicInformation(
                        username = username,
                        pronouns = parsePronouns(userProfile.pronouns)
                    )
                },
                socialMetrics = userProfile.schoolMates?.let { mates ->
                    SocialMetricsInformation(
                        bestiePoints = userProfile.bestiePoints ?: 0,
                        totalFriends = mates
                    )
                },
                ctaButton = takeIf {
                    groupChatsEnabled && userProfile.privacy == UserProfilePrivacy.PUBLIC &&
                        userProfile.blockedByMe != true
                }?.let {
                    CTAButtonType.MESSAGE
                },
                hideProfileReason = hideReason,
                countryName = userProfile.countryName,
                school = SchoolInformation(
                    name = userProfile.schoolName,
                    isStudent = true
                )
            )
        }
    }
}
