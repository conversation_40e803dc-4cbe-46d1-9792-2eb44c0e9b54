package co.thewordlab.luzia.features.profile.presentation.school.mates

import co.thewordlab.luzia.foundation.architecture.system.ViewState
import co.theworldlab.luzia.foundation.design.system.components.mate.UserMateUiModel

data class ProfileSchoolMatesViewState(
    val referralEnabled: Boolean = false,
    val isLoading: Boolean = false,
    val schoolItems: List<UserMateUiModel> = emptyList()
) : ViewState
