package co.thewordlab.luzia.features.profile.presentation.profilefill.wizard

import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class ProfileFillWizardViewEvents : ViewEvent {
    data object HideKeyboard : ProfileFillWizardViewEvents()
    data object Close : ProfileFillWizardViewEvents()
    data class ProfileCreated(val isStudent: Boolean) : ProfileFillWizardViewEvents()
    data object ShowAvatarGenerator : ProfileFillWizardViewEvents()
    data object GoToProfile : ProfileFillWizardViewEvents()
}
