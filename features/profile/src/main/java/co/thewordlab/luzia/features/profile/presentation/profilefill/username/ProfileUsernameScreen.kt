package co.thewordlab.luzia.features.profile.presentation.profilefill.username

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.LocalSubmitButtonContainer
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.localization.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.TextFieldForm
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

private const val SCROLL_ERROR = 1000

@Composable
fun ProfileUsernameScreen() {
    val viewModel: ProfileUsernameViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val submitButtonState = LocalSubmitButtonContainer.current
    LaunchedEffect(viewState.buttonState) {
        submitButtonState.bindButtonStates(
            wizardScreen = WizardScreen.USERNAME,
            buttonState = viewState.buttonState
        )
    }
    OnCreate("ProfileUsernameScreen") {
        viewModel.onViewAction(ProfileUsernameViewActions.OnCreate)
    }
    ViewModelEventEffect(viewModel) {
        when (it) {
            ProfileUsernameViewEvents.NavigateToNextScreen -> submitButtonState.submitScreen()
        }
    }
    ProfileUsernameContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@Composable
private fun ProfileUsernameContent(
    viewState: ProfileUsernameViewState,
    onViewActions: (ProfileUsernameViewActions) -> Unit
) {
    val focusRequester = remember { FocusRequester() }
    val scrollState = rememberScrollState()
    LaunchedEffect(viewState.usernameStatus) {
        if (viewState.usernameStatus == UsernameStatus.ERROR) {
            scrollState.animateScrollTo(SCROLL_ERROR)
        }
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .imePadding()
            .verticalScroll(scrollState)
            .padding(Spacing.X16.dp),
        verticalArrangement = Arrangement.spacedBy(Spacing.X12.dp)
    ) {
        LuziaText(
            text = stringResource(R.string.profile_username_title),
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary
        )
        LuziaText(
            text = stringResource(R.string.profile_username_desc),
            style = LuziaTheme.typography.body.regular.default,
            color = LuziaTheme.palette.text.secondary
        )
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
        TextFieldForm(
            messageText = viewState.username,
            placeholderText = stringResource(R.string.profile_username_hint),
            onDoneButtonKBListener = ({}),
            onTextChanged = { onViewActions(ProfileUsernameViewActions.OnUserNameChanged(it)) },
            focusRequester = focusRequester,
            isError = viewState.usernameStatus.isError,
            textError = viewState.usernameStatus.errorRes?.let { stringResource(it) }
        )
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        ProfileUsernameContent(
            viewState = ProfileUsernameViewState(),
            onViewActions = {}
        )
    }
}
