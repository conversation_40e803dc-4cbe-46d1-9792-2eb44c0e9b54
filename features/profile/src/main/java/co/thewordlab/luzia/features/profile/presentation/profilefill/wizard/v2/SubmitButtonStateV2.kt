package co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.v2

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.Stable
import androidx.compose.runtime.State
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreenV2
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.SubmitButtonState
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update

val LocalSubmitButtonContainerV2 =
    compositionLocalOf { SubmitButtonContainerStateV2(onSubmitScreen = { DO_NOTHING }) }

@Stable
class SubmitButtonContainerStateV2(
    private val onSubmitScreen: () -> Unit
) {

    private val buttonStates = MutableStateFlow<Map<WizardScreenV2, SubmitButtonState>>(emptyMap())

    @Composable
    fun collectButtonState(wizardScreen: WizardScreenV2): State<SubmitButtonState?> {
        return buttonStates.map { it[wizardScreen] }
            .collectAsStateWithLifecycle(SubmitButtonState())
    }

    fun bindButtonStates(wizardScreen: WizardScreenV2, buttonState: SubmitButtonState) {
        buttonStates.update { it.plus(wizardScreen to buttonState) }
    }

    fun submitScreen() = onSubmitScreen.invoke()
}

@Composable
fun SubmitButtonContainer(
    wizardScreen: WizardScreenV2,
    onSubmitScreen: () -> Unit,
    content: @Composable (SubmitButtonState?) -> Unit
) {
    val containerState = remember { SubmitButtonContainerStateV2(onSubmitScreen) }
    val buttonState by containerState.collectButtonState(wizardScreen)
    CompositionLocalProvider(
        LocalSubmitButtonContainerV2 provides containerState
    ) {
        content(buttonState)
    }
}
