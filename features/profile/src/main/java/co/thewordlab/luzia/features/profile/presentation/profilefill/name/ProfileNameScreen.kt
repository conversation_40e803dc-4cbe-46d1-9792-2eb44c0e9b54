package co.thewordlab.luzia.features.profile.presentation.profilefill.name

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.LocalSubmitButtonContainer
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.TextFieldForm
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.localization.R as localizationR

private const val MAX_LIMIT_CHAR = 50

@Composable
fun ProfileNameScreen() {
    val viewModel: ProfileNameViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val submitButtonState = LocalSubmitButtonContainer.current
    LaunchedEffect(viewState.buttonState) {
        submitButtonState.bindButtonStates(
            wizardScreen = WizardScreen.NAME,
            buttonState = viewState.buttonState
        )
    }
    OnCreate("ProfileNameScreen") {
        viewModel.onViewAction(ProfileNameViewActions.OnCreate)
    }
    ViewModelEventEffect(viewModel) {
        when (it) {
            ProfileNameViewEvents.NavigateBack -> submitButtonState.submitScreen()
        }
    }
    ProfileNameContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@Composable
private fun ProfileNameContent(
    viewState: ProfileNameViewState,
    onViewActions: (ProfileNameViewActions) -> Unit
) {
    val focusRequester = remember { FocusRequester() }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .imePadding()
            .verticalScroll(rememberScrollState())
            .padding(Spacing.X16.dp),
        verticalArrangement = Arrangement.spacedBy(Spacing.X12.dp)
    ) {
        LuziaText(
            text = stringResource(localizationR.string.profile_name_title),
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary
        )
        LuziaText(
            text = stringResource(localizationR.string.profile_name_desc),
            style = LuziaTheme.typography.body.regular.default,
            color = LuziaTheme.palette.text.secondary
        )
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
        TextFieldForm(
            messageText = viewState.name,
            placeholderText = stringResource(localizationR.string.signup_username_placeholder),
            onDoneButtonKBListener = ({}),
            onTextChanged = { onViewActions(ProfileNameViewActions.OnNameChanged(it)) },
            focusRequester = focusRequester,
            limitChar = MAX_LIMIT_CHAR,
            isError = viewState.showError
        )
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        ProfileNameContent(
            viewState = ProfileNameViewState(),
            onViewActions = { DO_NOTHING }
        )
    }
}
