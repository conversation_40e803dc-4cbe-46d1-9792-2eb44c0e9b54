package co.thewordlab.luzia.features.profile.presentation.profilefill.pronouns

import co.thewordlab.luzia.features.profile.presentation.profilefill.pronouns.models.PronounsUiModel
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.SubmitButtonState
import co.thewordlab.luzia.foundation.architecture.system.ViewState

internal data class ProfilePronounViewState(
    val selectedPronoun: PronounsUiModel? = null,
    val pronounOptions: List<PronounsUiModel> = PronounsUiModel.listOfPronouns(),
    val buttonState: SubmitButtonState = SubmitButtonState()
) : ViewState
