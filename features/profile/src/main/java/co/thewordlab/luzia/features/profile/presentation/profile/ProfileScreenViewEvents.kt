package co.thewordlab.luzia.features.profile.presentation.profile

import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreen
import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class ProfileScreenViewEvents : ViewEvent {
    data object NavigateToLogin : ProfileScreenViewEvents()
    data object NavigateToProfileFill : ProfileScreenViewEvents()
    data object NavigateToSchoolMates : ProfileScreenViewEvents()
    data object NavigateToBestiePoints : ProfileScreenViewEvents()
    data object ShowSchoolNotSetModal : ProfileScreenViewEvents()
    data object NavigateToSettings : ProfileScreenViewEvents()
    data object NavigateBack : ProfileScreenViewEvents()
    data object NavigateToEdit : ProfileScreenViewEvents()
    data object CopyUsername : ProfileScreenViewEvents()
    data class NavigateChatChannel(val channelId: String) : ProfileScreenViewEvents()
    data class NavigateToEditItem(val screen: WizardScreen) : ProfileScreenViewEvents()
}
