package co.thewordlab.luzia.features.profile.presentation.profilefill.background

import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.SubmitButtonState
import co.thewordlab.luzia.foundation.architecture.system.ViewState

val backgrounds = listOf(
    "https://luzia-prod-public.s3.eu-west-1.amazonaws.com/bg0%402x.png",
    "https://luzia-prod-public.s3.eu-west-1.amazonaws.com/bg1%402x.png",
    "https://luzia-prod-public.s3.eu-west-1.amazonaws.com/bg2%402x.png",
    "https://luzia-prod-public.s3.eu-west-1.amazonaws.com/b3%402x.png",
    "https://luzia-prod-public.s3.eu-west-1.amazonaws.com/b4%402x.png",
    "https://luzia-prod-public.s3.eu-west-1.amazonaws.com/b5%402x.png",
    "https://luzia-prod-public.s3.eu-west-1.amazonaws.com/bg6%402x.png",
    "https://luzia-prod-public.s3.eu-west-1.amazonaws.com/bg7%402x.png",
    "https://luzia-prod-public.s3.eu-west-1.amazonaws.com/b8%402x.png",
    "https://luzia-prod-public.s3.eu-west-1.amazonaws.com/b9%402x.png",
    "https://luzia-prod-public.s3.eu-west-1.amazonaws.com/b10%402x.png",
    "https://luzia-prod-public.s3.eu-west-1.amazonaws.com/b11%402x.png"
)

data class ProfileBackgroundViewState(
    val backgroundUrls: List<String> = backgrounds,
    val backgroundSelected: Int = -1,
    val buttonState: SubmitButtonState = SubmitButtonState()
) : ViewState
