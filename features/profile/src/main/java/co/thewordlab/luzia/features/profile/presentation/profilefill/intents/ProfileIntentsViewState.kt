package co.thewordlab.luzia.features.profile.presentation.profilefill.intents

import co.thewordlab.luzia.core.profile.domain.model.UserIntent
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.SubmitButtonState
import co.thewordlab.luzia.foundation.architecture.system.ViewState

data class ProfileIntentsViewState(
    val selectedIntentId: String? = null,
    val intents: List<UserIntent> = emptyList(),
    val isLoading: Boolean = false,
    val buttonState: SubmitButtonState = SubmitButtonState()
) : ViewState
