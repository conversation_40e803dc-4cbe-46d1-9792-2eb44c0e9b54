package co.thewordlab.luzia.features.profile.presentation.school.selection

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.features.profile.presentation.profilefill.school.ProfileSchoolContent
import co.thewordlab.luzia.features.profile.presentation.profilefill.school.ProfileSchoolViewActions
import co.thewordlab.luzia.features.profile.presentation.profilefill.school.ProfileSchoolViewEvents
import co.thewordlab.luzia.features.profile.presentation.profilefill.school.ProfileSchoolViewModel
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonFilled
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import kotlinx.coroutines.launch
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileSchoolSelectionScreen(onClosed: () -> Unit, onSchoolSelected: () -> Unit) {
    val bottomSheetState = rememberModalBottomSheetState(false)
    val coroutineScope = rememberCoroutineScope()
    var showSchoolSelection by remember { mutableStateOf(false) }

    ModalBottomSheet(
        modifier = Modifier.statusBarsPadding(),
        sheetState = bottomSheetState,
        containerColor = LuziaTheme.palette.surface.content,
        dragHandle = null,
        onDismissRequest = { onClosed() }
    ) {
        SchoolEmptyStateContent(modifier = Modifier) {
            coroutineScope.launch { bottomSheetState.hide() }
            showSchoolSelection = true
        }
    }

    if (showSchoolSelection) {
        SchoolSelectionScreen(
            onClosed = {
                showSchoolSelection = false
                onClosed()
            },
            onSchoolSelected = {
                showSchoolSelection = false
                onSchoolSelected()
            }
        )
    }
}

@Composable
private fun SchoolEmptyStateContent(
    modifier: Modifier,
    onNavigateToSchools: () -> Unit
) {
    Box(modifier = modifier.fillMaxSize().padding(Spacing.X16.dp)) {
        Column(
            modifier = Modifier.fillMaxWidth().padding(Spacing.X16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            LuziaText(
                text = stringResource(localizationR.string.profile_school_not_selected_title),
                style = LuziaTheme.typography.body.semiBold.default,
                color = LuziaTheme.palette.text.primary
            )
            Spacer(modifier = Modifier.height(Spacing.X16.dp))
            Icon(
                modifier = Modifier.size(IconSizes.X128.dp),
                painter = painterResource(designR.drawable.im_friends),
                contentDescription = null,
                tint = Color.Unspecified
            )
            Spacer(modifier = Modifier.height(Spacing.X16.dp))
            LuziaText(
                text = stringResource(localizationR.string.profile_school_not_selected_desc),
                style = LuziaTheme.typography.body.regular.small,
                color = LuziaTheme.palette.text.secondary
            )
            Spacer(modifier = Modifier.height(Spacing.X64.dp))
            ButtonFilled(
                modifier = Modifier.fillMaxWidth(),
                onClick = onNavigateToSchools,
                buttonText = stringResource(localizationR.string.button_start)
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SchoolSelectionScreen(onClosed: () -> Unit, onSchoolSelected: () -> Unit) {
    val viewModel: ProfileSchoolViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val bottomSheetState = rememberModalBottomSheetState(true)
    val coroutineScope = rememberCoroutineScope()

    fun closeSheet() {
        coroutineScope.launch {
            bottomSheetState.hide()
            onClosed()
        }
    }

    OnCreate("SchoolSelectionScreen") { viewModel.onViewAction(ProfileSchoolViewActions.OnCreate) }
    ViewModelEventEffect(viewModel) {
        when (it) {
            ProfileSchoolViewEvents.NavigateNextScreen -> onSchoolSelected()
        }
    }

    ModalBottomSheet(
        modifier = Modifier.statusBarsPadding(),
        sheetState = bottomSheetState,
        containerColor = LuziaTheme.palette.surface.content,
        dragHandle = null,
        onDismissRequest = { closeSheet() }
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            Spacer(Modifier.height(Spacing.X8.dp))
            Box(modifier = Modifier.fillMaxWidth()) {
                CloseIcon(onClose = { closeSheet() })
                LuziaText(
                    modifier = Modifier.fillMaxWidth().padding(top = Spacing.X16.dp),
                    text = stringResource(localizationR.string.profile_school_selection_title),
                    style = LuziaTheme.typography.headlines.h4,
                    color = LuziaTheme.palette.text.primary,
                    textAlign = TextAlign.Center
                )
            }
            Spacer(Modifier.height(Spacing.X16.dp))
            ProfileSchoolContent(
                modifier = Modifier.weight(1f),
                viewState = viewState,
                onViewActions = viewModel::onViewAction
            )
            Spacer(modifier = Modifier.height(Spacing.X16.dp))
            with(viewState.buttonState) {
                ButtonFilled(
                    modifier = Modifier.fillMaxWidth().padding(Spacing.X16.dp),
                    enabled = isEnabled,
                    isLoading = isLoading,
                    onClick = onClick,
                    buttonText = stringResource(text)
                )
            }
        }
    }
}

@Composable
private fun CloseIcon(onClose: () -> Unit) {
    IconButton(
        onClick = onClose,
        content = {
            Icon(
                painter = painterResource(designR.drawable.ic_close),
                contentDescription = null,
                tint = LuziaTheme.palette.text.primary
            )
        }
    )
}
