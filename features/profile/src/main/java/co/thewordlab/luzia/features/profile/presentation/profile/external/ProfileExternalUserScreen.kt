package co.thewordlab.luzia.features.profile.presentation.profile.external

import android.widget.Toast
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ClipboardManager
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.AnnotatedString
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenContent
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewActions
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewEvents
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.OnStart
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.legacy.composables.Loading
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
internal fun ProfileExternalUserScreen(
    userId: String,
    onNavigateBack: () -> Unit,
    onNavigateToChat: (String) -> Unit,
) {
    val viewModel: ProfileExternalUserScreenViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val context = LocalContext.current
    val clipboardManager: ClipboardManager = LocalClipboardManager.current
    OnCreate("ProfileExternalUserScreen") {
        viewModel.onViewAction(
            ProfileScreenViewActions.OnProfileIdLoaded(
                userId
            )
        )
    }
    OnStart { viewModel.onViewAction(ProfileScreenViewActions.OnStart) }

    ViewModelEventEffect(viewModel) {
        when (it) {
            ProfileScreenViewEvents.NavigateBack -> onNavigateBack()
            is ProfileScreenViewEvents.NavigateChatChannel -> onNavigateToChat(it.channelId)
            ProfileScreenViewEvents.CopyUsername -> {
                val text = context.getString(localizationR.string.username_copied)
                Toast.makeText(context, text, Toast.LENGTH_SHORT).show()
                clipboardManager.setText(AnnotatedString(viewState.basicInfo?.username.orEmpty()))
            }

            else -> DO_NOTHING
        }
    }

    Column(modifier = Modifier.fillMaxSize()) {
        if (viewState.displayName.isEmpty()) {
            Loading(modifier = Modifier.fillMaxSize())
        } else {
            ProfileScreenContent(
                viewState = viewState,
                onViewActions = viewModel::onViewAction
            )
        }
    }
}
