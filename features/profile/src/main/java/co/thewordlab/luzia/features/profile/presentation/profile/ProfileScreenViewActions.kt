package co.thewordlab.luzia.features.profile.presentation.profile

import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class ProfileScreenViewActions : ViewAction {
    data object OnStart : ProfileScreenViewActions()
    data object OnCreate : ProfileScreenViewActions()
    data object OnProfileFillStart : ProfileScreenViewActions()
    data object OnSchoolMatesClicked : ProfileScreenViewActions()
    data object OnSchoolNotSetClicked : ProfileScreenViewActions()
    data object OnBestiePointsClicked : ProfileScreenViewActions()
    data object OnNavigateToSchoolSelection : ProfileScreenViewActions()
    data object OnSettingsClicked : ProfileScreenViewActions()
    data object OnBackClicked : ProfileScreenViewActions()
    data object OnShowEditScreen : ProfileScreenViewActions()
    data object OnSendMessage : ProfileScreenViewActions()
    data object OnPictureFullScreenClicked : ProfileScreenViewActions()
    data class OnCloseAvatarSheet(val isAvatarGenerated: Boolean) : ProfileScreenViewActions()
    data class OnProfileIdLoaded(val userId: String) : ProfileScreenViewActions()
    data object OnCloseNavigation : ProfileScreenViewActions()
    data object OnReportUser : ProfileScreenViewActions()
    data object OnAvatarEdit : ProfileScreenViewActions()
    data object OnUsernameCopyClicked : ProfileScreenViewActions()
    data object OnUnblockUser : ProfileScreenViewActions()
}
