package co.thewordlab.luzia.features.profile.presentation.profilefill.intents

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreenV2
import co.thewordlab.luzia.core.profile.domain.model.UserIntent
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.v2.LocalSubmitButtonContainerV2
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.lds.cards.CardSelection
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.Loading
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun ProfileIntentsScreen() {
    val viewModel: ProfileIntentsViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val submitButtonState = LocalSubmitButtonContainerV2.current
    LaunchedEffect(viewState.buttonState) {
        submitButtonState.bindButtonStates(
            wizardScreen = WizardScreenV2.INTENTS,
            buttonState = viewState.buttonState.copy(
                text = co.thewordlab.luzia.foundation.localization.R.string.button_continue
            )
        )
    }
    OnCreate("ProfileIntentsScreen") {
        viewModel.onViewAction(ProfileIntentsViewActions.OnCreate)
    }
    ViewModelEventEffect(viewModel) {
        when (it) {
            ProfileIntentsViewEvents.NavigateBack -> submitButtonState.submitScreen()
        }
    }
    ProfileIntentsContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@Composable
private fun ProfileIntentsContent(
    viewState: ProfileIntentsViewState,
    onViewActions: (ProfileIntentsViewActions) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .imePadding()
            .verticalScroll(rememberScrollState())
            .padding(Spacing.X16.dp),
        verticalArrangement = Arrangement.spacedBy(Spacing.X20.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Spacing.X12.dp)
        ) {
            Icon(
                modifier = Modifier.size(IconSizes.X80.dp),
                painter = painterResource(R.drawable.luzia_with_background),
                tint = Color.Unspecified,
                contentDescription = null
            )
            LuziaText(
                text = stringResource(
                    co.thewordlab.luzia.foundation.localization.R.string.onboarding_intent_actions_title
                ),
                color = LuziaTheme.palette.text.primary,
                style = LuziaTheme.typography.headlines.h3
            )
        }

        if (viewState.isLoading) {
            Loading()
        } else {
            LazyColumn(
                modifier = Modifier.weight(1f),
                state = rememberLazyListState(),
                verticalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
            ) {
                items(viewState.intents.size) { index ->
                    with(viewState.intents[index]) {
                        CardSelection(
                            title = title,
                            isSelected = onboardingIntentId == viewState.selectedIntentId
                        ) {
                            onViewActions(
                                ProfileIntentsViewActions.OnIntentSelected(
                                    onboardingIntentId
                                )
                            )
                        }
                    }
                }
            }
        }
    }
}

@PreviewLightDark
@Composable
private fun Preview() {
    LuziaTheme {
        ProfileIntentsContent(
            viewState = ProfileIntentsViewState().copy(
                selectedIntentId = "id1",
                intents = listOf(
                    UserIntent("id1", "test 1"),
                    UserIntent("id2", "test 2"),
                    UserIntent("id3", "test 3")
                )
            ),
            onViewActions = { DO_NOTHING }
        )
    }
}
