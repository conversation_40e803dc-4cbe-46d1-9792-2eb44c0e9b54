package co.thewordlab.luzia.features.profile.presentation.school.mates

import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class ProfileSchoolMatesViewEvents : ViewEvent {
    data object NavigateBack : ProfileSchoolMatesViewEvents()
    data object NavigateToReferral : ProfileSchoolMatesViewEvents()
    data class OnNavigateToSchoolMateProfile(val userId: String) : ProfileSchoolMatesViewEvents()
}
