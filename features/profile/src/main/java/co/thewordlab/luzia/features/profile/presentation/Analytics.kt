package co.thewordlab.luzia.features.profile.presentation

import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreenV2
import co.thewordlab.luzia.core.profile.domain.model.UserProfile
import co.thewordlab.luzia.core.profile.domain.model.UserType
import co.thewordlab.luzia.core.profile.domain.model.isProfileCompleted
import co.thewordlab.luzia.foundation.analytics.AnalyticsActions
import co.thewordlab.luzia.foundation.analytics.AnalyticsEvents
import co.thewordlab.luzia.foundation.analytics.AnalyticsScreens
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.theworldlab.luzia.foundation.design.system.components.profile.AvatarState

data object ProfileWizardAvatar : AnalyticsScreens("generate_avatar")
data object ProfileDetail : AnalyticsScreens("profile_detail")
data object ProfileEdition : AnalyticsScreens("profile_edit")
data object SchoolMates : AnalyticsScreens("profile_school_mates")
data object SchoolMatesPopup : AnalyticsScreens("profile_school_mates_popup")
data object DismissSkipSchoolPopup : AnalyticsActions("skip_school_popup")
data object SubmitSkipSchoolPopup : AnalyticsActions("submit_school_reason")
data object ProfileCreatedSuccess : AnalyticsScreens("profile_success")

data object ProfileWizardSkipPronouns : AnalyticsActions("pronouns_skip")
data object ProfileEdit : AnalyticsActions("profile_edit")
data object ProfileRegister : AnalyticsActions("profile_register")
data object ProfileCreate : AnalyticsActions("profile_create")
data object ProfileAddSchool : AnalyticsActions("profile_add_school")
data object ProfileClassmates : AnalyticsActions("profile_classmates")
data object ProfileAvatarSelected : AnalyticsActions("avatar_selected")
data object ProfileAvatarClosed : AnalyticsActions("generate_avatar_close")
data object ProfileAvatarSaved : AnalyticsActions("generate_avatar_save")
data object ProfilePictureFullScreenClicked : AnalyticsActions("profile_picture_full_screen")
data object SchoolMatesOpenProfile : AnalyticsActions("profile_click")
data object ReferralCode : AnalyticsScreens("referral_code")
data object OpenReferralCode : AnalyticsActions("referral_open")
data object CopyReferralCode : AnalyticsActions("referral_copy")
data object ShareReferralCode : AnalyticsActions("referral_share")
data object StartChat : AnalyticsActions("start_chat")
data object SkipStartChat : AnalyticsActions("skip_start_chat")
data object EditPicture : AnalyticsActions("edit_picture")
data object ProfileMessageClick : AnalyticsActions("profile_message_click")
data object SchoolSubmit : AnalyticsActions("school_submit")
data object UsernameCopy : AnalyticsActions("username_copy")

data object ProfileSchoolState : AnalyticsEvents("school_state")
data object ProfileWizardUsernameError : AnalyticsEvents("username_error")
data object ProfileWizardNicknameError : AnalyticsEvents("nickname_error")
data object ProfileWizardBirthdayError : AnalyticsEvents("dob_error")
data object ProfileWizardSchoolError : AnalyticsEvents("school_error")
data object GenerateAvatarSuccess : AnalyticsEvents("generate_avatar_success")

const val MODAL = "modal"
const val MY_PICTURE = "myPicture"
const val AVATAR = "avatar"
const val HIDDEN = "hidden"
const val LOADING = "loading"
const val LOADED = "loaded"
const val REFERRAL_SOURCE_HOME = "home"
const val REFERRAL_SOURCE_SETTINGS = "settings"

const val STATUS_EMPTY = "empty"
private const val SCHOOL_STATUS_COMPLETED = "completed"
const val SCHOOL_MATES_LIST = "schoolmates_list"

fun UserProfile?.getSchoolStatus(): String =
    if (this?.isStudent == true) {
        when {
            !schoolName.isNullOrEmpty() -> SCHOOL_STATUS_COMPLETED
            else -> STATUS_EMPTY
        }
    } else {
        STATUS_EMPTY
    }

fun UserProfile.getUserProfileCustomProperties(): Map<Parameter, Any> {
    return mapOf(
        Parameter.HasEmail to !email.isNullOrEmpty(),
        Parameter.HasPhone to !phone.isNullOrEmpty(),
        Parameter.ProfileCompleted to isProfileCompleted(),
        Parameter.SchoolStatus to getSchoolStatus(),
        Parameter.UserType to (userType?.value ?: STATUS_EMPTY),
        Parameter.StudentType to (isStudent ?: STATUS_EMPTY),
        Parameter.ProfilePictureType to when (avatarState) {
            null -> Parameter.Empty
            is AvatarState.AiAvatar -> AVATAR
            is AvatarState.Initials -> HIDDEN
            is AvatarState.ProfileImage -> MY_PICTURE
        },
        Parameter.DayOfBirth to (birthdate ?: STATUS_EMPTY),
        Parameter.SchoolMatesCount to (schoolMates ?: 0),
        Parameter.Pronoun to (pronouns?.value ?: STATUS_EMPTY),
        Parameter.ProfilePrivacy to privacy?.value.orEmpty(),
        Parameter.UserName to (displayName ?: STATUS_EMPTY),
        Parameter.UserIntent to (onboardingIntentId ?: STATUS_EMPTY)
    )
}

fun clearUserProfileCustomProperties() =
    mapOf(
        Parameter.HasEmail to false,
        Parameter.HasPhone to false,
        Parameter.ProfileCompleted to false,
        Parameter.SchoolStatus to null.getSchoolStatus(),
        Parameter.StudentType to STATUS_EMPTY,
        Parameter.ProfilePictureType to STATUS_EMPTY,
        Parameter.DayOfBirth to STATUS_EMPTY,
        Parameter.Pronoun to STATUS_EMPTY,
        Parameter.UserType to UserType.GUEST.value,
        Parameter.UserIntent to STATUS_EMPTY
    )

// OnBoarding Flow v2
data class ProfileWizardV2NextClicked(val step: WizardScreenV2) : AnalyticsActions(
    "onboarding_next_tap",
    mapOf(
        ONBOARDING_STEP to getStepName(step)
    )
)

data class ProfileWizardV2BackClicked(val step: WizardScreenV2) : AnalyticsActions(
    "onboarding_back_tap",
    mapOf(
        ONBOARDING_STEP to getStepName(step)
    )
)

data object ProfileWizardV2FinalLoading :
    AnalyticsScreens("onboarding_step", mapOf(ONBOARDING_STEP to "hand-off"))
data object ProfileWizardV2AgeValidationFailed : AnalyticsEvents("onboarding_dob_underage")
data object ProfileWizardV2NotificationAccepted : AnalyticsActions("onboarding_notification_continue_tap")
data object ProfileWizardV2NotificationDismiss : AnalyticsActions("onboarding_notification_dismiss_tap")

private fun getStepName(step: WizardScreenV2): String =
    when (step) {
        WizardScreenV2.NAME -> "name"
        WizardScreenV2.BIRTHDAY -> "dob"
        WizardScreenV2.INTENTS -> "intents"
        WizardScreenV2.SELF_DESCRIPTION -> "self_description"
        WizardScreenV2.PERMISSIONS -> "notifications"
    }

data object ProfileWizardV2IntentsError : AnalyticsEvents("intents_error")
data object ProfileWizardV2UserDescriptionError : AnalyticsEvents("onboarding_free_text_error")
private const val ONBOARDING_STEP = "onboarding_step"
