package co.thewordlab.luzia.features.profile.presentation.profilefill.greeting

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreen
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.LocalSubmitButtonContainer
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.SubmitButtonState
import co.thewordlab.luzia.foundation.architecture.system.OnStart
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun ProfileGreetingScreen() {
    val viewModel: ProfileGreetingViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val submitButtonState = LocalSubmitButtonContainer.current
    LaunchedEffect(Unit) {
        submitButtonState.bindButtonStates(
            wizardScreen = WizardScreen.GREETING,
            buttonState = SubmitButtonState(
                isEnabled = true,
                isLoading = false,
                onClick = submitButtonState::submitScreen,
                text = localizationR.string.profile_start_wizard_button
            )
        )
    }
    OnStart { viewModel.onViewAction(ProfileGreetingViewActions.OnStart) }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(Spacing.X16.dp),
        verticalArrangement = Arrangement.spacedBy(Spacing.X12.dp)
    ) {
        LuziaText(
            stringResource(localizationR.string.profile_greeting_title, viewState.displayName),
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary
        )
        LuziaText(
            stringResource(localizationR.string.profile_greeting_desc),
            style = LuziaTheme.typography.body.regular.default,
            color = LuziaTheme.palette.text.secondary
        )
    }
}
