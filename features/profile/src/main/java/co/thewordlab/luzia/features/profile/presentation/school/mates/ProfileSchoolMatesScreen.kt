package co.thewordlab.luzia.features.profile.presentation.school.mates

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberTopAppBarState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.mate.UserMateUiModel
import co.theworldlab.luzia.foundation.design.system.components.navbar.LuziaNavBarDefaults
import co.theworldlab.luzia.foundation.design.system.components.navbar.NavigationAction
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBar
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBarModel
import co.theworldlab.luzia.foundation.design.system.components.profile.AvatarState
import co.theworldlab.luzia.foundation.design.system.components.profile.ProfileAvatar
import co.theworldlab.luzia.foundation.design.system.components.profile.ProfileAvatarUiModel
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.legacy.composables.Loading
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun ProfileSchoolMatesScreen(
    onNavigateBack: () -> Boolean,
    onNavigateToReferral: () -> Unit
) {
    val viewModel: ProfileSchoolMatesViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val navigation = LocalNavigation.current

    OnCreate("ProfileSchoolMatesScreen") { viewModel.onViewAction(ProfileSchoolMatesViewActions.OnCreate) }
    ViewModelEventEffect(viewModel) {
        when (it) {
            ProfileSchoolMatesViewEvents.NavigateBack -> onNavigateBack()
            is ProfileSchoolMatesViewEvents.OnNavigateToSchoolMateProfile -> {
                navigation.navigate(UserSessionRoutes.ExternalProfile(it.userId))
            }

            ProfileSchoolMatesViewEvents.NavigateToReferral -> onNavigateToReferral()
        }
    }

    ProfileSchoolMatesContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ProfileSchoolMatesContent(
    viewState: ProfileSchoolMatesViewState,
    onViewActions: (ProfileSchoolMatesViewActions) -> Unit,
) {
    val scrollBehavior = TopAppBarDefaults.enterAlwaysScrollBehavior(rememberTopAppBarState())

    Scaffold(
        modifier = Modifier.nestedScroll(scrollBehavior.nestedScrollConnection),
        containerColor = LuziaTheme.palette.surface.background,
        topBar = {
            TopNavigationBar(
                TopNavigationBarModel(
                    title = stringResource(id = localizationR.string.profile_metrics_school_mates),
                    navigationAction = NavigationAction.Icon(designR.drawable.ic_back_arrow) {
                        onViewActions(ProfileSchoolMatesViewActions.OnBackClicked)
                    },
                    colors = LuziaNavBarDefaults.colors(
                        containerColor = LuziaTheme.palette.surface.background,
                        scrolledContainerColor = LuziaTheme.palette.surface.background
                    ),
                    scrollBehavior = scrollBehavior
                )
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .padding(innerPadding)
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            if (viewState.isLoading) {
                Loading(modifier = Modifier.fillMaxSize())
            } else if (viewState.schoolItems.isEmpty()) {
                EmptyStateContent(viewState, onViewActions)
            } else {
                ListContent(
                    title = stringResource(id = localizationR.string.profile_school_list_title),
                    items = viewState.schoolItems,
                    onViewActions = onViewActions
                )
            }
        }
    }
}

@Composable
private fun EmptyStateContent(
    viewState: ProfileSchoolMatesViewState,
    onViewActions: (ProfileSchoolMatesViewActions) -> Unit,
) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(Modifier.height(Spacing.X24.dp))
        LuziaText(
            text = stringResource(id = localizationR.string.profile_school_mates_empty_state_title),
            style = LuziaTheme.typography.body.semiBold.small,
            color = LuziaTheme.palette.text.primary,
            textAlign = TextAlign.Center
        )
        Spacer(Modifier.height(Spacing.X60.dp))
        if (viewState.referralEnabled) {
            ReferralCardView(onViewActions)
        }
    }
}

@Composable
private fun ReferralCardView(onViewActions: (ProfileSchoolMatesViewActions) -> Unit) {
    Column(
        modifier = Modifier
            .padding(horizontal = Spacing.X16.dp)
            .shadow(Spacing.X16.dp)
            .fillMaxWidth()
            .clip(RoundedCornerShape(Corners.X4.dp))
            .background(
                color = if (LuziaTheme.isDarkTheme) {
                    LuziaTheme.palette.interactive.brandLight
                } else {
                    LuziaTheme.palette.surface.content
                },
                shape = RoundedCornerShape(Corners.X4.dp)
            )
            .padding(Spacing.X16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(Spacing.X16.dp)
    ) {
        Icon(
            modifier = Modifier.size(IconSizes.X128.dp),
            painter = painterResource(designR.drawable.im_friends),
            contentDescription = null,
            tint = Color.Unspecified
        )
        LuziaText(
            text = stringResource(localizationR.string.profile_school_mates_referral_title),
            style = LuziaTheme.typography.body.semiBold.default,
            color = LuziaTheme.palette.text.primary,
            textAlign = TextAlign.Center
        )
        LuziaText(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(localizationR.string.profile_school_mates_referral_desc),
            style = LuziaTheme.typography.body.regular.small,
            color = LuziaTheme.palette.text.secondary,
            textAlign = TextAlign.Center
        )
        InviteButton(onViewActions)
    }
}

@Composable
private fun InviteButton(onViewActions: (ProfileSchoolMatesViewActions) -> Unit) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Spacing.X16.dp),
        modifier = Modifier
            .clip(RoundedCornerShape(Corners.X4.dp))
            .background(LuziaTheme.palette.interactive.brandLight)
            .click { onViewActions(ProfileSchoolMatesViewActions.OnInviteFriends) }
            .padding(Spacing.X12.dp)
    ) {
        Icon(
            modifier = Modifier
                .size(Spacing.X32.dp)
                .clip(CircleShape)
                .background(LuziaTheme.palette.surface.content)
                .padding(Spacing.X4.dp),
            painter = painterResource(designR.drawable.ic_invite),
            contentDescription = null,
            tint = LuziaTheme.palette.interactive.brand
        )
        LuziaText(
            modifier = Modifier.weight(1f),
            text = stringResource(localizationR.string.referral_invite_section),
            style = LuziaTheme.typography.body.semiBold.small,
            color = LuziaTheme.palette.text.brand
        )
        Icon(
            modifier = Modifier.size(Spacing.X12.dp),
            painter = painterResource(designR.drawable.ic_chevron_right),
            contentDescription = null,
            tint = LuziaTheme.palette.interactive.brand
        )
    }
}

@Composable
private fun ListContent(
    title: String,
    items: List<UserMateUiModel>,
    onViewActions: (ProfileSchoolMatesViewActions) -> Unit,
) {
    if (items.isNotEmpty()) {
        LuziaText(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = Spacing.X16.dp),
            text = title,
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary
        )

        items.forEach { MateCellContent(info = it, onViewActions = onViewActions) }
    }
}

@Composable
private fun MateCellContent(
    info: UserMateUiModel,
    onViewActions: (ProfileSchoolMatesViewActions) -> Unit,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .click { onViewActions(ProfileSchoolMatesViewActions.OnSchoolMateClicked(info.masterUserId)) }
            .padding(Spacing.X16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        ProfileAvatar(
            modifier = Modifier.size(IconSizes.X64.dp),
            ProfileAvatarUiModel(
                avatarState = info.avatarState,
                bigStyle = false,
                isPrivate = !info.isPublic
            )
        )
        Spacer(Modifier.width(Spacing.X8.dp))
        Column(modifier = Modifier.weight(1f), verticalArrangement = Arrangement.Center) {
            LuziaText(
                text = info.name,
                style = LuziaTheme.typography.body.semiBold.default,
                color = LuziaTheme.palette.text.primary
            )
            LuziaText(
                text = info.username,
                style = LuziaTheme.typography.body.regular.default,
                color = LuziaTheme.palette.text.primary
            )
        }
        if (info.isPublic) {
            LuziaText(
                text = "${info.bp} BP",
                style = LuziaTheme.typography.body.regular.small,
                color = LuziaTheme.palette.text.secondary
            )
        }
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        ProfileSchoolMatesContent(
            viewState = ProfileSchoolMatesViewState(
                isLoading = false,
                schoolItems = listOf(
                    UserMateUiModel(
                        name = "Antonia",
                        username = "Anto",
                        avatarState = AvatarState.Initials("A"),
                        isStudent = false,
                        isPublic = true
                    ),
                    UserMateUiModel(
                        name = "Antonia",
                        username = "Anto",
                        avatarState = AvatarState.Initials("A"),
                        isStudent = false,
                        isPublic = true
                    ),
                    UserMateUiModel(
                        name = "Antonia",
                        username = "Anto",
                        avatarState = AvatarState.Initials("A"),
                        isStudent = false,
                        isPublic = true
                    )
                )
            ),
            onViewActions = { DO_NOTHING }
        )
    }
}

@Preview
@Composable
private fun EmptyStatePreview() {
    LuziaTheme {
        ProfileSchoolMatesContent(
            viewState = ProfileSchoolMatesViewState(),
            onViewActions = { DO_NOTHING }
        )
    }
}
