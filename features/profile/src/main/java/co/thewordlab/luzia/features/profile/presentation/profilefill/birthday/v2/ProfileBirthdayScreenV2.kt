package co.thewordlab.luzia.features.profile.presentation.profilefill.birthday.v2

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreenV2
import co.thewordlab.luzia.features.profile.presentation.profilefill.birthday.ProfileBirthdayViewActions
import co.thewordlab.luzia.features.profile.presentation.profilefill.birthday.ProfileBirthdayViewEvents
import co.thewordlab.luzia.features.profile.presentation.profilefill.birthday.ProfileBirthdayViewModel
import co.thewordlab.luzia.features.profile.presentation.profilefill.birthday.ProfileBirthdayViewState
import co.thewordlab.luzia.features.profile.presentation.profilefill.birthday.component.DatePattern
import co.thewordlab.luzia.features.profile.presentation.profilefill.birthday.component.v2.DateTextFieldV2
import co.thewordlab.luzia.features.profile.presentation.profilefill.wizard.v2.LocalSubmitButtonContainerV2
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import java.time.LocalDate
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun ProfileBirthdayScreenV2() {
    val viewModel: ProfileBirthdayViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()

    val submitButtonState = LocalSubmitButtonContainerV2.current
    LaunchedEffect(viewState.buttonState) {
        submitButtonState.bindButtonStates(
            wizardScreen = WizardScreenV2.BIRTHDAY,
            buttonState = viewState.buttonState.copy(text = localizationR.string.button_continue)
        )
    }

    ViewModelEventEffect(viewModel) {
        when (it) {
            ProfileBirthdayViewEvents.OnBirthdaySaved -> submitButtonState.submitScreen()
        }
    }
    OnCreate("ProfileBirthdayScreen") {
        viewModel.onViewAction(ProfileBirthdayViewActions.OnCreate(isV2Flow = true))
    }
    BirthdayScreenContent(viewState, viewModel::onViewAction)
}

@Composable
private fun BirthdayScreenContent(
    viewState: ProfileBirthdayViewState,
    onViewAction: (ProfileBirthdayViewActions) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .imePadding()
            .verticalScroll(rememberScrollState())
            .padding(Spacing.X16.dp),
        verticalArrangement = Arrangement.spacedBy(Spacing.X20.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Spacing.X12.dp)
        ) {
            Icon(
                modifier = Modifier.size(IconSizes.X80.dp),
                painter = painterResource(designR.drawable.luzia_with_background),
                tint = Color.Unspecified,
                contentDescription = null
            )
            LuziaText(
                text = stringResource(localizationR.string.onboarding_birthday_title),
                color = LuziaTheme.palette.text.primary,
                style = LuziaTheme.typography.headlines.h3
            )
        }
        BirthdayContent(
            viewState.birthday,
            viewState.isValidDate,
            viewState.datePattern,
            onViewAction
        )
    }
}

@Composable
private fun BirthdayContent(
    birthday: LocalDate?,
    isValidDate: Boolean,
    datePattern: DatePattern,
    onViewAction: (ProfileBirthdayViewActions) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        DateTextFieldV2(
            modifier = Modifier.wrapContentSize(),
            datePattern = datePattern,
            initialValue = birthday,
            isInError = !isValidDate,
            onValueChanged = { onViewAction(ProfileBirthdayViewActions.OnBirthdaySelected(it)) }
        )
        val (supportiveText, color) = if (!isValidDate) {
            localizationR.string.onboarding_birthday_age_limit_error to
                LuziaTheme.palette.accents.red.error50
        } else {
            localizationR.string.onboarding_birthday_age_limit_info to
                LuziaTheme.palette.text.secondary
        }
        Spacing.X8.Vertical()
        Row(
            modifier = Modifier.wrapContentSize(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Spacing.X4.dp)
        ) {
            Icon(
                painter = painterResource(designR.drawable.ic_info),
                contentDescription = null,
                tint = color
            )
            LuziaText(
                text = stringResource(id = supportiveText),
                style = LuziaTheme.typography.body.regular.caption,
                color = color
            )
        }
    }
}

@PreviewLightDark
@Composable
private fun Preview() {
    LuziaTheme {
        BirthdayScreenContent(ProfileBirthdayViewState(isValidDate = true)) { DO_NOTHING }
    }
}
