package co.thewordlab.luzia.features.profile.presentation.profile

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.bestiepoints.presentation.OpenBestiePointsScreen
import co.thewordlab.luzia.core.bestiepoints.presentation.models.StreaksBpSource
import co.thewordlab.luzia.core.navigation.usersession.model.WizardScreen
import co.thewordlab.luzia.core.profile.domain.GetUserProfileUseCase
import co.thewordlab.luzia.core.profile.domain.model.UserProfile
import co.thewordlab.luzia.core.profile.domain.model.UserType
import co.thewordlab.luzia.core.profile.domain.model.isProfileCompleted
import co.thewordlab.luzia.core.profile.presentation.getProfilePrompt
import co.thewordlab.luzia.features.profile.presentation.EditPicture
import co.thewordlab.luzia.features.profile.presentation.LOADED
import co.thewordlab.luzia.features.profile.presentation.LOADING
import co.thewordlab.luzia.features.profile.presentation.MODAL
import co.thewordlab.luzia.features.profile.presentation.ProfileAddSchool
import co.thewordlab.luzia.features.profile.presentation.ProfileAvatarClosed
import co.thewordlab.luzia.features.profile.presentation.ProfileClassmates
import co.thewordlab.luzia.features.profile.presentation.ProfileCreate
import co.thewordlab.luzia.features.profile.presentation.ProfileDetail
import co.thewordlab.luzia.features.profile.presentation.ProfileEdit
import co.thewordlab.luzia.features.profile.presentation.ProfilePictureFullScreenClicked
import co.thewordlab.luzia.features.profile.presentation.ProfileRegister
import co.thewordlab.luzia.features.profile.presentation.SchoolMatesPopup
import co.thewordlab.luzia.features.profile.presentation.UsernameCopy
import co.thewordlab.luzia.features.profile.presentation.getSchoolStatus
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewActions.OnBackClicked
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewActions.OnBestiePointsClicked
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewActions.OnCreate
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewActions.OnNavigateToSchoolSelection
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewActions.OnProfileFillStart
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewActions.OnSchoolMatesClicked
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewActions.OnSchoolNotSetClicked
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewActions.OnSettingsClicked
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewActions.OnShowEditScreen
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewEvents.CopyUsername
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewEvents.NavigateBack
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewEvents.NavigateToBestiePoints
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewEvents.NavigateToEdit
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewEvents.NavigateToEditItem
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewEvents.NavigateToLogin
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewEvents.NavigateToProfileFill
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewEvents.NavigateToSchoolMates
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewEvents.NavigateToSettings
import co.thewordlab.luzia.features.profile.presentation.profile.ProfileScreenViewEvents.ShowSchoolNotSetModal
import co.thewordlab.luzia.features.profile.presentation.profilefill.background.backgrounds
import co.thewordlab.luzia.features.profile.presentation.profilefill.pronouns.models.parsePronouns
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ProfileScreenViewModel @Inject constructor(
    private val analytics: Analytics,
    private val getUserProfileUseCase: GetUserProfileUseCase
) :
    ViewModel(),
    ViewModelActions<ProfileScreenViewActions>,
    ViewModelEvents<ProfileScreenViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<ProfileScreenViewState> by ViewModelStatesImpl(ProfileScreenViewState()) {

    @Suppress("CyclomaticComplexMethod")
    override fun onViewAction(action: ProfileScreenViewActions) {
        when (action) {
            OnProfileFillStart -> onProfileFillStart()
            OnBestiePointsClicked -> {
                analytics.logAction(
                    OpenBestiePointsScreen,
                    mapOf(Parameter.Source to StreaksBpSource.Profile.source)
                )
                sendEvent(NavigateToBestiePoints)
            }

            OnSchoolMatesClicked -> {
                analytics.logAction(ProfileClassmates)
                sendEvent(NavigateToSchoolMates)
            }

            OnSchoolNotSetClicked -> {
                analytics.logScreen(SchoolMatesPopup)
                sendEvent(ShowSchoolNotSetModal)
            }

            OnNavigateToSchoolSelection -> {
                analytics.logAction(ProfileAddSchool, mapOf(Parameter.Source to MODAL))
                onProfileFillStart()
            }

            OnBackClicked -> sendEvent(NavigateBack)
            OnSettingsClicked -> sendEvent(NavigateToSettings)
            OnShowEditScreen -> {
                if (viewState.value.ctaButton == CTAButtonType.EDIT) {
                    analytics.logAction(ProfileEdit)
                    sendEvent(NavigateToEdit)
                }
            }

            OnCreate -> {
                listenToProfile()
            }

            ProfileScreenViewActions.OnStart -> onStart()
            ProfileScreenViewActions.OnPictureFullScreenClicked ->
                analytics.logAction(ProfilePictureFullScreenClicked)

            is ProfileScreenViewActions.OnCloseAvatarSheet ->
                trackAvatarClosed(action.isAvatarGenerated)

            ProfileScreenViewActions.OnCloseNavigation,
            is ProfileScreenViewActions.OnProfileIdLoaded,
            ProfileScreenViewActions.OnReportUser,
            -> DO_NOTHING

            ProfileScreenViewActions.OnAvatarEdit -> {
                analytics.logAction(EditPicture)
                sendEvent(NavigateToEditItem(WizardScreen.PROFILE_IMAGE))
            }

            ProfileScreenViewActions.OnUsernameCopyClicked -> onUsernameCopyClicked()
            else -> DO_NOTHING
        }
    }

    private fun onUsernameCopyClicked() {
        analytics.logActionWithProps(UsernameCopy)
        sendEvent(CopyUsername)
    }

    private fun trackAvatarClosed(isAvatarGenerated: Boolean) {
        analytics.logAction(
            ProfileAvatarClosed,
            mapOf(
                Parameter.Type to when (isAvatarGenerated) {
                    true -> LOADED
                    false -> LOADING
                }
            )
        )
    }

    private fun onStart() {
        analytics.logScreen(ProfileDetail, mapOf(Parameter.Myself to true))
    }

    private fun onProfileFillStart() {
        viewModelScope.launch {
            val isFullUser = getUserProfileUseCase().firstOrNull()?.userType == UserType.FULL_USER
            if (isFullUser) {
                sendEvent(NavigateToProfileFill)
            } else {
                sendEvent(NavigateToLogin)
            }
        }
    }

    private fun listenToProfile() {
        getUserProfileUseCase()
            .onEach(::updateProfilePrompt)
            .filterNotNull()
            .onEach(::updateProfile)
            .launchIn(viewModelScope)
    }

    private fun updateProfile(userProfile: UserProfile) {
        updateState { state ->
            state.copy(
                displayName = userProfile.displayName.orEmpty(),
                avatarState = userProfile.avatarState,
                bannerImageUrl = userProfile.backgroundURL ?: backgrounds[0],
                ctaButton = userProfile.username.takeIf { !it.isNullOrEmpty() }?.let {
                    CTAButtonType.EDIT
                },
                basicInfo = userProfile.username?.let { username ->
                    BasicInformation(
                        username = username,
                        pronouns = parsePronouns(userProfile.pronouns)
                    )
                },
                socialMetrics = userProfile.schoolMates?.let { mates ->
                    SocialMetricsInformation(
                        bestiePoints = userProfile.bestiePoints ?: 0,
                        totalFriends = mates
                    )
                },
                school = SchoolInformation(
                    name = userProfile.schoolName,
                    isStudent = userProfile.isStudent
                ),
                countryName = userProfile.countryName
            )
        }
        analytics.setUserProperties(
            mapOf(
                Parameter.ProfileCompleted to userProfile.isProfileCompleted(),
                Parameter.SchoolStatus to userProfile.getSchoolStatus()
            )
        )
    }

    private fun updateProfilePrompt(userProfile: UserProfile?) {
        val prompt = userProfile.getProfilePrompt(
            onRegisterPromptStart = {
                analytics.logAction(ProfileRegister)
                onViewAction(OnProfileFillStart)
            },
            onProfilePromptStart = {
                analytics.logAction(ProfileCreate)
                onViewAction(OnProfileFillStart)
            }
        )
        updateState { it.copy(prompt = prompt) }
    }
}
