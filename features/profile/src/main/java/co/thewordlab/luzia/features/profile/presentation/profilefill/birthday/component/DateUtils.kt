package co.thewordlab.luzia.features.profile.presentation.profilefill.birthday.component

import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.snapshots.SnapshotStateMap
import java.time.LocalDate
import java.util.Locale

@Suppress("MagicNumber")
object DateUtils {

    fun localDateToFieldMap(date: LocalDate?): SnapshotStateMap<DateField, DateFieldValue> {
        val map = mutableStateMapOf(
            DateField.Day to DateFieldValue(DateField.Day),
            DateField.Month to DateFieldValue(DateField.Month),
            DateField.Year to DateFieldValue(DateField.Year)
        )
        if (date != null) {
            for (field in map.values) {
                val datePart = when (field.type) {
                    DateField.Day -> date.dayOfMonth
                    DateField.Month -> date.monthValue
                    DateField.Year -> date.year
                }
                val datePartToString = when {
                    getDigitsCount(datePart) < field.type.length ->
                        "0".repeat(field.type.length - getDigitsCount(datePart)) + datePart.toString()

                    else -> datePart.toString()
                }
                for (i in 0 until field.values.size) {
                    val digit = datePartToString[i].digitToInt()
                    field.setValue(i, digit)
                }
            }
        }
        return map
    }

    private fun getDigitsCount(number: Int): Int {
        return when (number) {
            in -9..9 -> 1
            else -> 1 + getDigitsCount(number / 10)
        }
    }

    class Range<T : Comparable<T>> internal constructor(val lower: T, val upper: T) {
        operator fun contains(t: T): Boolean = t in lower..upper

        override fun equals(other: Any?): Boolean {
            var result = false
            if (other == null) {
                return false
            }
            if (this === other) {
                return true
            }
            if (other is Range<*>) {
                if (lower == other.lower && upper == other.upper) {
                    result = true
                }
            }
            return result
        }

        override fun hashCode(): Int {
            return hashCode(lower, upper)
        }

        override fun toString(): String =
            String.format(Locale.getDefault(), "[%s, %s]", lower, upper)

        companion object {
            @JvmStatic
            fun <T : Comparable<T>> create(lower: T, upper: T): Range<T> =
                Range(lower, upper)

            fun <T> hashCode(lower: T, upper: T): Int {
                val hashCodeLower = lower.hashCode()
                val hashCodeUpper = upper.hashCode()
                return (hashCodeLower shl 5) - hashCodeLower xor hashCodeUpper
            }
        }

        init {
            require(lower <= upper) { "lower must be less than or equal to upper" }
        }
    }
}
