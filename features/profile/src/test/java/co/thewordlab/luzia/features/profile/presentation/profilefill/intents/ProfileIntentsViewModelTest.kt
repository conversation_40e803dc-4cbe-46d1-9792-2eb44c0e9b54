package co.thewordlab.luzia.features.profile.presentation.profilefill.intents

import app.cash.turbine.test
import co.thewordlab.luzia.core.profile.data.api.model.UserProfileUpdateRequest
import co.thewordlab.luzia.core.profile.domain.ProfileRepository
import co.thewordlab.luzia.core.profile.domain.model.UserIntent
import co.thewordlab.luzia.core.profile.domain.model.UserProfile
import co.thewordlab.luzia.core.profile.domain.model.UserType
import co.thewordlab.luzia.features.profile.presentation.ProfileWizardV2IntentsError
import co.thewordlab.luzia.features.profile.presentation.STATUS_EMPTY
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.networking.model.AppErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.asSuccess
import co.thewordlab.luzia.foundation.testing.dispatchers.MainDispatcherRule
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Rule
import org.junit.Test

@Suppress("MaxLineLength")
@ExperimentalCoroutinesApi
class ProfileIntentsViewModelTest {

    @MockK
    private lateinit var profileRepository: ProfileRepository

    @MockK
    private lateinit var analytics: Analytics

    private lateinit var sut: ProfileIntentsViewModel

    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()

    @Before
    fun setUp() {
        MockKAnnotations.init(this, relaxed = true)
        sut = ProfileIntentsViewModel(profileRepository, analytics)
    }

    @Test
    fun `given OnCreate action when onViewAction and getUserIntents fails then tracks error and navigates back`() = runTest {
        val error = ResultOf.Failure<List<UserIntent>, AppErrors>(AppErrors.AuthRequired)
        coEvery { profileRepository.getUserIntents() } returns error

        sut.viewEvent.test {
            sut.onViewAction(ProfileIntentsViewActions.OnCreate)
            mainDispatcherRule.advanceUntilIdle()
            verify { analytics.trackEvent(ProfileWizardV2IntentsError) }
            val viewState = sut.viewState.value
            assertFalse(viewState.isLoading)
            assertEquals(ProfileIntentsViewEvents.NavigateBack, awaitItem())
            cancelAndConsumeRemainingEvents()
        }
    }

    @Test
    fun `given OnIntentSelected action when onViewAction then updates selectedIntentId and button state`() = runTest {
        val intentId = "selectedId"

        sut.onViewAction(ProfileIntentsViewActions.OnIntentSelected(intentId))

        val viewState = sut.viewState.value
        assertEquals(intentId, viewState.selectedIntentId)
        assertTrue(viewState.buttonState.isEnabled)
    }

    @Test
    fun `given button onClick when saveIntent and updateUserProfile succeeds then sets user properties and navigates back`() = runTest {
        val selectedIntentId = "testIntentId"
        coEvery { profileRepository.getUserIntents() } returns emptyList<UserIntent>().asSuccess()
        sut.onViewAction(ProfileIntentsViewActions.OnCreate)
        sut.onViewAction(ProfileIntentsViewActions.OnIntentSelected(selectedIntentId))

        val request = UserProfileUpdateRequest(onboardingIntentId = selectedIntentId)
        coEvery { profileRepository.updateUserProfile(request) } returns UserProfile(
            userType = UserType.FULL_USER
        ).asSuccess()

        sut.viewEvent.test {
            sut.viewState.value.buttonState.onClick()
            mainDispatcherRule.advanceUntilIdle()
            verify { analytics.setUserProperties(mapOf(Parameter.UserIntent to selectedIntentId)) }
            assertEquals(ProfileIntentsViewEvents.NavigateBack, awaitItem())
            cancelAndConsumeRemainingEvents()
        }
        assertFalse(sut.viewState.value.buttonState.isLoading)
    }

    @Test
    fun `given button onClick with empty selectedIntentId when saveIntent and updateUserProfile succeeds then sets empty user property and navigates back`() = runTest {
        coEvery { profileRepository.getUserIntents() } returns emptyList<UserIntent>().asSuccess()
        sut.onViewAction(ProfileIntentsViewActions.OnCreate)

        val request = UserProfileUpdateRequest(onboardingIntentId = null)
        coEvery { profileRepository.updateUserProfile(request) } returns UserProfile(
            userType = UserType.FULL_USER
        ).asSuccess()

        sut.viewEvent.test {
            sut.viewState.value.buttonState.onClick()
            mainDispatcherRule.advanceUntilIdle()

            verify { analytics.setUserProperties(mapOf(Parameter.UserIntent to STATUS_EMPTY)) }
            assertEquals(ProfileIntentsViewEvents.NavigateBack, awaitItem())
            cancelAndConsumeRemainingEvents()
        }
        assertFalse(sut.viewState.value.buttonState.isLoading)
    }

    @Test
    fun `given button onClick when saveIntent and updateUserProfile fails then tracks error and navigates back`() = runTest {
        val selectedIntentId = "testIntentId"
        coEvery { profileRepository.getUserIntents() } returns emptyList<UserIntent>().asSuccess()
        sut.onViewAction(ProfileIntentsViewActions.OnCreate)
        sut.onViewAction(ProfileIntentsViewActions.OnIntentSelected(selectedIntentId))

        val request = UserProfileUpdateRequest(onboardingIntentId = selectedIntentId)
        val error = ResultOf.Failure<UserProfile, AppErrors>(AppErrors.AuthRequired)
        coEvery { profileRepository.updateUserProfile(request) } returns error

        sut.viewEvent.test {
            sut.viewState.value.buttonState.onClick()
            mainDispatcherRule.advanceUntilIdle()
            verify { analytics.trackEvent(ProfileWizardV2IntentsError) }
            assertEquals(ProfileIntentsViewEvents.NavigateBack, awaitItem())
            cancelAndConsumeRemainingEvents()
        }
        assertFalse(sut.viewState.value.buttonState.isLoading)
    }
}
