package co.thewordlab.luzia.features.profile.presentation.profilefill.userdescription

import co.thewordlab.luzia.core.profile.domain.ProfileRepository
import co.thewordlab.luzia.core.profile.domain.model.UserProfile
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.networking.model.asSuccess
import co.thewordlab.luzia.foundation.testing.dispatchers.MainDispatcherRule
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Rule
import org.junit.Test

@ExperimentalCoroutinesApi
class ProfileUserDescriptionViewModelTest {

    @MockK
    private lateinit var profileRepository: ProfileRepository

    @MockK
    private lateinit var analytics: Analytics

    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()

    private lateinit var sut: ProfileUserDescriptionViewModel

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        sut = ProfileUserDescriptionViewModel(profileRepository, analytics)
    }

    @Test
    fun `given OnCreate action when onViewAction then updates buttonState`() = runTest {
        sut.onViewAction(ProfileUserDescriptionViewActions.OnCreate)

        val viewState = sut.viewState.value
        assertTrue(viewState.buttonState.isEnabled)
        assertFalse(viewState.buttonState.isLoading)
    }

    @Test
    fun `given OnTextChanged action when onViewAction then updates prompt and buttonState`() =
        runTest {
            val testText = "New description"
            sut.onViewAction(ProfileUserDescriptionViewActions.OnTextChanged(testText))

            val viewState = sut.viewState.value
            assertEquals(testText, viewState.prompt)
            assertFalse(viewState.inputNotValid)
            assertTrue(viewState.buttonState.isEnabled)
        }

    @Test
    fun `given empty text action when onViewAction then updates prompt and buttonState`() =
        runTest {
            val testText = ""
            sut.onViewAction(ProfileUserDescriptionViewActions.OnTextChanged(testText))

            val viewState = sut.viewState.value
            assertEquals(testText, viewState.prompt)
            assertFalse(viewState.inputNotValid)
            assertTrue(viewState.buttonState.isEnabled)
        }

    @Test
    fun `given save click action when invoked then value is updated`() =
        runTest {
            coEvery { profileRepository.updateUserProfile(any()) } returns UserProfile().asSuccess()
            val testText = "New description"
            sut.onViewAction(ProfileUserDescriptionViewActions.OnCreate)
            sut.onViewAction(ProfileUserDescriptionViewActions.OnTextChanged(testText))

            sut.viewState.value.buttonState.onClick()
            mainDispatcherRule.advanceUntilIdle()

            coVerify { profileRepository.updateUserProfile(any()) }
        }

    @Test
    fun `given save click action when invoked then value is not updated`() =
        runTest {
            coEvery { profileRepository.updateUserProfile(any()) } returns UserProfile().asSuccess()
            val testText = ""
            sut.onViewAction(ProfileUserDescriptionViewActions.OnCreate)
            sut.onViewAction(ProfileUserDescriptionViewActions.OnTextChanged(testText))

            sut.viewState.value.buttonState.onClick()

            coVerify(exactly = 0) { profileRepository.updateUserProfile(any()) }
        }
}
