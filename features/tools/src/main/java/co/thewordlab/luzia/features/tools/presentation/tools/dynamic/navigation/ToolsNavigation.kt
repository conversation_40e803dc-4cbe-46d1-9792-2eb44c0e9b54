package co.thewordlab.luzia.features.tools.presentation.tools.dynamic.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.navOptions
import androidx.navigation.toRoute
import co.thewordlab.luzia.core.camera.result.CameraResultProviderView
import co.thewordlab.luzia.core.navigation.camera.CameraAnalysisModel
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.features.tools.presentation.tools.dynamic.DynamicToolsScreen
import co.thewordlab.luzia.features.tools.presentation.tools.dynamic.result.DynamicToolsResultScreen
import co.theworldlab.luzia.foundation.design.system.components.camera.permission.rememberCameraPermissionState
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory

fun NavGraphBuilder.dynamicTools() {
    composable<UserSessionRoutes.DynamicTool> { backstackEntry ->
        val navigation = LocalNavigation.current
        val route = backstackEntry.toRoute<UserSessionRoutes.DynamicTool>()
        val cameraPermission = rememberCameraPermissionState(
            onGranted = {
                navigation.navigate(
                    UserSessionRoutes.Camera(route.toolId, CameraAnalysisModel.DynamicFlow)
                )
            }
        )
        val moshi: Moshi = Moshi.Builder()
            .add(KotlinJsonAdapterFactory())
            .build()
        CameraResultProviderView(backstackEntry) {
            DynamicToolsScreen(
                toolId = route.toolId,
                origin = route.origin,
                onResult = {
                    navigation.navigate(
                        UserSessionRoutes.DynamicToolResult(
                            moshi.adapter(DynamicToolNavInfo::class.java).toJson(it)
                        )
                    )
                },
                onNavigateBack = { navigation.goBack() },
                cameraPermissionState = cameraPermission,
            )
        }
    }

    composable<UserSessionRoutes.DynamicToolResult> { backstackEntry ->
        val navigation = LocalNavigation.current
        val route = backstackEntry.toRoute<UserSessionRoutes.DynamicToolResult>()
        val moshi: Moshi = Moshi.Builder()
            .add(KotlinJsonAdapterFactory())
            .build()
        val navInfo = moshi.adapter(DynamicToolNavInfo::class.java).fromJson(route.configContent)
        val toolId = navInfo?.toolId.orEmpty()
        val origin = navInfo?.origin.orEmpty()
        DynamicToolsResultScreen(
            navInfo = navInfo,
            onResetTool = {
                navigation.navigate(
                    route = UserSessionRoutes.DynamicTool(toolId, origin),
                    options = navOptions {
                        popUpTo(UserSessionRoutes.DynamicTool(toolId, origin)) { inclusive = true }
                    }
                )
            },
            onNavigateBack = {
                navigation.goBackTo(
                    UserSessionRoutes.DynamicTool(toolId, origin),
                    true
                )
            }
        )
    }
}
