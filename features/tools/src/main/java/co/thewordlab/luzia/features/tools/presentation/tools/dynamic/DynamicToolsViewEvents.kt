package co.thewordlab.luzia.features.tools.presentation.tools.dynamic

import co.thewordlab.luzia.features.tools.domain.models.dynamic.ToolComponent
import co.thewordlab.luzia.features.tools.presentation.tools.dynamic.navigation.DynamicToolNavInfo
import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class DynamicToolsViewEvents : ViewEvent {
    data object NavigateBack : DynamicToolsViewEvents()
    data class NavigateToTemplateOptionsSelector(
        val configOption: ToolComponent,
        val optionSelected: String?
    ) : DynamicToolsViewEvents()

    data class NavigateToResult(val config: DynamicToolNavInfo) : DynamicToolsViewEvents()
    data object OpenFilePicker : DynamicToolsViewEvents()
    data object OpenPhotoPicker : DynamicToolsViewEvents()
    data object OpenCamera : DynamicToolsViewEvents()
    data object ShowEmptyContentOnPaste : DynamicToolsViewEvents()
    data object ShowFileTooBig : DynamicToolsViewEvents()
}
