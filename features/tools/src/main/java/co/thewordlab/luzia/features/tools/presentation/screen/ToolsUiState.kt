package co.thewordlab.luzia.features.tools.presentation.screen

import androidx.compose.runtime.Immutable
import co.thewordlab.fouundation.persistence.tool.ToolEntity
import co.thewordlab.luzia.core.gamification.components.pill.GamificationPillUiModel

@Immutable
data class ToolsUiState(
    val tools: List<ToolEntity> = emptyList(),
    val homeTools: List<ToolEntity> = emptyList(),
    val onAction: (ToolsAction) -> Unit = {},
    val showBestiePointsBanner: Boolean = false,
    val showBestiePointsNavigation: Boolean = false,
    val gamificationData: GamificationPillUiModel? = null
)

sealed interface ToolsAction {
    data class TappedOnTool(val item: ToolEntity, val origin: String) : ToolsAction

    data class CloseUpcomingPopup(val item: ToolEntity) : ToolsAction

    data class NotifyUpcomingPopup(val item: ToolEntity) : ToolsAction

    data class CloseUpdatePopup(val item: ToolEntity) : ToolsAction

    data class OpenGooglePlayStoreForUpdate(val item: ToolEntity) : ToolsAction
}

sealed interface ToolsEvent {
    data class NavigateToImagine(val id: String) : ToolsEvent

    data object NavigateToVision : ToolsEvent

    data object NavigateToDocumentTool : ToolsEvent

    data object NavigateToMathTool : ToolsEvent

    data class NavigateToWebTool(val title: String, val link: String) : ToolsEvent

    data class ShowUpcomingPopup(val tool: ToolEntity) : ToolsEvent

    data class ShowUpdatePopup(val tool: ToolEntity) : ToolsEvent
    data object NavigateToSignup : ToolsEvent
    data class NavigateToDynamicTools(val toolId: String) : ToolsEvent
}
