package co.thewordlab.luzia.features.tools.presentation.tools.dynamic.result

import co.thewordlab.luzia.features.tools.domain.models.dynamic.DynamicToolModel
import co.thewordlab.luzia.foundation.architecture.system.ViewState

data class DynamicToolsResultViewState(
    val toolModel: DynamicToolModel? = null,
    val isLoading: Boolean = false,
    val content: String? = null,
    val showFeedback: Boolean = true,
    val isError: <PERSON>olean = false,
    val errorMessage: String = ""
) : ViewState
