package co.thewordlab.luzia.features.tools.presentation.tools.dynamic

import android.content.ActivityNotFoundException
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberTopAppBarState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.platform.ClipboardManager
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.camera.result.LocalCameraResultDelegate
import co.thewordlab.luzia.core.tools.domain.model.ToolSupportedFile
import co.thewordlab.luzia.features.tools.domain.models.dynamic.ComponentType
import co.thewordlab.luzia.features.tools.domain.models.dynamic.DynamicToolModel
import co.thewordlab.luzia.features.tools.domain.models.dynamic.QualifierOption
import co.thewordlab.luzia.features.tools.domain.models.dynamic.ToolComponent
import co.thewordlab.luzia.features.tools.presentation.tools.dynamic.navigation.DynamicToolNavInfo
import co.thewordlab.luzia.features.tools.presentation.tools.dynamic.selector.DynamicToolsOptionsSelector
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.common.extensions.resolveFileInfo
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.camera.permission.CameraPermissionState
import co.theworldlab.luzia.foundation.design.system.components.error.ErrorViewContent
import co.theworldlab.luzia.foundation.design.system.components.navbar.LuziaNavBarDefaults
import co.theworldlab.luzia.foundation.design.system.components.navbar.NavigationAction
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBar
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBarModel
import co.theworldlab.luzia.foundation.design.system.components.scaffold.LocalAppState
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.legacy.composables.BaseContextualMenuOptions
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonFilled
import co.theworldlab.luzia.foundation.design.system.legacy.composables.Loading
import co.theworldlab.luzia.foundation.design.system.legacy.composables.LuziaContextualMenu
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun DynamicToolsScreen(
    toolId: String,
    origin: String,
    onResult: (DynamicToolNavInfo) -> Unit,
    onNavigateBack: () -> Unit,
    cameraPermissionState: CameraPermissionState
) {
    val viewModel: DynamicToolsViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val appState = LocalAppState.current
    val context = LocalContext.current
    val cameraResultDelegate = LocalCameraResultDelegate.current
    val cameraResultViewState by cameraResultDelegate.viewState.collectAsStateWithLifecycle()
    val showOptionSelector =
        remember { mutableStateOf<Pair<ToolComponent, String?>?>(null) }
    val galleryLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.PickVisualMedia()
    ) { uri ->
        uri?.let {
            resolveFileInfo(context, it) { name, size ->
                viewModel.onViewAction(
                    DynamicToolsViewActions.OnImageReceived(it.toString(), name, size)
                )
            }
        }
    }
    val docsLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.OpenDocument()
    ) { uri ->
        uri?.let {
            resolveFileInfo(context, it) { name, size ->
                viewModel.onViewAction(
                    DynamicToolsViewActions.OnDocumentReceived(it.toString(), name, size)
                )
            }
        }
    }

    OnCreate("DynamicToolsScreen") {
        viewModel.onViewAction(DynamicToolsViewActions.OnCreate(toolId, origin))
    }

    ViewModelEventEffect(viewModel) { event ->
        when (event) {
            DynamicToolsViewEvents.NavigateBack ->
                onNavigateBack()

            is DynamicToolsViewEvents.NavigateToTemplateOptionsSelector ->
                showOptionSelector.value = Pair(event.configOption, event.optionSelected)

            is DynamicToolsViewEvents.NavigateToResult ->
                onResult(event.config)

            DynamicToolsViewEvents.OpenCamera ->
                cameraPermissionState.launchCamera()

            DynamicToolsViewEvents.OpenFilePicker ->
                try {
                    docsLauncher.launch(arrayOf(ToolSupportedFile.PDF.mimeType))
                } catch (ex: ActivityNotFoundException) {
                    appState.showSnackBar(context.getString(localizationR.string.generic_error))
                    Log.e("LuziaApp", ex.message.orEmpty())
                }

            DynamicToolsViewEvents.OpenPhotoPicker -> {
                val request = PickVisualMediaRequest.Builder()
                    .setMediaType(ActivityResultContracts.PickVisualMedia.ImageOnly)
                    .build()
                galleryLauncher.launch(request)
            }

            DynamicToolsViewEvents.ShowEmptyContentOnPaste ->
                appState.showSnackBar(context.getString(localizationR.string.dynamic_tools_empty_paste))

            DynamicToolsViewEvents.ShowFileTooBig ->
                appState.showSnackBar(context.getString(localizationR.string.tool_document_limit_size_error))
        }
    }

    LaunchedEffect(cameraResultViewState.cameraResult) {
        cameraResultViewState.cameraResult?.uri?.let { uri ->
            resolveFileInfo(context, uri) { name, size ->
                viewModel.onViewAction(
                    DynamicToolsViewActions.OnImageReceived(uri.toString(), name, size)
                )
            }
        }
    }

    showOptionSelector.value?.let { config ->
        DynamicToolsOptionsSelector(
            configurationOption = config.first,
            optionSelected = config.second,
            onClose = {
                showOptionSelector.value = null
                viewModel.onViewAction(DynamicToolsViewActions.OnCloseOptionsSelector)
            },
            onOptionSelected = { option ->
                showOptionSelector.value = null
                viewModel.onViewAction(
                    DynamicToolsViewActions.OnOptionsSelectorApplied(
                        config.first.id,
                        option
                    )
                )
            }
        )
    }

    ToolsTemplateContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ToolsTemplateContent(
    viewState: DynamicToolsViewState,
    onViewActions: (DynamicToolsViewActions) -> Unit
) {
    val scrollBehavior =
        TopAppBarDefaults.exitUntilCollapsedScrollBehavior(rememberTopAppBarState())
    Scaffold(
        modifier = Modifier.nestedScroll(scrollBehavior.nestedScrollConnection),
        topBar = {
            TopNavigationBar(
                TopNavigationBarModel(
                    colors = LuziaNavBarDefaults.colors(containerColor = LuziaTheme.palette.surface.background),
                    scrollBehavior = scrollBehavior,
                    navigationAction = NavigationAction.Icon(
                        if (viewState.isError) {
                            R.drawable.ic_close
                        } else {
                            R.drawable.ic_back_arrow
                        }
                    ) { onViewActions(DynamicToolsViewActions.OnBackClicked) },
                    actions = if (viewState.hasInputLoaded && viewState.inputText.isNotEmpty()) {
                        listOf(
                            NavigationAction.Text(stringResource(localizationR.string.dynamic_tools_clear_title)) {
                                onViewActions(DynamicToolsViewActions.OnClearClicked)
                            }
                        )
                    } else {
                        emptyList()
                    }
                )
            )
        },
        containerColor = LuziaTheme.palette.surface.background
    ) { innerPadding ->
        viewState.toolModel?.let {
            ToolContent(
                modifier = Modifier.padding(innerPadding),
                templateModel = it,
                viewState = viewState,
                onViewActions = onViewActions
            )
        } ?: run {
            when {
                viewState.isLoading -> LoadingViewContent()
                viewState.isError -> ErrorViewContent(
                    viewState.errorMessage
                ) { onViewActions(DynamicToolsViewActions.OnRetryClicked) }

                else -> EmptyViewContent()
            }
        }
    }
}

@Composable
private fun LoadingViewContent() {
    Loading(showTranslucentBackground = true)
}

@Composable
private fun EmptyViewContent() {
    DO_NOTHING
}

@Composable
private fun ToolContent(
    modifier: Modifier,
    templateModel: DynamicToolModel,
    viewState: DynamicToolsViewState,
    onViewActions: (DynamicToolsViewActions) -> Unit
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .testTag("containerDynamicTools")
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .verticalScroll(rememberScrollState()),
        ) {
            Spacing.X48.Vertical()
            templateModel.body.forEachIndexed { index, component ->
                ComponentContent(
                    viewState = viewState,
                    component = component,
                    index = index,
                    componentCurrentValue = getSelectedOptionForComponent(
                        component,
                        viewState.currentOptionsSelected
                    ),
                    isLastItem = index == templateModel.body.size - 1,
                    onViewActions = onViewActions
                )
            }
        }
        ButtonsContent(
            templateModel.footer.filter { it.type == ComponentType.BUTTON },
            viewState.actionEnabled,
            viewState.isLoading,
            onViewActions
        )
    }
}

private fun getSelectedOptionForComponent(
    component: ToolComponent,
    options: List<OptionSelectedModel>?
): QualifierOption? {
    val optionId = options?.firstOrNull { it.componentId == component.id }?.optionId
    return component.options.firstOrNull { it.id == optionId }
}

@Composable
private fun ButtonsContent(
    buttons: List<ToolComponent>,
    actionsEnabled: Boolean,
    isLoading: Boolean,
    onViewActions: (DynamicToolsViewActions) -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .shadow(Spacing.X16.dp)
            .background(color = LuziaTheme.palette.surface.content)
            .imePadding()
    ) {
        when (buttons.size) {
            1 -> ButtonFilled(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(Spacing.X16.dp)
                    .testTag("buttonContinue"),
                enabled = actionsEnabled,
                isLoading = isLoading,
                onClick = { onViewActions(DynamicToolsViewActions.OnLaunchClicked) },
                buttonText = buttons.first().title
            )

            else -> DO_NOTHING
        }
    }
}

@Composable
private fun ComponentContent(
    viewState: DynamicToolsViewState,
    index: Int,
    component: ToolComponent,
    componentCurrentValue: QualifierOption?,
    isLastItem: Boolean,
    onViewActions: (DynamicToolsViewActions) -> Unit
) {
    when (component.type) {
        ComponentType.MEDIA_INPUT -> {
            InputContent(
                viewState.inputText,
                viewState.inputFileName,
                component.placeholder.orEmpty(),
                component.maxLength,
                onViewActions
            )
            Spacing.X16.Vertical()
        }

        ComponentType.SELECTOR -> SelectorContent(
            component = component,
            index = index,
            showDivider = !isLastItem,
            selectedOption = componentCurrentValue ?: component.options.firstOrNull(),
            onViewActions = onViewActions
        )

        ComponentType.TITLE -> {
            LuziaText(
                modifier = Modifier
                    .fillMaxWidth()
                    .testTag("titleContent"),
                text = component.content.orEmpty(),
                style = LuziaTheme.typography.headlines.h4,
                color = LuziaTheme.palette.text.primary,
                textAlign = TextAlign.Center
            )
            Spacing.X24.Vertical()
        }

        ComponentType.LABEL,
        ComponentType.HTML,
        ComponentType.BUTTON,
        ComponentType.NONE -> DO_NOTHING
    }
}

@Composable
private fun SelectorContent(
    component: ToolComponent,
    index: Int,
    showDivider: Boolean,
    selectedOption: QualifierOption?,
    onViewActions: (DynamicToolsViewActions) -> Unit
) {
    OptionItem(
        componentId = component.id,
        index = index,
        title = component.title,
        optionSelected = selectedOption,
        onViewActions = onViewActions
    )
    if (showDivider) {
        HorizontalDivider(
            modifier = Modifier.padding(start = Spacing.X16.dp),
            color = LuziaTheme.palette.primitives.neutral.neutral50.copy(alpha = 0.3f)
        )
    }
}

@Composable
private fun InputContent(
    inputText: String,
    inputFileName: String,
    hint: String,
    maxLength: Int?,
    onViewActions: (DynamicToolsViewActions) -> Unit
) {
    AnimatedVisibility(inputFileName.isNotEmpty()) {
        FileInputContent(inputFileName, onViewActions)
    }
    AnimatedVisibility(inputFileName.isEmpty()) {
        ManualInputContent(inputText, hint, maxLength, onViewActions)
    }
}

@Composable
private fun FileInputContent(
    inputFileName: String,
    onViewActions: (DynamicToolsViewActions) -> Unit
) {
    val (backgroundColor, primaryTextColor, secondaryTextColor) = if (LuziaTheme.isDarkTheme) {
        listOf(
            LuziaTheme.palette.accents.green.green50,
            LuziaTheme.palette.text.primary,
            LuziaTheme.palette.primitives.neutral.neutral30
        )
    } else {
        listOf(
            LuziaTheme.palette.accents.green.green90,
            LuziaTheme.palette.text.primary,
            LuziaTheme.palette.primitives.neutral.neutral50
        )
    }
    Box(
        modifier = Modifier
            .padding(horizontal = Spacing.X16.dp)
            .fillMaxWidth()
            .background(backgroundColor, RoundedCornerShape(Corners.X4.dp))
            .padding(start = Spacing.X16.dp, bottom = Spacing.X16.dp)
    ) {
        IconButton(
            modifier = Modifier
                .padding(Spacing.X8.dp)
                .size(IconSizes.X32.dp)
                .align(Alignment.TopEnd),
            onClick = { onViewActions(DynamicToolsViewActions.OnClearFileClicked) }
        ) {
            Icon(
                modifier = Modifier.size(IconSizes.X24.dp),
                painter = painterResource(id = R.drawable.ic_close),
                contentDescription = null,
                tint = primaryTextColor
            )
        }
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = Spacing.X24.dp, end = Spacing.X16.dp)
        ) {
            Icon(
                modifier = Modifier.size(IconSizes.X16.dp),
                painter = painterResource(id = R.drawable.ic_file),
                contentDescription = null,
                tint = primaryTextColor
            )
            Spacing.X8.Horizontal()
            Column {
                LuziaText(
                    text = stringResource(localizationR.string.file_uploaded),
                    style = LuziaTheme.typography.body.semiBold.small,
                    color = primaryTextColor
                )
                Spacing.X8.Vertical()
                LuziaText(
                    text = inputFileName,
                    style = LuziaTheme.typography.body.semiBold.small,
                    color = secondaryTextColor
                )
            }
        }
    }
}

@Suppress("LongMethod")
@Composable
private fun ManualInputContent(
    inputText: String,
    hint: String,
    maxLength: Int?,
    onViewActions: (DynamicToolsViewActions) -> Unit
) {
    val textStyle = LuziaTheme.typography.body.semiBold.default
    val textColor = LuziaTheme.palette.text.primary
    val mergedTextStyle = textStyle.merge(TextStyle(color = textColor))
    val clipboardManager: ClipboardManager = LocalClipboardManager.current

    Column(
        modifier = Modifier
            .padding(horizontal = Spacing.X16.dp)
            .fillMaxWidth()
            .background(LuziaTheme.palette.surface.content, RoundedCornerShape(Corners.X4.dp))
            .padding(bottom = Spacing.X16.dp, top = Spacing.X8.dp)
            .testTag("inputContent")
    ) {
        TextField(
            modifier = Modifier
                .fillMaxWidth()
                .testTag("inputField"),
            value = inputText,
            onValueChange = { onViewActions(DynamicToolsViewActions.OnInputTextChanged(it)) },
            textStyle = mergedTextStyle,
            placeholder = {
                LuziaText(
                    text = hint,
                    style = LuziaTheme.typography.body.semiBold.small,
                    color = LuziaTheme.palette.text.helper
                )
            },
            colors = TextFieldDefaults.colors(
                focusedContainerColor = Color.Transparent,
                unfocusedContainerColor = Color.Transparent,
                disabledContainerColor = Color.Transparent,
                errorContainerColor = LuziaTheme.palette.accents.red.error90,
                focusedIndicatorColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent,
                disabledIndicatorColor = Color.Transparent,
            ),
            supportingText = {
                AnimatedVisibility(maxLength != null && maxLength != 0 && inputText.isNotEmpty()) {
                    LuziaText(
                        modifier = Modifier.fillMaxWidth(),
                        text = "${inputText.length}/$maxLength",
                        style = LuziaTheme.typography.body.regular.footnote,
                        color = LuziaTheme.palette.text.helper,
                        textAlign = TextAlign.End
                    )
                }
            }
        )
        AnimatedVisibility(inputText.isEmpty()) {
            Spacing.X16.Vertical()
            val showContextualMenu = remember { mutableStateOf(false) }
            var contextMenuPosition = DpOffset(0.dp, 0.dp)
            val itemHeight = remember { mutableStateOf(0.dp) }
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = Spacing.X16.dp),
                horizontalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
            ) {
                ButtonFilled(
                    modifier = Modifier
                        .wrapContentWidth()
                        .onGloballyPositioned { coordinates ->
                            with(coordinates.positionInRoot()) {
                                contextMenuPosition = DpOffset(this.x.dp, this.y.dp)
                            }
                        },
                    onClick = {
                        showContextualMenu.value = true
                        onViewActions(DynamicToolsViewActions.OnAttachFileClicked)
                    },
                    iconPainter = painterResource(designR.drawable.ic_upload_24),
                    buttonText = stringResource(localizationR.string.dynamic_tools_upload),
                    containerColor = LuziaTheme.palette.interactive.brandLight,
                    contentColor = LuziaTheme.palette.text.brand,
                    containerOnPressedColor = LuziaTheme.palette.interactive.brand.copy(
                        alpha = 0.2f
                    )
                )
                ButtonFilled(
                    modifier = Modifier.wrapContentWidth(),
                    onClick = {
                        with(clipboardManager.getText()?.text) {
                            onViewActions(DynamicToolsViewActions.OnPasteClicked(this))
                        }
                    },
                    iconPainter = painterResource(designR.drawable.ic_copy),
                    buttonText = stringResource(localizationR.string.dynamic_tools_paste),
                    containerColor = LuziaTheme.palette.interactive.brandLight,
                    contentColor = LuziaTheme.palette.text.brand,
                    containerOnPressedColor = LuziaTheme.palette.interactive.brand.copy(
                        alpha = 0.2f
                    )
                )
            }
            if (showContextualMenu.value) {
                LuziaContextualMenu(
                    isVisible = true,
                    pressOffset = contextMenuPosition,
                    itemHeight = itemHeight.value,
                    onDismiss = { showContextualMenu.value = false },
                    dropDownItemList = BaseContextualMenuOptions.attachFileDropDownList(
                        fileInputEnabled = true,
                        onUploadDocument = { onViewActions(DynamicToolsViewActions.OnUploadClicked) },
                        onAttachPhoto = { onViewActions(DynamicToolsViewActions.OnAttachPhotoClicked) },
                        onTakePhoto = { onViewActions(DynamicToolsViewActions.OnTakePhotoClicked) },
                        onDismiss = { showContextualMenu.value = false }
                    )
                )
            }
        }
    }
}

@Composable
private fun OptionItem(
    componentId: String,
    index: Int,
    title: String,
    optionSelected: QualifierOption?,
    onViewActions: (DynamicToolsViewActions) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .click { onViewActions(DynamicToolsViewActions.OnOptionSelected(componentId)) }
            .padding(Spacing.X16.dp)
            .testTag("selector$index"),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
        ) {
            LuziaText(
                text = title,
                style = LuziaTheme.typography.body.regular.small,
                color = LuziaTheme.palette.text.primary
            )
            optionSelected?.let {
                LuziaText(
                    text = optionSelected.title.orEmpty(),
                    style = LuziaTheme.typography.body.semiBold.default,
                    color = LuziaTheme.palette.text.primary
                )
            }
        }
        Icon(
            modifier = Modifier.size(Spacing.X12.dp),
            painter = painterResource(R.drawable.ic_chevron_right),
            tint = LuziaTheme.palette.text.secondary,
            contentDescription = null
        )
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        ToolsTemplateContent(
            viewState = DynamicToolsViewState(),
            onViewActions = { DO_NOTHING }
        )
    }
}
