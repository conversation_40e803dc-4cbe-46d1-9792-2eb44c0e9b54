package co.thewordlab.luzia.features.tools.presentation.tools.math

import android.net.Uri
import androidx.activity.compose.BackHandler
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.layout
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.offset
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.paging.compose.collectAsLazyPagingItems
import co.thewordlab.luzia.core.camera.result.CameraResultViewActions
import co.thewordlab.luzia.core.camera.result.LocalCameraResultDelegate
import co.thewordlab.luzia.core.chat.domain.models.message.LocalSelectedMessageState
import co.thewordlab.luzia.core.chat.presentation.list.ChatListView
import co.thewordlab.luzia.core.chat.presentation.list.model.ChatListOptions
import co.thewordlab.luzia.core.chat.presentation.list.model.ChatListSearchState
import co.thewordlab.luzia.core.sharing.presentation.container.MessageActionsContainer
import co.thewordlab.luzia.features.chat.presentation.details.ChatActions
import co.thewordlab.luzia.features.tools.presentation.tools.common.ToolInfoHeader
import co.thewordlab.luzia.foundation.analytics.Event
import co.thewordlab.luzia.foundation.analytics.LocalAnalytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.analytics.ScreenView
import co.thewordlab.luzia.foundation.architecture.system.OnResume
import co.thewordlab.luzia.foundation.architecture.system.OnStop
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.camera.permission.rememberCameraPermissionState
import co.theworldlab.luzia.foundation.design.system.components.input.AddResourcesConfig
import co.theworldlab.luzia.foundation.design.system.components.input.InputMessage
import co.theworldlab.luzia.foundation.design.system.components.input.InputToolbar
import co.theworldlab.luzia.foundation.design.system.components.input.InputToolbarConfig
import co.theworldlab.luzia.foundation.design.system.components.input.ResourceType
import co.theworldlab.luzia.foundation.design.system.components.navbar.NavBarMainView
import co.theworldlab.luzia.foundation.design.system.components.scaffold.LocalAppState
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialog
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogButton
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogTextDefaults
import co.theworldlab.luzia.foundation.design.system.model.UiImage
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

private const val TOOL = "math"

@Suppress("LongMethod", "CyclomaticComplexMethod")
@Composable
fun MathToolScreen(
    onDismiss: () -> Unit,
    onNavigateToCamera: (openInGallery: Boolean) -> Unit,
    viewModel: MathToolViewModel = hiltViewModel(),
) {
    val messageFileTooBig = stringResource(id = localizationR.string.tool_document_limit_size_error)
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val messages = viewModel.messages.collectAsLazyPagingItems()
    var showDismissDialog by remember { mutableStateOf(false) }
    var showNewFlowDialog by remember { mutableStateOf(false) }
    val analytics = LocalAnalytics.current
    val appState = LocalAppState.current
    val context = LocalContext.current
    val cameraResultDelegate = LocalCameraResultDelegate.current
    val cameraViewState by cameraResultDelegate.viewState.collectAsStateWithLifecycle()
    val cameraPermission = rememberCameraPermissionState(onGranted = { onNavigateToCamera(false) })
    ViewModelEventEffect(events = viewModel) { event ->
        when (event) {
            MathToolViewEvents.ShowDislikedFeedbackMessage ->
                appState.showSnackBar(message = context.getString(localizationR.string.feedback_answer_reject))

            MathToolViewEvents.ShowLikedFeedbackMessage ->
                appState.showSnackBar(
                    message = context.getString(localizationR.string.feedback_answer_approve)
                )

            MathToolViewEvents.OpenCamera -> onNavigateToCamera(false)
            MathToolViewEvents.OpenGallery -> onNavigateToCamera(true)
        }
    }

    OnResume { viewModel.onViewAction(MathToolViewActions.OnResume) }
    OnStop { viewModel.onViewAction(MathToolViewActions.OnStop) }

    DismissInterceptor(
        showDismissDialog = showDismissDialog,
        onDismiss = {
            analytics.logEvent(Event.ExitChat, mapOf(Parameter.Tool to TOOL))
            onDismiss.invoke()
        },
        onShowDialog = {
            if (uiState.shouldShowExitConfirmation) {
                showDismissDialog = true
            } else {
                onDismiss()
            }
        },
        onDismissDialog = { showDismissDialog = false }
    )
    NewFlowInterceptor(
        showNewFlowDialog = showNewFlowDialog,
        onConfirm = {
            showNewFlowDialog = false
            uiState.onNewFlowClicked.invoke()
            cameraPermission.launchCamera()
        },
        onDismissDialog = { showNewFlowDialog = false }
    )
    MathToolEventHandler(
        viewModel = viewModel,
        onNavigateToCamera = {
            if (messages.itemCount > 0) {
                showNewFlowDialog = true
            } else {
                cameraPermission.launchCamera()
            }
        },
        onFileSizeTooBig = { appState.showSnackBar(message = messageFileTooBig) }
    )
    MessageActionsContainer(modifier = Modifier.fillMaxSize()) {
        val shareState = LocalSelectedMessageState.current
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .testTag("containerMaths")
        ) {
            NavBarMainView(
                title = stringResource(id = localizationR.string.tool_maths_title),
                image = null,
                onLeftIconClicked = {
                    if (uiState.shouldShowExitConfirmation) {
                        showDismissDialog = true
                    } else {
                        onDismiss()
                    }
                },
                endSection = takeIf { shareState.isActive }?.let {
                    {
                        TextButton(
                            onClick = {
                                shareState.clear()
                            },
                            content = {
                                LuziaText(
                                    stringResource(localizationR.string.cancel),
                                    LuziaTheme.typography.body.semiBold.footnote,
                                    color = LuziaTheme.palette.interactive.brand
                                )
                            }
                        )
                    }
                }
            )
            ChatListView(
                modifier = Modifier.weight(1f),
                options = ChatListOptions(
                    enableMessagingActions = false,
                    enableFavoriteActions = false,
                    highLightMessageId = null,
                    showIcebreakers = false,
                    searchState = ChatListSearchState()
                ),
                controller = viewModel,
                headerMessageItem = {
                    ToolInfoHeader(
                        modifier =
                        Modifier.layout { measurable, constraints ->
                            val sidePadding = 16.dp
                            val placeable =
                                measurable.measure(constraints.offset(horizontal = sidePadding.roundToPx() * 2))
                            layout(placeable.width, placeable.height) {
                                placeable.place(0, 0)
                            }
                        },
                        uiImage = UiImage.Resource(designR.drawable.luzia_with_background),
                        descriptionRes = localizationR.string.tool_maths_description
                    )
                }
            )
            InputToolbar(
                modifier = Modifier.fillMaxWidth(),
                openKeyboard = false,
                config = InputToolbarConfig(
                    isSendEnabled = uiState.isSendEnabled,
                    actionsEnabled = true,
                    addResourcesConfig = AddResourcesConfig.OnlyPhotos,
                    onSendMessage = {
                        when (it) {
                            is InputMessage.Audio ->
                                uiState.actionSink(ChatActions.SendAudioRecording(it.file))

                            is InputMessage.Text -> {
                                uiState.actionSink(
                                    ChatActions.SendTextMessage(
                                        it.text,
                                        it.imageUri
                                    )
                                )
                            }
                        }
                    },
                    addTooltip = null,
                    cameraResult = cameraViewState.cameraResult,
                    onClearCameraResult = {
                        cameraResultDelegate.onViewAction(CameraResultViewActions.OnClearCameraResult)
                    },
                    onAddResources = {
                        when (it) {
                            ResourceType.Camera -> viewModel.onViewAction(
                                MathToolViewActions.OnPhotoSelected(
                                    openGallery = false
                                )
                            )

                            ResourceType.Gallery -> viewModel.onViewAction(
                                MathToolViewActions.OnPhotoSelected(
                                    openGallery = true
                                )
                            )

                            ResourceType.File -> DO_NOTHING
                        }
                    }
                )
            )
        }
    }
}

@Composable
private fun DismissInterceptor(
    showDismissDialog: Boolean,
    onShowDialog: () -> Unit,
    onDismissDialog: () -> Unit,
    onDismiss: () -> Unit,
) {
    val analytics = LocalAnalytics.current
    LaunchedEffect(key1 = showDismissDialog) {
        if (showDismissDialog) {
            analytics.logScreenView(
                ScreenView.ExitChatPopup,
                mapOf(Parameter.Tool to TOOL)
            )
        }
    }
    if (showDismissDialog) {
        LuziaAlertDialog(
            confirmButton =
            LuziaAlertDialogButton(
                stringResource(id = localizationR.string.button_exit),
                action = onDismiss,
                isPrimaryAction = true
            ),
            dismissButton =
            LuziaAlertDialogButton(
                stringResource(id = localizationR.string.cancel),
                action = onDismissDialog
            ),
            title =
            LuziaAlertDialogText(
                stringResource(id = localizationR.string.tool_document_dismiss_title),
                LuziaAlertDialogTextDefaults.Title()
            ),
            text =
            LuziaAlertDialogText(
                stringResource(id = localizationR.string.tool_maths_dismiss_description),
                LuziaAlertDialogTextDefaults.Description()
            )
        )
    }
    BackHandler(onBack = onShowDialog)
}

@Composable
private fun NewFlowInterceptor(
    showNewFlowDialog: Boolean,
    onDismissDialog: () -> Unit,
    onConfirm: () -> Unit,
) {
    val analytics = LocalAnalytics.current
    LaunchedEffect(key1 = showNewFlowDialog) {
        if (showNewFlowDialog) {
            analytics.logScreenView(
                ScreenView.ExitChatPopup,
                mapOf(Parameter.Tool to TOOL)
            )
        }
    }
    if (showNewFlowDialog) {
        LuziaAlertDialog(
            confirmButton =
            LuziaAlertDialogButton(
                stringResource(id = localizationR.string.tools_maths_restart_ok_cta),
                action = onConfirm,
                isPrimaryAction = true
            ),
            dismissButton =
            LuziaAlertDialogButton(
                stringResource(id = localizationR.string.cancel),
                action = onDismissDialog
            ),
            title =
            LuziaAlertDialogText(
                stringResource(id = localizationR.string.tool_maths_restart_title),
                LuziaAlertDialogTextDefaults.Title()
            ),
            text =
            LuziaAlertDialogText(
                stringResource(id = localizationR.string.tool_maths_restart_description),
                LuziaAlertDialogTextDefaults.Description()
            )
        )
    }
}

@Composable
private fun MathToolEventHandler(
    viewModel: MathToolViewModel,
    onNavigateToCamera: () -> Unit,
    onFileSizeTooBig: suspend () -> Unit,
) {
    LaunchedEffect(Unit) {
        viewModel.events.collect { list ->
            list.forEach {
                when (it) {
                    MathToolEvent.ShowCameraFlow -> onNavigateToCamera()
                    MathToolEvent.FileSizeTooBig -> onFileSizeTooBig()
                }
            }
            viewModel.consumeEvents()
        }
    }
}

private fun ManagedActivityResultLauncher<PickVisualMediaRequest, Uri?>.openGallery() {
    val request = PickVisualMediaRequest.Builder()
        .setMediaType(ActivityResultContracts.PickVisualMedia.ImageOnly)
        .build()
    launch(request)
}
