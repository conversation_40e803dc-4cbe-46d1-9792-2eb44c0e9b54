package co.thewordlab.luzia.features.tools.presentation.tools.math

import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class MathToolViewActions : ViewAction {
    data object OnResume : MathToolViewActions()
    data object OnStop : MathToolViewActions()
    data object OnMaxPromptLengthMessageShown : MathToolViewActions()
    data class OnPhotoSelected(val openGallery: Boolean) : MathToolViewActions()
}
