package co.thewordlab.luzia.features.tools.presentation.tools.dynamic.result

import co.thewordlab.luzia.features.tools.presentation.tools.dynamic.navigation.DynamicToolNavInfo
import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class DynamicToolsResultViewActions : ViewAction {
    data class OnCreate(val navInfo: DynamicToolNavInfo?) : DynamicToolsResultViewActions()
    data object OnBackClicked : DynamicToolsResultViewActions()
    data object OnShareClicked : DynamicToolsResultViewActions()
    data class OnButtonClicked(val componentId: String) : DynamicToolsResultViewActions()
    data object OnRetryClicked : DynamicToolsResultViewActions()
    data object OnLiked : DynamicToolsResultViewActions()
    data object OnDisliked : DynamicToolsResultViewActions()
}
