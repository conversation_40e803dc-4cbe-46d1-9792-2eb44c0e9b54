package co.thewordlab.luzia.features.tools.presentation.tools.dynamic

import co.thewordlab.luzia.features.tools.domain.models.dynamic.DynamicToolModel
import co.thewordlab.luzia.foundation.architecture.system.ViewState

data class DynamicToolsViewState(
    val toolModel: DynamicToolModel? = null,
    val currentOptionsSelected: List<OptionSelectedModel>? = null,
    val isLoading: Boolean = false,
    val hasInputLoaded: Boolean = false,
    val inputText: String = "",
    val inputFileName: String = "",
    val actionEnabled: Boolean = false,
    val isError: Boolean = false,
    val errorMessage: String = "",
) : ViewState

data class OptionSelectedModel(
    val componentId: String,
    val optionId: String
)
