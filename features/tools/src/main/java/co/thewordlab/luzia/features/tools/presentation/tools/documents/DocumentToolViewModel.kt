package co.thewordlab.luzia.features.tools.presentation.tools.documents

import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import co.thewordlab.fouundation.persistence.chat.ChatMetadata
import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.fouundation.persistence.chat.MessageType
import co.thewordlab.luzia.core.chat.analytics.SendMessageEventHelper
import co.thewordlab.luzia.core.chat.domain.ChatController
import co.thewordlab.luzia.core.chat.presentation.search.SearchViewState
import co.thewordlab.luzia.core.feedback.domain.FeedbackSource
import co.thewordlab.luzia.core.tools.domain.repository.DocumentToolRepository
import co.thewordlab.luzia.features.chat.MaxPromptLimitReached
import co.thewordlab.luzia.features.chat.presentation.details.ChatActions
import co.thewordlab.luzia.features.personality.domain.LUZIA_ID_PERSONALITY
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Event
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.common.extensions.cachedIn
import co.thewordlab.luzia.foundation.common.haptic.HapticFeedbackManager
import co.thewordlab.luzia.foundation.files.importing.FileImporter
import co.thewordlab.luzia.foundation.messages.data.model.asEvent
import co.thewordlab.luzia.foundation.messages.di.InMemory
import co.thewordlab.luzia.foundation.networking.model.AppErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.getDataOrNull
import co.theworldlab.luzia.foundation.design.system.events.AppEventContainer
import co.theworldlab.luzia.foundation.design.system.events.EventsHandler
import co.theworldlab.luzia.foundation.design.system.events.EventsHandlerImpl
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class DocumentToolViewModel @Inject constructor(
    @InMemory private val documentToolRepository: DocumentToolRepository,
    private val hapticFeedbackManager: HapticFeedbackManager,
    private val fileImporter: FileImporter,
    private val dispatcher: CoroutineDispatcher,
    private val analytics: Analytics,
    private val appEventContainer: AppEventContainer,
    private val sendMessageEventHelper: SendMessageEventHelper
) : ViewModel(),
    ChatController,
    EventsHandler<DocumentToolEvent> by EventsHandlerImpl(),
    ViewModelActions<DocumentToolViewActions>,
    ViewModelEvents<DocumentToolViewEvents> by ViewModelEventsImpl(),
    SendMessageEventHelper by sendMessageEventHelper {

    private val mutableState: MutableStateFlow<DocumentToolUiState> =
        MutableStateFlow(
            DocumentToolUiState(
                actionSink = ::onAction,
                onUserTappedUpload = ::onUserTappedUpload,
                onDocumentSelected = ::onDocumentSelected
            )
        )

    private val isAiResponding = MutableStateFlow(false)

    private val uploadState = MutableStateFlow(DocumentUploadState.NONE)

    private val iceBreakers = MutableStateFlow<List<String>>(emptyList())

    private val messages = MutableStateFlow<PagingData<MessageEntity>>(PagingData.empty())

    private var shouldShowExitConfirmation = false
    private var attachedFileId: String? = null

    val uiState: StateFlow<DocumentToolUiState> =
        combine(
            isAiResponding,
            uploadState,
            iceBreakers,
            mutableState
        ) { isAiResponding, uploadState, iceBreakers, state ->
            state.copy(
                isSendEnabled = !isAiResponding && uploadState == DocumentUploadState.SUCCESS,
                isUploading = uploadState == DocumentUploadState.UPLOADING,
                iceBreakers = if (state.file != null && uploadState == DocumentUploadState.SUCCESS) {
                    iceBreakers
                } else {
                    emptyList()
                },
                shouldShowExitConfirmation = shouldShowExitConfirmation
            )
        }.cachedIn(
            viewModelScope,
            DocumentToolUiState(
                actionSink = ::onAction,
                onUserTappedUpload = ::onUserTappedUpload,
                onDocumentSelected = ::onDocumentSelected
            )
        )

    init {
        loadIcebreakers()
        observeMessages()
    }

    private fun observeMessages() = viewModelScope.launch {
        documentToolRepository.getMessages(LUZIA_ID_PERSONALITY)
            .cachedIn(viewModelScope)
            .collect { messages.value = it }
    }

    override fun onViewAction(action: DocumentToolViewActions) {
        when (action) {
            DocumentToolViewActions.OnResume -> clearMessagesMetrics()
            DocumentToolViewActions.OnStop -> sendLastMessageMetrics()
            DocumentToolViewActions.OnMaxPromptLengthMessageShown ->
                analytics.logEvent(
                    MaxPromptLimitReached,
                    mapOf(Parameter.Tool to TOOL)
                )
        }
    }

    override fun getFeedbackSource(): FeedbackSource {
        return FeedbackSource.Document
    }

    override suspend fun textToSpeech(text: String): ResultOf<Uri, AppErrors> {
        // TODO: will be done in next PR
        return ResultOf.Failure(AppErrors.Unknown)
    }

    private fun loadIcebreakers() = viewModelScope.launch {
        val result = documentToolRepository.getIceBreakers()
        if (result is ResultOf.Success) {
            iceBreakers.update { result.data.iceBreakers }
        }
    }

    private fun onAction(action: ChatActions) {
        when (action) {
            is ChatActions.SendTextMessage -> sendTextMessage(action)
            is ChatActions.SendAudioRecording -> sendAudioRecording(action)
            is ChatActions.PerformHaptic -> performFeedback()
        }
    }

    private fun onDocumentSelected(uri: Uri) {
        analytics.logEvent(Event.ItemUpload, mapOf(Parameter.Tool to TOOL))
        viewModelScope.launch(dispatcher) {
            uploadState.update { DocumentUploadState.UPLOADING }
            val file = fileImporter.resolveFile(uri, "File_", ".pdf")
            if (file != null) {
                val isFileSizeAcceptable = file.length() <= FILE_SIZE_LIMIT
                if (isFileSizeAcceptable) {
                    val result =
                        documentToolRepository.sendFileForDocumentTool(
                            personalityId = LUZIA_ID_PERSONALITY,
                            file = file,
                            insertDocumentInChatAfterNextMessage = false
                        )
                    logMessageEvent(MessageType.File)
                    result.asEvent()?.let(appEventContainer::sendEvent)
                    when (result) {
                        is ResultOf.Failure -> {
                            uploadState.update { DocumentUploadState.ERROR }
                        }

                        is ResultOf.Success -> {
                            shouldShowExitConfirmation = true
                            attachedFileId = result.data.attachmentId
                            uploadState.update { DocumentUploadState.SUCCESS }
                        }
                    }
                    mutableState.update { it.copy(file = file) }
                } else {
                    uploadState.update { DocumentUploadState.NONE }
                    sendEvent(DocumentToolEvent.FileSizeTooBig)
                }
            }
        }
    }

    private fun onUserTappedUpload() {
        analytics.logEvent(Event.UploadFileButtonTap, mapOf(Parameter.Tool to TOOL))
        sendEvent(DocumentToolEvent.ShowFilePicker)
    }

    private fun sendTextMessage(action: ChatActions.SendTextMessage) {
        viewModelScope.launch {
            isAiResponding.update { true }
            logMessageEvent(MessageType.Text)
            val result = documentToolRepository.sendQuestionForDocumentTool(
                LUZIA_ID_PERSONALITY,
                attachedFileId,
                action.text
            )
            result.asEvent()?.let(appEventContainer::sendEvent)
            isAiResponding.update { false }
        }
    }

    private fun sendAudioRecording(action: ChatActions.SendAudioRecording) {
        viewModelScope.launch {
            isAiResponding.update { true }
            logMessageEvent(MessageType.AudioRecord)
            analytics.logEvent(Event.VoiceChat, getEventProperties(MessageType.AudioRecord))
            val result = documentToolRepository.sendAudioRecordingForDocument(
                LUZIA_ID_PERSONALITY,
                attachedFileId,
                action.file
            )
            result.asEvent()?.let(appEventContainer::sendEvent)
            isAiResponding.update { false }
        }
    }

    private fun logMessageEvent(messageType: MessageType) {
        sendMessageMetrics(getEventProperties(messageType).toMutableMap())
    }

    private fun getEventProperties(messageType: MessageType): Map<Parameter, String> {
        return mapOf(
            Parameter.Personality to PERSONALITY,
            Parameter.Tool to TOOL,
            Parameter.Type to messageType.property
        )
    }

    private fun performFeedback() {
        viewModelScope.launch { hapticFeedbackManager.shot() }
    }

    private companion object {
        const val PERSONALITY = "tool"
        const val TOOL = "document"
        const val FILE_SIZE_LIMIT = 20 * 1024 * 1024
    }

    override val searchState: Flow<SearchViewState<MessageEntity>> = flowOf(SearchViewState())

    override fun getChatMessages(): Flow<PagingData<MessageEntity>> {
        return messages
    }

    override fun getChatMetadata(): Flow<ChatMetadata?> {
        return documentToolRepository.getChatMetadata(LUZIA_ID_PERSONALITY)
    }

    override fun hasUnreadMessages(): Flow<Boolean> = flowOf(false)

    override suspend fun getIceBreakers(): Flow<List<String>> =
        flowOf(documentToolRepository.getIceBreakers().getDataOrNull()?.iceBreakers.orEmpty())

    override suspend fun sendTextMessage(text: String) {
        if (uploadState.value != DocumentUploadState.UPLOADING) {
            sendTextMessage(ChatActions.SendTextMessage(text, null))
        }
    }

    override suspend fun resendMessage(message: MessageEntity) {
        viewModelScope.launch {
            isAiResponding.update { true }
            logMessageEvent(message.messageType)
            val result = documentToolRepository.resendMessage(attachedFileId, message)
            result.asEvent()?.let(appEventContainer::sendEvent)
            isAiResponding.update { false }
        }
    }

    override suspend fun updateMessage(messageId: Long, text: String) {
        DO_NOTHING
    }

    override suspend fun dismissFeedback(feedbackId: String) {
        documentToolRepository.dismissFeedback(feedbackId)
    }
}
