@file:OptIn(ExperimentalComposeUiApi::class)

package co.thewordlab.luzia.features.tools.presentation.tools.dynamic.selector

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.tooling.preview.Preview
import co.thewordlab.luzia.features.tools.domain.models.dynamic.QualifierOption
import co.thewordlab.luzia.features.tools.domain.models.dynamic.ToolComponent
import co.thewordlab.luzia.features.tools.domain.models.dynamic.buildSummarizeTool
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.selection.LuziaRadioIcon
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import kotlinx.coroutines.launch
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

private const val WEIGHT = 0.8f

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DynamicToolsOptionsSelector(
    configurationOption: ToolComponent,
    optionSelected: String?,
    onClose: () -> Unit,
    onOptionSelected: (QualifierOption) -> Unit
) {
    val bottomSheetState = rememberModalBottomSheetState(true)
    val coroutineScope = rememberCoroutineScope()
    fun closeSheet() {
        coroutineScope.launch {
            bottomSheetState.hide()
            onClose()
        }
    }
    LaunchedEffect(Unit) { coroutineScope.launch { bottomSheetState.expand() } }
    ModalBottomSheet(
        modifier = Modifier.statusBarsPadding(),
        sheetState = bottomSheetState,
        containerColor = LuziaTheme.palette.surface.background,
        dragHandle = null,
        onDismissRequest = { closeSheet() },
    ) {
        DynamicToolsOptionContent(
            configurationOption,
            configurationOption.options.firstOrNull { it.id == optionSelected }
                ?: configurationOption.options.first(),
            onOptionSelected,
            ::closeSheet
        )
    }
}

@Composable
private fun BoxScope.CloseButton(onClose: () -> Unit) {
    IconButton(
        modifier = Modifier
            .align(Alignment.TopStart)
            .semantics { testTagsAsResourceId = true }
            .testTag("buttonSelectorClose"),
        onClick = onClose,
        content = {
            Icon(
                painter = painterResource(designR.drawable.ic_close),
                contentDescription = null,
                tint = LuziaTheme.palette.text.primary
            )
        }
    )
}

@Composable
private fun BoxScope.ApplyButton(onApply: () -> Unit) {
    ButtonText(
        modifier = Modifier.align(Alignment.TopEnd),
        buttonText = stringResource(localizationR.string.dynamic_tools_apply),
        onClick = onApply,
        contentColor = LuziaTheme.palette.text.primary
    )
}

@Composable
private fun DynamicToolsOptionContent(
    configurationOption: ToolComponent,
    optionSelected: QualifierOption,
    onOptionSelected: (QualifierOption) -> Unit,
    onClose: () -> Unit
) {
    val optionToSelect = remember { mutableStateOf(optionSelected) }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(vertical = Spacing.X16.dp)
            .semantics { testTagsAsResourceId = true }
            .testTag("dynamicToolsSelectorContent")
    ) {
        Spacing.X8.Vertical()
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = Spacing.X8.dp, end = Spacing.X16.dp)
        ) {
            CloseButton(onClose = onClose)
            ApplyButton(onApply = { onOptionSelected(optionToSelect.value) })
        }
        Spacing.X16.Vertical()
        LuziaText(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = Spacing.X16.dp),
            text = configurationOption.title,
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScroll(rememberScrollState())
        ) {
            Spacing.X16.Vertical()
            configurationOption.options.forEach { option ->
                OptionItem(
                    item = option,
                    isSelected = option == optionToSelect.value,
                    onOptionSelected = { optionToSelect.value = it }
                )
            }
        }
    }
}

@Composable
private fun OptionItem(
    item: QualifierOption,
    isSelected: Boolean,
    onOptionSelected: (QualifierOption) -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .click { onOptionSelected(item) }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = Spacing.X8.dp, horizontal = Spacing.X16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Spacing.X16.dp)
        ) {
            Column(modifier = Modifier.weight(WEIGHT)) {
                LuziaText(
                    text = item.title.orEmpty(),
                    style = LuziaTheme.typography.body.semiBold.default,
                    color = LuziaTheme.palette.text.primary
                )
                item.description?.let {
                    LuziaText(
                        text = it,
                        style = LuziaTheme.typography.body.regular.default,
                        color = LuziaTheme.palette.text.helper
                    )
                }
            }
            LuziaRadioIcon(
                selected = isSelected,
                onClick = { onOptionSelected(item) }
            )
        }
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        Column(modifier = Modifier.background(LuziaTheme.palette.surface.background)) {
            with(buildSummarizeTool()) {
                DynamicToolsOptionContent(
                    configurationOption = this.body.first(),
                    optionSelected = this.body.first().options.first(),
                    onOptionSelected = { DO_NOTHING },
                    onClose = { DO_NOTHING }
                )
            }
        }
    }
}
