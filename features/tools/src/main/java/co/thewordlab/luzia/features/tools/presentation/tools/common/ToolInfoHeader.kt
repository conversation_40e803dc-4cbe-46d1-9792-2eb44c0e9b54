package co.thewordlab.luzia.features.tools.presentation.tools.common

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.model.UiImage
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import coil3.compose.AsyncImage

@Composable
fun ToolInfoHeader(modifier: Modifier = Modifier, uiImage: UiImage, @StringRes descriptionRes: Int) {
    Row(
        modifier =
        modifier
            .background(LuziaTheme.palette.surface.content)
            .padding(horizontal = 13.dp, vertical = 10.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        AsyncImage(
            modifier = Modifier.size(40.dp),
            model =
            when (uiImage) {
                is UiImage.Plain -> uiImage.url
                is UiImage.Resource -> uiImage.resourceId
            },
            contentDescription = stringResource(id = descriptionRes)
        )
        LuziaText(
            text = stringResource(id = descriptionRes),
            color = LuziaTheme.palette.text.primary,
            style = LuziaTheme.typography.body.semiBold.small
        )
    }
}
