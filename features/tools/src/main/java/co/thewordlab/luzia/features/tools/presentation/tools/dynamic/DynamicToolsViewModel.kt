package co.thewordlab.luzia.features.tools.presentation.tools.dynamic

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.tools.DynamicToolAttachmentClicked
import co.thewordlab.luzia.core.tools.DynamicToolInputButtonClicked
import co.thewordlab.luzia.core.tools.DynamicToolSubmitClicked
import co.thewordlab.luzia.core.tools.DynamicTools
import co.thewordlab.luzia.core.tools.INPUT_TYPE_ATTACHMENT
import co.thewordlab.luzia.core.tools.INPUT_TYPE_CAMERA
import co.thewordlab.luzia.core.tools.INPUT_TYPE_FILES
import co.thewordlab.luzia.core.tools.INPUT_TYPE_GALLERY
import co.thewordlab.luzia.core.tools.INPUT_TYPE_PASTE
import co.thewordlab.luzia.core.tools.PARAMETER_INPUT
import co.thewordlab.luzia.core.tools.PARAMETER_TOOL
import co.thewordlab.luzia.core.tools.PARAMETER_TYPE
import co.thewordlab.luzia.core.tools.buildParamsForLaunchAction
import co.thewordlab.luzia.core.tools.domain.model.DynamicToolComponentType
import co.thewordlab.luzia.core.tools.domain.model.DynamicToolConfig
import co.thewordlab.luzia.core.tools.domain.model.ToolSupportedFile
import co.thewordlab.luzia.core.tools.domain.repository.ToolsRepository
import co.thewordlab.luzia.features.tools.domain.mappers.mapToModel
import co.thewordlab.luzia.features.tools.domain.models.dynamic.ComponentType
import co.thewordlab.luzia.features.tools.domain.models.dynamic.DynamicToolModel
import co.thewordlab.luzia.features.tools.presentation.tools.dynamic.navigation.DynamicToolNavInfo
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class DynamicToolsViewModel @Inject constructor(
    private val toolsRepository: ToolsRepository,
    private val analytics: Analytics
) :
    ViewModel(),
    ViewModelActions<DynamicToolsViewActions>,
    ViewModelEvents<DynamicToolsViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<DynamicToolsViewState> by ViewModelStatesImpl(DynamicToolsViewState()) {

    private var toolId: String? = null
    private var origin: String? = null
    private var currentFileUri: String = ""
    private var currentFileInfo: ToolSupportedFile = ToolSupportedFile.NONE

    @Suppress("CyclomaticComplexMethod", "LongMethod")
    override fun onViewAction(action: DynamicToolsViewActions) {
        when (action) {
            is DynamicToolsViewActions.OnCreate -> {
                toolId = action.toolId
                origin = action.origin
                analytics.logScreenWithProps(DynamicTools, mapOf(PARAMETER_TOOL to action.toolId))
                requestToolConfiguration(toolId)
            }

            DynamicToolsViewActions.OnBackClicked ->
                sendEvent(DynamicToolsViewEvents.NavigateBack)

            is DynamicToolsViewActions.OnOptionSelected ->
                onOptionSelected(action.componentId)

            is DynamicToolsViewActions.OnPasteClicked -> {
                analytics.logActionWithProps(
                    DynamicToolInputButtonClicked,
                    mapOf(
                        PARAMETER_TOOL to toolId.orEmpty(),
                        PARAMETER_INPUT to INPUT_TYPE_PASTE
                    )
                )
                onPasteClicked(action.text)
            }

            DynamicToolsViewActions.OnAttachFileClicked ->
                analytics.logActionWithProps(
                    DynamicToolInputButtonClicked,
                    mapOf(
                        PARAMETER_TOOL to toolId.orEmpty(),
                        PARAMETER_INPUT to INPUT_TYPE_ATTACHMENT
                    )
                )

            DynamicToolsViewActions.OnUploadClicked -> {
                analytics.logActionWithProps(
                    DynamicToolAttachmentClicked,
                    mapOf(
                        PARAMETER_TOOL to toolId.orEmpty(),
                        PARAMETER_TYPE to INPUT_TYPE_FILES
                    )
                )
                sendEvent(DynamicToolsViewEvents.OpenFilePicker)
            }

            DynamicToolsViewActions.OnLaunchClicked ->
                navigateToResult()

            DynamicToolsViewActions.OnCloseOptionsSelector -> {
                DO_NOTHING
            }

            is DynamicToolsViewActions.OnOptionsSelectorApplied -> {
                onConfigOptionSelected(action)
            }

            DynamicToolsViewActions.OnClearClicked -> {
                updateState {
                    it.copy(
                        hasInputLoaded = false,
                        inputText = "",
                        inputFileName = "",
                        actionEnabled = false
                    )
                }
            }

            DynamicToolsViewActions.OnClearFileClicked -> {
                updateState {
                    it.copy(
                        hasInputLoaded = false,
                        inputFileName = "",
                        actionEnabled = false
                    )
                }
            }

            is DynamicToolsViewActions.OnInputTextChanged -> {
                updateState {
                    it.copy(
                        hasInputLoaded = action.text.isNotEmpty(),
                        inputText = action.text,
                        actionEnabled = action.text.isNotEmpty()
                    )
                }
            }

            DynamicToolsViewActions.OnAttachPhotoClicked -> {
                analytics.logActionWithProps(
                    DynamicToolAttachmentClicked,
                    mapOf(
                        PARAMETER_TOOL to toolId.orEmpty(),
                        PARAMETER_TYPE to INPUT_TYPE_GALLERY
                    )
                )

                sendEvent(DynamicToolsViewEvents.OpenPhotoPicker)
            }

            DynamicToolsViewActions.OnTakePhotoClicked -> {
                analytics.logActionWithProps(
                    DynamicToolAttachmentClicked,
                    mapOf(
                        PARAMETER_TOOL to toolId.orEmpty(),
                        PARAMETER_TYPE to INPUT_TYPE_CAMERA
                    )
                )
                sendEvent(DynamicToolsViewEvents.OpenCamera)
            }

            is DynamicToolsViewActions.OnDocumentReceived ->
                handleFileUri(action.docUri, action.name, action.size, ToolSupportedFile.PDF)

            is DynamicToolsViewActions.OnImageReceived ->
                handleFileUri(action.imageUri, action.name, action.size, ToolSupportedFile.PHOTO)

            DynamicToolsViewActions.OnRetryClicked ->
                requestToolConfiguration(toolId)
        }
    }

    private fun onConfigOptionSelected(action: DynamicToolsViewActions.OnOptionsSelectorApplied) {
        val options = viewState.value.currentOptionsSelected.orEmpty().map {
            if (it.componentId == action.componentId) {
                OptionSelectedModel(it.componentId, action.option.id.orEmpty())
            } else {
                it
            }
        }
        updateState { it.copy(currentOptionsSelected = options) }
    }

    private fun navigateToResult() {
        with(viewState.value) {
            val selectedOptions = buildSelectedOptions()
            val config = DynamicToolNavInfo(
                toolId = toolModel?.id.orEmpty(),
                fileUri = currentFileUri,
                fileInfo = currentFileInfo,
                config = selectedOptions,
                origin = origin.orEmpty()
            )
            analytics.logActionWithProps(
                DynamicToolSubmitClicked,
                buildParamsForLaunchAction(toolId.orEmpty(), origin.orEmpty(), selectedOptions)
            )
            sendEvent(DynamicToolsViewEvents.NavigateToResult(config))
        }
    }

    private fun buildSelectedOptions(): List<DynamicToolConfig> {
        val options = viewState.value.currentOptionsSelected?.map {
            DynamicToolConfig(
                componentId = it.componentId,
                selectedOption = it.optionId,
                type = DynamicToolComponentType.SELECTOR
            )
        }?.toMutableList() ?: mutableListOf()

        val inputId =
            viewState.value.toolModel?.body?.firstOrNull { it.type == ComponentType.MEDIA_INPUT }?.id
        if (inputId != null && viewState.value.inputText.isNotEmpty()) {
            options.add(
                DynamicToolConfig(
                    componentId = inputId,
                    content = viewState.value.inputText,
                    type = DynamicToolComponentType.MEDIA_INPUT
                )
            )
        }
        return options
    }

    private fun requestToolConfiguration(toolId: String?) = viewModelScope.launch {
        toolId?.let {
            updateState { it.copy(isLoading = true, isError = false) }
            when (val result = toolsRepository.getDynamicTool(toolId)) {
                is ResultOf.Failure ->
                    showError(result.error.description)

                is ResultOf.Success -> {
                    val toolModel = result.data.mapToModel()
                    updateState {
                        it.copy(
                            toolModel = toolModel,
                            currentOptionsSelected = extractDefaultSelectedOptions(toolModel),
                            isLoading = false
                        )
                    }
                }
            }
        } ?: showError("")
    }

    private fun showError(message: String) {
        updateState {
            it.copy(
                isError = true,
                errorMessage = message,
                isLoading = false
            )
        }
    }

    private fun extractDefaultSelectedOptions(toolModel: DynamicToolModel) =
        toolModel.body
            .filter { it.type == ComponentType.SELECTOR }
            .map { OptionSelectedModel(it.id, it.options.firstOrNull()?.id.orEmpty()) }

    private fun handleFileUri(
        fileUri: String,
        fileName: String,
        size: Long,
        fileInfo: ToolSupportedFile
    ) {
        val isFileSizeAcceptable = size <= MAX_FILE_SIZE_ALLOWED
        if (isFileSizeAcceptable) {
            currentFileUri = fileUri
            currentFileInfo = fileInfo
            updateState {
                it.copy(hasInputLoaded = true, inputFileName = fileName, actionEnabled = true)
            }
        } else {
            updateState {
                it.copy(hasInputLoaded = false, inputFileName = "", actionEnabled = false)
            }
            sendEvent(DynamicToolsViewEvents.ShowFileTooBig)
        }
    }

    private fun onPasteClicked(text: String?) {
        if (!text.isNullOrEmpty()) {
            updateState { it.copy(hasInputLoaded = true, inputText = text, actionEnabled = true) }
        } else {
            sendEvent(DynamicToolsViewEvents.ShowEmptyContentOnPaste)
        }
    }

    private fun onOptionSelected(componentId: String) {
        with(viewState.value) {
            val component =
                toolModel?.body?.firstOrNull { it.id == componentId }
            val optionSelected =
                currentOptionsSelected?.firstOrNull { it.componentId == componentId }?.optionId
            component?.let {
                sendEvent(
                    DynamicToolsViewEvents.NavigateToTemplateOptionsSelector(
                        optionSelected = optionSelected,
                        configOption = it
                    )
                )
            }
        }
    }

    private companion object {
        const val MAX_FILE_SIZE_ALLOWED = 20 * 1024 * 1024
    }
}
