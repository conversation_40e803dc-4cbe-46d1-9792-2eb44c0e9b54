package co.thewordlab.luzia.features.tools.presentation.tools.math

import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import co.thewordlab.fouundation.persistence.chat.ChatMetadata
import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.fouundation.persistence.chat.MessageType
import co.thewordlab.luzia.core.chat.analytics.SendMessageEventHelper
import co.thewordlab.luzia.core.chat.domain.ChatController
import co.thewordlab.luzia.core.chat.presentation.search.SearchViewState
import co.thewordlab.luzia.core.feedback.domain.FeedbackSource
import co.thewordlab.luzia.core.tools.domain.repository.MathToolRepository
import co.thewordlab.luzia.features.chat.MaxPromptLimitReached
import co.thewordlab.luzia.features.chat.presentation.details.ChatActions
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Event
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.common.extensions.cachedIn
import co.thewordlab.luzia.foundation.common.haptic.HapticFeedbackManager
import co.thewordlab.luzia.foundation.files.importing.FileImporter
import co.thewordlab.luzia.foundation.messages.data.model.asEvent
import co.thewordlab.luzia.foundation.networking.model.AppErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.getDataOrNull
import co.theworldlab.luzia.foundation.design.system.events.AppEventContainer
import co.theworldlab.luzia.foundation.design.system.events.EventsHandler
import co.theworldlab.luzia.foundation.design.system.events.EventsHandlerImpl
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@Suppress("TooManyFunctions")
@HiltViewModel
class MathToolViewModel @Inject constructor(
    private val mathToolRepository: MathToolRepository,
    private val hapticFeedbackManager: HapticFeedbackManager,
    private val fileImporter: FileImporter,
    private val analytics: Analytics,
    private val appEventContainer: AppEventContainer,
    private val sendMessageEventHelper: SendMessageEventHelper,
    private val dispatcher: CoroutineDispatcher
) : ViewModel(),
    ChatController,
    EventsHandler<MathToolEvent> by EventsHandlerImpl(),
    ViewModelActions<MathToolViewActions>,
    ViewModelEvents<MathToolViewEvents> by ViewModelEventsImpl(),
    SendMessageEventHelper by sendMessageEventHelper {

    private val mutableState: MutableStateFlow<MathToolUiState> =
        MutableStateFlow(
            MathToolUiState(
                actionSink = ::onAction,
                onUserTappedUpload = ::onUserTappedUpload,
                onNewFlowClicked = ::onNewFlowClicked
            )
        )

    private val isAiResponding = MutableStateFlow(false)

    private val uploadState = MutableStateFlow(MathUploadState.NONE)

    private var shouldShowExitConfirmation = false

    override val searchState: Flow<SearchViewState<MessageEntity>> = flowOf(SearchViewState())

    val uiState: StateFlow<MathToolUiState> =
        combine(
            isAiResponding,
            uploadState,
            mutableState
        ) { isAiResponding, uploadState, state ->
            state.copy(
                isSendEnabled = !isAiResponding,
                isUploading = uploadState == MathUploadState.UPLOADING,
                shouldShowExitConfirmation = shouldShowExitConfirmation
            )
        }.cachedIn(
            viewModelScope,
            MathToolUiState(
                actionSink = ::onAction,
                onUserTappedUpload = ::onUserTappedUpload,
                onNewFlowClicked = ::onNewFlowClicked
            )
        )

    val messages by lazy { mathToolRepository.getMessages().cachedIn(viewModelScope) }

    override fun onViewAction(action: MathToolViewActions) {
        when (action) {
            MathToolViewActions.OnResume -> clearMessagesMetrics()
            MathToolViewActions.OnStop -> sendLastMessageMetrics()
            MathToolViewActions.OnMaxPromptLengthMessageShown ->
                analytics.logEvent(
                    MaxPromptLimitReached,
                    mapOf(Parameter.Tool to TOOL)
                )

            is MathToolViewActions.OnPhotoSelected -> {
                analytics.logEvent(Event.ItemUpload, mapOf(Parameter.Tool to TOOL))
                if (action.openGallery) {
                    sendEvent(MathToolViewEvents.OpenGallery)
                } else {
                    sendEvent(MathToolViewEvents.OpenCamera)
                }
            }
        }
    }

    private fun onImageSelected(uri: Uri, text: String) {
        viewModelScope.launch(dispatcher) {
            uploadState.update { MathUploadState.UPLOADING }
            val file = fileImporter.resolveFile(uri, "File_", ".jpg")
            if (file != null) {
                val isFileSizeAcceptable = file.length() <= FILE_SIZE_LIMIT
                if (isFileSizeAcceptable) {
                    isAiResponding.update { true }
                    val result = mathToolRepository.sendImage(file, text)
                    result.asEvent()?.let(appEventContainer::sendEvent)
                    logMessageEvent(MessageType.Image)
                    when (result) {
                        is ResultOf.Failure -> {
                            uploadState.update { MathUploadState.ERROR }
                        }

                        is ResultOf.Success -> {
                            shouldShowExitConfirmation = true
                            uploadState.update { MathUploadState.SUCCESS }
                        }
                    }
                    mutableState.update { it.copy(file = file) }
                    isAiResponding.update { false }
                } else {
                    uploadState.update { MathUploadState.NONE }
                    sendEvent(MathToolEvent.FileSizeTooBig)
                }
            }
        }
    }

    private fun onAction(action: ChatActions) {
        when (action) {
            is ChatActions.SendTextMessage -> sendTextMessage(action)
            is ChatActions.SendAudioRecording -> sendAudioRecording(action)
            is ChatActions.PerformHaptic -> performFeedback()
        }
    }

    private fun onUserTappedUpload() {
        analytics.logEvent(Event.ItemUpload, mapOf(Parameter.Tool to TOOL))
        sendEvent(MathToolEvent.ShowCameraFlow)
    }

    private fun sendTextMessage(action: ChatActions.SendTextMessage) {
        val uri = action.imageUri
        if (uri != null) {
            onImageSelected(uri, action.text)
        } else {
            sendTextMessageInternal(action)
        }
    }

    private fun sendTextMessageInternal(action: ChatActions.SendTextMessage) {
        viewModelScope.launch {
            shouldShowExitConfirmation = true
            isAiResponding.update { true }
            logMessageEvent(MessageType.Text)
            val result = mathToolRepository.chatCompletion(action.text)
            result.asEvent()?.let(appEventContainer::sendEvent)
            isAiResponding.update { false }
        }
    }

    private fun sendAudioRecording(action: ChatActions.SendAudioRecording) {
        viewModelScope.launch {
            isAiResponding.update { true }
            logMessageEvent(MessageType.AudioRecord)
            analytics.logEvent(Event.VoiceChat, getEventProperties(MessageType.AudioRecord))
            val result = mathToolRepository.sendAudioRecording(action.file)
            result.asEvent()?.let(appEventContainer::sendEvent)
            isAiResponding.update { false }
        }
    }

    private fun logMessageEvent(messageType: MessageType) {
        sendMessageMetrics(getEventProperties(messageType).toMutableMap())
    }

    private fun getEventProperties(messageType: MessageType): Map<Parameter, String> {
        return mapOf(
            Parameter.Personality to PERSONALITY,
            Parameter.Tool to TOOL,
            Parameter.Type to messageType.property
        )
    }

    private fun performFeedback() {
        viewModelScope.launch { hapticFeedbackManager.shot() }
    }

    private fun onNewFlowClicked() {
        viewModelScope.launch { mathToolRepository.clearMessages() }
    }

    override fun getChatMessages(): Flow<PagingData<MessageEntity>> {
        return messages
    }

    override fun getChatMetadata(): Flow<ChatMetadata?> {
        return mathToolRepository.getChatMetadata()
    }

    override fun hasUnreadMessages(): Flow<Boolean> = flowOf(false)

    override suspend fun getIceBreakers(): Flow<List<String>> =
        flowOf(mathToolRepository.getIceBreakers().getDataOrNull()?.iceBreakers.orEmpty())

    override suspend fun sendTextMessage(text: String) {
        sendTextMessage(ChatActions.SendTextMessage(text, null))
    }

    override suspend fun resendMessage(message: MessageEntity) {
        isAiResponding.update { true }
        logMessageEvent(message.messageType)
        val result = mathToolRepository.resendMessage(message)
        result.asEvent()?.let(appEventContainer::sendEvent)
        isAiResponding.update { false }
    }

    override suspend fun updateMessage(messageId: Long, text: String) {
        DO_NOTHING
    }

    override suspend fun dismissFeedback(feedbackId: String) {
        mathToolRepository.dismissFeedback(feedbackId)
    }

    override fun getFeedbackSource(): FeedbackSource {
        return FeedbackSource.Math
    }

    override suspend fun textToSpeech(text: String): ResultOf<Uri, AppErrors> {
        // TODO: will be done in next PR
        return ResultOf.Failure(AppErrors.Unknown)
    }

    private companion object {
        const val PERSONALITY = "tool"
        const val TOOL = "math"
        const val FILE_SIZE_LIMIT = 20 * 1024 * 1024
    }
}
