package co.thewordlab.luzia.features.tools.presentation.tools.documents

import app.cash.turbine.test
import co.thewordlab.fouundation.persistence.chat.ChatMetadata
import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.fouundation.persistence.chat.MessageType
import co.thewordlab.luzia.core.chat.analytics.SendMessageEventHelper
import co.thewordlab.luzia.core.feedback.domain.FeedbackSource
import co.thewordlab.luzia.core.tools.domain.repository.DocumentToolRepository
import co.thewordlab.luzia.features.personality.domain.LUZIA_ID_PERSONALITY
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.common.haptic.HapticFeedbackManager
import co.thewordlab.luzia.foundation.files.importing.FileImporter
import co.thewordlab.luzia.foundation.messages.data.model.ToolDetailDto
import co.thewordlab.luzia.foundation.networking.model.AppErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.testing.dispatchers.MainDispatcherRule
import co.theworldlab.luzia.foundation.design.system.events.AppEventContainer
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Rule
import org.junit.Test

@OptIn(ExperimentalCoroutinesApi::class)
class DocumentToolViewModelTest {

    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()

    @MockK
    private lateinit var documentToolRepository: DocumentToolRepository

    @MockK
    private lateinit var hapticFeedbackManager: HapticFeedbackManager

    @MockK
    private lateinit var fileImporter: FileImporter

    @MockK
    private lateinit var analytics: Analytics

    @MockK
    private lateinit var appEventContainer: AppEventContainer

    @MockK
    private lateinit var sendMessageEventHelper: SendMessageEventHelper

    private lateinit var sut: DocumentToolViewModel

    private val testDispatcher = StandardTestDispatcher()

    @Before
    fun setUp() {
        MockKAnnotations.init(this, relaxed = true)

        // Mock common repository methods to prevent ClassCastException
        coEvery {
            documentToolRepository.sendQuestionForDocumentTool(any(), any(), any())
        } returns ResultOf.Success(mockk())

        sut = DocumentToolViewModel(
            documentToolRepository,
            hapticFeedbackManager,
            fileImporter,
            testDispatcher,
            analytics,
            appEventContainer,
            sendMessageEventHelper
        )
    }

    @Test
    fun `when getChatMetadata then returns flow from repository`() = runTest {
        // Given
        val chatMetadata = mockk<ChatMetadata>()
        val metadataFlow = flowOf(chatMetadata)
        every { documentToolRepository.getChatMetadata(LUZIA_ID_PERSONALITY) } returns metadataFlow

        // When
        val result = sut.getChatMetadata()

        // Then
        result.test {
            assertEquals(chatMetadata, awaitItem())
            awaitComplete()
        }
        verify { documentToolRepository.getChatMetadata(LUZIA_ID_PERSONALITY) }
    }

    @Test
    fun `when hasUnreadMessages then returns flowOf false`() = runTest {
        sut.hasUnreadMessages().test {
            assertFalse(awaitItem())
            awaitComplete()
        }
    }

    @Test
    fun `when getIceBreakers then returns icebreakers from repository`() = runTest {
        // Given
        val iceBreakers = listOf("Hi", "Hello")
        val toolDetailDto = ToolDetailDto(iceBreakers)
        coEvery { documentToolRepository.getIceBreakers() } returns ResultOf.Success(toolDetailDto)

        // When/Then
        sut.getIceBreakers().test {
            assertEquals(iceBreakers, awaitItem())
            awaitComplete()
        }
    }

    @Test
    fun `when getIceBreakers and repository returns empty iceBreakers then returns empty list`() = runTest {
        // Given
        val toolDetailDto = ToolDetailDto(emptyList())
        coEvery { documentToolRepository.getIceBreakers() } returns ResultOf.Success(toolDetailDto)

        // When/Then
        sut.getIceBreakers().test {
            assertEquals(emptyList<String>(), awaitItem())
            awaitComplete()
        }
    }

    @Test
    fun `when getIceBreakers and repository returns failure then returns empty list`() = runTest {
        // Given
        coEvery { documentToolRepository.getIceBreakers() } returns ResultOf.Failure(AppErrors.Unknown)

        // When/Then
        sut.getIceBreakers().test {
            assertEquals(emptyList<String>(), awaitItem())
            awaitComplete()
        }
    }

    @Test
    fun `given message when resendMessage then calls repository resendMessage`() = runTest {
        // Given
        val mockMessage = mockk<MessageEntity>()
        every { mockMessage.messageType } returns MessageType.Text
        coEvery { documentToolRepository.resendMessage(any(), mockMessage) } returns ResultOf.Success(mockk())

        // When
        sut.resendMessage(mockMessage)
        mainDispatcherRule.advanceUntilIdle()
        testDispatcher.scheduler.advanceUntilIdle()

        // Then
        coVerify { documentToolRepository.resendMessage(any(), mockMessage) }
    }

    @Test
    fun `given feedbackId when dismissFeedback then calls repository dismissFeedback`() = runTest {
        // Given
        val feedbackId = "feedback123"

        // When
        sut.dismissFeedback(feedbackId)

        // Then
        coVerify { documentToolRepository.dismissFeedback(feedbackId) }
    }

    @Test
    fun `when getFeedbackSource then returns Document`() {
        assertEquals(FeedbackSource.Document, sut.getFeedbackSource())
    }

    @Test
    fun `when textToSpeech then returns Failure Unknown`() = runTest {
        // When
        val result = sut.textToSpeech("hello")

        // Then
        assertTrue(result is ResultOf.Failure && result.error == AppErrors.Unknown)
    }

    @Test
    fun `given text when not uploading then calls repository sendQuestion`() = runTest {
        // Given
        val text = "What is this document about?"

        // When
        sut.sendTextMessage(text)
        mainDispatcherRule.advanceUntilIdle()
        testDispatcher.scheduler.advanceUntilIdle()

        // Then
        coVerify { documentToolRepository.sendQuestionForDocumentTool(LUZIA_ID_PERSONALITY, any(), text) }
        coVerify { sendMessageEventHelper.sendMessageMetrics(any()) }
    }

    @Test
    fun `given text when sendTextMessage and uploading then does not call repository`() = runTest {
        // Given
        val text = "What is this document about?"
        val uploadStateField = DocumentToolViewModel::class.java.getDeclaredField("uploadState")
        uploadStateField.isAccessible = true
        val uploadState = uploadStateField.get(sut) as MutableStateFlow<DocumentUploadState>
        uploadState.value = DocumentUploadState.UPLOADING

        // When
        sut.sendTextMessage(text)
        mainDispatcherRule.advanceUntilIdle()
        testDispatcher.scheduler.advanceUntilIdle()

        // Then
        coVerify(exactly = 0) { documentToolRepository.sendQuestionForDocumentTool(any(), any(), any()) }
    }
}
