package co.thewordlab.luzia.features.tools.domain.mappers

import co.thewordlab.luzia.core.tools.domain.model.DynamicTool
import co.thewordlab.luzia.core.tools.domain.model.DynamicToolButtonType
import co.thewordlab.luzia.core.tools.domain.model.DynamicToolComponent
import co.thewordlab.luzia.core.tools.domain.model.DynamicToolComponentType
import co.thewordlab.luzia.core.tools.domain.model.DynamicToolOptions
import co.thewordlab.luzia.features.tools.domain.models.dynamic.ActionType
import co.thewordlab.luzia.features.tools.domain.models.dynamic.ComponentType
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Test

@Suppress("MaxLineLength")
class DynamicToolsMapperTest {

    @Test
    fun `given DynamicTool when mapToModel then returns DynamicToolModel`() = runTest {
        val dynamicTool = DynamicTool(
            id = "testId",
            name = "testName",
            description = "testDescription",
            body = emptyList(),
            footer = emptyList(),
            resultId = "testResultId"
        )

        val result = dynamicTool.mapToModel()

        assertEquals("testId", result.id)
        assertEquals("testName", result.name)
        assertEquals("testDescription", result.description)
        assertTrue(result.body.isEmpty())
        assertTrue(result.footer.isEmpty())
    }

    @Test
    fun `given DynamicTool with body and footer when mapToModel then returns DynamicToolModel with mapped components`() =
        runTest {
            val options = listOf(
                DynamicToolOptions(
                    id = "optionId",
                    title = "optionTitle",
                    subtitle = "optionSubtitle"
                )
            )
            val component = DynamicToolComponent(
                id = "componentId",
                type = DynamicToolComponentType.TITLE,
                title = "componentTitle",
                subtitle = "componentSubtitle",
                options = options,
                maxLength = 10,
                placeholder = "componentPlaceholder",
                content = "componentContent",
                rawHTML = "<p>componentRawHTML</p>",
                actionType = DynamicToolButtonType.DEEPLINK,
                action = "componentAction",
                enableScroll = true,
                enableFullScreen = true
            )
            val dynamicTool = DynamicTool(
                id = "testId",
                name = "testName",
                description = "testDescription",
                body = listOf(component),
                footer = listOf(component),
                resultId = "testResultId"
            )

            val result = dynamicTool.mapToModel()

            assertEquals("testId", result.id)
            assertEquals("testName", result.name)
            assertEquals("testDescription", result.description)
            assertEquals(1, result.body.size)
            assertEquals(1, result.footer.size)

            val bodyComponent = result.body.first()
            assertEquals("componentId", bodyComponent.id)
            assertEquals(ComponentType.TITLE, bodyComponent.type)
            assertEquals("componentTitle", bodyComponent.title)
            assertEquals("componentSubtitle", bodyComponent.subtitle)
            assertEquals(1, bodyComponent.options.size)
            assertEquals("optionId", bodyComponent.options.first().id)
            assertEquals("optionTitle", bodyComponent.options.first().title)
            assertEquals("optionSubtitle", bodyComponent.options.first().description)
            assertEquals(10, bodyComponent.maxLength)
            assertEquals("componentPlaceholder", bodyComponent.placeholder)
            assertEquals("componentContent", bodyComponent.content)
            assertEquals("<p>componentRawHTML</p>", bodyComponent.rawHTML)
            assertEquals(ActionType.DEEPLINK, bodyComponent.actionType)
            assertEquals("componentAction", bodyComponent.action)
            assertTrue(bodyComponent.enableScroll ?: false)
            assertTrue(bodyComponent.enableFullScreen ?: false)

            val footerComponent = result.footer.first()
            assertEquals("componentId", footerComponent.id)
            assertEquals(ComponentType.TITLE, footerComponent.type)
            assertEquals("componentTitle", footerComponent.title)
            assertEquals("componentSubtitle", footerComponent.subtitle)
            assertEquals(1, footerComponent.options.size)
            assertEquals("optionId", footerComponent.options.first().id)
            assertEquals("optionTitle", footerComponent.options.first().title)
            assertEquals("optionSubtitle", footerComponent.options.first().description)
            assertEquals(10, footerComponent.maxLength)
            assertEquals("componentPlaceholder", footerComponent.placeholder)
            assertEquals("componentContent", footerComponent.content)
            assertEquals("<p>componentRawHTML</p>", footerComponent.rawHTML)
            assertEquals(ActionType.DEEPLINK, footerComponent.actionType)
            assertEquals("componentAction", footerComponent.action)
        }

    @Test
    fun `given DynamicTool with null component properties when mapToModel then returns DynamicToolModel with default values`() =
        runTest {
            val componentWithNulls = DynamicToolComponent(
                id = "",
                type = DynamicToolComponentType.NONE,
                title = "",
                subtitle = "",
                options = emptyList(),
                maxLength = 1,
                placeholder = "",
                content = "",
                rawHTML = "",
                actionType = DynamicToolButtonType.NONE,
                action = "",
                enableScroll = false,
                enableFullScreen = false
            )
            val dynamicTool = DynamicTool(
                id = "testId",
                name = "testName",
                description = "testDescription",
                body = listOf(componentWithNulls),
                footer = listOf(componentWithNulls),
                resultId = "testResultId"
            )

            val result = dynamicTool.mapToModel()

            val bodyComponent = result.body.first()
            assertEquals("", bodyComponent.id)
            assertEquals(ComponentType.NONE, bodyComponent.type)
            assertEquals("", bodyComponent.title)
            assertEquals("", bodyComponent.subtitle)
            assertTrue(bodyComponent.options.isEmpty())
            assertEquals(1, bodyComponent.maxLength)
            assertEquals("", bodyComponent.placeholder)
            assertEquals("", bodyComponent.content)
            assertEquals("", bodyComponent.rawHTML)
            assertEquals(ActionType.NONE, bodyComponent.actionType)
            assertEquals("", bodyComponent.action)
            assertFalse(bodyComponent.enableScroll ?: true)
            assertFalse(bodyComponent.enableFullScreen ?: true)

            assertEquals(1, result.footer.size)
            val firstFooterComponent = result.footer.first() // This is componentWithNulls
            assertEquals("", firstFooterComponent.id)
            assertEquals(ComponentType.NONE, firstFooterComponent.type)
            assertEquals("", firstFooterComponent.title)
        }

    @Test
    fun `given null DynamicToolComponent when toComponentModel then returns ToolComponent with default values`() {
        val dynamicToolComponent: DynamicToolComponent? = null
        val expectedComponentType = ComponentType.getValue("")
        val expectedActionType = ActionType.getValue("")

        val result = dynamicToolComponent.toComponentModel()

        assertEquals("", result.id)
        assertEquals(expectedComponentType, result.type)
        assertEquals("", result.title)
        assertEquals("", result.subtitle)
        assertTrue(result.options.isEmpty())
        assertNull(result.maxLength)
        assertNull(result.placeholder)
        assertNull(result.content)
        assertNull(result.rawHTML)
        assertEquals(expectedActionType, result.actionType)
        assertNull(result.action)
        assertNull(result.enableScroll)
        assertNull(result.enableFullScreen)
    }

    @Test
    fun `given DynamicToolComponent with all properties when toComponentModel then maps all properties correctly`() {
        val options = listOf(
            DynamicToolOptions(id = "opt1", title = "Option 1", subtitle = "Subtitle 1"),
            DynamicToolOptions(id = "opt2", title = "Option 2", subtitle = "Subtitle 2")
        )
        val dynamicToolComponent = DynamicToolComponent(
            id = "componentId123",
            type = DynamicToolComponentType.TITLE,
            title = "Component Title",
            subtitle = "Component Subtitle",
            options = options,
            maxLength = 100,
            placeholder = "Enter text here",
            content = "Initial content",
            rawHTML = "<p>Hello World</p>",
            actionType = DynamicToolButtonType.DEEPLINK,
            action = "app://deeplink/path",
            enableScroll = true,
            enableFullScreen = false
        )
        val expectedComponentType = ComponentType.getValue(DynamicToolComponentType.TITLE.value)
        val expectedActionType = ActionType.getValue(DynamicToolButtonType.DEEPLINK.value)

        val result = dynamicToolComponent.toComponentModel()

        assertEquals("componentId123", result.id)
        assertEquals(expectedComponentType, result.type)
        assertEquals("Component Title", result.title)
        assertEquals("Component Subtitle", result.subtitle)
        assertEquals(2, result.options.size)
        assertEquals("opt1", result.options[0].id)
        assertEquals("Option 1", result.options[0].title)
        assertEquals("Subtitle 1", result.options[0].description)
        assertEquals("opt2", result.options[1].id)
        assertEquals("Option 2", result.options[1].title)
        assertEquals("Subtitle 2", result.options[1].description)
        assertEquals(100, result.maxLength)
        assertEquals("Enter text here", result.placeholder)
        assertEquals("Initial content", result.content)
        assertEquals("<p>Hello World</p>", result.rawHTML)
        assertEquals(expectedActionType, result.actionType)
        assertEquals("app://deeplink/path", result.action)
        assertTrue(result.enableScroll ?: false)
        assertFalse(result.enableFullScreen ?: true)
    }
}
