package co.thewordlab.luzia.features.home.presentation

import android.net.Uri
import co.thewordlab.luzia.core.chat.domain.models.proactive.ProactiveMessage
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class HomeViewEvents : ViewEvent {
    data class DisplayProactiveInAppMessage(val messages: List<ProactiveMessage>) : HomeViewEvents()
    data class NavigateToJoinGroup(val routes: UserSessionRoutes.Invite) : HomeViewEvents()
    data class NavigateToMajorReward(val rewardId: String) : HomeViewEvents()
    data object NavigateToProfileWizard : HomeViewEvents()
    data object NavigateToReferral : HomeViewEvents()
    data object NavigateToSignup : HomeViewEvents()
    data object NavigateToCustomBestie : HomeViewEvents()
    data object NavigateToCustomBestieSignup : HomeViewEvents()
    data object NavigateToCustomBestieCreationNotAllowed : HomeViewEvents()
    data class NavigateToDeeplink(val uri: Uri) : HomeViewEvents()
}
