package co.thewordlab.luzia.features.home.presentation

import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class HomeViewActions : ViewAction {
    data class OnCreate(val comingFromBottomBar: Boolean) : HomeViewActions()
    data object OnStart : HomeViewActions()
    data object OnProactiveMessageActionClicked : HomeViewActions()
    data object OnDismissProactiveMessage : HomeViewActions()
    data object OnReferralClicked : HomeViewActions()
    data object OnNavigateToSignup : HomeViewActions()
    data object OnAddCustomBestieClicked : HomeViewActions()
    data object OnNewChatClicked : HomeViewActions()
    data object OnClaimReward : HomeViewActions()
    data object OnAttemptCloseReward : HomeViewActions()
    data object OnCloseReward : HomeViewActions()
    data object OnGamificationPillTapped : HomeViewActions()
}
