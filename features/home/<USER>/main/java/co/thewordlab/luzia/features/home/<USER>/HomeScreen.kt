package co.thewordlab.luzia.features.home.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import co.thewordlab.fouundation.persistence.tool.ToolEntity
import co.thewordlab.luzia.core.ads.components.BannerAdView
import co.thewordlab.luzia.core.bestiepoints.presentation.components.StreakBestiePointsView
import co.thewordlab.luzia.core.bestiepoints.presentation.models.StreakBestiePointsNavBarUiModel
import co.thewordlab.luzia.core.bestiepoints.presentation.models.StreakBestiePointsViewType
import co.thewordlab.luzia.core.connectivity.screen.ConnectivityScreenSize
import co.thewordlab.luzia.core.connectivity.screen.ConnectivityScreenView
import co.thewordlab.luzia.core.gamification.components.pill.GamificationPillLargeView
import co.thewordlab.luzia.core.gamification.components.pill.GamificationPillUiModel
import co.thewordlab.luzia.core.gamification.domain.model.Reward
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.features.home.CAROUSEL_TYPE_PERSONALITIES
import co.thewordlab.luzia.features.home.presentation.components.LuziaNewChatView
import co.thewordlab.luzia.features.personality.domain.models.PersonalityModel
import co.thewordlab.luzia.features.personality.presentation.custombestie.models.AddCustomBestiePosition
import co.thewordlab.luzia.features.personality.presentation.custombestie.models.PersonalitiesViewState
import co.thewordlab.luzia.features.tools.presentation.components.ToolCard
import co.thewordlab.luzia.features.tools.presentation.components.ToolCardColors
import co.thewordlab.luzia.features.tools.presentation.screen.ToolsAction
import co.thewordlab.luzia.features.tools.presentation.screen.ToolsUiState
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Event
import co.thewordlab.luzia.foundation.analytics.LocalAnalytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.bottombar.LandingDestinations
import co.theworldlab.luzia.foundation.design.system.components.braze.BrazeCardLocation
import co.theworldlab.luzia.foundation.design.system.components.braze.BrazeCustomCard
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.ButtonSize
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.LuziaSecondaryButton
import co.theworldlab.luzia.foundation.design.system.components.navbar.SectionHeader
import co.theworldlab.luzia.foundation.design.system.components.personality.AddPersonalityCompactView
import co.theworldlab.luzia.foundation.design.system.components.personality.PersonalityCompactView
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.addShadow
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.legacy.composables.LuziaCardDefaults
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

private const val NUM_CARDS = 4
private const val DELAY_INITIAL_SCROLL_RESET = 500L

@Composable
fun NavigationHeader(
    viewState: HomeViewState,
    homeViewModel: HomeViewModel,
    navigation: HomeScreenNavigation,
    bestiePointsModel: StreakBestiePointsNavBarUiModel
) {
    Column(
        modifier = Modifier
            .statusBarsPadding()
            .fillMaxWidth()
            .testTag("containerHome")
            .padding(horizontal = Spacing.X16.dp)
    ) {
        Spacer(modifier = Modifier.height(Spacing.X16.dp))
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (refTitle, refActions) = createRefs()
            LuziaText(
                modifier = Modifier
                    .constrainAs(refTitle) {
                        top.linkTo(parent.top)
                        start.linkTo(parent.start)
                        bottom.linkTo(parent.bottom)
                        end.linkTo(refActions.start, margin = Spacing.X8.dp)
                        width = Dimension.fillToConstraints
                    },
                text = buildTitle(
                    isFullUser = viewState.isFullUser,
                    userName = viewState.userName,
                    wrapTitle = viewState.gamificationModel == null
                ),
                color = LuziaTheme.palette.text.primary,
                style = LuziaTheme.typography.headlines.h3,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            Row(
                modifier = Modifier.constrainAs(refActions) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    end.linkTo(parent.end)
                    width = Dimension.wrapContent
                },
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.End
            ) {
                if (viewState.gamificationModel == null) {
                    BestiePointsContent(bestiePointsModel)
                }
                if (viewState.isReferralEnabled) {
                    IconButton(onClick = { homeViewModel.onViewAction(HomeViewActions.OnReferralClicked) }) {
                        Icon(
                            modifier = Modifier
                                .size(IconSizes.X32.dp)
                                .background(
                                    color = LuziaTheme.palette.surface.content,
                                    shape = CircleShape
                                )
                                .padding(Spacing.X4.dp),
                            painter = painterResource(id = R.drawable.ic_gift_24),
                            tint = LuziaTheme.palette.text.primary,
                            contentDescription = null
                        )
                    }
                }
                ProfileAvatarView(
                    modifier = Modifier
                        .size(IconSizes.X32.dp)
                        .testTag("buttonProfile"),
                    isFullUser = viewState.isFullUser,
                    avatarState = viewState.avatarState,
                    showBadge = viewState.showProfileBadge
                ) {
                    navigation.onNavigateToProfile()
                }
            }
        }
        Spacer(modifier = Modifier.height(Spacing.X8.dp))
    }
}

@Composable
private fun BestiePointsContent(
    bestiePointsModel: StreakBestiePointsNavBarUiModel
) {
    StreakBestiePointsView(
        modifier = Modifier
            .wrapContentWidth()
            .clip(RoundedCornerShape(Corners.X4.dp))
            .click { bestiePointsModel.bestiePointsClicked() },
        type = StreakBestiePointsViewType.Compact,
        onNavigateToBestiePoints = bestiePointsModel.bestiePointsClicked,
        onNavigateToSignup = bestiePointsModel.onNavigateToSignup,
        source = bestiePointsModel.source
    )
    Spacer(modifier = Modifier.width(Spacing.X4.dp))
}

@Suppress("LongMethod", "CyclomaticComplexMethod")
@Composable
fun HomeScreenContent(
    modifier: Modifier,
    personalitiesViewState: PersonalitiesViewState,
    toolsUiState: ToolsUiState,
    navigation: HomeScreenNavigation,
    gamificationModel: GamificationPillUiModel?,
    pendingReward: Reward?,
    brazeCardsEnabled: Boolean,
    bannerAdsEnabled: Boolean,
    onViewActions: (HomeViewActions) -> Unit
) {
    val analytics = LocalAnalytics.current
    val localNavigation = LocalNavigation.current
    val listState = rememberLazyListState()
    var brazeTopVisible by remember { mutableStateOf(true) }
    var brazeMiddleVisible by remember { mutableStateOf(true) }
    var brazeBottomVisible by remember { mutableStateOf(true) }
    var brazeFooterVisible by remember { mutableStateOf(true) }
    val coroutineScope = rememberCoroutineScope()

    LaunchedEffect(Unit) {
        coroutineScope.launch {
            delay(DELAY_INITIAL_SCROLL_RESET)
            listState.animateScrollToItem(0, listState.firstVisibleItemScrollOffset)
        }
    }
    LazyColumn(modifier = modifier, state = listState) {
        if (gamificationModel != null) {
            item {
                GamificationPillLargeView(
                    data = gamificationModel,
                    onClick = { localNavigation.navigate(UserSessionRoutes.Gamification) },
                    modifier = Modifier
                        .padding(horizontal = Spacing.X16.dp)
                        .padding(top = Spacing.X16.dp)
                )
            }
        }
        if (pendingReward != null) {
            item {
                PendingRewardView(reward = pendingReward, onViewActions)
            }
        }
        if (brazeTopVisible && brazeCardsEnabled) {
            item {
                BrazeCustomCard(
                    location = BrazeCardLocation.TOP,
                    onLinkClicked = { navigation.onLinkClicked(it) },
                    onDataReceived = {
                        brazeTopVisible = it > 0
                    }
                )
            }
        }

        item {
            LuziaNewChatView { personalityId ->
                onViewActions(HomeViewActions.OnNewChatClicked)
                localNavigation.navigate(
                    UserSessionRoutes.ChatDetail(
                        personalityId = personalityId,
                        isCustomBestie = false,
                        openKeyboardOnLaunch = true
                    )
                )
            }
        }

        if (brazeMiddleVisible && brazeCardsEnabled) {
            item {
                BrazeCustomCard(
                    location = BrazeCardLocation.MIDDLE,
                    onLinkClicked = { navigation.onLinkClicked(it) },
                    onDataReceived = { brazeMiddleVisible = it > 0 }
                )
            }
        }

        personalitiesContent(
            personalitiesViewState = personalitiesViewState,
            analytics = analytics,
            navigation = navigation,
            onViewActions = onViewActions
        )

        if (bannerAdsEnabled) {
            item {
                BannerAdView()
            }
        }

        if (brazeBottomVisible && brazeCardsEnabled) {
            item {
                BrazeCustomCard(
                    location = BrazeCardLocation.BOTTOM,
                    onLinkClicked = { navigation.onLinkClicked(it) },
                    onDataReceived = { brazeBottomVisible = it > 0 }
                )
            }
        }
        toolsUiState.homeTools.takeIf { it.isNotEmpty() }?.let { tools ->
            item { ToolsContent(tools, analytics, toolsUiState, navigation) }
        }
        if (brazeFooterVisible && brazeCardsEnabled) {
            item {
                BrazeCustomCard(
                    location = BrazeCardLocation.FOOTER,
                    onLinkClicked = { navigation.onLinkClicked(it) },
                    onDataReceived = { brazeFooterVisible = it > 0 }
                )
                Spacing.X16.Vertical()
            }
        }
    }
}

@Composable
private fun PendingRewardView(reward: Reward, onViewActions: (HomeViewActions) -> Unit) {
    Box(
        modifier = Modifier
            .padding(top = Spacing.X16.dp)
            .padding(horizontal = Spacing.X16.dp)
            .addShadow()
            .clip(RoundedCornerShape(Corners.X4.dp))
            .testTag("viewPendingReward")
    ) {
        Image(
            modifier = Modifier.matchParentSize(),
            painter = painterResource(designR.drawable.im_claim_banner),
            contentDescription = null
        )
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = Spacing.X16.dp)
                .padding(end = Spacing.X8.dp),
            horizontalArrangement = Arrangement.spacedBy(Spacing.X12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = { onViewActions(HomeViewActions.OnAttemptCloseReward) }) {
                Icon(
                    painter = painterResource(designR.drawable.ic_close),
                    contentDescription = stringResource(localizationR.string.close_icon_content_description),
                    tint = LuziaTheme.palette.interactive.contrast
                )
            }
            Column(modifier = Modifier.weight(1f)) {
                LuziaText(
                    modifier = Modifier.fillMaxWidth(),
                    text = reward.title,
                    style = LuziaTheme.typography.body.semiBold.small,
                    color = LuziaTheme.palette.text.contrast
                )
                LuziaText(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(localizationR.string.gamification_available_reward),
                    style = LuziaTheme.typography.body.regular.footnote,
                    color = LuziaTheme.palette.text.contrast
                )
            }
            LuziaSecondaryButton(
                onClick = { onViewActions(HomeViewActions.OnClaimReward) },
                text = stringResource(localizationR.string.gamification_claim_now),
                size = ButtonSize.SMALL
            )
        }
    }
}

private fun LazyListScope.personalitiesContent(
    personalitiesViewState: PersonalitiesViewState,
    analytics: Analytics,
    navigation: HomeScreenNavigation,
    onViewActions: (HomeViewActions) -> Unit
) {
    with(personalitiesViewState) {
        if (personalities.isNotEmpty()) {
            item {
                PersonalitiesContent(
                    personalities,
                    addCustomBestiePosition,
                    analytics,
                    navigation.onNavigateToChat,
                    onViewActions
                )
            }
        }
    }
}

@Composable
private fun buildTitle(isFullUser: Boolean, userName: String, wrapTitle: Boolean): String {
    val middleChar = if (wrapTitle) "\n" else " "
    val suffix = when (isFullUser) {
        true -> ",$middleChar$userName!"
        else -> "!"
    }
    return "${stringResource(localizationR.string.home_title_hello)}$suffix"
}

@Composable
private fun PersonalitiesContent(
    personalities: List<PersonalityModel>,
    addCustomBestiePosition: AddCustomBestiePosition,
    analytics: Analytics,
    onNavigateToChat: (LandingDestinations) -> Unit,
    onViewActions: (HomeViewActions) -> Unit
) {
    val navigation = LocalNavigation.current
    Box(modifier = Modifier.padding(vertical = Spacing.X8.dp, horizontal = Spacing.X16.dp)) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .addShadow(),
            colors = LuziaCardDefaults.cardColors(containerColor = LuziaTheme.palette.surface.content),
        ) {
            PersonalitiesHeader(analytics, onNavigateToChat)
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .horizontalScroll(rememberScrollState())
                    .padding(horizontal = Spacing.X8.dp)
            ) {
                if (addCustomBestiePosition == AddCustomBestiePosition.START) {
                    AddPersonalityCompactView { onViewActions(HomeViewActions.OnAddCustomBestieClicked) }
                }
                personalities
                    .forEach { personality ->
                        PersonalityCompactView(
                            id = personality.personalityId,
                            isCustomBestie = personality.isCustomBestie,
                            title = personality.name,
                            image = personality.avatar,
                            onNavigateToPersonality = { personalityId, isCustomBestie ->
                                val props = mapOf(
                                    Parameter.Personality to personalityId,
                                    Parameter.CarouselType to CAROUSEL_TYPE_PERSONALITIES
                                )
                                analytics.logEvent(Event.CarouselInteract, props)
                                navigation.navigate(
                                    UserSessionRoutes.ChatDetail(
                                        personalityId = personalityId,
                                        isCustomBestie = isCustomBestie
                                    )
                                )
                            }
                        )
                    }
                if (addCustomBestiePosition == AddCustomBestiePosition.END) {
                    AddPersonalityCompactView { onViewActions(HomeViewActions.OnAddCustomBestieClicked) }
                }
            }
            Spacer(modifier = Modifier.height(Spacing.X16.dp))
        }
    }
}

@Composable
private fun ToolsContent(
    tools: List<ToolEntity>,
    analytics: Analytics,
    toolsUiState: ToolsUiState,
    navigation: HomeScreenNavigation
) {
    val density = LocalDensity.current
    var maxHeight by remember { mutableStateOf(0.dp) }
    Box(
        modifier = Modifier
            .padding(top = Spacing.X8.dp, bottom = Spacing.X16.dp)
            .padding(horizontal = Spacing.X16.dp)
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .addShadow(),
            colors = LuziaCardDefaults.cardColors(),
        ) {
            ToolsHeader(analytics, navigation.onNavigateToTools)
            ConnectivityScreenView(
                size = ConnectivityScreenSize.IN_VIEW,
                modifier = Modifier.padding(Spacing.X16.dp)
            ) {
                val modifier = Modifier
                    .weight(1f)
                    .onSizeChanged { size ->
                        val heightDp = with(density) { size.height.toDp() }
                        if (heightDp > maxHeight) {
                            maxHeight = heightDp
                        }
                    }
                Column {
                    Spacer(modifier = Modifier.height(Spacing.X8.dp))
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = Spacing.X16.dp),
                        horizontalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
                    ) {
                        ToolCardContent(
                            modifier = modifier,
                            analytics = analytics,
                            toolsUiState = toolsUiState,
                            entity = tools[0],
                            maxHeight = maxHeight
                        )
                        tools.getOrNull(1)?.let {
                            ToolCardContent(
                                modifier = modifier,
                                analytics = analytics,
                                toolsUiState = toolsUiState,
                                entity = it,
                                maxHeight = maxHeight
                            )
                        }
                    }
                    if (tools.size >= NUM_CARDS) {
                        Spacer(modifier = Modifier.height(Spacing.X8.dp))
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = Spacing.X16.dp),
                            horizontalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
                        ) {
                            ToolCardContent(
                                modifier = modifier,
                                analytics = analytics,
                                toolsUiState = toolsUiState,
                                entity = tools[2],
                                maxHeight = maxHeight
                            )
                            ToolCardContent(
                                modifier = modifier,
                                analytics = analytics,
                                toolsUiState = toolsUiState,
                                entity = tools[3],
                                maxHeight = maxHeight
                            )
                        }
                    }
                    Spacer(modifier = Modifier.height(Spacing.X16.dp))
                }
            }
        }
    }
}

@Composable
private fun ToolCardContent(
    modifier: Modifier,
    maxHeight: Dp,
    entity: ToolEntity,
    analytics: Analytics,
    toolsUiState: ToolsUiState
) {
    ToolCard(
        modifier = modifier,
        title = entity.name,
        description = entity.description,
        icon = entity.icon,
        isUpcoming = entity.upcoming != null,
        enforcedHeight = maxHeight,
        onClick = {
            val props = mapOf(
                Parameter.Tool to entity.id,
                Parameter.CarouselType to "tools"
            )
            analytics.logEvent(Event.CarouselInteract, props)
            toolsUiState.onAction(ToolsAction.TappedOnTool(entity, ORIGIN_HOME))
        },
        colors = ToolCardColors(
            cardBackground = LuziaTheme.palette.primitives.neutral.neutral7,
            iconBackground = LuziaTheme.palette.surface.content,
            text = LuziaTheme.palette.text.primary,
            labelText = LuziaTheme.palette.text.contrast,
            labelBackground = LuziaTheme.palette.primitives.neutral.neutral100
        )
    )
}

@Composable
private fun ToolsHeader(
    analytics: Analytics,
    onNavigateToTools: () -> Unit
) {
    SectionHeader(
        modifier = Modifier.padding(horizontal = Spacing.X16.dp),
        title = stringResource(id = localizationR.string.tasks_text),
        cta = stringResource(id = localizationR.string.common_see_all),
        tagId = "buttonTools",
        onCtaClicked = {
            analytics.logEvent(
                Event.CarouselAll,
                mapOf(Parameter.CarouselType to "tools")
            )
            onNavigateToTools()
        }
    )
}

@Composable
private fun PersonalitiesHeader(
    analytics: Analytics,
    onNavigateToChat: (LandingDestinations) -> Unit
) {
    SectionHeader(
        modifier = Modifier.padding(horizontal = Spacing.X16.dp),
        title = stringResource(id = localizationR.string.common_characters),
        cta = stringResource(id = localizationR.string.common_see_all),
        tagId = "buttonCharacters",
        onCtaClicked = {
            analytics.logEvent(
                Event.CarouselAll,
                mapOf(Parameter.CarouselType to CAROUSEL_TYPE_PERSONALITIES)
            )
            onNavigateToChat(LandingDestinations.CHATS)
        }
    )
}
