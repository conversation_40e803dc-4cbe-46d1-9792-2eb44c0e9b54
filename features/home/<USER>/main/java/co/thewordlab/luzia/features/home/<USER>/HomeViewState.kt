package co.thewordlab.luzia.features.home.presentation

import co.thewordlab.luzia.core.gamification.components.pill.GamificationPillUiModel
import co.thewordlab.luzia.core.gamification.domain.model.Reward
import co.thewordlab.luzia.features.personality.presentation.custombestie.models.PersonalitiesViewState
import co.thewordlab.luzia.foundation.architecture.system.ViewState
import co.theworldlab.luzia.foundation.design.system.components.profile.AvatarState

data class HomeViewState(
    val isFullUser: Boolean = false,
    val avatarState: AvatarState? = null,
    val showProfileBadge: Boolean = false,
    val userName: String = "",
    val isReferralEnabled: Boolean = false,
    val personalitiesViewState: PersonalitiesViewState = PersonalitiesViewState(),
    val gamificationModel: GamificationPillUiModel? = null,
    val pendingReward: Reward? = null,
    val showRewardDismissPopup: Boolean = false,
    val brazeCardsEnabled: <PERSON><PERSON><PERSON> = false,
    val bannerAdsEnabled: <PERSON><PERSON><PERSON> = false
) : ViewState
