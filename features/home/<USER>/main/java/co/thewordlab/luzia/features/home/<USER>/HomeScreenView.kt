package co.thewordlab.luzia.features.home.presentation

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.bestiepoints.presentation.components.StreaksPointsSignupPopup
import co.thewordlab.luzia.core.bestiepoints.presentation.models.StreakBestiePointsNavBarUiModel
import co.thewordlab.luzia.core.bestiepoints.presentation.models.StreaksBpSource
import co.thewordlab.luzia.core.chat.domain.models.proactive.ProactiveMessage
import co.thewordlab.luzia.core.chat.domain.models.proactive.getDestination
import co.thewordlab.luzia.core.connectivity.banner.ConnectivityBannerView
import co.thewordlab.luzia.core.gamification.components.popup.RewardDismissPopupView
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes.CustomBestieCreation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes.CustomBestieCreationModal
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes.CustomBestieSignupModal
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes.MajorReward
import co.thewordlab.luzia.features.tools.presentation.screen.ToolsActionHandler
import co.thewordlab.luzia.features.tools.presentation.screen.ToolsViewModel
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.OnStart
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.features.proactive.messaging.presentation.components.alertdialog.ProActiveMessageAlertDialog
import co.theworldlab.luzia.features.proactive.messaging.presentation.components.alertdialog.ProActiveMessageAlertDialogUiModel
import co.theworldlab.luzia.foundation.design.system.components.badge.badge
import co.theworldlab.luzia.foundation.design.system.components.profile.AvatarState
import co.theworldlab.luzia.foundation.design.system.components.profile.ProfileAvatar
import co.theworldlab.luzia.foundation.design.system.components.profile.ProfileAvatarUiModel
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

const val ORIGIN_HOME = "home"

@Suppress("LongMethod", "CyclomaticComplexMethod")
@Composable
fun HomeScreenView(
    comingFromBottomBar: Boolean,
    navigation: HomeScreenNavigation
) {
    val homeViewModel: HomeViewModel = hiltViewModel()
    val toolsViewModel: ToolsViewModel = hiltViewModel()
    val viewState by homeViewModel.viewState.collectAsStateWithLifecycle()
    var showProactiveMessage by remember { mutableStateOf<ProactiveMessage?>(null) }
    val navController = LocalNavigation.current
    OnCreate("HomeScreenView") {
        homeViewModel.onViewAction(
            HomeViewActions.OnCreate(
                comingFromBottomBar
            )
        )
    }
    OnStart { homeViewModel.onViewAction(HomeViewActions.OnStart) }

    ViewModelEventEffect(events = homeViewModel) { event ->
        when (event) {
            is HomeViewEvents.DisplayProactiveInAppMessage ->
                event.messages.firstOrNull()?.let {
                    showProactiveMessage = it
                }

            HomeViewEvents.NavigateToProfileWizard -> navigation.onNavigateToProfileWizard()
            HomeViewEvents.NavigateToReferral -> navigation.onNavigateToReferral()
            HomeViewEvents.NavigateToSignup -> navigation.onNavigateToSignup()
            is HomeViewEvents.NavigateToJoinGroup -> navController.navigate(event.routes)
            HomeViewEvents.NavigateToCustomBestie ->
                navController.navigate(CustomBestieCreation)

            HomeViewEvents.NavigateToCustomBestieCreationNotAllowed ->
                navController.navigate(CustomBestieCreationModal)

            HomeViewEvents.NavigateToCustomBestieSignup ->
                navController.navigate(CustomBestieSignupModal)

            is HomeViewEvents.NavigateToMajorReward -> {
                navController.navigate(MajorReward(event.rewardId))
            }

            is HomeViewEvents.NavigateToDeeplink -> navController.handleDeepLink(event.uri)
        }
    }

    showProactiveMessage?.let {
        ProActiveMessageAlertDialog(
            model = ProActiveMessageAlertDialogUiModel(
                title = it.header,
                description = it.content,
                buttonText = it.buttons.firstOrNull()?.title.orEmpty(),
                buttonAction = {
                    homeViewModel.onViewAction(HomeViewActions.OnProactiveMessageActionClicked)
                    navigation.onLinkClicked(
                        it.buttons.firstOrNull()?.action?.getDestination().orEmpty()
                    )
                    showProactiveMessage = null
                },
                dismissAction = { comesFromCloseButton ->
                    if (comesFromCloseButton) {
                        homeViewModel.onViewAction(HomeViewActions.OnDismissProactiveMessage)
                    }
                    showProactiveMessage = null
                },
                secondaryContent = { DO_NOTHING }
            )
        )
    }

    var showSignupPopup by remember { mutableStateOf(false) }
    if (showSignupPopup) {
        StreaksPointsSignupPopup(
            onClose = { showSignupPopup = false },
            onGoToSignup = {
                showSignupPopup = false
                homeViewModel.onViewAction(HomeViewActions.OnNavigateToSignup)
            }
        )
    }

    if (viewState.showRewardDismissPopup) {
        RewardDismissPopupView(
            data = viewState.gamificationModel,
            onDismiss = { homeViewModel.onViewAction(HomeViewActions.OnCloseReward) }
        )
    }

    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .semantics { testTagsAsResourceId = true }
                .testTag("containerHome")
        ) {
            NavigationHeader(
                viewState,
                homeViewModel,
                navigation,
                StreakBestiePointsNavBarUiModel(
                    title = stringResource(id = localizationR.string.bottom_bar_home),
                    bestiePointsClicked = { navigation.onNavigateToBestiePoints() },
                    onNavigateToSignup = { showSignupPopup = true },
                    source = StreaksBpSource.Home,
                )
            )
            val toolsUiState by toolsViewModel.uiState.collectAsStateWithLifecycle()
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                HomeScreenContent(
                    personalitiesViewState = viewState.personalitiesViewState,
                    gamificationModel = viewState.gamificationModel,
                    navigation = navigation,
                    toolsUiState = toolsUiState,
                    modifier = Modifier.fillMaxSize(),
                    pendingReward = viewState.pendingReward,
                    brazeCardsEnabled = viewState.brazeCardsEnabled,
                    bannerAdsEnabled = viewState.bannerAdsEnabled,
                    onViewActions = homeViewModel::onViewAction
                )
                ToolsActionHandler(
                    modifier = Modifier,
                    toolsViewModel = toolsViewModel,
                    uiState = toolsUiState,
                    onNavigateToImagine = navigation.onNavigateToImagine,
                    onNavigateToVision = navigation.onNavigateToVision,
                    onNavigateToDocumentTool = navigation.onNavigateToDocumentTool,
                    onNavigateToMathTool = navigation.onNavigateToMathTool,
                    onNavigateToWeb = navigation.onNavigateToWeb,
                    onNavigateToSignup = navigation.onNavigateToSignup,
                    onNavigateToDynamicTools = {
                        navigation.onNavigateToDynamicTools(
                            it,
                            ORIGIN_HOME
                        )
                    }
                )
            }
        }

        ConnectivityBannerView(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(Spacing.X8.dp)
        )
    }
}

@Composable
fun ProfileAvatarView(
    modifier: Modifier = Modifier,
    isFullUser: Boolean,
    avatarState: AvatarState?,
    showBadge: Boolean,
    onNavigateToProfile: () -> Unit
) {
    if (isFullUser) {
        ProfileAvatar(
            modifier,
            ProfileAvatarUiModel(
                avatarState = avatarState,
                action = onNavigateToProfile,
                showBadge = showBadge
            )
        )
    } else {
        IconButton(
            modifier = modifier.badge(
                show = showBadge,
                radiusRatio = 0.2f,
                offset = (-Spacing.X12.dp)
            ),
            onClick = onNavigateToProfile
        ) {
            Icon(
                modifier = Modifier
                    .size(IconSizes.X48.dp)
                    .background(
                        color = LuziaTheme.palette.surface.content,
                        shape = CircleShape
                    )
                    .padding(Spacing.X6.dp),
                painter = painterResource(id = designR.drawable.ic_profile),
                tint = LuziaTheme.palette.text.primary,
                contentDescription = null
            )
        }
    }
}
