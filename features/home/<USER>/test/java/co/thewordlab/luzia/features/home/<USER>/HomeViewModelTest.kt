package co.thewordlab.luzia.features.home.presentation

import co.thewordlab.luzia.core.chat.domain.models.proactive.ProactiveMessageEntityMapper
import co.thewordlab.luzia.core.gamification.domain.DismissGamificationNotificationUseCase
import co.thewordlab.luzia.core.gamification.domain.FetchGamificationNotificationUseCase
import co.thewordlab.luzia.core.gamification.domain.GamificationRepository
import co.thewordlab.luzia.core.gamification.presentation.GamificationUiModelMapper
import co.thewordlab.luzia.core.profile.domain.GetUserProfileUseCase
import co.thewordlab.luzia.core.sharing.domain.repository.StreamRepository
import co.thewordlab.luzia.features.personality.data.repository.PersonalityRepository
import co.thewordlab.luzia.features.personality.domain.repository.CustomBestieRepository
import co.thewordlab.luzia.features.personality.domain.usecases.CustomBestieAccessPointClickedUseCase
import co.thewordlab.luzia.features.personality.domain.usecases.IsCustomBestieCreatedUseCase
import co.thewordlab.luzia.features.personality.domain.usecases.IsCustomBestieFlowAvailableUseCase
import co.thewordlab.luzia.features.shortcuts.ShortcutManager
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.foundation.config.FeatureFlagTreatment
import co.thewordlab.luzia.foundation.testing.dispatchers.MainDispatcherRule
import co.theworldlab.luzia.features.proactive.messaging.domain.repository.ProActiveMessagingRepository
import co.theworldlab.luzia.features.proactive.messaging.domain.usecases.HandleProactiveMessagesUseCase
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import kotlin.test.assertEquals

class HomeViewModelTest {
    private val personalityRepository: PersonalityRepository = mockk()
    private val proActiveMessagingRepo: ProActiveMessagingRepository = mockk()
    private val handleProactiveMessagesUseCase: HandleProactiveMessagesUseCase = mockk()
    private val getUserProfileUseCase: GetUserProfileUseCase = mockk()
    private val featureFlagManager: FeatureFlagManager = mockk()
    private val shortcutManager: ShortcutManager = mockk()
    private val analytics: Analytics = mockk(relaxUnitFun = true)
    private val proactiveMessageEntityMapper: ProactiveMessageEntityMapper = mockk()
    private val streamRepository: StreamRepository = mockk()
    private val isCustomBestieCreatedUseCase: IsCustomBestieCreatedUseCase = mockk()
    private val customBestieAccessPointClickedUseCase: CustomBestieAccessPointClickedUseCase =
        mockk()
    private val customBestieRepository: CustomBestieRepository = mockk()
    private val gamificationRepository: GamificationRepository = mockk()
    private val gamificationUiModelMapper: GamificationUiModelMapper = mockk()
    private val fetchGamificationNotificationUseCase: FetchGamificationNotificationUseCase = mockk()
    private val dismissGamificationNotificationUseCase: DismissGamificationNotificationUseCase =
        mockk()
    private val isCustomBestieFlowAvailableUseCase: IsCustomBestieFlowAvailableUseCase = mockk()

    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()

    @Before
    fun setup() {
        coEvery { getUserProfileUseCase() } returns flowOf(null)
        coEvery { gamificationRepository.getData() } returns flowOf(null)
        coEvery { personalityRepository.getAllPersonalities() } returns flowOf(emptyList())
        coEvery { customBestieRepository.getAllCustomBesties(any()) } returns flowOf(emptyList())
        coEvery { isCustomBestieFlowAvailableUseCase() } returns flowOf(false)
        coEvery {
            gamificationUiModelMapper.mapToGamificationPillUiModel(
                gamification = any(),
                isFullUser = any(),
                useTotalPoints = any()
            )
        } returns null
        coEvery {
            fetchGamificationNotificationUseCase(any(), any())
        } returns flowOf(null)
        coEvery {
            featureFlagManager.get<Boolean>(FeatureFlag.ReferralProgramEnabled)
        } returns true
        coEvery {
            featureFlagManager.getImmediate<Boolean>(FeatureFlag.BrazeEnabled)
        } returns true
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.BannerAdsEnabled)
        } returns FeatureFlagTreatment.CONTROL.value
    }

    @Test
    fun `brazeCardsEnabled is true when BrazeEnabled feature flag is true`() = runTest {
        coEvery { featureFlagManager.getImmediate<Boolean>(FeatureFlag.BrazeEnabled) } returns true

        val viewModel = createViewModel()
        mainDispatcherRule.advanceUntilIdle()

        val viewState = viewModel.viewState.value
        assertEquals(true, viewState.brazeCardsEnabled)
    }

    @Test
    fun `brazeCardsEnabled is false when BrazeEnabled feature flag is false`() = runTest {
        coEvery { featureFlagManager.getImmediate<Boolean>(FeatureFlag.BrazeEnabled) } returns false

        val viewModel = createViewModel()
        mainDispatcherRule.advanceUntilIdle()

        val viewState = viewModel.viewState.value
        assertEquals(false, viewState.brazeCardsEnabled)
    }

    @Test
    fun `bannerAdsEnabled is true when BannerAdsEnabled feature flag is control`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.BannerAdsEnabled)
        } returns FeatureFlagTreatment.CONTROL.value

        val viewModel = createViewModel()
        mainDispatcherRule.advanceUntilIdle()

        val viewState = viewModel.viewState.value
        assertEquals(false, viewState.bannerAdsEnabled)
    }

    @Test
    fun `bannerAdsEnabled is false when BannerAdsEnabled feature flag is not control`() = runTest {
        coEvery {
            featureFlagManager.get<String>(FeatureFlag.BannerAdsEnabled)
        } returns FeatureFlagTreatment.TREATMENT_A.value

        val viewModel = createViewModel()
        mainDispatcherRule.advanceUntilIdle()

        val viewState = viewModel.viewState.value
        assertEquals(true, viewState.bannerAdsEnabled)
    }

    private fun createViewModel() = HomeViewModel(
        personalityRepository,
        proActiveMessagingRepo,
        handleProactiveMessagesUseCase,
        getUserProfileUseCase,
        featureFlagManager,
        shortcutManager,
        analytics,
        proactiveMessageEntityMapper,
        streamRepository,
        isCustomBestieCreatedUseCase,
        customBestieAccessPointClickedUseCase,
        customBestieRepository,
        gamificationRepository,
        gamificationUiModelMapper,
        fetchGamificationNotificationUseCase,
        dismissGamificationNotificationUseCase,
        isCustomBestieFlowAvailableUseCase
    )
}
