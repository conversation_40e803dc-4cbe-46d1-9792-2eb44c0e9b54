plugins {
	alias(libs.plugins.luzia.android.feature)
}

android {
	namespace = "co.thewordlab.luzia.features.signup"
}

dependencies {
	implementation(libs.androidx.credentials)
	implementation(libs.androidx.credentials.play.services)
	implementation(libs.installreferrer)
	implementation(libs.google.identity)
	implementation(libs.androidx.appcompat)
	implementation(libs.androidx.browser)
	implementation(libs.androidx.dataStore.preferences)
	implementation(libs.androidx.media3.ui)
	implementation(libs.androidx.media3.exoplayer)

	implementation(projects.core.notifications)
	implementation(projects.core.profile)
	implementation(projects.core.signup)
	implementation(projects.core.gamification)
	implementation(projects.features.personality)
	implementation(projects.features.profile)
	implementation(projects.foundation.analytics)
	implementation(projects.foundation.architectureSystem)
	implementation(projects.foundation.localization)
	implementation(projects.foundation.messages)
	implementation(projects.foundation.networking)
	implementation(projects.foundation.securelib)
    implementation(libs.play.services.base)
	implementation(libs.appauth)

	testImplementation(projects.foundation.testing)
}
