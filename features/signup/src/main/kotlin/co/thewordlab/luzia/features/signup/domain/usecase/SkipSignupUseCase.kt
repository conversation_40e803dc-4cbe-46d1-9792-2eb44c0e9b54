package co.thewordlab.luzia.features.signup.domain.usecase

import co.thewordlab.luzia.core.profile.domain.ProfileRepository
import co.thewordlab.luzia.features.signup.domain.models.SkipSignupResult
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import kotlinx.coroutines.flow.firstOrNull
import javax.inject.Inject

class SkipSignupUseCase @Inject constructor(
    private val userSessionManager: UserSessionManager,
    private val profileRepository: ProfileRepository
) {

    suspend operator fun invoke(): SkipSignupResult {
        val currentSession = userSessionManager.userSession.firstOrNull()
        return if (currentSession == null) {
            val newSession = userSessionManager.createSession()
            if (newSession == null) {
                SkipSignupResult.NoSession
            } else {
                SkipSignupResult.CreatedGuestUser
            }
        } else {
            if (profileRepository.isSessionLocked()) {
                SkipSignupResult.UserLocked
            } else {
                SkipSignupResult.AllowSkip
            }
        }
    }
}
