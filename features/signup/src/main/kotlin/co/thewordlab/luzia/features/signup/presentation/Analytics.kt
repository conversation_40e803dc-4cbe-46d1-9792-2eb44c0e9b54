package co.thewordlab.luzia.features.signup.presentation

import co.thewordlab.luzia.foundation.analytics.AnalyticsActions
import co.thewordlab.luzia.foundation.analytics.AnalyticsEvents
import co.thewordlab.luzia.foundation.analytics.AnalyticsScreens

data object SignupEmail : AnalyticsScreens("sign_up_email")
data object SignupName : AnalyticsScreens("sign_up_name")

data class SkipSignup(val source: String) : AnalyticsActions("skip_signup", mapOf(TYPE to source))
data object ClickOnGoogleButton : AnalyticsActions("signup_google")

data object GoogleAuthSuccess : AnalyticsEvents("google_auth_success")
data object SignupSuccess : AnalyticsEvents("signup_email_success")
data object SignupFail : AnalyticsEvents("signup_email_fail")

data object SignupFallbackWeb : AnalyticsEvents("signup_fallback_web")

private const val TYPE = "type"
const val SOURCE_SKIP_BACK = "native"
const val SOURCE_SKIP_BUTTON = "button"
