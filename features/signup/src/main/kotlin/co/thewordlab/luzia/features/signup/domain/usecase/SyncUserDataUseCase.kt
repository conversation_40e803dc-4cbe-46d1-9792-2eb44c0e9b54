package co.thewordlab.luzia.features.signup.domain.usecase

import co.thewordlab.luzia.core.profile.data.api.model.UserProfileUpdateRequest
import co.thewordlab.luzia.core.profile.domain.AvatarRepository
import co.thewordlab.luzia.core.profile.domain.GetUserProfileUseCase
import co.thewordlab.luzia.core.profile.domain.ProfileRepository
import co.thewordlab.luzia.features.personality.domain.repository.CustomBestieRepository
import co.thewordlab.luzia.features.profile.presentation.getUserProfileCustomProperties
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.analytics.UserIdentification
import co.thewordlab.luzia.foundation.messages.domain.repository.ChatRepository
import co.thewordlab.luzia.foundation.messages.domain.usecases.IsFirstMessageSentUseCase
import co.thewordlab.luzia.foundation.networking.interceptor.language.LanguageHandler
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import co.thewordlab.luzia.foundation.networking.session.UserType
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.firstOrNull
import javax.inject.Inject

@Suppress("LongParameterList")
class SyncUserDataUseCase @Inject constructor(
    private val profileRepository: ProfileRepository,
    private val getUserProfileUseCase: GetUserProfileUseCase,
    private val userSessionManager: UserSessionManager,
    private val getTimeZoneUseCase: GetTimeZoneUseCase,
    private val languageHandler: LanguageHandler,
    private val analytics: Analytics,
    private val avatarRepository: AvatarRepository,
    private val chatRepository: ChatRepository,
    private val customBestieRepository: CustomBestieRepository,
    private val isFirstMessageSentUseCase: IsFirstMessageSentUseCase
) {

    suspend operator fun invoke() {
        isFirstMessageSentUseCase()
        analytics.setUserProperties(
            mapOf(
                Parameter.MessagesCount to chatRepository.countAllLuziaMessages(),
                Parameter.Besties to (
                    customBestieRepository.getAllCustomBesties(forceSync = false)
                        .firstOrNull()?.size ?: 0
                    )
            )
        )
        val session = userSessionManager.userSession.firstOrNull()
        if (session?.userType == UserType.FULL_USER) {
            getUserProfileUseCase()
                .distinctUntilChanged()
                .collectLatest { profile ->
                    profile?.let {
                        val serverTimeZone = profile.utcOffset
                        val currentTimeZone = getTimeZoneUseCase()
                        if (currentTimeZone != serverTimeZone) {
                            val request = UserProfileUpdateRequest(
                                displayName = profile.displayName.orEmpty(),
                                utcOffset = currentTimeZone,
                                preferredLanguage = languageHandler.languageAndCountryCode()
                            )
                            profileRepository.updateUserProfile(request)
                        }
                        updateLocalProfileImage(it.avatarURL)
                        analytics.setUserProperties(profile.getUserProfileCustomProperties())
                        analytics.setUserIdentification(UserIdentification(email = profile.email))
                    }
                }
        } else {
            analytics.setUserProperties(mapOf(Parameter.UserType to UserType.GUEST.value))
            analytics.setUserIdentification(UserIdentification(email = ""))
        }
    }

    private suspend fun updateLocalProfileImage(avatarURL: String?) {
        avatarURL?.let {
            val hasLocalImage = avatarRepository.profileImage.firstOrNull() != null
            if (avatarURL.contains(GOOGLE_CONTENT) && !hasLocalImage) {
                avatarRepository.saveProfileImage(avatarURL)
            }
        }
    }

    private companion object {
        const val GOOGLE_CONTENT = "googleusercontent"
    }
}
