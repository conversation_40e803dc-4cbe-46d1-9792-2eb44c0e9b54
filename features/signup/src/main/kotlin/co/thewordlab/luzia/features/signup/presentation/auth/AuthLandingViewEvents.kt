package co.thewordlab.luzia.features.signup.presentation.auth

import android.content.Intent
import androidx.credentials.GetCredentialRequest
import co.thewordlab.luzia.core.signup.web.GoogleWebErrors
import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class AuthLandingViewEvents : ViewEvent {
    data object NavigateToPrivacyPolicy : AuthLandingViewEvents()
    data object NavigateToTermsOfUse : AuthLandingViewEvents()
    data object NavigateToProfileFill : AuthLandingViewEvents()
    data object NavigateToGuestFill : AuthLandingViewEvents()
    data class ShowGoogleAuthError(val errors: GoogleWebErrors) : AuthLandingViewEvents()
    data object NavigateHome : AuthLandingViewEvents()
    data class TriggerWebAuthFlow(val intent: Intent) : AuthLandingViewEvents()
    data class TriggerNativeAuthFlow(
        val request: GetCredentialRequest,
        val retry: Boolean
    ) : AuthLandingViewEvents()

    data class UpdateDeviceLanguage(val countryCode: String) : AuthLandingViewEvents()
}
