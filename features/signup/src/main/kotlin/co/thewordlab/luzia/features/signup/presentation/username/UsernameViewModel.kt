package co.thewordlab.luzia.features.signup.presentation.username

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.profile.data.api.model.UserProfileUpdateRequest
import co.thewordlab.luzia.core.profile.domain.ProfileRepository
import co.thewordlab.luzia.features.signup.domain.usecase.GetTimeZoneUseCase
import co.thewordlab.luzia.features.signup.presentation.SignupName
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.networking.interceptor.language.LanguageHandler
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class UsernameViewModel @Inject constructor(
    private val profileRepository: ProfileRepository,
    private val analytics: Analytics,
    private val getTimeZoneUseCase: GetTimeZoneUseCase,
    private val languageHandler: LanguageHandler
) :
    ViewModel(),
    ViewModelActions<UsernameViewActions>,
    ViewModelEvents<UsernameViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<UsernameViewState> by ViewModelStatesImpl(UsernameViewState()) {

    override fun onViewAction(action: UsernameViewActions) {
        when (action) {
            is UsernameViewActions.OnNextButtonClicked -> {
                if (viewState.value.isValidUsername) {
                    syncUsername(viewState.value.username)
                }
            }

            is UsernameViewActions.OnValidateUsername -> validateUsername(action.name)
            is UsernameViewActions.OnStart -> {
                analytics.logScreen(SignupName)
                updateState(UsernameViewState(username = action.name))
            }
        }
    }

    private fun validateUsername(username: String) {
        updateState { it.copy(username = username, isValidUsername = username.isNotEmpty()) }
    }

    private fun syncUsername(username: String) = viewModelScope.launch {
        updateState(viewState.value.copy(showLoading = true))
        val timeZone = getTimeZoneUseCase()
        val request = UserProfileUpdateRequest(
            displayName = username,
            utcOffset = timeZone,
            preferredLanguage = languageHandler.languageAndCountryCode()
        )
        when (profileRepository.updateUserProfile(request)) {
            is ResultOf.Failure -> {
                hideLoading()
                sendEvent(UsernameViewEvents.ShowError)
            }

            is ResultOf.Success -> {
                hideLoading()
                sendEvent(UsernameViewEvents.Close)
            }
        }
    }

    private fun hideLoading() = updateState(viewState.value.copy(showLoading = false))
}
