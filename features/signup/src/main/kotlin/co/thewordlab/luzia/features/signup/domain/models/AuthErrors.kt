package co.thewordlab.luzia.features.signup.domain.models

import co.thewordlab.luzia.foundation.networking.model.CommonErrors
import co.thewordlab.luzia.foundation.networking.model.Error

sealed class AuthErrors(val description: String) : Error {

    data class CommonError(val error: CommonErrors) : AuthErrors(error.description)
    data object UserCreationError : AuthErrors("Error creating the user profile")
    data object UserForcedToRegister : AuthErrors("User token expired as guest, should register")
    data object UserDeviceTokenAlreadyInUse : AuthErrors("User device token already in use, should regenerate it")
}
