package co.theworldlab.luzia.features.gamification.presentation.majorreward

import androidx.core.net.toUri
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.gamification.analytics.GamificationAnalytics
import co.thewordlab.luzia.core.gamification.analytics.GamificationAnalytics.TapClaimReward
import co.thewordlab.luzia.core.gamification.analytics.GamificationAnalytics.TapDismissAlert
import co.thewordlab.luzia.core.gamification.analytics.GamificationAnalytics.TapDismissReward
import co.thewordlab.luzia.core.gamification.analytics.GamificationOrigin.Celebration
import co.thewordlab.luzia.core.gamification.domain.DelayGamificationNotificationUseCase
import co.thewordlab.luzia.core.gamification.domain.DismissGamificationNotificationUseCase
import co.thewordlab.luzia.core.gamification.domain.GamificationRepository
import co.thewordlab.luzia.core.gamification.presentation.GamificationUiModelMapper
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MajorRewardViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val gamificationRepository: GamificationRepository,
    private val mapper: GamificationUiModelMapper,
    private val dismissGamificationNotificationUseCase: DismissGamificationNotificationUseCase,
    private val delayGamificationNotificationUseCase: DelayGamificationNotificationUseCase,
    private val analytics: Analytics
) :
    ViewModel(),
    ViewModelActions<MajorRewardViewActions>,
    ViewModelEvents<MajorRewardViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<MajorRewardViewState> by ViewModelStatesImpl(MajorRewardViewState()) {

    private val rewardId: String =
        savedStateHandle.get<String>(UserSessionRoutes.MajorReward::rewardId.name)
            ?: error("rewardId not found in SavedStateHandle")

    override fun onViewAction(action: MajorRewardViewActions) {
        when (action) {
            is MajorRewardViewActions.OnCreate -> {
                sendAnalyticsData()
                loadReward()
            }

            MajorRewardViewActions.Claim -> onClaim()
            MajorRewardViewActions.DismissAttempt -> onDismissAttempt()
            MajorRewardViewActions.Dismiss -> onDismiss()
        }
    }

    private fun sendAnalyticsData() = viewModelScope.launch {
        val level = gamificationRepository.getData().firstOrNull()?.level ?: 0
        val screen = GamificationAnalytics.RewardCelebrationScreen(level)
        analytics.trackScreen(screen)
    }

    private fun onDismiss() {
        viewModelScope.launch {
            val action = TapDismissAlert(Celebration, rewardId)
            analytics.trackAction(action)
            delayGamificationNotificationUseCase.invoke(rewardId)
            sendEvent(MajorRewardViewEvents.NavigateBack)
        }
    }

    private fun onClaim() {
        val id = viewState.value.rewardId
        val action = TapClaimReward(Celebration, id)
        analytics.trackAction(action)
        viewModelScope.launch {
            dismissGamificationNotificationUseCase.invoke(rewardId, true)
            sendEvent(MajorRewardViewEvents.NavigateToDeeplink(viewState.value.deeplink))
        }
    }

    private fun onDismissAttempt() {
        val action = TapDismissReward(Celebration, rewardId)
        analytics.trackAction(action)
        updateState { it.copy(showDismissPopup = true) }
    }

    private fun loadReward() {
        gamificationRepository.getData()
            .filterNotNull()
            .onEach { data ->
                val model = mapper.mapToGamificationPillUiModel(data, true)
                updateState { it.copy(data = model) }
            }
            .launchIn(viewModelScope)
        viewModelScope.launch {
            val reward = gamificationRepository.getRewardById(rewardId)
            if (reward == null) {
                sendEvent(MajorRewardViewEvents.NavigateBack)
            } else {
                updateState {
                    it.copy(
                        rewardId = reward.id,
                        rewardTitle = reward.title,
                        rewardDescription = reward.message,
                        deeplink = reward.deeplink.toUri(),
                        dismissLabel = reward.dismissLabel,
                        claimLabel = reward.claimLabel
                    )
                }
            }
        }
    }
}
