package co.theworldlab.luzia.features.gamification.presentation.majorreward

import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class MajorRewardViewActions : ViewAction {
    data object OnCreate : MajorRewardViewActions()
    data object DismissAttempt : MajorRewardViewActions()
    data object Dismiss : MajorRewardViewActions()
    data object Claim : MajorRewardViewActions()
}
