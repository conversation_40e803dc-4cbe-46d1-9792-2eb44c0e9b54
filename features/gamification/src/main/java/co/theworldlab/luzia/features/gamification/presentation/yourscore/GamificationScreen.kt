package co.theworldlab.luzia.features.gamification.presentation.yourscore

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.gamification.components.badges.BadgeCarouselCardView
import co.thewordlab.luzia.core.gamification.components.cards.GamificationCarouselView
import co.thewordlab.luzia.core.gamification.components.level.CurrentLevelCardView
import co.thewordlab.luzia.core.gamification.components.popup.GamificationInfoPopupView
import co.thewordlab.luzia.core.gamification.components.popup.LockedRewardInfoPopupView
import co.thewordlab.luzia.core.gamification.components.streaks.StreaksCardView
import co.thewordlab.luzia.core.gamification.presentation.GamificationUiComponent
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes.Signup
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.LuziaPrimaryButton
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopCenteredNavigationBar
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.addShadow
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun GamificationScreen() {
    val viewModel: GamificationViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val navigation = LocalNavigation.current
    OnCreate("GamificationScreen") {
        viewModel.onViewAction(GamificationViewActions.OnCreate)
    }
    ViewModelEventEffect(viewModel) {
        when (it) {
            GamificationViewEvents.NavigateBack -> navigation.goBack()
            is GamificationViewEvents.NavigateDeeplink -> navigation.handleDeepLink(it.uri)
            GamificationViewEvents.NavigateToSignup -> {
                navigation.navigate(Signup(false))
            }
        }
    }
    viewState.lockedInfoForLevel?.let { level ->
        LockedRewardInfoPopupView(
            modifier = Modifier,
            level = level,
            onDismiss = { viewModel.onViewAction(GamificationViewActions.OnDismissLockedLevelPopup) }
        )
    }
    if (viewState.showInfoPopup) {
        GamificationInfoPopupView(
            modifier = Modifier,
            items = viewState.informationItems,
            onDismiss = { viewModel.onViewAction(GamificationViewActions.OnDismissInfoPopup) }
        )
    }
    GamificationContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@SuppressLint("UnusedMaterial3ScaffoldPaddingParameter")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun GamificationContent(
    viewState: GamificationViewState,
    onViewActions: (GamificationViewActions) -> Unit
) {
    Scaffold(
        containerColor = LuziaTheme.palette.surface.background
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            TopCenteredNavigationBar(
                modifier = Modifier.padding(horizontal = Spacing.X12.dp),
                title = stringResource(localizationR.string.bestie_points_your_score),
                titleStyle = LuziaTheme.typography.body.semiBold.default,
                navigationIconRes = designR.drawable.ic_close,
                actionBackground = LuziaTheme.palette.interactive.contrast,
                onNavigationIconClick = { onViewActions(GamificationViewActions.OnClose) },
                endActions = listOf(
                    designR.drawable.ic_info to { onViewActions(GamificationViewActions.OnInfoClicked) }
                )
            )
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(Spacing.X16.dp),
                contentPadding = PaddingValues(vertical = Spacing.X16.dp)
            ) {
                items(viewState.sections, { it.id }) {
                    when (it) {
                        is GamificationUiComponent.LevelCard -> CurrentLevelCardView(data = it.model)
                        GamificationUiComponent.SignupCard -> SignupCardView(onViewActions)
                        is GamificationUiComponent.StreakCard -> StreaksCardView(it.model)
                        is GamificationUiComponent.Badges -> BadgeCarouselCardView(it.model)
                        is GamificationUiComponent.Carousel -> GamificationCarouselView(
                            model = it.model,
                            onSurpriseClick = { onViewActions(GamificationViewActions.OnSurpriseCardTapped) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun SignupCardView(
    onViewActions: (GamificationViewActions) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = Spacing.X16.dp)
            .addShadow()
            .clip(RoundedCornerShape(Corners.X4.dp))
            .background(LuziaTheme.palette.surface.content)
            .padding(horizontal = Spacing.X16.dp, vertical = Spacing.X12.dp),
        verticalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
    ) {
        LuziaText(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(localizationR.string.gamification_signup_title),
            style = LuziaTheme.typography.body.semiBold.default,
            color = LuziaTheme.palette.text.primary
        )
        LuziaText(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(localizationR.string.gamification_signup_desc),
            style = LuziaTheme.typography.body.regular.small,
            color = LuziaTheme.palette.text.secondary
        )
        LuziaText(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(localizationR.string.group_chat_mins),
            style = LuziaTheme.typography.body.regular.footnote,
            color = LuziaTheme.palette.text.helper
        )
        LuziaPrimaryButton(
            modifier = Modifier.fillMaxWidth(),
            onClick = { onViewActions(GamificationViewActions.OnSignupClicked) },
            text = stringResource(localizationR.string.profile_signup_title_cta)
        )
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        GamificationContent(
            viewState = GamificationViewState(),
            onViewActions = {}
        )
    }
}
