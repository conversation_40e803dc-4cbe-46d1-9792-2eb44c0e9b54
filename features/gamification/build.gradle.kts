plugins {
    alias(libs.plugins.luzia.android.feature)
}

android {
    namespace = "co.theworldlab.luzia.features.gamification"
}

dependencies {
    implementation(libs.androidx.appcompat)
    implementation(libs.androidx.dataStore.core)
    implementation(libs.androidx.dataStore.preferences)
    implementation(projects.core.profile)
    implementation(projects.core.navigation)
    implementation(projects.core.gamification)
    implementation(projects.foundation.analytics)
    implementation(projects.foundation.architectureSystem)
    implementation(projects.foundation.designSystem)
    implementation(projects.foundation.localization)
}