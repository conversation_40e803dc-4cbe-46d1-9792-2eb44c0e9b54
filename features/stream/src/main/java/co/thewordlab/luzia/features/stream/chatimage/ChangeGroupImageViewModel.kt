package co.thewordlab.luzia.features.stream.chatimage

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.sharing.GroupImageUpdateConfirm
import co.thewordlab.luzia.core.sharing.domain.usecase.ChangeGroupImageUseCase
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ChangeGroupImageViewModel @Inject constructor(
    private val analytics: Analytics,
    private val changeGroupImageUseCase: ChangeGroupImageUseCase
) : ViewModel(),
    ViewModelActions<ChangeGroupImageViewActions>,
    ViewModelEvents<ChangeGroupImageViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<ChangeGroupImageViewState> by ViewModelStatesImpl(ChangeGroupImageViewState()) {

    override fun onViewAction(action: ChangeGroupImageViewActions) {
        when (action) {
            is ChangeGroupImageViewActions.OnCreate -> onCreate(action)
            is ChangeGroupImageViewActions.OnSelectImage -> onSelectImage(action.imageUrl)
            ChangeGroupImageViewActions.OnSubmit -> onSubmit()
            ChangeGroupImageViewActions.OnBack -> onBack()
        }
    }

    private fun onBack() {
        sendEvent(ChangeGroupImageViewEvents.NavigateBack)
    }

    private fun onCreate(action: ChangeGroupImageViewActions.OnCreate) {
        updateState { it.copy(groupImageUrl = action.imageUrl, cid = action.cid) }
    }

    private fun onSelectImage(imageUrl: String) {
        updateState { it.copy(groupImageUrl = imageUrl) }
    }

    private fun onSubmit() = viewModelScope.launch {
        analytics.logAction(GroupImageUpdateConfirm)
        updateState { it.copy(isLoading = true) }
        changeGroupImageUseCase.invoke(viewState.value.cid, viewState.value.groupImageUrl)
        sendEvent(ChangeGroupImageViewEvents.NavigateBack)
    }
}
