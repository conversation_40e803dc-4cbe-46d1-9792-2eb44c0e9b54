package co.thewordlab.luzia.features.stream.chatmenu

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.sharing.AddMembers
import co.thewordlab.luzia.core.sharing.BlockReportEntryPoint
import co.thewordlab.luzia.core.sharing.BlockUser
import co.thewordlab.luzia.core.sharing.GroupSettingsOptionClick
import co.thewordlab.luzia.core.sharing.ReportUser
import co.thewordlab.luzia.core.sharing.ShareGroupLink
import co.thewordlab.luzia.core.sharing.UnBlockUser
import co.thewordlab.luzia.core.sharing.domain.model.SharingErrors
import co.thewordlab.luzia.core.sharing.domain.model.isBlocked
import co.thewordlab.luzia.core.sharing.domain.model.masterUserId
import co.thewordlab.luzia.core.sharing.domain.repository.SharingRepository
import co.thewordlab.luzia.core.sharing.domain.usecase.BlockUserUseCase
import co.thewordlab.luzia.core.sharing.domain.usecase.ReportUserUseCase
import co.thewordlab.luzia.features.stream.chatmenu.StreamChatMenuViewEvents.LinkGenerated
import co.thewordlab.luzia.features.stream.chatmenu.StreamChatMenuViewEvents.NavigateToUser
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.AnalyticsActions
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import dagger.hilt.android.lifecycle.HiltViewModel
import io.getstream.chat.android.models.Channel
import io.getstream.chat.android.models.User
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class StreamChatMenuViewModel @Inject constructor(
    private val analytics: Analytics,
    private val sharingRepository: SharingRepository,
    private val reportUserUseCase: ReportUserUseCase,
    private val blockUserUseCase: BlockUserUseCase
) : ViewModel(),
    ViewModelActions<StreamChatMenuViewActions>,
    ViewModelEvents<StreamChatMenuViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<StreamChatMenuViewState> by ViewModelStatesImpl(StreamChatMenuViewState()) {

    override fun onViewAction(action: StreamChatMenuViewActions) {
        when (action) {
            StreamChatMenuViewActions.OnAddNewMembers -> onAddNewMembers()
            StreamChatMenuViewActions.OnChannelChangeImage -> onChannelChangeImage()
            StreamChatMenuViewActions.OnChannelChangeName -> onChannelChangeName()
            is StreamChatMenuViewActions.OnChannelChanged -> onChannelChanged(action.channel)
            is StreamChatMenuViewActions.OnGenerateLink -> onGenerateLink()
            is StreamChatMenuViewActions.OnUserAvatarClick -> onUserAvatarClick(action.user)
            is StreamChatMenuViewActions.OnOptionClicked -> onOptionClicked(action.option)
            StreamChatMenuViewActions.OnBlockToggle -> onBlockToggle()
            StreamChatMenuViewActions.OnReportUser -> onReportUser()
        }
    }

    private fun onUserAvatarClick(user: User) {
        val masterUserId = user.masterUserId()
        sendEvent(NavigateToUser(masterUserId))
    }

    private fun onGenerateLink() {
        val channel = viewState.value.channel ?: return
        logAction(channel, ShareGroupLink)
        viewModelScope.launch {
            when (val result = sharingRepository.generateInviteLink(channel)) {
                is ResultOf.Failure<String, SharingErrors.GenerateLink> -> {
                    analytics.reportException(result.error.errorMessage)
                }

                is ResultOf.Success<String, SharingErrors.GenerateLink> -> {
                    sendEvent(LinkGenerated(result.data))
                }
            }
        }
    }

    private fun onChannelChanged(channel: Channel?) {
        updateState {
            it.copy(
                channel = channel,
                isUserBlocked = channel.isBlocked()
            )
        }
    }

    private fun onChannelChangeName() {
        val channel = viewState.value.channel ?: return
        onOptionClicked(OPTION_NAME)
        sendEvent(StreamChatMenuViewEvents.NavigateBack)
        sendEvent(StreamChatMenuViewEvents.NavigateToRename(channel.cid, channel.name))
    }

    private fun onChannelChangeImage() {
        val channel = viewState.value.channel ?: return
        onOptionClicked(OPTION_IMAGE)
        sendEvent(StreamChatMenuViewEvents.NavigateBack)
        sendEvent(StreamChatMenuViewEvents.NavigateToImage(channel.cid, channel.image))
    }

    private fun onAddNewMembers() {
        val channel = viewState.value.channel ?: return
        val members = channel.members.map { it.user.masterUserId() }
        logAction(channel, AddMembers)
        sendEvent(StreamChatMenuViewEvents.NavigateBack)
        sendEvent(StreamChatMenuViewEvents.NavigateToAddFriends(channel.cid, members))
    }

    private fun logAction(channel: Channel, action: AnalyticsActions) {
        val props = mapOf(
            Parameter.ChannelId to channel.cid,
            Parameter.CountGroup to channel.memberCount,
            Parameter.EntryPoint to SOURCE_SETTINGS
        )
        analytics.logAction(action, props)
    }

    private fun onOptionClicked(option: String) {
        val props = mapOf(Parameter.Option to option)
        analytics.logAction(GroupSettingsOptionClick, props)
    }

    private fun onReportUser() = viewModelScope.launch {
        val channel = viewState.value.channel ?: return@launch
        val masterUserId = channel.members.map { it.user.masterUserId() }.firstOrNull()
        masterUserId?.let { reportUserUseCase(it, channel.cid) }
        analytics.trackAction(ReportUser(BlockReportEntryPoint.ONE_TO_ONE_CHAT))
        sendEvent(StreamChatMenuViewEvents.NavigateBack)
        sendEvent(StreamChatMenuViewEvents.ShowReportedMessage)
    }

    private fun onBlockToggle() = viewModelScope.launch {
        val channel = viewState.value.channel ?: return@launch
        val isBlocked = !viewState.value.isUserBlocked
        if (isBlocked) {
            analytics.trackAction(BlockUser(BlockReportEntryPoint.ONE_TO_ONE_CHAT))
        } else {
            analytics.trackAction(UnBlockUser(BlockReportEntryPoint.ONE_TO_ONE_CHAT))
        }
        val masterUserId = channel.members.map { it.user.masterUserId() }.firstOrNull()
        masterUserId?.let { blockUserUseCase(isBlocked, it) }
        sendEvent(StreamChatMenuViewEvents.NavigateBack)
    }

    internal companion object {
        const val SOURCE_SETTINGS = "settings"
        const val OPTION_PROFILE = "profile_picture"
        const val OPTION_IMAGE = "image"
        const val OPTION_NAME = "name"
        const val OPTION_MUTE = "mute"
        const val OPTION_UNMUTE = "unmute"
        const val OPTION_LEAVE = "leave"
    }
}
