package co.thewordlab.luzia.features.stream.chats

import androidx.annotation.StringRes
import co.thewordlab.luzia.foundation.architecture.system.ViewEvent
import co.thewordlab.luzia.foundation.localization.R as localizationR

sealed class StreamChatsViewEvents : ViewEvent {
    data object NavigateBack : StreamChatsViewEvents()
    data object NavigateToReferral : StreamChatsViewEvents()
    data object NavigateToNewChat : StreamChatsViewEvents()
    data class ShowFailedMessage(val error: StreamChatsError) : StreamChatsViewEvents()
    data class OpenChat(val channelId: String) : StreamChatsViewEvents()
}

enum class StreamChatsError(@StringRes val errorRes: Int) {
    GroupCreationFailed(localizationR.string.generic_error)
}
