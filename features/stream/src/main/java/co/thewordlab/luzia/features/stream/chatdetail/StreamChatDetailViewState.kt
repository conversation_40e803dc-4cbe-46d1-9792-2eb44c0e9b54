package co.thewordlab.luzia.features.stream.chatdetail

import co.thewordlab.luzia.core.sharing.domain.ClientState
import co.thewordlab.luzia.foundation.architecture.system.ViewState
import co.theworldlab.luzia.features.settings.domain.models.ModeTheme
import io.getstream.chat.android.models.Channel
import io.getstream.chat.android.models.User

data class StreamChatDetailViewState(
    val clientState: ClientState = ClientState.Initializing,
    val themeMode: ModeTheme = ModeTheme.DarkMode(false),
    val channelId: String? = null,
    val userToAcceptOrBlock: User? = null,
    val isOneToOne: Boolean = true,
    val channel: Channel = Channel(),
    val bottomSection: StreamChatDetailBottomSection = StreamChatDetailBottomSection.INPUT
) : ViewState

enum class StreamChatDetailBottomSection {
    INPUT,
    BLOCKED,
    NEED_FRIENDS
}
