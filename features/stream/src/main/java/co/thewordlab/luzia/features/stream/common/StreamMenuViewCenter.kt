package co.thewordlab.luzia.features.stream.common

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import co.thewordlab.luzia.features.stream.chatmenu.StreamChatMenuViewActions
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import io.getstream.chat.android.compose.state.channels.list.ChannelOptionState
import io.getstream.chat.android.compose.ui.theme.ChatTheme
import io.getstream.chat.android.ui.common.state.channels.actions.ChannelAction
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
internal fun StreamMenuViewCenter(
    onChannelOptionClick: (ChannelAction) -> Unit,
    channelOptions: List<ChannelOptionState>,
    isOneToOne: Boolean,
    isUserBlocked: Boolean,
    onViewActions: (StreamChatMenuViewActions) -> Unit,
) {
    ChannelOptions(channelOptions, isOneToOne, isUserBlocked, onChannelOptionClick, onViewActions)
}

@Composable
private fun ChannelOptions(
    options: List<ChannelOptionState>,
    isOneToOne: Boolean,
    isUserBlocked: Boolean,
    onChannelOptionClick: (ChannelAction) -> Unit,
    onViewActions: (StreamChatMenuViewActions) -> Unit,
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight(),
    ) {
        if (!isOneToOne) {
            item {
                Item(
                    modifier = Modifier.testTag("buttonChangeImage"),
                    title = stringResource(localizationR.string.group_chats_change_group_image),
                    titleColor = ChatTheme.colors.textHighEmphasis,
                    iconPainter = painterResource(designR.drawable.ic_gallery_24),
                    iconColor = ChatTheme.colors.textLowEmphasis,
                    onChannelOptionClick = {
                        onViewActions(StreamChatMenuViewActions.OnChannelChangeImage)
                    }
                )
            }
            item {
                Item(
                    modifier = Modifier.testTag("buttonEditGroupName"),
                    title = stringResource(localizationR.string.group_chats_change_group_name),
                    titleColor = ChatTheme.colors.textHighEmphasis,
                    iconPainter = painterResource(designR.drawable.ic_edit),
                    iconColor = ChatTheme.colors.textLowEmphasis,
                    onChannelOptionClick = {
                        onViewActions(StreamChatMenuViewActions.OnChannelChangeName)
                    }
                )
            }
            item {
                Item(
                    modifier = Modifier.testTag("buttonAddFriends"),
                    title = stringResource(localizationR.string.group_chats_add_friend_cta),
                    titleColor = ChatTheme.colors.textHighEmphasis,
                    iconPainter = painterResource(designR.drawable.ic_groups_add_member),
                    iconColor = ChatTheme.colors.textLowEmphasis,
                    onChannelOptionClick = {
                        onViewActions(StreamChatMenuViewActions.OnAddNewMembers)
                    }
                )
            }
            item {
                Item(
                    modifier = Modifier.testTag("buttonInvite"),
                    title = stringResource(localizationR.string.group_chats_invite_link_cta),
                    titleColor = ChatTheme.colors.textHighEmphasis,
                    iconPainter = painterResource(designR.drawable.ic_link_24),
                    iconColor = ChatTheme.colors.textLowEmphasis,
                    onChannelOptionClick = {
                        onViewActions(StreamChatMenuViewActions.OnGenerateLink("settings"))
                    }
                )
            }
        }
        items(options) { option ->
            Item(
                title = option.title,
                titleColor = option.titleColor,
                iconPainter = option.iconPainter,
                iconColor = option.iconColor,
                onChannelOptionClick = { onChannelOptionClick(option.action) },
            )
        }
        if (isOneToOne) {
            item {
                Item(
                    modifier = Modifier.testTag("buttonBlockUser"),
                    title = if (isUserBlocked) {
                        stringResource(localizationR.string.common_unblock_user)
                    } else {
                        stringResource(localizationR.string.common_block_user)
                    },
                    titleColor = LuziaTheme.palette.accents.red.error50,
                    iconPainter = painterResource(designR.drawable.ic_block_user_20),
                    iconColor = LuziaTheme.palette.accents.red.error50,
                    onChannelOptionClick = {
                        onViewActions(StreamChatMenuViewActions.OnBlockToggle)
                    }
                )
            }
            item {
                Item(
                    modifier = Modifier.testTag("buttonReportUser"),
                    title = stringResource(localizationR.string.report_user),
                    titleColor = LuziaTheme.palette.accents.red.error50,
                    iconPainter = painterResource(designR.drawable.ic_report_user_20),
                    iconColor = LuziaTheme.palette.accents.red.error50,
                    onChannelOptionClick = {
                        onViewActions(StreamChatMenuViewActions.OnReportUser)
                    }
                )
            }
        }
    }
}

@Composable
private fun Item(
    modifier: Modifier = Modifier,
    title: String,
    titleColor: Color,
    iconPainter: Painter,
    iconColor: Color,
    onChannelOptionClick: () -> Unit
) {
    Spacer(
        modifier = Modifier
            .fillMaxWidth()
            .height(0.5.dp)
            .background(color = ChatTheme.colors.borders),
    )
    ChannelOptionsItem(
        title = title,
        titleColor = titleColor,
        leadingIcon = {
            Icon(
                modifier = modifier
                    .size(56.dp)
                    .padding(16.dp),
                painter = iconPainter,
                tint = iconColor,
                contentDescription = null,
            )
        },
        onClick = onChannelOptionClick
    )
}

@Composable
private fun ChannelOptionsItem(
    title: String,
    titleColor: Color,
    leadingIcon: @Composable () -> Unit,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier
            .fillMaxWidth()
            .height(56.dp)
            .clickable(
                onClick = onClick,
                indication = ripple(),
                interactionSource = remember { MutableInteractionSource() },
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Start,
    ) {
        leadingIcon()

        Text(
            text = title,
            style = ChatTheme.typography.bodyBold,
            color = titleColor,
        )
    }
}
