package co.thewordlab.luzia.features.stream.chats

import co.thewordlab.luzia.core.sharing.domain.ClientState
import co.thewordlab.luzia.foundation.architecture.system.ViewState
import co.theworldlab.luzia.features.settings.domain.models.ModeTheme
import io.getstream.chat.android.models.Channel
import io.getstream.chat.android.models.querysort.QuerySortByField.Companion.descByName
import io.getstream.chat.android.models.querysort.QuerySorter

data class StreamChatsViewState(
    val themeMode: ModeTheme = ModeTheme.SystemMode(false),
    val clientState: ClientState = ClientState.Initializing,
    val querySort: QuerySorter<Channel> =
        descByName<Channel>("last_updated").descByName("unread_count"),
    val newGroupLoading: Boolean = false
) : ViewState
