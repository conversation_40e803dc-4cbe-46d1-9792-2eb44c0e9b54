package co.thewordlab.luzia.features.stream.chatstab

import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class StreamChatsInTabViewEvents : ViewEvent {
    data object NavigateToBesties : StreamChatsInTabViewEvents()
    data object NavigateToSignup : StreamChatsInTabViewEvents()
    data object NavigateToProfileFill : StreamChatsInTabViewEvents()
    data object NavigateToReferral : StreamChatsInTabViewEvents()
    data object NavigateToNewMessage : StreamChatsInTabViewEvents()
    data class NavigateChatDetail(val cid: String) : StreamChatsInTabViewEvents()
}
