package co.thewordlab.luzia.features.stream.chatfriends

import co.thewordlab.luzia.core.sharing.domain.model.ShareChannel
import co.thewordlab.luzia.foundation.architecture.system.ViewState

data class AddFriendsViewState(
    val cid: String = "",
    val searchQuery: String = "",
    val isLoading: Boolean = false,
    val isSubmitting: Boolean = false,
    val selected: List<ShareChannel.Mate> = emptyList(),
    val schoolmates: List<ShareChannel> = emptyList(),
    val searchResults: List<ShareChannel> = emptyList(),
) : ViewState
