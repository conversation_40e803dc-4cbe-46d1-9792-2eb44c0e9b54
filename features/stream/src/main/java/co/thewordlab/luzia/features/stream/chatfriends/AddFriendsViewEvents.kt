package co.thewordlab.luzia.features.stream.chatfriends

import androidx.annotation.StringRes
import co.thewordlab.luzia.foundation.architecture.system.ViewEvent
import co.thewordlab.luzia.foundation.localization.R as localizationR

sealed class AddFriendsViewEvents : ViewEvent {
    data class ShowError(val error: AddFriendsError) : AddFriendsViewEvents()
    data object NavigateBack : AddFriendsViewEvents()
    data object HideKeyboard : AddFriendsViewEvents()
}

enum class AddFriendsError(@StringRes val errorMessageId: Int) {
    SCHOOLMATES_NOT_LOADED(localizationR.string.group_chat_error_loading_schoolmates),
    ADD_MEMBER_FAILED(localizationR.string.generic_error)
}
