package co.thewordlab.luzia.features.stream.chats

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.luzia.core.sharing.CreateGroupIconAction
import co.thewordlab.luzia.core.sharing.domain.usecase.GetConnectedClientUseCase
import co.thewordlab.luzia.core.sharing.domain.usecase.GetNewChatDecisionUseCase
import co.thewordlab.luzia.core.sharing.domain.usecase.model.NewChatDecision
import co.thewordlab.luzia.features.stream.chats.StreamChatsViewEvents.NavigateBack
import co.thewordlab.luzia.features.stream.chats.StreamChatsViewEvents.NavigateToNewChat
import co.thewordlab.luzia.features.stream.chats.StreamChatsViewEvents.OpenChat
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.theworldlab.luzia.features.settings.domain.models.ModeTheme
import co.theworldlab.luzia.features.settings.domain.repository.SettingsRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class StreamChatsViewModel @Inject constructor(
    private val getConnectedClientUseCase: GetConnectedClientUseCase,
    private val dispatcher: CoroutineDispatcher,
    private val settingsRepository: SettingsRepository,
    private val getNewChatDecisionUseCase: GetNewChatDecisionUseCase,
    private val analytics: Analytics
) : ViewModel(),
    ViewModelActions<StreamChatsViewActions>,
    ViewModelEvents<StreamChatsViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<StreamChatsViewState> by ViewModelStatesImpl(StreamChatsViewState()) {

    override fun onViewAction(action: StreamChatsViewActions) {
        when (action) {
            is StreamChatsViewActions.OnCreate -> {
                listenToThemeMode()
                initializeClient()
            }

            StreamChatsViewActions.OnBack -> sendEvent(NavigateBack)
            is StreamChatsViewActions.OnOpenChannel -> {
                sendEvent(OpenChat(action.channelId))
            }

            StreamChatsViewActions.OnNewChatTapped -> onNewChatTapped()
        }
    }

    private fun onNewChatTapped() = viewModelScope.launch {
        if (viewState.value.newGroupLoading) return@launch
        analytics.logAction(CreateGroupIconAction, mapOf(Parameter.EmptyState to true))
        updateState { it.copy(newGroupLoading = true) }
        getNewChatDecisionUseCase.invoke()?.let {
            when (it) {
                NewChatDecision.OpenNewChat -> sendEvent(NavigateToNewChat)
                is NewChatDecision.OpenChannel -> sendEvent(OpenChat(it.cid))
            }
        }
        updateState { it.copy(newGroupLoading = false) }
    }

    private fun initializeClient() {
        getConnectedClientUseCase.invoke()
            .onEach { client -> updateState { it.copy(clientState = client) } }
            .flowOn(dispatcher)
            .launchIn(viewModelScope)
    }

    private fun listenToThemeMode() {
        settingsRepository.themeModeFlow
            .onEach { updateTheme(it) }
            .launchIn(viewModelScope)
    }

    private fun updateTheme(themeId: Int) {
        updateState { it.copy(themeMode = ModeTheme.findTheme(themeId)) }
    }
}
