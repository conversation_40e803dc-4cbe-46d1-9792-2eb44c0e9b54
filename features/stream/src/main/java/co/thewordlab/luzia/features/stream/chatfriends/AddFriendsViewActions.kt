package co.thewordlab.luzia.features.stream.chatfriends

import co.thewordlab.luzia.core.sharing.domain.model.ShareChannel
import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class AddFriendsViewActions : ViewAction {
    data class OnCreate(val cid: String, val members: List<String>) : AddFriendsViewActions()
    data class OnSelect(val channel: ShareChannel) : AddFriendsViewActions()
    data class OnQueryChanged(val query: String) : AddFriendsViewActions()
    data object OnBack : AddFriendsViewActions()
    data object OnSubmit : AddFriendsViewActions()
}
