package co.thewordlab.luzia.features.stream.chatdetail

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes

fun NavGraphBuilder.getStreamChatDetail() {
    composable<UserSessionRoutes.GetStreamChatDetail> { backstackEntry ->
        val navigation = LocalNavigation.current
        val route = backstackEntry.toRoute<UserSessionRoutes.GetStreamChatDetail>()
        StreamChatDetailScreen(
            channelId = route.channelId,
            onBack = { navigation.goBack() }
        )
    }
}
