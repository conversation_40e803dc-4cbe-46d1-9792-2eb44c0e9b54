package co.thewordlab.luzia.features.stream.chatstab

import co.thewordlab.luzia.core.profile.presentation.entrypoints.chat.FriendsTab
import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class StreamChatsInTabViewActions : ViewAction {
    data object OnCreate : StreamChatsInTabViewActions()
    data object OnNavigateToSignup : StreamChatsInTabViewActions()
    data object OnBestiePointsClicked : StreamChatsInTabViewActions()
    data object OnNavigateToNewMessage : StreamChatsInTabViewActions()
    data class OnProfileEntryCta(val friendsTab: FriendsTab) : StreamChatsInTabViewActions()
}
