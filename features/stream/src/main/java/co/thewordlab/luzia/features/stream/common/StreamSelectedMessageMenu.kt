package co.thewordlab.luzia.features.stream.common

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.AnimationConstants
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.scaffold.LocalAppState
import io.getstream.chat.android.compose.ui.components.messageoptions.defaultMessageOptionsState
import io.getstream.chat.android.compose.ui.components.selectedmessage.SelectedMessageMenu
import io.getstream.chat.android.compose.viewmodel.messages.MessageComposerViewModel
import io.getstream.chat.android.compose.viewmodel.messages.MessageListViewModel
import io.getstream.chat.android.models.Message
import io.getstream.chat.android.models.User
import io.getstream.chat.android.ui.common.state.messages.BlockUser
import io.getstream.chat.android.ui.common.state.messages.Flag
import io.getstream.chat.android.ui.common.state.messages.UnblockUser
import io.getstream.chat.android.ui.common.state.messages.list.SelectedMessageOptionsState
import io.getstream.chat.android.ui.common.state.messages.updateMessage
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun BoxScope.StreamSelectedMessageMenu(
    listViewModel: MessageListViewModel,
    composerViewModel: MessageComposerViewModel,
    onBlockUser: (User) -> Unit,
    onUnblockUser: (User) -> Unit,
    onFlagMessage: (Message) -> Unit,
) {
    val selectedMessageState = listViewModel.currentMessagesState.selectedMessageState
    val selectedMessage = selectedMessageState?.message ?: Message()
    val user by listViewModel.user.collectAsState()
    val ownCapabilities = selectedMessageState?.ownCapabilities ?: setOf()
    val isVisible =
        selectedMessageState is SelectedMessageOptionsState && selectedMessage.id.isNotEmpty()
    val appState = LocalAppState.current
    val context = LocalContext.current
    val messageOptions = defaultMessageOptionsState(
        selectedMessage = selectedMessage,
        currentUser = user,
        isInThread = listViewModel.isInThread,
        ownCapabilities = ownCapabilities
    )
    AnimatedVisibility(
        visible = isVisible,
        enter = fadeIn(),
        exit = fadeOut(animationSpec = tween(durationMillis = AnimationConstants.DefaultDurationMillis / 2)),
    ) {
        SelectedMessageMenu(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .animateEnterExit(
                    enter = slideInVertically(
                        initialOffsetY = { height -> height },
                        animationSpec = tween(),
                    ),
                    exit = slideOutVertically(
                        targetOffsetY = { height -> height },
                        animationSpec = tween(durationMillis = AnimationConstants.DefaultDurationMillis / 2),
                    ),
                ),
            messageOptions = messageOptions,
            message = selectedMessage,
            ownCapabilities = ownCapabilities,
            onMessageAction = remember(composerViewModel, listViewModel) {
                {
                        action ->
                    action.updateMessage(
                        action.message.copy(
                            skipPushNotification = true,
                            skipEnrichUrl = false,
                        ),
                    ).let {
                        composerViewModel.performMessageAction(it)
                        listViewModel.performMessageAction(it)
                        when (it) {
                            is BlockUser -> {
                                val text =
                                    context.getString(localizationR.string.common_user_blocked_info)
                                appState.showSnackBar(text)
                                onBlockUser(it.message.user)
                            }

                            is UnblockUser -> {
                                onUnblockUser(it.message.user)
                            }

                            is Flag -> {
                                onFlagMessage(it.message)
                            }

                            else -> DO_NOTHING
                        }
                    }
                }
            },
            onShowMoreReactionsSelected = remember(listViewModel) {
                {
                    listViewModel.selectExtendedReactions(selectedMessage)
                }
            },
            onDismiss = remember(listViewModel) { { listViewModel.removeOverlay() } },
        )
    }
}
