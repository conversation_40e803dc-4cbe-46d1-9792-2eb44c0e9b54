package co.thewordlab.luzia.features.stream.chatmenu

import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class StreamChatMenuViewEvents : ViewEvent {
    data object NavigateBack : StreamChatMenuViewEvents()
    data object ShowReportedMessage : StreamChatMenuViewEvents()
    data class NavigateToUser(val masterUserId: String) : StreamChatMenuViewEvents()
    data class LinkGenerated(val link: String) : StreamChatMenuViewEvents()
    data class NavigateToRename(val cid: String, val name: String) : StreamChatMenuViewEvents()
    data class NavigateToImage(val cid: String, val url: String) : StreamChatMenuViewEvents()
    data class NavigateToAddFriends(val cid: String, val members: List<String>) : StreamChatMenuViewEvents()
}
