package co.thewordlab.luzia.features.stream.chatdetail

import co.thewordlab.luzia.foundation.architecture.system.ViewEvent

sealed class StreamChatDetailViewEvents : ViewEvent {
    data class NavigateToUser(val masterUserId: String) : StreamChatDetailViewEvents()
    data class LinkGenerated(val link: String) : StreamChatDetailViewEvents()
    data object NavigateBack : StreamChatDetailViewEvents()
    data class NavigateToAddFriends(val cid: String, val members: List<String>) :
        StreamChatDetailViewEvents()
}
