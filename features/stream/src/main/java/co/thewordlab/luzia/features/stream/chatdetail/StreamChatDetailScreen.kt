package co.thewordlab.luzia.features.stream.chatdetail

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes.ExternalProfile
import co.thewordlab.luzia.core.sharing.domain.ClientState
import co.thewordlab.luzia.features.stream.chatmenu.StreamChatMenuScreen
import co.thewordlab.luzia.features.stream.common.StreamMessagesScreen
import co.thewordlab.luzia.features.stream.theme.LuziaGetStreamTheme
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.OnResume
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.shareText
import co.theworldlab.luzia.foundation.design.system.components.scaffold.LocalAppState
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import com.halilibo.richtext.ui.RichTextThemeProvider
import io.getstream.chat.android.client.ChatClient
import io.getstream.chat.android.compose.ui.components.LoadingIndicator
import io.getstream.chat.android.compose.viewmodel.channels.ChannelListViewModel
import io.getstream.chat.android.compose.viewmodel.channels.ChannelViewModelFactory
import io.getstream.chat.android.compose.viewmodel.messages.MessagesViewModelFactory
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun StreamChatDetailScreen(
    channelId: String,
    onBack: () -> Unit
) {
    val viewModel: StreamChatDetailViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val navigation = LocalNavigation.current
    val context = LocalContext.current
    OnCreate("StreamChatDetailScreen") {
        viewModel.onViewAction(StreamChatDetailViewActions.OnCreate(channelId))
    }
    OnResume {
        viewModel.onViewAction(StreamChatDetailViewActions.OnResume)
    }
    ViewModelEventEffect(viewModel) {
        when (it) {
            StreamChatDetailViewEvents.NavigateBack -> onBack()
            is StreamChatDetailViewEvents.NavigateToUser -> {
                navigation.navigate(ExternalProfile(it.masterUserId))
            }

            is StreamChatDetailViewEvents.LinkGenerated -> {
                val text =
                    context.getString(localizationR.string.group_chats_invite_link_text, it.link)
                context.shareText(text)
            }

            is StreamChatDetailViewEvents.NavigateToAddFriends -> {
                navigation.navigate(UserSessionRoutes.GetStreamAddFriends(it.cid, it.members))
            }
        }
    }
    StreamChatDetailContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@Composable
private fun StreamChatDetailContent(
    viewState: StreamChatDetailViewState,
    onViewActions: (StreamChatDetailViewActions) -> Unit,
) {
    val appState = LocalAppState.current
    val context = LocalContext.current
    LuziaGetStreamTheme(viewState.themeMode) {
        Box {
            when (viewState.clientState) {
                ClientState.Failed -> {
                    val message = context.getString(localizationR.string.apiErrorTitle)
                    appState.showSnackBar(message)
                }

                ClientState.Initializing -> LoadingIndicator(modifier = Modifier.fillMaxSize())
                is ClientState.Ready -> MessagesReadyScreen(
                    client = viewState.clientState.client,
                    viewState = viewState,
                    onViewActions = onViewActions
                )
            }
        }
    }
}

@Composable
private fun MessagesReadyScreen(
    client: ChatClient,
    viewState: StreamChatDetailViewState,
    onViewActions: (StreamChatDetailViewActions) -> Unit,
) {
    viewState.channelId?.let {
        val context = LocalContext.current
        val channelViewModelFactory = ChannelViewModelFactory(client)
        val channelListViewModel: ChannelListViewModel =
            viewModel(
                ChannelListViewModel::class.java,
                key = null,
                factory = channelViewModelFactory
            )
        RichTextThemeProvider(
            textStyleProvider = { LuziaTheme.typography.body.regular.default },
            contentColorProvider = { LuziaTheme.palette.text.primary }
        ) {
            Box(Modifier.fillMaxSize()) {
                StreamMessagesScreen(
                    viewState = viewState,
                    onViewActions = onViewActions,
                    viewModelFactory = MessagesViewModelFactory(
                        context = context,
                        channelId = viewState.channelId,
                        messageLimit = 30
                    ),
                    onHeaderTitleClick = {
                        onViewActions(StreamChatDetailViewActions.OnChatSettingsOpen("title"))
                        channelListViewModel.selectChannel(it)
                    },
                    onChannelAvatarClick = {
                        onViewActions(StreamChatDetailViewActions.OnChatSettingsOpen("avatar"))
                        channelListViewModel.selectChannel(it)
                    }
                )
                StreamChatMenuScreen(
                    modifier = Modifier.navigationBarsPadding(),
                    listViewModel = channelListViewModel,
                    shouldGoBackWhenChannelDeleted = true,
                    onBack = { onViewActions(StreamChatDetailViewActions.OnBackPressed) }
                )
            }
        }
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        StreamChatDetailContent(
            viewState = StreamChatDetailViewState(),
            onViewActions = {}
        )
    }
}
