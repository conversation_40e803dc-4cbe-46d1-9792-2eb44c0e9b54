package co.thewordlab.luzia.features.stream.invite

import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class StreamInviteViewActions : ViewAction {
    data class OnCreate(val routes: UserSessionRoutes.Invite) : StreamInviteViewActions()
    data object OnDismiss : StreamInviteViewActions()
    data object OnAccept : StreamInviteViewActions()
    data object OnReject : StreamInviteViewActions()
}
