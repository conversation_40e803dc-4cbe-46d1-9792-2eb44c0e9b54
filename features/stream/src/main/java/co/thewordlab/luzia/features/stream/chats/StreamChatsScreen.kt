package co.thewordlab.luzia.features.stream.chats

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes.GetStreamChatDetail
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes.ReferralCode
import co.thewordlab.luzia.core.navigation.usersession.model.NewChatPurpose
import co.thewordlab.luzia.core.sharing.domain.ClientState
import co.thewordlab.luzia.features.stream.chatmenu.StreamChatMenuScreen
import co.thewordlab.luzia.features.stream.common.StreamChannelItem
import co.thewordlab.luzia.features.stream.theme.LuziaGetStreamTheme
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.scaffold.LocalAppState
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.addShadow
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonFilled
import co.theworldlab.luzia.foundation.design.system.legacy.composables.LuziaCardDefaults
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import io.getstream.chat.android.compose.ui.channels.list.ChannelList
import io.getstream.chat.android.compose.ui.components.LoadingIndicator
import io.getstream.chat.android.compose.ui.theme.ChatTheme
import io.getstream.chat.android.compose.viewmodel.channels.ChannelListViewModel
import io.getstream.chat.android.compose.viewmodel.channels.ChannelViewModelFactory
import io.getstream.chat.android.models.InitializationState
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun StreamChatsScreen() {
    val viewModel: StreamChatsViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val navigation = LocalNavigation.current
    val context = LocalContext.current
    val appState = LocalAppState.current
    OnCreate("StreamChatsScreen") {
        viewModel.onViewAction(StreamChatsViewActions.OnCreate)
    }
    ViewModelEventEffect(viewModel) {
        when (it) {
            StreamChatsViewEvents.NavigateBack -> {
                navigation.goBack()
            }

            is StreamChatsViewEvents.OpenChat -> {
                navigation.navigate(GetStreamChatDetail(it.channelId))
            }

            StreamChatsViewEvents.NavigateToReferral -> {
                navigation.navigate(ReferralCode)
            }

            StreamChatsViewEvents.NavigateToNewChat -> {
                navigation.navigate(UserSessionRoutes.NewChat(NewChatPurpose.MESSAGE))
            }

            is StreamChatsViewEvents.ShowFailedMessage -> {
                val message = context.getString(it.error.errorRes)
                appState.showSnackBar(message)
            }
        }
    }
    StreamChatsContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@Composable
private fun StreamChatsContent(
    viewState: StreamChatsViewState,
    onViewActions: (StreamChatsViewActions) -> Unit,
) {
    val appState = LocalAppState.current
    val context = LocalContext.current
    Card(
        modifier = Modifier
            .padding(horizontal = Spacing.X16.dp)
            .padding(top = Spacing.X16.dp)
            .addShadow()
            .fillMaxSize(),
        colors = LuziaCardDefaults.cardColors(),
        shape = RoundedCornerShape(topStart = Spacing.X16.dp, topEnd = Spacing.X16.dp)
    ) {
        ChatTheme {
            LuziaGetStreamTheme(viewState.themeMode) {
                Box(modifier = Modifier.fillMaxSize()) {
                    when (val state = viewState.clientState) {
                        ClientState.Failed -> {
                            val message = context.getString(localizationR.string.apiErrorTitle)
                            appState.showSnackBar(message)
                        }

                        ClientState.Initializing -> LoadingIndicator(modifier = Modifier.fillMaxSize())
                        is ClientState.Ready -> ChatsReadyScreen(
                            clientState = state,
                            viewState = viewState,
                            onViewActions = onViewActions
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ChatsReadyScreen(
    clientState: ClientState.Ready,
    viewState: StreamChatsViewState,
    onViewActions: (StreamChatsViewActions) -> Unit,
) {
    val state by clientState.client.clientState.initializationState.collectAsStateWithLifecycle()
    when (state) {
        InitializationState.INITIALIZING -> LoadingIndicator(modifier = Modifier.fillMaxSize())
        InitializationState.NOT_INITIALIZED -> DO_NOTHING
        InitializationState.COMPLETE -> {
            StreamChatsView(
                clientState = clientState,
                viewState = viewState,
                onViewActions = onViewActions
            )
        }
    }
}

@Composable
private fun StreamChatsView(
    clientState: ClientState.Ready,
    viewState: StreamChatsViewState,
    onViewActions: (StreamChatsViewActions) -> Unit,
) {
    val listViewModel: ChannelListViewModel = viewModel(
        modelClass = ChannelListViewModel::class.java,
        key = null,
        factory = ChannelViewModelFactory(
            chatClient = clientState.client,
            filters = clientState.filters,
            querySort = viewState.querySort,
        )
    )
    val channelsState = listViewModel.channelsState
    when {
        channelsState.isLoading -> LoadingIndicator(Modifier.fillMaxSize())
        channelsState.channelItems.isEmpty() -> EmptyChannelsView(viewState, onViewActions)
        else -> Box(modifier = Modifier.fillMaxSize()) {
            ChannelList(
                modifier = Modifier.fillMaxSize(),
                viewModel = listViewModel,
                onChannelClick = { channel ->
                    onViewActions(StreamChatsViewActions.OnOpenChannel(channel.cid))
                },
                onSearchResultClick = {},
                onChannelLongClick = remember(listViewModel) {
                    {
                        listViewModel.selectChannel(it)
                    }
                },
                channelContent = { itemState ->
                    val user by listViewModel.user.collectAsState()
                    StreamChannelItem(
                        channelItem = itemState,
                        currentUser = user,
                        onChannelClick = { channel ->
                            onViewActions(StreamChatsViewActions.OnOpenChannel(channel.cid))
                        },
                        onChannelLongClick = remember(listViewModel) {
                            {
                                listViewModel.selectChannel(it)
                            }
                        }
                    )
                }
            )
            StreamChatMenuScreen(
                listViewModel = listViewModel,
                shouldGoBackWhenChannelDeleted = false,
                onBack = { onViewActions(StreamChatsViewActions.OnBack) }
            )
        }
    }
}

@Composable
private fun EmptyChannelsView(
    viewState: StreamChatsViewState,
    onViewActions: (StreamChatsViewActions) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = Spacing.X16.dp),
        verticalArrangement = Arrangement.spacedBy(Spacing.X20.dp, Alignment.CenterVertically),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Image(
            modifier = Modifier.size(IconSizes.X100.dp),
            painter = painterResource(designR.drawable.im_group_chat),
            contentDescription = null
        )
        LuziaText(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(localizationR.string.group_chat_no_groups_title),
            style = LuziaTheme.typography.body.semiBold.default,
            color = LuziaTheme.palette.text.primary,
            textAlign = TextAlign.Center
        )
        LuziaText(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(localizationR.string.group_chats_empty_description),
            style = LuziaTheme.typography.body.regular.small,
            color = LuziaTheme.palette.text.secondary,
            textAlign = TextAlign.Center
        )
        ButtonFilled(
            modifier = Modifier.fillMaxWidth().testTag("buttonCreateNewChat"),
            isLoading = viewState.newGroupLoading,
            onClick = { onViewActions(StreamChatsViewActions.OnNewChatTapped) },
            buttonText = stringResource(localizationR.string.group_chats_empty_cta)
        )
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        StreamChatsContent(
            viewState = StreamChatsViewState(),
            onViewActions = {}
        )
    }
}
