package co.thewordlab.luzia.features.stream.invite

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.theworldlab.luzia.foundation.design.system.components.scaffold.LocalAppState
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonFilled
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import coil3.compose.AsyncImage
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun StreamInviteScreen(
    routes: UserSessionRoutes.Invite
) {
    val viewModel: StreamInviteViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val navigation = LocalNavigation.current
    val appState = LocalAppState.current
    val context = LocalContext.current
    OnCreate("StreamInviteScreen") {
        viewModel.onViewAction(StreamInviteViewActions.OnCreate(routes))
    }
    ViewModelEventEffect(viewModel) {
        when (it) {
            StreamInviteViewEvents.NavigateBack -> navigation.goBack()
            StreamInviteViewEvents.JoinFailedMessage -> {
                val message = context.getString(localizationR.string.generic_error)
                appState.showSnackBar(message)
            }

            is StreamInviteViewEvents.NavigateToChannel -> {
                navigation.navigate(UserSessionRoutes.GetStreamChatDetail(it.channelId))
            }
        }
    }
    StreamInviteContent(
        viewState = viewState,
        onViewActions = viewModel::onViewAction
    )
}

@Composable
private fun StreamInviteContent(
    viewState: StreamInviteViewState,
    onViewActions: (StreamInviteViewActions) -> Unit
) {
    Box(
        contentAlignment = Alignment.TopEnd,
        modifier = Modifier.padding(Spacing.X32.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(Corners.X5.dp))
                .background(LuziaTheme.palette.surface.content)
                .padding(Spacing.X24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            AsyncImage(
                modifier = Modifier
                    .size(Spacing.X56.dp)
                    .clip(CircleShape)
                    .align(Alignment.CenterHorizontally),
                contentScale = ContentScale.Crop,
                model = viewState.image,
                contentDescription = null
            )
            Spacing.X20.Vertical()
            LuziaText(
                modifier = Modifier.fillMaxWidth(),
                text = viewState.name,
                color = LuziaTheme.palette.text.primary,
                textAlign = TextAlign.Center,
                style = LuziaTheme.typography.body.semiBold.small
            )
            Spacing.X4.Vertical()
            LuziaText(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(localizationR.string.member_count, viewState.membersCount),
                color = LuziaTheme.palette.text.secondary,
                textAlign = TextAlign.Center,
                style = LuziaTheme.typography.body.regular.footnote
            )
            Spacing.X20.Vertical()
            ButtonFilled(
                modifier = Modifier.fillMaxWidth(),
                isLoading = viewState.isLoading,
                onClick = { onViewActions(StreamInviteViewActions.OnAccept) },
                buttonText = stringResource(localizationR.string.join_group_chat)
            )
            Spacing.X8.Vertical()
            ButtonFilled(
                modifier = Modifier.fillMaxWidth(),
                containerColor = Color.Transparent,
                contentColor = LuziaTheme.palette.interactive.brand,
                containerOnPressedColor = LuziaTheme.palette.interactive.brand.copy(alpha = 0.1f),
                onClick = { onViewActions(StreamInviteViewActions.OnReject) },
                buttonText = stringResource(localizationR.string.reject_invitation)
            )
            Spacing.X8.Vertical()
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
            ) {
                Icon(
                    painter = painterResource(designR.drawable.ic_info),
                    contentDescription = null,
                    tint = LuziaTheme.palette.text.secondary
                )
                LuziaText(
                    text = stringResource(localizationR.string.invitation_disclaimer),
                    style = LuziaTheme.typography.body.semiBold.caption,
                    color = LuziaTheme.palette.text.secondary
                )
            }
        }
        Box(modifier = Modifier.padding(Spacing.X8.dp)) {
            IconButton(
                modifier = Modifier.wrapContentWidth(),
                onClick = { onViewActions(StreamInviteViewActions.OnDismiss) }
            ) {
                Icon(
                    painter = painterResource(id = designR.drawable.ic_close),
                    contentDescription = null,
                    tint = LuziaTheme.palette.text.primary
                )
            }
        }
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        StreamInviteContent(
            viewState = StreamInviteViewState(
                id = "cid",
                membersCount = "78",
                image = "https://clipart-library.com/data_images/320477.png",
                name = "Art Group Chat"
            ),
            onViewActions = {}
        )
    }
}
