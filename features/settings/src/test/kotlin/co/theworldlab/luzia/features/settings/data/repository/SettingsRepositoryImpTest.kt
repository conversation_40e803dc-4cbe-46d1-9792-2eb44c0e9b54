package co.theworldlab.luzia.features.settings.data.repository

import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import co.thewordlab.fouundation.persistence.LuziaDataStore
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

@ExperimentalCoroutinesApi
class SettingsRepositoryImpTest {

    private lateinit var settingsRepository: SettingsRepositoryImp

    private val luziaDataStore = mockk<LuziaDataStore>()
    private val themeMode = intPreferencesKey("user_theme_mode")
    private val hapticFeedback = booleanPreferencesKey("user_haptic_feedback")
    private val overlayPreference = booleanPreferencesKey("screen_overlay_enabled")

    @Before
    fun setup() {
        // Setup default behavior for mocks
        every { luziaDataStore.getDataFlow(themeMode) } returns flowOf(1)
        every { luziaDataStore.getDataFlow(hapticFeedback) } returns flowOf(true)
        every { luziaDataStore.getDataFlow(overlayPreference) } returns flowOf(true)
        coEvery { luziaDataStore.saveData(any(), any<Int>()) } returns Unit
        coEvery { luziaDataStore.saveData(any(), any<Boolean>()) } returns Unit

        // Initialize the repository
        settingsRepository = SettingsRepositoryImp(luziaDataStore)
    }

    @Test
    fun `themeModeFlow should get data from LuziaDataStore`() = runTest {
        // When
        val result = settingsRepository.themeModeFlow.first()

        // Then
        assertEquals(1, result)
        verify { luziaDataStore.getDataFlow(themeMode) }
    }

    @Test
    fun `themeModeFlow should return default value when data is null`() = runTest {
        // Given
        every { luziaDataStore.getDataFlow(themeMode) } returns flowOf(null)

        // When
        val result = settingsRepository.themeModeFlow.first()

        // Then
        assertEquals(0, result)
        verify { luziaDataStore.getDataFlow(themeMode) }
    }

    @Test
    fun `hapticFeedbackFlow should get data from LuziaDataStore`() = runTest {
        // When
        val result = settingsRepository.hapticFeedbackFlow.first()

        // Then
        assertTrue(result)
        verify { luziaDataStore.getDataFlow(hapticFeedback) }
    }

    @Test
    fun `hapticFeedbackFlow should return default value when data is null`() = runTest {
        // Given
        every { luziaDataStore.getDataFlow(hapticFeedback) } returns flowOf(null)

        // When
        val result = settingsRepository.hapticFeedbackFlow.first()

        // Then
        assertFalse(result)
        verify { luziaDataStore.getDataFlow(hapticFeedback) }
    }

    @Test
    fun `overlayChoice should get data from LuziaDataStore`() = runTest {
        // When
        val result = settingsRepository.overlayChoice.first()

        // Then
        assertTrue(result)
        verify { luziaDataStore.getDataFlow(overlayPreference) }
    }

    @Test
    fun `overlayChoice should return default value when data is null`() = runTest {
        // Given
        every { luziaDataStore.getDataFlow(overlayPreference) } returns flowOf(null)

        // When
        val result = settingsRepository.overlayChoice.first()

        // Then
        assertFalse(result)
        verify { luziaDataStore.getDataFlow(overlayPreference) }
    }

    @Test
    fun `updateThemeMode should save data to LuziaDataStore`() = runTest {
        // When
        settingsRepository.updateThemeMode(2)

        // Then
        coVerify { luziaDataStore.saveData(themeMode, 2) }
    }

    @Test
    fun `updateHapticFeedback should save data to LuziaDataStore`() = runTest {
        // When
        settingsRepository.updateHapticFeedback(false)

        // Then
        coVerify { luziaDataStore.saveData(hapticFeedback, false) }
    }

    @Test
    fun `updateOverlayChoice should save data to LuziaDataStore`() = runTest {
        // When
        settingsRepository.updateOverlayChoice(false)

        // Then
        coVerify { luziaDataStore.saveData(overlayPreference, false) }
    }
}
