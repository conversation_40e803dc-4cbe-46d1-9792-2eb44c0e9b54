package co.theworldlab.luzia.features.settings.domain.models

const val ID_SYSTEM_MODE = 0
const val ID_LIGHT_MODE = 1
const val ID_DARK_MODE = 2

sealed class ModeTheme(val id: Int, val isSelected: <PERSON>olean, val label: String) {
    class SystemMode(isSelected: Boolean = false) : ModeTheme(ID_SYSTEM_MODE, isSelected, "system")
    class LightMode(isSelected: Boolean = false) : ModeTheme(ID_LIGHT_MODE, isSelected, "light")
    class DarkMode(isSelected: Boolean = false) : ModeTheme(ID_DARK_MODE, isSelected, "dark")

    companion object {
        fun findTheme(id: Int): ModeTheme {
            return when (id) {
                ID_SYSTEM_MODE -> SystemMode(true)
                ID_LIGHT_MODE -> LightMode(true)
                else -> DarkMode(true)
            }
        }
    }
}
