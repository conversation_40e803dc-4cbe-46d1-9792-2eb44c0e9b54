package co.theworldlab.luzia.features.settings.di

import co.thewordlab.fouundation.persistence.LuziaDataStore
import co.theworldlab.luzia.features.settings.data.repository.SettingsRepositoryImp
import co.theworldlab.luzia.features.settings.domain.repository.SettingsRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
object SettingsModule {

    @Provides
    fun provideRepository(
        luziaDataStore: LuziaDataStore
    ): SettingsRepository = SettingsRepositoryImp(luziaDataStore)
}
