package co.theworldlab.luzia.features.settings.presentation.settings

import co.thewordlab.luzia.core.navigation.usersession.model.SettingDestinations
import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class SettingViewActions : ViewAction {
    data class OnCreate(val destination: SettingDestinations) : SettingViewActions()
    data class OnContactUs(val message: String) : SettingViewActions()
    data object OnCloseContactUs : SettingViewActions()
    data object OnReferralClick : SettingViewActions()
    data object OnDeleteHistoryClicked : SettingViewActions()
    data class OnUpdateHapticFeedback(val enabled: Boolean) : SettingViewActions()
    data class OnLuziaOverlayToggle(val enabled: Boolean) : SettingViewActions()
    data object OnDeleteGuestAccount : SettingViewActions()
    data object OnDeleteGuestAccountConfirmed : SettingViewActions()
}
