@file:OptIn(ExperimentalMaterial3Api::class)

package co.theworldlab.luzia.features.settings.presentation.settings

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.content.FileProvider
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.core.navigation.usersession.model.SettingDestinations
import co.thewordlab.luzia.foundation.analytics.LocalAnalytics
import co.thewordlab.luzia.foundation.analytics.ScreenView
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.OnResume
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.features.settings.domain.models.ID_SYSTEM_MODE
import co.theworldlab.luzia.features.settings.presentation.getLanguageName
import co.theworldlab.luzia.features.settings.presentation.getNameMode
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.LuziaTextButton
import co.theworldlab.luzia.foundation.design.system.components.navbar.LuziaNavBarDefaults
import co.theworldlab.luzia.foundation.design.system.components.navbar.NavigationAction
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBar
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBarModel
import co.theworldlab.luzia.foundation.design.system.components.scaffold.LocalAppState
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonFilled
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonOutlined
import co.theworldlab.luzia.foundation.design.system.legacy.composables.LuziaListItemLeadingTrailingIconContent
import co.theworldlab.luzia.foundation.design.system.legacy.composables.LuziaSwitch
import co.theworldlab.luzia.foundation.design.system.legacy.composables.MenuListItem
import co.theworldlab.luzia.foundation.design.system.legacy.composables.TextFieldForm
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialog
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogButton
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogTextDefaults
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import kotlinx.coroutines.launch
import java.io.File
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

private const val MAX_MESSAGE_LENGTH = 500

fun NavGraphBuilder.settings() {
    composable<UserSessionRoutes.Settings> { backstackEntry ->
        val destination = backstackEntry.toRoute<UserSessionRoutes.Settings>().destinations
        SettingsScreen(destination)
    }
}

@Composable
private fun SettingsScreen(destination: SettingDestinations) {
    val viewModel: SettingsViewModel = hiltViewModel()
    val navigation = LocalNavigation.current
    var isDeleteChallengeOpen by remember { mutableStateOf(false) }
    val settingsNavigation = SettingsScreenNavigation(
        onNavigateBack = { navigation.goBack() },
        onNavigateToLanguage = { navigation.navigate(UserSessionRoutes.LanguageSelect) },
        onNavigateToAccount = { navigation.navigate(UserSessionRoutes.Account) },
        onNavigateToSignUp = { navigation.navigate(UserSessionRoutes.Signup(it)) },
        onNavigateToChangeTheme = { navigation.navigate(UserSessionRoutes.ThemeSelect) },
        onNavigateToPrivacyPolicyAndTermsAndConditions = {
            navigation.navigate(UserSessionRoutes.PrivacyPolicyTerms)
        },
        onNavigateToReferralCode = { navigation.navigate(UserSessionRoutes.ReferralCode) },
    )
    SettingsScreenScaffold(settingsNavigation) {
        SettingsScreenContent(
            destination = destination,
            onDeleteClick = { isDeleteChallengeOpen = true },
            settingsViewModel = viewModel,
            settingsNavigation = settingsNavigation
        )
        if (isDeleteChallengeOpen) {
            SettingsDeleteAlertDialog(
                onConfirm = {
                    viewModel.onViewAction(SettingViewActions.OnDeleteHistoryClicked)
                    isDeleteChallengeOpen = false
                },
                onDismiss = {
                    isDeleteChallengeOpen = false
                }
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SettingsScreenScaffold(
    settingsNavigation: SettingsScreenNavigation,
    content: @Composable BoxScope.() -> Unit,
) {
    Scaffold(
        topBar = {
            TopNavigationBar(
                TopNavigationBarModel(
                    title = stringResource(id = localizationR.string.settings),
                    navigationAction = NavigationAction.Icon(R.drawable.ic_back_arrow) {
                        settingsNavigation.onNavigateBack()
                    },
                    colors = LuziaNavBarDefaults.colors(containerColor = LuziaTheme.palette.surface.background),
                )
            )
        },
        containerColor = LuziaTheme.palette.surface.background
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(top = Spacing.X8.dp)
        ) {
            content()
        }
    }
}

@Composable
private fun SettingsDivider() {
    HorizontalDivider(color = LuziaTheme.palette.border.primary)
}

@Composable
private fun SettingsDeleteAlertDialog(onConfirm: () -> Unit, onDismiss: () -> Unit) {
    LuziaAlertDialog(
        confirmButton =
        LuziaAlertDialogButton(
            stringResource(id = localizationR.string.delete),
            action = onConfirm,
            isPrimaryAction = true
        ),
        dismissButton =
        LuziaAlertDialogButton(
            stringResource(id = localizationR.string.cancel),
            action = onDismiss
        ),
        title =
        LuziaAlertDialogText(
            stringResource(id = localizationR.string.delete_all_data_challenge_title),
            LuziaAlertDialogTextDefaults.Title()
        ),
        text =
        LuziaAlertDialogText(
            stringResource(id = localizationR.string.delete_all_data_challenge_description),
            LuziaAlertDialogTextDefaults.Description()
        )
    )
}

@Composable
private fun SettingsScreenHapticFeedbackContent(
    viewState: SettingViewState,
    onViewActions: (SettingViewActions) -> Unit,
) {
    Spacer(modifier = Modifier.height(Spacing.X16.dp))
    Row(
        modifier = Modifier.padding(horizontal = Spacing.X16.dp),
        horizontalArrangement = Arrangement.spacedBy(Spacing.X24.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f, fill = false),
            verticalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
        ) {
            LuziaText(
                stringResource(id = localizationR.string.haptic_feedback),
                style = LuziaTheme.typography.body.semiBold.default,
                color = LuziaTheme.palette.text.primary
            )
            LuziaText(
                stringResource(id = localizationR.string.haptic_feedback_description),
                modifier = Modifier.weight(1f, fill = false),
                style = LuziaTheme.typography.body.regular.small,
                color = LuziaTheme.palette.text.secondary
            )
        }
        LuziaSwitch(
            checked = viewState.hapticEnabled,
            onCheckedChange = {
                onViewActions(SettingViewActions.OnUpdateHapticFeedback(it))
            }
        )
    }
    Spacer(modifier = Modifier.height(Spacing.X16.dp))
}

@Suppress("LongMethod", "CyclomaticComplexMethod")
@Composable
private fun SettingsScreenContent(
    destination: SettingDestinations,
    onDeleteClick: () -> Unit,
    settingsViewModel: SettingsViewModel,
    settingsNavigation: SettingsScreenNavigation,
) {
    val state = settingsViewModel.viewState.collectAsStateWithLifecycle().value
    val context = LocalContext.current
    val analytics = LocalAnalytics.current
    var showContactUs by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    val appState = LocalAppState.current

    ViewModelEventEffect(settingsViewModel) {
        when (it) {
            SettingViewEvents.ShowContactUs -> showContactUs = true
            is SettingViewEvents.OpenContactUs -> context.openContactUs(it.file, it.message)
            SettingViewEvents.NavigateToReferral -> settingsNavigation.onNavigateToReferralCode()
            SettingViewEvents.ShowDeleteDialog ->
                showDeleteDialog = true

            SettingViewEvents.ShowGenericError ->
                appState.showSnackBar(context.getString(localizationR.string.generic_error))

            SettingViewEvents.NavigateToSignup ->
                settingsNavigation.onNavigateToSignUp(true)
        }
    }

    OnCreate("SettingsScreen") {
        settingsViewModel.onViewAction(
            SettingViewActions.OnCreate(
                destination
            )
        )
    }

    if (showContactUs) {
        ContactUsBottomSheet(
            onClose = { showContactUs = false },
            onViewAction = { settingsViewModel.onViewAction(it) }
        )
    }

    if (showDeleteDialog) {
        DeleteAccountDialog(
            onDismiss = { showDeleteDialog = false },
            onViewActions = {
                showDeleteDialog = false
                settingsViewModel.onViewAction(it)
            }
        )
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(bottom = Spacing.X8.dp)
    ) {
        Column {
            if (state.referralEnabled) {
                MenuListItem(
                    modifier = Modifier
                        .click { settingsViewModel.onViewAction(SettingViewActions.OnReferralClick) }
                        .padding(Spacing.X16.dp),
                    headlineTextInBold = true,
                    headlineText = stringResource(id = localizationR.string.referral_invite_section),
                    trailingItem = {
                        LuziaListItemLeadingTrailingIconContent(
                            painter = painterResource(id = designR.drawable.ic_chevron_right),
                            contentDescription = stringResource(id = localizationR.string.chevron_right),
                            tint = LuziaTheme.palette.text.primary
                        ).View()
                    }
                )
                SettingsDivider()
            }
            MenuListItem(
                modifier = Modifier
                    .click {
                        if (state.isFullUser) {
                            settingsNavigation.onNavigateToAccount()
                        } else {
                            settingsNavigation.onNavigateToSignUp(false)
                        }
                    }
                    .padding(Spacing.X16.dp),
                headlineTextInBold = true,
                headlineText = stringResource(id = localizationR.string.account_section),
                trailingItem = {
                    LuziaListItemLeadingTrailingIconContent(
                        painter = painterResource(id = designR.drawable.ic_chevron_right),
                        contentDescription = stringResource(id = localizationR.string.chevron_right),
                        tint = LuziaTheme.palette.text.primary
                    ).View()
                }
            )
            SettingsDivider()
            if (state.isFullUser) {
                MenuListItem(
                    modifier = Modifier
                        .click { settingsNavigation.onNavigateToLanguage() }
                        .padding(Spacing.X16.dp),
                    headlineText = stringResource(id = localizationR.string.title_language_app),
                    supportingText = getLanguageName(context = context),
                    supportingTextMaxLines = 1,
                    trailingItem = {
                        LuziaListItemLeadingTrailingIconContent(
                            painter = painterResource(id = designR.drawable.ic_chevron_right),
                            contentDescription = stringResource(id = localizationR.string.chevron_right),
                            tint = LuziaTheme.palette.text.primary
                        ).View()
                    }
                )
                SettingsDivider()
            }
            MenuListItem(
                modifier = Modifier
                    .click { settingsNavigation.onNavigateToChangeTheme() }
                    .padding(Spacing.X16.dp),
                headlineText = stringResource(id = localizationR.string.settings_theme),
                supportingText = stringResource(
                    id = getNameMode(state.themeModeId ?: ID_SYSTEM_MODE)
                ),
                supportingTextMaxLines = 1,
                trailingItem = {
                    LuziaListItemLeadingTrailingIconContent(
                        painter = painterResource(id = designR.drawable.ic_chevron_right),
                        contentDescription = stringResource(id = localizationR.string.chevron_right),
                        tint = LuziaTheme.palette.text.primary
                    ).View()
                }
            )
            SettingsDivider()
            MenuListItem(
                modifier = Modifier
                    .click {
                        analytics.logScreenView(ScreenView.PrivacyAndTerms)
                        settingsNavigation.onNavigateToPrivacyPolicyAndTermsAndConditions()
                    }
                    .padding(Spacing.X16.dp),
                headlineTextInBold = true,
                headlineText = stringResource(id = localizationR.string.privacy_and_terms_label),
                trailingItem = {
                    LuziaListItemLeadingTrailingIconContent(
                        painter = painterResource(id = designR.drawable.ic_chevron_right),
                        contentDescription = stringResource(id = localizationR.string.chevron_right),
                        tint = LuziaTheme.palette.text.primary
                    ).View()
                }
            )
            SettingsDivider()
            SettingsScreenHapticFeedbackContent(state) {
                settingsViewModel.onViewAction(it)
            }
            if (state.showLuziaOverlayOption) {
                SettingsScreenOverlayToggleIntent(state) {
                    settingsViewModel.onViewAction(it)
                }
            }
            SettingsDivider()
            MenuListItem(
                modifier = Modifier
                    .click { showContactUs = true }
                    .padding(Spacing.X16.dp),
                headlineTextInBold = true,
                headlineText = stringResource(id = localizationR.string.contact_us),
                trailingItem = {
                    LuziaListItemLeadingTrailingIconContent(
                        painter = painterResource(id = designR.drawable.ic_chevron_right),
                        contentDescription = stringResource(id = localizationR.string.chevron_right),
                        tint = LuziaTheme.palette.text.primary
                    ).View()
                }
            )
            SettingsDivider()
            Spacer(modifier = Modifier.height(Spacing.X16.dp))
            Column(modifier = Modifier.padding(horizontal = Spacing.X16.dp)) {
                LuziaText(
                    stringResource(id = localizationR.string.your_data),
                    style = LuziaTheme.typography.body.semiBold.default,
                    color = LuziaTheme.palette.text.primary
                )
                Spacing.X8.Vertical()
                LuziaText(
                    stringResource(id = localizationR.string.your_data_description),
                    style = LuziaTheme.typography.body.regular.small,
                    color = LuziaTheme.palette.text.secondary
                )
            }
        }
        Spacing.X24.Vertical()
        ButtonOutlined(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = Spacing.X16.dp),
            onClick = onDeleteClick,
            buttonText = stringResource(id = localizationR.string.delete_all_data),
            enabled = !state.isDataEmpty
        )

        if (!state.isFullUser) {
            Spacing.X24.Vertical()
            Column(modifier = Modifier.padding(horizontal = Spacing.X16.dp)) {
                LuziaText(
                    stringResource(id = localizationR.string.delete_guest_account_description),
                    style = LuziaTheme.typography.body.regular.small,
                    color = LuziaTheme.palette.text.secondary
                )
                Spacing.X16.Vertical()
                ButtonOutlined(
                    modifier = Modifier.fillMaxWidth(),
                    onClick = { settingsViewModel.onViewAction(SettingViewActions.OnDeleteGuestAccount) },
                    buttonText = stringResource(id = localizationR.string.delete_guest_account_title)
                )
            }
        }
        Spacing.X24.Vertical()
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun ContactUsBottomSheet(
    onClose: () -> Unit,
    onViewAction: (SettingViewActions) -> Unit,
) {
    val coroutineScope = rememberCoroutineScope()
    val bottomSheetState = rememberModalBottomSheetState(true)
    val focusRequester = remember { FocusRequester() }
    var messageToShare by remember { mutableStateOf("") }

    fun closeSheet() {
        coroutineScope.launch {
            onClose()
            bottomSheetState.hide()
        }
    }

    ModalBottomSheet(
        modifier = Modifier
            .statusBarsPadding()
            .semantics { testTagsAsResourceId = true }
            .testTag("containerContactUs"),
        onDismissRequest = ::closeSheet,
        containerColor = LuziaTheme.palette.surface.content,
        dragHandle = null,
        sheetState = bottomSheetState
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.X16.dp)
                .verticalScroll(rememberScrollState())
        ) {
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.TopEnd
            ) {
                IconButton(
                    onClick = {
                        onViewAction(SettingViewActions.OnCloseContactUs)
                        onClose()
                    },
                    content = {
                        Icon(
                            painter = painterResource(designR.drawable.ic_close),
                            contentDescription = null,
                            tint = LuziaTheme.palette.text.primary
                        )
                    }
                )
                LuziaText(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = Spacing.X8.dp),
                    text = stringResource(localizationR.string.contact_us),
                    style = LuziaTheme.typography.headlines.h4,
                    color = LuziaTheme.palette.text.primary,
                    textAlign = TextAlign.Center
                )
            }
            Spacer(Modifier.height(Spacing.X16.dp))
            TextFieldForm(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(min = 100.dp),
                singleLine = false,
                messageText = messageToShare,
                placeholderText = stringResource(localizationR.string.contact_us_type_message_placeholder),
                onDoneButtonKBListener = ({}),
                onTextChanged = { messageToShare = it },
                focusRequester = focusRequester,
                limitChar = MAX_MESSAGE_LENGTH,
                supportingText = String.format(
                    stringResource(localizationR.string.contact_us_char_left),
                    MAX_MESSAGE_LENGTH - messageToShare.length
                )
            )
            Spacer(Modifier.height(Spacing.X8.dp))
            ButtonFilled(
                modifier = Modifier.fillMaxWidth(),
                onClick = {
                    closeSheet()
                    onViewAction(SettingViewActions.OnContactUs(messageToShare))
                },
                enabled = messageToShare.trim().isNotEmpty(),
                buttonText = stringResource(id = localizationR.string.tool_maths_image_send_cta)
            )
        }
    }
}

private fun Context.openContactUs(file: File, message: String) {
    val authority = "$packageName.fileprovider"
    val mailTo = "<EMAIL>"
    val subject = getString(localizationR.string.contact_us_title)
    val to = arrayOf(mailTo)
    val uri = FileProvider.getUriForFile(this, authority, file)
    val selectorIntent = Intent(Intent.ACTION_SENDTO).apply {
        data = Uri.parse("mailto:") // only email apps should handle this
    }
    val emailIntent = Intent(Intent.ACTION_SEND).apply {
        putExtra(Intent.EXTRA_EMAIL, to)
        putExtra(Intent.EXTRA_SUBJECT, subject)
        putExtra(Intent.EXTRA_TEXT, message)
        putExtra(Intent.EXTRA_STREAM, uri)
        selector = selectorIntent
    }
    if (emailIntent.resolveActivity(packageManager) != null) {
        startActivity(emailIntent)
    }
}

@Composable
private fun DeleteAccountDialog(
    onDismiss: () -> Unit,
    onViewActions: (SettingViewActions) -> Unit,
) {
    LuziaAlertDialog(
        confirmButton = LuziaAlertDialogButton(
            title = stringResource(id = localizationR.string.delete_button),
            action = { onViewActions(SettingViewActions.OnDeleteGuestAccountConfirmed) },
            isPrimaryAction = true
        ),
        dismissButton = LuziaAlertDialogButton(
            title = stringResource(id = localizationR.string.cancel),
            action = { onDismiss() }
        ),
        title = LuziaAlertDialogText(
            stringResource(id = localizationR.string.delete_guest_account_confirmation_title),
            LuziaAlertDialogTextDefaults.Title()
        ),
        text = LuziaAlertDialogText(
            stringResource(id = localizationR.string.delete_guest_account_confirmation_desc),
            LuziaAlertDialogTextDefaults.Description()
        )
    )
}

@Composable
private fun SettingsScreenOverlayToggleIntent(
    viewState: SettingViewState,
    onViewActions: (SettingViewActions) -> Unit,
) {
    var overlayPermissionGranted by remember { mutableStateOf(false) }
    val context = LocalContext.current
    var showPermissionRationale by remember { mutableStateOf(false) }
    val settingsLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult(),
        onResult = { DO_NOTHING }
    )
    OnResume {
        overlayPermissionGranted = Settings.canDrawOverlays(context)
    }
    Spacer(modifier = Modifier.height(Spacing.X16.dp))
    Row(
        modifier = Modifier.padding(horizontal = Spacing.X16.dp),
        horizontalArrangement = Arrangement.spacedBy(Spacing.X24.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f, fill = false),
            verticalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
        ) {
            LuziaText(
                stringResource(id = localizationR.string.luzia_overlay_toggle),
                style = LuziaTheme.typography.body.semiBold.default,
                color = LuziaTheme.palette.text.primary
            )
            LuziaText(
                stringResource(id = localizationR.string.haptic_feedback_description),
                modifier = Modifier.weight(1f, fill = false),
                style = LuziaTheme.typography.body.regular.small,
                color = LuziaTheme.palette.text.secondary
            )
        }
        if (!overlayPermissionGranted) {
            LuziaTextButton(
                onClick = { showPermissionRationale = true },
                text = stringResource(localizationR.string.permission_needed)
            )
        } else {
            LuziaSwitch(
                checked = viewState.luziaOverlayEnabled && Settings.canDrawOverlays(context),
                onCheckedChange = { turnedOn ->
                    onViewActions(SettingViewActions.OnLuziaOverlayToggle(turnedOn))
                }
            )
        }
    }
    Spacer(modifier = Modifier.height(Spacing.X16.dp))
    if (showPermissionRationale) {
        DisplayOverOtherAppsRationaleDialog(
            onConfirm = {
                onViewActions(SettingViewActions.OnLuziaOverlayToggle(true))
                showPermissionRationale = false
                val intent = Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    "package:${context.packageName}".toUri()
                )
                settingsLauncher.launch(intent)
            },
            onDismiss = { showPermissionRationale = false }
        )
    }
}

@Composable
private fun DisplayOverOtherAppsRationaleDialog(
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    val title = stringResource(localizationR.string.permission_needed)
    val settingsTitle = stringResource(localizationR.string.settings)
    val laterText = stringResource(localizationR.string.ask_me_later_text)
    val description = stringResource(localizationR.string.luzia_overlay_permission_text)
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text(text = title) },
        text = { Text(text = description) },
        confirmButton = { TextButton(onClick = onConfirm) { Text(settingsTitle) } },
        dismissButton = { TextButton(onClick = onDismiss) { Text(laterText) } }
    )
}
