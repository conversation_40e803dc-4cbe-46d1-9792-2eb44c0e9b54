package co.theworldlab.luzia.features.settings.presentation.account

import co.thewordlab.luzia.foundation.architecture.system.ViewAction

sealed class AccountViewActions : ViewAction {
    data object OnCreate : AccountViewActions()
    data object OnStart : AccountViewActions()
    data object OnNavigateBackClicked : AccountViewActions()
    data class OnConfirmUsernameClicked(val userName: String) : AccountViewActions()
    data object OnDeleteAccountClicked : AccountViewActions()
    data object OnLogoutClicked : AccountViewActions()
    data object OnLogoutOrDeleteAccountSuccess : AccountViewActions()
}
