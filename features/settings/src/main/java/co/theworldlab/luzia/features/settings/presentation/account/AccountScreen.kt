package co.theworldlab.luzia.features.settings.presentation.account

import android.Manifest
import android.app.Activity
import android.content.ContentResolver
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.stringResource
import androidx.core.app.ActivityCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.thewordlab.luzia.core.signup.handler.rememberAuthState
import co.thewordlab.luzia.foundation.architecture.system.OnCreate
import co.thewordlab.luzia.foundation.architecture.system.OnStart
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.ImageUtils
import co.thewordlab.luzia.foundation.localization.R
import co.theworldlab.luzia.features.imagine.domain.MyImagineImage
import co.theworldlab.luzia.foundation.design.system.components.navbar.LuziaNavBarDefaults
import co.theworldlab.luzia.foundation.design.system.components.navbar.NavigationAction
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBar
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBarModel
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonOutlined
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.TextFieldFormWithLabel
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialog
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogButton
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogTextDefaults
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaLoadingAlertDialog
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR

private const val INITIAL_SCALE = 0.8f
private const val TARGET_SCALE = 0.95f
private const val SLIDE_OUT_OFFSET = 8

@Suppress("LongMethod")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun AccountScreen(onNavigateBack: () -> Unit, onNavigateToSignUp: () -> Unit) {
    val viewModel: AccountViewModel = hiltViewModel()
    val uiAccountState = viewModel.viewState.collectAsStateWithLifecycle().value
    val context = LocalContext.current

    OnCreate("AccountScreen") { viewModel.onViewAction(AccountViewActions.OnCreate) }
    OnStart { viewModel.onViewAction(AccountViewActions.OnStart) }

    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current

    var userNameEditionEnabled by remember { mutableStateOf(false) }
    var userNameText by remember { mutableStateOf("") }
    var userPhone by remember { mutableStateOf("") }
    var signOutDialogVisible by remember { mutableStateOf(false) }
    var deleteAccountDialogVisible by remember { mutableStateOf(false) }
    val loadingDialogVisible by remember(uiAccountState.loading) { mutableStateOf(uiAccountState.loading) }
    var userNameIsError by remember(uiAccountState.errorUsernameEmpty) {
        mutableStateOf(uiAccountState.errorUsernameEmpty)
    }
    val contentResolver: ContentResolver = LocalContext.current.contentResolver
    val activity = LocalView.current.context as Activity
    var isPermissionDialogVisible by remember { mutableStateOf(false) }
    val authState = rememberAuthState(viewModel.googleWebAuthManager)

    val intentSenderLauncher: ActivityResultLauncher<IntentSenderRequest> =
        rememberLauncherForActivityResult(ActivityResultContracts.StartIntentSenderForResult()) {
            viewModel.onViewAction(AccountViewActions.OnLogoutOrDeleteAccountSuccess)
        }

    val launcherWriteExternalStorage =
        rememberLauncherForActivityResult(
            contract = ActivityResultContracts.RequestPermission()
        ) { isGranted ->
            if (!isGranted) {
                if (!ActivityCompat.shouldShowRequestPermissionRationale(
                        activity,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE
                    )
                ) {
                    isPermissionDialogVisible = true
                }
            } else {
                deleteAccountDialogVisible = true
            }
        }

    LaunchedEffect(userNameEditionEnabled) {
        if (userNameEditionEnabled) {
            focusRequester.requestFocus()
        } else {
            focusManager.clearFocus()
        }
    }

    LaunchedEffect(uiAccountState.phone, uiAccountState.userName) {
        userNameText = uiAccountState.userName
        userPhone = uiAccountState.phone
    }

    LaunchedEffect(uiAccountState.isEditedSuccess) {
        if (uiAccountState.isEditedSuccess) {
            userNameEditionEnabled = false
        }
    }

    ViewModelEventEffect(events = viewModel) { event ->
        when (event) {
            AccountViewEvents.ShowError ->
                Toast.makeText(
                    context,
                    context.getString(R.string.generic_error),
                    Toast.LENGTH_LONG
                ).show()

            AccountViewEvents.NavigateBack -> onNavigateBack()
            AccountViewEvents.NavigateToSignUp -> onNavigateToSignUp()
            is AccountViewEvents.DeleteAllImages ->
                deleteAllImages(
                    event.images,
                    contentResolver,
                    intentSenderLauncher
                )

            AccountViewEvents.LogoutFromGoogleAuth -> authState.logout()
        }
    }

    if (loadingDialogVisible) {
        LuziaLoadingAlertDialog(title = stringResource(id = R.string.loading_update_data))
    }

    AnimatedVisibility(
        visible = signOutDialogVisible,
        enter = fadeIn(spring(stiffness = Spring.StiffnessHigh)) +
            scaleIn(
                initialScale = INITIAL_SCALE,
                animationSpec =
                spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessMediumLow
                )
            ),
        exit = slideOutVertically { it / SLIDE_OUT_OFFSET } + fadeOut() + scaleOut(targetScale = TARGET_SCALE)
    ) {
        LuziaAlertDialog(
            confirmButton = LuziaAlertDialogButton(
                title = stringResource(id = R.string.signout_button),
                action = {
                    signOutDialogVisible = false
                    viewModel.onViewAction(AccountViewActions.OnLogoutClicked)
                },
                isPrimaryAction = true
            ),
            dismissButton = LuziaAlertDialogButton(
                title = stringResource(id = R.string.cancel),
                action = {
                    signOutDialogVisible = false
                }
            ),
            title = LuziaAlertDialogText(
                stringResource(id = R.string.title_dialog_signout),
                LuziaAlertDialogTextDefaults.Title()
            ),
            text = LuziaAlertDialogText(
                stringResource(id = R.string.info_dialog_signout),
                LuziaAlertDialogTextDefaults.Description()
            )
        )
    }

    AnimatedVisibility(
        visible = deleteAccountDialogVisible,
        enter = fadeIn(spring(stiffness = Spring.StiffnessHigh)) +
            scaleIn(
                initialScale = INITIAL_SCALE,
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessMediumLow
                )
            ),
        exit = slideOutVertically { it / SLIDE_OUT_OFFSET } + fadeOut() + scaleOut(targetScale = TARGET_SCALE)
    ) {
        LuziaAlertDialog(
            confirmButton = LuziaAlertDialogButton(
                title = stringResource(id = R.string.delete_button),
                action = {
                    deleteAccountDialogVisible = false
                    viewModel.onViewAction(AccountViewActions.OnDeleteAccountClicked)
                },
                isPrimaryAction = true
            ),
            dismissButton = LuziaAlertDialogButton(
                title = stringResource(id = R.string.cancel),
                action = {
                    deleteAccountDialogVisible = false
                }
            ),
            title = LuziaAlertDialogText(
                stringResource(id = R.string.title_dialog_delete_account),
                LuziaAlertDialogTextDefaults.Title()
            ),
            text = LuziaAlertDialogText(
                stringResource(id = R.string.info_dialog_delete_account),
                LuziaAlertDialogTextDefaults.Description()
            )
        )
    }

    Scaffold(
        topBar = {
            TopNavigationBar(
                TopNavigationBarModel(
                    title = stringResource(id = R.string.account_section),
                    navigationAction = NavigationAction.Icon(designR.drawable.ic_back_arrow) {
                        viewModel.onViewAction(AccountViewActions.OnNavigateBackClicked)
                    },
                    colors = LuziaNavBarDefaults.colors(containerColor = LuziaTheme.palette.surface.background),
                    actions = if (userNameEditionEnabled) {
                        listOf(
                            NavigationAction.Icon(designR.drawable.ic_check) {
                                viewModel.onViewAction(
                                    AccountViewActions.OnConfirmUsernameClicked(
                                        userNameText
                                    )
                                )
                            }
                        )
                    } else {
                        emptyList()
                    }
                )
            )
        },
        containerColor = LuziaTheme.palette.surface.background
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(LuziaTheme.palette.surface.background)
                .navigationBarsPadding()
                .padding(top = paddingValues.calculateTopPadding())
                .imePadding()
                .verticalScroll(rememberScrollState())
                .padding(Spacing.X16.dp)
        ) {
            TextFieldFormWithLabel(
                label = stringResource(id = R.string.username_label),
                onDoneButtonKBListener = { focusManager.clearFocus() },
                onTextChanged = {
                    userNameIsError = false
                    userNameText = it
                },
                isEnabled = userNameEditionEnabled,
                isError = userNameIsError,
                focusRequester = focusRequester,
                supportingText = stringResource(id = R.string.username_supporting_text),
                textError = stringResource(id = R.string.error_field_required),
                isEditable = true,
                onClickEditable = { userNameEditionEnabled = true },
                messageText = userNameText,
                limitChar = 30,
                placeholderText = stringResource(id = R.string.username_placeholder)
            )
            Spacer(modifier = Modifier.height(Spacing.X24.dp))

            TextFieldFormWithLabel(
                label = stringResource(id = R.string.email_label),
                messageText = uiAccountState.email,
                isNumericTextField = false,
                limitChar = 15,
                focusRequester = focusRequester,
                singleLine = true,
                isEnabled = false
            )

            Spacer(modifier = Modifier.height(Spacing.X24.dp))
            ButtonOutlined(
                modifier = Modifier.fillMaxWidth(),
                onClick = {
                    signOutDialogVisible = true
                },
                buttonText = stringResource(id = R.string.signout_button),
                enabled = !userNameEditionEnabled
            )
            Spacer(modifier = Modifier.height(Spacing.X24.dp))
            ButtonText(
                modifier = Modifier.fillMaxWidth(),
                onClick = {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S_V2) {
                        deleteAccountDialogVisible = true
                    } else {
                        launcherWriteExternalStorage.launch(Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    }
                },
                buttonText = stringResource(id = R.string.delete_account_button),
                enabled = !userNameEditionEnabled,
                contentColor = LuziaTheme.palette.text.primary
            )
        }
    }
}

@Suppress("NestedBlockDepth", "ReturnCount")
private fun deleteAllImages(
    list: List<MyImagineImage>,
    contentResolver: ContentResolver,
    intentSenderLauncher: ActivityResultLauncher<IntentSenderRequest>
): Boolean? {
    val errorImagesList = mutableListOf<Uri?>()

    list.forEach { myImagineImage ->
        myImagineImage.file?.let {
            if (it.exists()) {
                val imageUri = ImageUtils.getImageContentUri(it, contentResolver)
                try {
                    if (imageUri != null) {
                        val rowsDeleted = contentResolver.delete(imageUri, null, null)
                        if (rowsDeleted > 0) {
                            if (it.exists()) {
                                it.delete()
                            }
                        }
                    }
                } catch (expected: SecurityException) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        errorImagesList.add(imageUri)
                    } else {
                        return false
                    }
                } catch (expected: Exception) {
                    return false
                }
            }
        }
    }
    if (errorImagesList.isEmpty()) {
        return true
    } else {
        val intentSender =
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> {
                    MediaStore.createDeleteRequest(contentResolver, errorImagesList).intentSender
                }

                else -> null
            }
        intentSender?.let { sender ->
            intentSenderLauncher.launch(
                IntentSenderRequest.Builder(sender).build()
            )
        }
        return null
    }
}
