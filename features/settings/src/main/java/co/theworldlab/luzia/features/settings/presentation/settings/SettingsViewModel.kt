package co.theworldlab.luzia.features.settings.presentation.settings

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.fouundation.persistence.chat.ChatDao
import co.thewordlab.luzia.core.navigation.usersession.model.SettingDestinations
import co.thewordlab.luzia.core.profile.domain.GetUserProfileUseCase
import co.thewordlab.luzia.core.profile.domain.model.UserType
import co.thewordlab.luzia.features.profile.presentation.OpenReferralCode
import co.thewordlab.luzia.features.profile.presentation.REFERRAL_SOURCE_SETTINGS
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Event
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.architecture.system.ViewModelActions
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEvents
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventsImpl
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStates
import co.thewordlab.luzia.foundation.architecture.system.ViewModelStatesImpl
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.theworldlab.luzia.features.settings.ContactUsCancel
import co.theworldlab.luzia.features.settings.ContactUsSend
import co.theworldlab.luzia.features.settings.DeleteAccount
import co.theworldlab.luzia.features.settings.Settings
import co.theworldlab.luzia.features.settings.domain.DeleteAccountUseCase
import co.theworldlab.luzia.features.settings.domain.repository.SettingsRepository
import co.theworldlab.luzia.features.settings.presentation.contact.ContactReportGenerator
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val getUserProfileUseCase: GetUserProfileUseCase,
    private val dispatcher: CoroutineDispatcher,
    private val chatDao: ChatDao,
    private val contactReportGenerator: ContactReportGenerator,
    private val analytics: Analytics,
    private val featureFlagManager: FeatureFlagManager,
    private val settingsRepository: SettingsRepository,
    private val deleteAccountUseCase: DeleteAccountUseCase,
) : ViewModel(),
    ViewModelActions<SettingViewActions>,
    ViewModelEvents<SettingViewEvents> by ViewModelEventsImpl(),
    ViewModelStates<SettingViewState> by ViewModelStatesImpl(SettingViewState()) {

    override fun onViewAction(action: SettingViewActions) {
        when (action) {
            is SettingViewActions.OnContactUs -> onContactUsTapped(action.message)
            SettingViewActions.OnCloseContactUs -> analytics.logAction(ContactUsCancel)
            SettingViewActions.OnReferralClick -> {
                analytics.logAction(
                    OpenReferralCode,
                    mapOf(Parameter.Source to REFERRAL_SOURCE_SETTINGS)
                )
                sendEvent(SettingViewEvents.NavigateToReferral)
            }

            SettingViewActions.OnDeleteHistoryClicked -> {
                analytics.logEvent(Event.DeleteHistory)
                performPersistedMessagesWipe()
            }

            is SettingViewActions.OnCreate -> {
                analytics.logScreen(Settings)
                listenToHapticFeedbackSetting()
                listenToOverlaySetting()
                listenToFullUser()
                getThemeMode()
                handleDestinations(action.destination)
            }

            is SettingViewActions.OnUpdateHapticFeedback -> {
                analytics.logEvent(Event.HapticFeedback, mapOf(Parameter.Enable to action.enabled))
                updateHapticFeedback(action.enabled)
            }

            is SettingViewActions.OnLuziaOverlayToggle -> {
                updateOverlayChoice(action.enabled)
            }

            SettingViewActions.OnDeleteGuestAccount ->
                sendEvent(SettingViewEvents.ShowDeleteDialog)

            SettingViewActions.OnDeleteGuestAccountConfirmed ->
                deleteGuestAccount()
        }
    }

    private fun handleDestinations(destination: SettingDestinations) {
        when (destination) {
            SettingDestinations.CONTACT_US -> sendEvent(SettingViewEvents.ShowContactUs)
            SettingDestinations.NONE -> DO_NOTHING
        }
    }

    private fun deleteGuestAccount() = viewModelScope.launch {
        analytics.logEvent(DeleteAccount)
        when (deleteAccountUseCase()) {
            false -> sendEvent(SettingViewEvents.ShowGenericError)
            true -> sendEvent(SettingViewEvents.NavigateToSignup)
        }
    }

    private fun listenToFullUser() {
        getUserProfileUseCase()
            .map { it?.userType == UserType.FULL_USER }
            .distinctUntilChanged()
            .flowOn(dispatcher)
            .onEach { result ->
                val referralEnabled: Boolean =
                    featureFlagManager.get(FeatureFlag.ReferralProgramEnabled)
                updateState {
                    it.copy(
                        isFullUser = result,
                        referralEnabled = result && referralEnabled
                    )
                }
            }
            .launchIn(viewModelScope)
    }

    private fun listenToHapticFeedbackSetting() {
        settingsRepository.hapticFeedbackFlow
            .onEach { result -> updateState { it.copy(hapticEnabled = result) } }
            .flowOn(dispatcher)
            .launchIn(viewModelScope)
    }

    private fun listenToOverlaySetting() {
        settingsRepository.overlayChoice
            .onEach { result ->
                val enabled: Boolean = featureFlagManager.get(FeatureFlag.OverlayEnabled)
                updateState {
                    it.copy(
                        luziaOverlayEnabled = result,
                        showLuziaOverlayOption = enabled
                    )
                }
            }
            .launchIn(viewModelScope)
    }

    private fun performPersistedMessagesWipe() = viewModelScope.launch {
        chatDao.deleteAllHistory()
    }

    private fun updateHapticFeedback(value: Boolean) = viewModelScope.launch {
        settingsRepository.updateHapticFeedback(value)
    }

    private fun updateOverlayChoice(value: Boolean) = viewModelScope.launch {
        settingsRepository.updateOverlayChoice(value)
    }

    private fun getThemeMode() {
        settingsRepository.themeModeFlow
            .onEach { result -> updateState { it.copy(themeModeId = result) } }
            .flowOn(dispatcher)
            .launchIn(viewModelScope)
    }

    private fun onContactUsTapped(message: String) {
        viewModelScope.launch {
            val file = contactReportGenerator.generateReport()
            sendEvent(SettingViewEvents.OpenContactUs(file, message))
            analytics.logAction(ContactUsSend)
        }
    }
}
