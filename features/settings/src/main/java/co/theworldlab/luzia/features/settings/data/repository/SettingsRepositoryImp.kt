package co.theworldlab.luzia.features.settings.data.repository

import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import co.thewordlab.fouundation.persistence.LuziaDataStore
import co.theworldlab.luzia.features.settings.domain.repository.SettingsRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map

class SettingsRepositoryImp(
    private val luziaDataStore: LuziaDataStore
) : SettingsRepository {

    override val themeModeFlow
        get() = luziaDataStore.getDataFlow(themeMode)
            .map { it ?: 0 }
            .distinctUntilChanged()

    override val hapticFeedbackFlow
        get() = luziaDataStore.getDataFlow(hapticFeedback)
            .map { it ?: false }
            .distinctUntilChanged()

    override val overlayChoice: Flow<Boolean>
        get() = luziaDataStore.getDataFlow(overlayPreference)
            .map { it ?: false }
            .distinctUntilChanged()

    override suspend fun updateThemeMode(value: Int) {
        luziaDataStore.saveData(themeMode, value)
    }

    override suspend fun updateHapticFeedback(enabled: Boolean) {
        luziaDataStore.saveData(hapticFeedback, enabled)
    }

    override suspend fun updateOverlayChoice(enabled: Boolean) {
        luziaDataStore.saveData(overlayPreference, enabled)
    }

    private companion object {

        const val KEY_THEME_MODE = "user_theme_mode"
        const val KEY_HAPTIC_FEEDBACK = "user_haptic_feedback"
        const val KEY_OVERLAY_CHOICE = "screen_overlay_enabled"
        val themeMode = intPreferencesKey(KEY_THEME_MODE)
        val hapticFeedback = booleanPreferencesKey(KEY_HAPTIC_FEEDBACK)
        val overlayPreference = booleanPreferencesKey(KEY_OVERLAY_CHOICE)
    }
}
