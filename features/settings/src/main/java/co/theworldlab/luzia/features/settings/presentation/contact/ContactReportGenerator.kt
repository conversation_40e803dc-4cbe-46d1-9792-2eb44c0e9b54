package co.theworldlab.luzia.features.settings.presentation.contact

import android.content.Context
import android.content.pm.PackageInfo
import android.os.Build
import co.thewordlab.luzia.foundation.files.manager.FileManager
import co.thewordlab.luzia.foundation.networking.device.DeviceManagement
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject

data class ContactReportGenerator @Inject constructor(
    @ApplicationContext private val context: Context,
    private val userSessionManager: UserSessionManager,
    private val deviceManagement: DeviceManagement,
    private val fileManager: FileManager
) {

    suspend fun generateReport(): File = withContext(Dispatchers.IO) {
        val adId = deviceManagement.getAdvertisingId()
        val session = userSessionManager.userSession.firstOrNull()
        val deviceId = session?.deviceKey
        val pInfo: PackageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
        val log = """
            ============
            AD_ID = $adId
            DEVICE_ID = $deviceId
            DEVICE = ${Build.DEVICE}
            BRAND = ${Build.BRAND}
            MANUFACTURER = ${Build.MANUFACTURER}
            MODEL = ${Build.MODEL}
            SDK_INT = ${Build.VERSION.SDK_INT}
            APP_VERSION = ${pInfo.versionName}
            ============
        """.trimIndent()
        return@withContext fileManager.createLogFile(log)
    }
}
