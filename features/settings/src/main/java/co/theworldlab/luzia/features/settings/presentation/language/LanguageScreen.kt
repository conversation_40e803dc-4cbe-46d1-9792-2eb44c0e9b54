package co.theworldlab.luzia.features.settings.presentation.language

import android.widget.Toast
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.foundation.architecture.system.OnStart
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.navbar.LuziaNavBarDefaults
import co.theworldlab.luzia.foundation.design.system.components.navbar.NavigationAction
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBar
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBarModel
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ItemSearchList
import co.theworldlab.luzia.foundation.design.system.legacy.composables.Loading
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.localization.updateDeviceLanguage
import kotlinx.coroutines.launch
import co.thewordlab.luzia.foundation.localization.R as localizationR

fun NavGraphBuilder.languageSelection() {
    composable<UserSessionRoutes.LanguageSelect> {
        val navigation = LocalNavigation.current
        LanguageScreen(onNavigateBack = { navigation.goBack() })
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun LanguageScreen(onNavigateBack: () -> Unit) {
    val viewModel: LanguageViewModel = hiltViewModel()
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    OnStart { viewModel.onViewAction(LanguageViewActions.OnStart) }

    ViewModelEventEffect(events = viewModel) { event ->
        when (event) {
            LanguageViewEvents.ShowError ->
                Toast.makeText(
                    context,
                    context.getString(localizationR.string.generic_error),
                    Toast.LENGTH_LONG
                ).show()

            LanguageViewEvents.NavigateBack -> onNavigateBack()
            LanguageViewEvents.UpdateLanguage ->
                coroutineScope.launch { context.updateDeviceLanguage(viewState.countryCodeSelected) }
        }
    }

    Scaffold(
        topBar = {
            TopNavigationBar(
                TopNavigationBarModel(
                    title = stringResource(id = localizationR.string.title_language_app),
                    navigationAction = NavigationAction.Icon(R.drawable.ic_back_arrow) {
                        viewModel.onViewAction(LanguageViewActions.OnNavigateBackClicked)
                    },
                    colors = LuziaNavBarDefaults.colors(containerColor = LuziaTheme.palette.surface.background),
                )
            )
        },
        containerColor = LuziaTheme.palette.surface.background
    ) { paddingValues ->
        LanguagesContent(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            viewState = viewState
        ) {
            viewModel.onViewAction(it)
        }
    }
}

@Composable
private fun LanguagesContent(
    modifier: Modifier,
    viewState: LanguageViewState,
    onViewAction: (LanguageViewActions) -> Unit,
) {
    val scrollState = rememberLazyListState()
    if (viewState.showLoading) {
        Loading()
    }
    LazyColumn(
        modifier = modifier,
        state = scrollState
    ) {
        itemsIndexed(viewState.languageList) { _, item ->
            ItemSearchList(
                code = item.code,
                nameDisplay = item.name,
                isSelected = item.selected,
                onClickListener = { code ->
                    onViewAction(LanguageViewActions.OnLanguageSelected(code))
                },
                showRightIcon = true
            )
        }
    }
}
