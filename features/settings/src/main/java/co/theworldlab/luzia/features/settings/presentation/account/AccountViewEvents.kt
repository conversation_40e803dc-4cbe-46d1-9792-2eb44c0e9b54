package co.theworldlab.luzia.features.settings.presentation.account

import co.thewordlab.luzia.foundation.architecture.system.ViewEvent
import co.theworldlab.luzia.features.imagine.domain.MyImagineImage

sealed class AccountViewEvents : ViewEvent {
    data object ShowError : AccountViewEvents()
    data object NavigateBack : AccountViewEvents()
    data object NavigateToSignUp : AccountViewEvents()
    data object LogoutFromGoogleAuth : AccountViewEvents()
    data class DeleteAllImages(
        val images: List<MyImagineImage>
    ) : AccountViewEvents()
}
