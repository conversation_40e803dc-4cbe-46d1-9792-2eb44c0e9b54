package co.theworldlab.luzia.features.proactive.messaging

import co.thewordlab.luzia.foundation.analytics.AnalyticsActions
import co.thewordlab.luzia.foundation.analytics.AnalyticsEvents
import co.thewordlab.luzia.foundation.analytics.AnalyticsScreens

data object ShareContentScreen : AnalyticsScreens(SHARE_CONTENT)
data object ShareContentOpenProfile : AnalyticsActions("profile_click")

data object ProactiveMessageCloseClicked : AnalyticsActions("proactive_close")
data object ProactiveMessageImpression : AnalyticsEvents("proactive_impression")

const val SHARE_CONTENT = "share_content"
