package co.theworldlab.luzia.features.proactive.messaging.domain.usecases

import co.thewordlab.fouundation.persistence.chat.ChatDao
import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.luzia.core.chat.domain.models.proactive.ProactiveMessageType
import co.thewordlab.luzia.core.chat.domain.models.proactive.ProactiveMessages
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.theworldlab.luzia.features.proactive.messaging.domain.models.asProactiveMessage
import co.theworldlab.luzia.features.proactive.messaging.domain.repository.ProActiveMessagingRepository
import com.squareup.moshi.Moshi
import javax.inject.Inject

class HandleProactiveChatMessagesUseCase @Inject constructor(
    private val repository: ProActiveMessagingRepository,
    private val chatDao: Chat<PERSON><PERSON>,
    private val moshi: <PERSON><PERSON>,
) {

    suspend operator fun invoke() =
        when (val result = repository.getAllMessages()) {
            is ResultOf.Failure -> DO_NOTHING
            is ResultOf.Success -> extractActions(result.data)
        }

    private suspend fun extractActions(data: List<ProactiveMessages>) {
        val storedProactiveMessages =
            chatDao.getAllMessages().filter { !it.proactivePayload.isNullOrEmpty() }
        data.filter { it.type == ProactiveMessageType.CHAT }
            .forEach { persistChatMessages(it, storedProactiveMessages) }
    }

    private suspend fun persistChatMessages(
        proactiveMessage: ProactiveMessages,
        storedProactiveMessages: List<MessageEntity>,
    ) {
        val jsonPayload = moshi.adapter(ProactiveMessages::class.java).toJson(proactiveMessage)
        val message = jsonPayload.asProactiveMessage(proactiveMessage.destination)
        storedProactiveMessages.firstOrNull { storedMessage ->
            val storedProactiveMessage = moshi.adapter(ProactiveMessages::class.java)
                .fromJson(storedMessage.proactivePayload.orEmpty())
            storedProactiveMessage?.messageId == proactiveMessage.messageId
        } ?: chatDao.insertMessage(message)
    }
}
