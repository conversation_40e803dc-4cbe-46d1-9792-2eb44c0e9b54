package co.theworldlab.luzia.features.proactive.messaging.presentation.components.alertdialog

import androidx.compose.runtime.Composable
import androidx.compose.ui.window.DialogProperties

data class ProActiveMessageAlertDialogUiModel(
    val buttonText: String,
    val buttonAction: () -> Unit,
    val dismissAction: (comesFromCloseButton: Boolean) -> Unit,
    val title: String,
    val description: String,
    val secondaryContent: @Composable () -> Unit,
    val properties: DialogProperties = DialogProperties()
)
