package co.theworldlab.luzia.features.proactive.messaging.data.repository

import co.thewordlab.luzia.core.chat.domain.models.proactive.ButtonAction
import co.thewordlab.luzia.core.chat.domain.models.proactive.MessageButton
import co.thewordlab.luzia.core.chat.domain.models.proactive.MessageButtonAction
import co.thewordlab.luzia.core.chat.domain.models.proactive.ProactiveMessage
import co.thewordlab.luzia.core.chat.domain.models.proactive.ProactiveMessageEntityMapper
import co.thewordlab.luzia.core.chat.domain.models.proactive.ProactiveMessageType
import co.thewordlab.luzia.core.chat.domain.models.proactive.ProactiveMessages
import co.thewordlab.luzia.core.chat.domain.models.proactive.SharedMessage
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.messages.domain.repository.ProactiveMessageHandler
import co.thewordlab.luzia.foundation.networking.model.CommonErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.asFailure
import co.thewordlab.luzia.foundation.networking.model.asResult
import co.thewordlab.luzia.foundation.networking.model.asSuccess
import co.thewordlab.luzia.foundation.networking.model.getDataOrNull
import co.thewordlab.luzia.foundation.networking.model.map
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import co.theworldlab.luzia.features.proactive.messaging.data.api.ProActiveMessagingApi
import co.theworldlab.luzia.features.proactive.messaging.data.api.models.MessageAckRequest
import co.theworldlab.luzia.features.proactive.messaging.data.api.models.MessagesResponseDto
import co.theworldlab.luzia.features.proactive.messaging.domain.models.ProactiveMessageErrors
import co.theworldlab.luzia.features.proactive.messaging.domain.repository.ProActiveMessagingRepository
import com.slack.eithernet.ApiResult
import kotlinx.coroutines.flow.firstOrNull
import javax.inject.Inject

class ProActiveMessagingRepositoryImp @Inject constructor(
    private val apiDataSource: ProActiveMessagingApi,
    private val userSessionManager: UserSessionManager,
    private val proactiveMessageEntityMapper: ProactiveMessageEntityMapper,
) : ProActiveMessagingRepository, ProactiveMessageHandler {

    override suspend fun getAllMessages(): ResultOf<List<ProactiveMessages>, ProactiveMessageErrors> {
        val masterUserId = userSessionManager.userSession.firstOrNull()?.masterUserId.orEmpty()
        val result = apiDataSource.getMessages(masterUserId).asResult { error ->
            val proactiveError = error.mapToProactiveMessageError()
            proactiveError.asFailure()
        }

        return result.map { messages.mapToDomain() }
    }

    override suspend fun markMessagesAsRead(
        messages: List<String>,
    ): ResultOf<Unit, ProactiveMessageErrors> =
        if (messages.isNotEmpty()) {
            val result = apiDataSource.markMessageAsRead(MessageAckRequest(messages))
                .asResult { error ->
                    val authError = error.mapToProactiveMessageError()
                    authError.asFailure()
                }

            result.map { DO_NOTHING }
        } else {
            Unit.asSuccess()
        }

    override fun extractMessage(payload: String): String {
        return proactiveMessageEntityMapper.buildProactiveEntity(payload)
            ?.messages
            ?.firstOrNull()
            ?.text.orEmpty()
    }

    override fun extractContentInjection(payload: String): String? {
        return proactiveMessageEntityMapper.buildProactiveEntity(payload)?.contentInjection
    }

    override suspend fun readProactiveMessages(personalityId: String) {
        val messageIds = getAllMessages().getDataOrNull()
            ?.filter { it.destination == personalityId && it.type == ProactiveMessageType.CHAT }
            ?.map { it.messageId }
            ?: emptyList()
        markMessagesAsRead(messageIds)
    }

    private fun List<MessagesResponseDto>.mapToDomain() =
        map {
            ProactiveMessages(
                messageId = it.messageId,
                destination = it.destination,
                type = it.requestType,
                groupId = it.groupId,
                contentInjection = it.contentInjection,
                messages = it.messages.map { message ->
                    ProactiveMessage(
                        type = message.type,
                        text = message.text.orEmpty(),
                        mediaUrl = message.mediaUrl.orEmpty(),
                        header = message.header.orEmpty(),
                        content = message.content.orEmpty(),
                        buttons = message.buttons?.map { button ->
                            MessageButton(
                                id = button.id,
                                title = button.title,
                                action = MessageButtonAction(
                                    chat = button.action.chat?.let { chat ->
                                        ButtonAction(
                                            message = chat.message,
                                            content = chat.target
                                        )
                                    },
                                    link = button.action.link?.let { link ->
                                        ButtonAction(
                                            message = link.message,
                                            content = link.url
                                        )
                                    }
                                )
                            )
                        } ?: emptyList(),
                        sharedMessages = message.sharedMessages?.map { shared ->
                            SharedMessage(
                                userMessage = shared.prompt.orEmpty(),
                                aiResponse = shared.completion.orEmpty()
                            )
                        } ?: emptyList(),
                        masterUserId = message.masterUserId.orEmpty()
                    )
                }
            )
        }

    private fun ApiResult.Failure<*>.mapToProactiveMessageError(): ProactiveMessageErrors =
        when (this) {
            is ApiResult.Failure.NetworkFailure -> ProactiveMessageErrors.CommonError(CommonErrors.NetworkError)
            is ApiResult.Failure.HttpFailure -> {
                when (code) {
                    SHARING_LIMIT_REACHED -> ProactiveMessageErrors.SharingLimitReached
                    else -> ProactiveMessageErrors.CommonError(CommonErrors.UnspecifiedError)
                }
            }

            else -> ProactiveMessageErrors.CommonError(CommonErrors.UnspecifiedError)
        }

    private companion object {
        const val SHARING_LIMIT_REACHED = 429
    }
}
