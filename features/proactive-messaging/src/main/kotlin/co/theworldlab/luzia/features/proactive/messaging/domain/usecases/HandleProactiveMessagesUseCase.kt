package co.theworldlab.luzia.features.proactive.messaging.domain.usecases

import co.thewordlab.fouundation.persistence.chat.ChatDao
import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.luzia.core.chat.domain.models.proactive.ProactiveMessageType
import co.thewordlab.luzia.core.chat.domain.models.proactive.ProactiveMessages
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.theworldlab.luzia.features.proactive.messaging.domain.models.asProactiveMessage
import co.theworldlab.luzia.features.proactive.messaging.domain.repository.ProActiveMessagingRepository
import co.theworldlab.luzia.features.proactive.messaging.domain.usecases.model.ProactiveAction
import com.squareup.moshi.Moshi
import javax.inject.Inject

class HandleProactiveMessagesUseCase @Inject constructor(
    private val repository: ProActiveMessagingRepository,
    private val chatDao: <PERSON><PERSON><PERSON><PERSON>,
    private val moshi: <PERSON><PERSON>,
) {

    suspend operator fun invoke(): List<ProactiveAction> =
        when (val result = repository.getAllMessages()) {
            is ResultOf.Failure -> emptyList()
            is ResultOf.Success -> extractActions(result.data)
        }

    private suspend fun extractActions(data: List<ProactiveMessages>): List<ProactiveAction> {
        val storedProactiveMessages =
            chatDao.getAllMessages().filter { !it.proactivePayload.isNullOrEmpty() }
        data.filter { it.type == ProactiveMessageType.CHAT }
            .forEach { persistChatMessages(it, storedProactiveMessages) }
        val actions = data.filter { it.type == ProactiveMessageType.IN_APP }.map {
            ProactiveAction.ShowPopupMessage(it)
        }
        return actions
    }

    private suspend fun persistChatMessages(
        proactiveMessage: ProactiveMessages,
        storedProactiveMessages: List<MessageEntity>,
    ) {
        val jsonPayload = moshi.adapter(ProactiveMessages::class.java).toJson(proactiveMessage)
        val message = jsonPayload.asProactiveMessage(proactiveMessage.destination)
        storedProactiveMessages.firstOrNull { storedMessage ->
            val storedProactiveMessage = moshi.adapter(ProactiveMessages::class.java)
                .fromJson(storedMessage.proactivePayload.orEmpty())
            storedProactiveMessage?.messageId == proactiveMessage.messageId
        } ?: chatDao.insertMessage(message)
    }
}
