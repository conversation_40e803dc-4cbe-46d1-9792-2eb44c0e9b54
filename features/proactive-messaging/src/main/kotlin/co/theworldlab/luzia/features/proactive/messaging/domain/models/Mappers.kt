package co.theworldlab.luzia.features.proactive.messaging.domain.models

import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.fouundation.persistence.chat.MessageType
import java.lang.System.currentTimeMillis

fun String.asProactiveMessage(personalityId: String): MessageEntity {
    return MessageEntity(
        messageId = 0L,
        text = "",
        personalityId = personalityId,
        isAi = true,
        isRead = false,
        isLoading = false,
        messageType = MessageType.Proactive,
        error = null,
        fileName = null,
        timeStamp = currentTimeMillis(),
        feedbackId = null,
        proactivePayload = this,
        requestId = null,
        deleted = false,
        isFavorite = false,
        maxPromptLength = null,
        contentInjection = null,
        webSearchData = null,
        webViewData = null
    )
}
