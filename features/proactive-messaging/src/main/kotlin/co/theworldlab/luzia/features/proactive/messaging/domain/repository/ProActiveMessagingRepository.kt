package co.theworldlab.luzia.features.proactive.messaging.domain.repository

import co.thewordlab.luzia.core.chat.domain.models.proactive.ProactiveMessages
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.theworldlab.luzia.features.proactive.messaging.domain.models.ProactiveMessageErrors

interface ProActiveMessagingRepository {

    suspend fun getAllMessages(): ResultOf<List<ProactiveMessages>, ProactiveMessageErrors>
    suspend fun markMessagesAsRead(messages: List<String>): ResultOf<Unit, ProactiveMessageErrors>
}
