plugins {
    alias(libs.plugins.luzia.android.feature)
}

android {
    namespace = "co.thewordlab.luzia.features.imagine"
}

dependencies {
    implementation(libs.androidx.media.exif)
    implementation(projects.core.feedback)
    implementation(projects.core.navigation)
    implementation(projects.core.tools)
    implementation(projects.foundation.analytics)
    implementation(projects.foundation.architectureSystem)
    implementation(projects.foundation.common)
    implementation(projects.foundation.designSystem)
    implementation(projects.foundation.localization)
    implementation(projects.foundation.networking)
    testImplementation(projects.foundation.testing)
}