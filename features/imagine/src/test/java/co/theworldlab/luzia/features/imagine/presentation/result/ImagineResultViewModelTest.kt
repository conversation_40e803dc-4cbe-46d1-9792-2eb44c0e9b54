package co.theworldlab.luzia.features.imagine.presentation.result

import co.thewordlab.luzia.core.feedback.domain.FeedbackRepository
import co.thewordlab.luzia.core.feedback.presentation.FeedbackBravo
import co.thewordlab.luzia.core.feedback.presentation.FeedbackDismiss
import co.thewordlab.luzia.core.feedback.presentation.FeedbackDisplayed
import co.thewordlab.luzia.core.feedback.presentation.FeedbackNegative
import co.thewordlab.luzia.core.feedback.presentation.FeedbackViewActions
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.analytics.ScreenView
import co.thewordlab.luzia.foundation.networking.model.CommonErrors
import co.thewordlab.luzia.foundation.networking.model.asFailure
import co.thewordlab.luzia.foundation.networking.model.asSuccess
import co.thewordlab.luzia.foundation.testing.dispatchers.MainDispatcherRule
import co.theworldlab.luzia.features.imagine.ImageGenerationLimitReached
import co.theworldlab.luzia.features.imagine.domain.models.ContentImagine
import co.theworldlab.luzia.features.imagine.domain.models.ImagineErrors
import co.theworldlab.luzia.features.imagine.domain.repository.ImagineRepository
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Rule
import org.junit.Test

@Suppress("MaxLineLength")
@ExperimentalCoroutinesApi
class ImagineResultViewModelTest {

    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()

    @MockK
    lateinit var imagineRepository: ImagineRepository

    @MockK
    lateinit var feedbackRepository: FeedbackRepository

    @MockK
    lateinit var analytics: Analytics

    private lateinit var sut: ImagineResultViewModel

    @Before
    fun setUp() {
        MockKAnnotations.init(this, relaxed = true)
        sut = ImagineResultViewModel(imagineRepository, feedbackRepository, analytics)
    }

    @Test
    fun `given successful image generation when setPrompt then updates UI state with URI and feedbackId`() =
        runTest {
            val prompt = "test_prompt"
            val imagineId = "test_imagine_id"
            val feedbackId = "test_feedback_id"
            val contentURI = "test_uri"
            val imagineResult = ContentImagine(contentURI, feedbackId)

            coEvery { imagineRepository.sendPromptImagine(prompt, imagineId) } returns flowOf(
                imagineResult.asSuccess()
            )
            every { analytics.logScreenView(ScreenView.ImagineCreationInProgress) } returns Unit
            every {
                analytics.logScreenView(
                    ScreenView.ImagineCreated,
                    mapOf(Parameter.MessageId to feedbackId)
                )
            } returns Unit
            every { analytics.logEvent(FeedbackDisplayed) } returns Unit

            sut.setPrompt(prompt, imagineId)
            mainDispatcherRule.advanceUntilIdle()

            val uiState = sut.uiImagineResultState.first()
            assertEquals(contentURI, uiState.uri)
            assertEquals(feedbackId, uiState.showFeedback)
            assertFalse(uiState.isLoading)
            assertNull(uiState.isInError)
            verify { analytics.logScreenView(ScreenView.ImagineCreationInProgress) }
            verify {
                analytics.logScreenView(
                    ScreenView.ImagineCreated,
                    mapOf(Parameter.MessageId to feedbackId)
                )
            }
            verify { analytics.logEvent(FeedbackDisplayed) }
        }

    @Test
    fun `given failed image generation due to limit reached when setPrompt then updates UI state with error and logs analytics`() =
        runTest {
            val prompt = "test_prompt"
            val imagineId = "test_imagine_id"
            val error = ImagineErrors.LimitReached

            coEvery {
                imagineRepository.sendPromptImagine(
                    prompt,
                    imagineId
                )
            } returns flowOf(error.asFailure())
            every { analytics.logScreenView(ScreenView.ImagineCreationInProgress) } returns Unit
            every { analytics.logEvent(ImageGenerationLimitReached) } returns Unit

            sut.setPrompt(prompt, imagineId)
            mainDispatcherRule.advanceUntilIdle()

            val uiState = sut.uiImagineResultState.first()
            assertEquals(error, uiState.isInError)
            assertNull(uiState.showFeedback)
            assertFalse(uiState.isLoading)
            assertTrue(uiState.errorShowed)
            verify { analytics.logScreenView(ScreenView.ImagineCreationInProgress) }
            verify { analytics.logEvent(ImageGenerationLimitReached) }
        }

    @Test
    fun `given failed image generation with common error when setPrompt then updates UI state with error`() =
        runTest {
            val prompt = "test_prompt"
            val imagineId = "test_imagine_id"
            val error = ImagineErrors.CommonError(CommonErrors.NetworkError)

            coEvery {
                imagineRepository.sendPromptImagine(
                    prompt,
                    imagineId
                )
            } returns flowOf(error.asFailure())
            every { analytics.logScreenView(ScreenView.ImagineCreationInProgress) } returns Unit

            sut.setPrompt(prompt, imagineId)
            mainDispatcherRule.advanceUntilIdle()

            val uiState = sut.uiImagineResultState.first()
            assertEquals(error, uiState.isInError)
            assertNull(uiState.showFeedback)
            assertFalse(uiState.isLoading)
            assertTrue(uiState.errorShowed)
            verify { analytics.logScreenView(ScreenView.ImagineCreationInProgress) }
        }

    @Test
    fun `given OnFeedbackReceived with OnDisliked when onViewAction then sends feedback and updates UI`() =
        runTest {
            val feedbackId = "test_feedback_id"
            val action = ImagineResultViewActions.OnFeedbackReceived(
                FeedbackViewActions.OnDisliked(feedbackId)
            )

            coEvery { feedbackRepository.sendFeedback(any()) } returns Unit.asSuccess()
            every { analytics.trackAction(FeedbackNegative()) } returns Unit

            sut.onViewAction(action)
            mainDispatcherRule.advanceUntilIdle()

            coVerify { feedbackRepository.sendFeedback(any()) }
            verify { analytics.trackAction(FeedbackNegative()) }

            val event = sut.viewEvent.first() as ImagineResultViewEvents.ShowDislikedFeedbackMessage
            assertNotNull(event)
            val uiState = sut.uiImagineResultState.first()
            assertNull(uiState.showFeedback)
        }

    @Test
    fun `given OnFeedbackReceived with OnDismiss when onViewAction then tracks analytics and updates UI`() =
        runTest {
            val feedbackId = "test_feedback_id"
            val action =
                ImagineResultViewActions.OnFeedbackReceived(FeedbackViewActions.OnDismiss(feedbackId))

            every { analytics.trackAction(FeedbackDismiss) } returns Unit

            sut.onViewAction(action)
            mainDispatcherRule.advanceUntilIdle()

            verify { analytics.trackAction(FeedbackDismiss) }
            val uiState = sut.uiImagineResultState.first()
            assertNull(uiState.showFeedback)
        }

    @Test
    fun `given OnFeedbackReceived with OnLiked when onViewAction then sends feedback and updates UI`() =
        runTest {
            val feedbackId = "test_feedback_id"
            val action =
                ImagineResultViewActions.OnFeedbackReceived(FeedbackViewActions.OnLiked(feedbackId))

            coEvery { feedbackRepository.sendFeedback(any()) } returns Unit.asSuccess()
            every { analytics.trackAction(FeedbackBravo()) } returns Unit

            sut.onViewAction(action)
            mainDispatcherRule.advanceUntilIdle()

            coVerify { feedbackRepository.sendFeedback(any()) }
            verify { analytics.trackAction(FeedbackBravo()) }
            val event = sut.viewEvent.first() as ImagineResultViewEvents.ShowLikedFeedbackMessage
            assertNotNull(event)
            val uiState = sut.uiImagineResultState.first()
            assertNull(uiState.showFeedback)
        }

    @Test
    fun `when saveImage then updates UI state isSaved to true`() = runTest {
        sut.saveImage()
        val uiState = sut.uiImagineResultState.first()
        assertTrue(uiState.isSaved)
    }

    @Test
    fun `when updateNavigateToRegister then updates UI state navigateToRegister to false`() =
        runTest {
            sut.updateNavigateToRegister()
            val uiState = sut.uiImagineResultState.first()
            assertFalse(uiState.navigateToRegister)
        }
}
