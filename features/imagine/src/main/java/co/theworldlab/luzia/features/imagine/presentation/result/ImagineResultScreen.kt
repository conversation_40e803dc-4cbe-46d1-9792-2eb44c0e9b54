package co.theworldlab.luzia.features.imagine.presentation.result

import android.Manifest
import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.ContentResolver
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.provider.Settings
import android.util.Base64
import android.widget.Toast
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toBitmap
import androidx.exifinterface.media.ExifInterface
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import co.thewordlab.luzia.core.feedback.presentation.FeedbackViewActions
import co.thewordlab.luzia.core.navigation.common.LocalNavigation
import co.thewordlab.luzia.core.navigation.usersession.UserSessionRoutes
import co.thewordlab.luzia.foundation.analytics.Event
import co.thewordlab.luzia.foundation.analytics.LocalAnalytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.analytics.ScreenView
import co.thewordlab.luzia.foundation.architecture.system.ViewModelEventEffect
import co.thewordlab.luzia.foundation.common.extensions.ImageUtils.saveImage
import co.theworldlab.luzia.features.imagine.domain.models.ImagineErrors
import co.theworldlab.luzia.foundation.design.system.components.message.feedback.FeedbackUiModel
import co.theworldlab.luzia.foundation.design.system.components.message.feedback.FeedbackView
import co.theworldlab.luzia.foundation.design.system.components.navbar.LuziaNavBarDefaults
import co.theworldlab.luzia.foundation.design.system.components.navbar.NavigationAction
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBar
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBarModel
import co.theworldlab.luzia.foundation.design.system.components.scaffold.LocalAppState
import co.theworldlab.luzia.foundation.design.system.legacy.composables.BaseToolBar
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ImagineImageContainer
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ImagineImageContainerType
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ReportDialog
import co.theworldlab.luzia.foundation.design.system.legacy.composables.TextFieldMultiline
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialog
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogButton
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogTextDefaults
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import coil3.ImageLoader
import coil3.asDrawable
import coil3.request.ImageRequest
import coil3.request.SuccessResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

const val IMAGE_PREFIX_NAME = "LuzIAImage-"
const val IMAGE_JPEG_MIME_TYPE = "image/jpeg"
const val IMAGINE_LUZIA_FOLDER = "Imagine Luzia"
const val JPG_EXTENSION = ".jpg"
const val PACKAGE_URI = "package"
private const val LIMIT_CHAR_DEFAULT = 500
private const val IMAGE_QUALITY = 100

fun NavGraphBuilder.imagineResult() {
    composable<UserSessionRoutes.ImagineToolResult> { backstackEntry ->
        val result = backstackEntry.toRoute<UserSessionRoutes.ImagineToolResult>()
        ImagineResultScreen(result.prompt, result.imagineId)
    }
}

@Suppress("UnusedMaterial3ScaffoldPaddingParameter", "LongMethod", "CyclomaticComplexMethod")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ImagineResultScreen(
    prompt: String,
    imagineId: String
) {
    val context = LocalContext.current
    val imagineResultViewModel: ImagineResultViewModel = hiltViewModel()
    val navigation = LocalNavigation.current
    val appState = LocalAppState.current

    ViewModelEventEffect(events = imagineResultViewModel) { event ->
        when (event) {
            ImagineResultViewEvents.ShowDislikedFeedbackMessage ->
                appState.showSnackBar(message = context.getString(localizationR.string.feedback_answer_reject))

            ImagineResultViewEvents.ShowLikedFeedbackMessage ->
                appState.showSnackBar(message = context.getString(localizationR.string.feedback_answer_approve))
        }
    }

    LaunchedEffect(key1 = prompt, key2 = imagineId) {
        if (imagineResultViewModel.uiImagineResultState.value.uri == null &&
            !imagineResultViewModel.uiImagineResultState.value.isLoading
        ) {
            imagineResultViewModel.setPrompt(prompt, imagineId)
        }
    }

    val analytics = LocalAnalytics.current
    var showExitErrorAlert by remember {
        mutableStateOf(false)
    }
    val uiImagineState =
        imagineResultViewModel.uiImagineResultState.collectAsStateWithLifecycle().value

    var showRegenerateAlert by remember {
        mutableStateOf(false)
    }
    var showExitAlert by remember {
        mutableStateOf(false)
    }
    var isGoingBack by remember { mutableStateOf(false) }

    val shareLauncher =
        rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) {}

    var bitmapLoaded by remember {
        mutableStateOf<Bitmap?>(null)
    }
    val scope = rememberCoroutineScope()
    val activity = LocalView.current.context as Activity
    var isPermissionDialogVisible by remember { mutableStateOf(false) }

    val launcher =
        rememberLauncherForActivityResult(contract = ActivityResultContracts.RequestPermission()) { isGranted ->
            if (!isGranted) {
                if (!ActivityCompat.shouldShowRequestPermissionRationale(
                        activity,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE
                    )
                ) {
                    isPermissionDialogVisible = true
                }
            } else {
                download(
                    scope,
                    context,
                    uiImagineState,
                    prompt,
                    uiImagineState.showFeedback.orEmpty()
                )
                imagineResultViewModel.saveImage()
            }
        }
    val launcherReadImages =
        rememberLauncherForActivityResult(
            contract = ActivityResultContracts.RequestMultiplePermissions()
        ) { results ->
            var showDialog = false
            results.forEach { permission ->
                if (!permission.value) {
                    if (!ActivityCompat.shouldShowRequestPermissionRationale(
                            activity,
                            permission.key
                        )
                    ) {
                        showDialog = true
                    }
                }
            }
            val allowAll = results.all { it.value }
            if (showDialog) {
                isPermissionDialogVisible = true
            } else if (allowAll) {
                navigation.navigate(UserSessionRoutes.ImagineGallery)
            }
        }

    val launcherReadImagesInAPI34 =
        rememberLauncherForActivityResult(
            contract = ActivityResultContracts.RequestMultiplePermissions()
        ) { results ->
            var showDialog = false
            results.forEach { permission ->
                if (!permission.value) {
                    if (!ActivityCompat.shouldShowRequestPermissionRationale(
                            activity,
                            permission.key
                        )
                    ) {
                        showDialog = true
                    }
                }
            }
            val allowAll = results.all { it.value }
            if (showDialog || !allowAll) {
                isPermissionDialogVisible = true
            } else {
                navigation.navigate(UserSessionRoutes.ImagineGallery)
            }
        }

    if (isPermissionDialogVisible) {
        LuziaAlertDialog(
            confirmButton =
            LuziaAlertDialogButton(
                stringResource(id = localizationR.string.go_to_settings),
                action = {
                    val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                    val uri = Uri.fromParts(PACKAGE_URI, context.packageName, null)
                    intent.data = uri
                    context.startActivity(intent)
                    isPermissionDialogVisible = false
                },
                isPrimaryAction = true
            ),
            dismissButton =
            LuziaAlertDialogButton(
                stringResource(id = localizationR.string.cancel),
                action = {
                    isPermissionDialogVisible = false
                }
            ),
            title =
            LuziaAlertDialogText(
                stringResource(id = localizationR.string.permission_permanently_denied),
                LuziaAlertDialogTextDefaults.Title()
            ),
            text =
            LuziaAlertDialogText(
                stringResource(id = localizationR.string.activate_permission_permanently_denied),
                LuziaAlertDialogTextDefaults.Description()
            )
        )
    }

    LaunchedEffect(uiImagineState.isLoading) {
        if (!uiImagineState.isLoading) {
            showExitErrorAlert = false
        }
    }

    LaunchedEffect(uiImagineState.navigateToRegister) {
        if (uiImagineState.navigateToRegister) {
            imagineResultViewModel.updateNavigateToRegister()
            navigation.navigate(UserSessionRoutes.Signup())
        }
    }

    Scaffold(
        topBar = {
            TopNavigationBar(
                TopNavigationBarModel(
                    title = stringResource(id = localizationR.string.imagine_result_image_title),
                    navigationAction = NavigationAction.Icon(designR.drawable.ic_back_arrow) {
                        if (uiImagineState.uri == null && uiImagineState.isInError != null) {
                            showExitErrorAlert = true
                        } else if (!uiImagineState.isSaved && uiImagineState.isInError != null) {
                            showExitAlert = true
                        } else {
                            if (!isGoingBack) {
                                navigation.goBack()
                                isGoingBack = true
                            }
                        }
                    },
                    colors = LuziaNavBarDefaults.colors(containerColor = LuziaTheme.palette.surface.background),
                    actions = listOf(
                        NavigationAction.Icon(designR.drawable.ic_gallery) {
                            analytics.logEvent(Event.GalleryTap)
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                                val readMedia =
                                    ContextCompat.checkSelfPermission(
                                        context,
                                        Manifest.permission.READ_MEDIA_IMAGES
                                    ) == PackageManager.PERMISSION_GRANTED
                                val readMediaUserSelected =
                                    ContextCompat.checkSelfPermission(
                                        context,
                                        Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED
                                    ) == PackageManager.PERMISSION_GRANTED

                                if (!readMedia && readMediaUserSelected) {
                                    isPermissionDialogVisible = true
                                } else {
                                    launcherReadImagesInAPI34.launch(
                                        arrayOf(
                                            Manifest.permission.READ_MEDIA_IMAGES,
                                            Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED
                                        )
                                    )
                                }
                            } else if (Build.VERSION.SDK_INT == Build.VERSION_CODES.TIRAMISU) {
                                launcherReadImages.launch(arrayOf(Manifest.permission.READ_MEDIA_IMAGES))
                            } else {
                                launcherReadImages.launch(arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE))
                            }
                        }
                    )
                )
            )
        }
    ) { paddingValues ->

        BackHandler {
            if (uiImagineState.uri == null && uiImagineState.isInError != null) {
                showExitErrorAlert = true
            } else if (!uiImagineState.isSaved && uiImagineState.isInError != null) {
                showExitAlert = true
            } else {
                if (!isGoingBack) {
                    navigation.goBack()
                    isGoingBack = true
                }
            }
        }

        if (showExitErrorAlert) {
            LuziaAlertDialog(
                confirmButton =
                LuziaAlertDialogButton(
                    stringResource(id = localizationR.string.button_exit),
                    action = {
                        if (!isGoingBack) {
                            navigation.goBack()
                            isGoingBack = true
                        }
                    },
                    isPrimaryAction = true
                ),
                dismissButton =
                LuziaAlertDialogButton(
                    stringResource(id = localizationR.string.cancel),
                    action = {
                        showExitErrorAlert = false
                    }
                ),
                title =
                LuziaAlertDialogText(
                    stringResource(id = localizationR.string.text_title_alert_exit_imagine),
                    LuziaAlertDialogTextDefaults.Title()
                ),
                text =
                LuziaAlertDialogText(
                    stringResource(id = localizationR.string.text_description_alert_exit_error_imagine),
                    LuziaAlertDialogTextDefaults.Description()
                )
            )
        }
        if (showExitAlert) {
            LuziaAlertDialog(
                confirmButton =
                LuziaAlertDialogButton(
                    stringResource(id = localizationR.string.button_exit),
                    action = {
                        if (!isGoingBack) {
                            navigation.goBack()
                            isGoingBack = true
                        }
                    },
                    isPrimaryAction = true
                ),
                dismissButton =
                LuziaAlertDialogButton(
                    stringResource(id = localizationR.string.cancel),
                    action = {
                        showExitAlert = false
                    }
                ),
                title =
                LuziaAlertDialogText(
                    stringResource(id = localizationR.string.text_title_alert_exit_imagine),
                    LuziaAlertDialogTextDefaults.Title()
                ),
                text =
                LuziaAlertDialogText(
                    stringResource(id = localizationR.string.text_description_alert_exit_imagine),
                    LuziaAlertDialogTextDefaults.Description()
                )
            )
        }
        if (showRegenerateAlert) {
            LuziaAlertDialog(
                confirmButton =
                LuziaAlertDialogButton(
                    stringResource(id = localizationR.string.button_regenerate),
                    action = {
                        showRegenerateAlert = false
                        imagineResultViewModel.retryImagine(prompt)
                    },
                    isPrimaryAction = true
                ),
                dismissButton =
                LuziaAlertDialogButton(
                    stringResource(id = localizationR.string.cancel),
                    action = {
                        showRegenerateAlert = false
                    }
                ),
                title =
                LuziaAlertDialogText(
                    stringResource(id = localizationR.string.text_title_alert_regenerate_imagine),
                    LuziaAlertDialogTextDefaults.Title()
                ),
                text =
                LuziaAlertDialogText(
                    stringResource(id = localizationR.string.text_description_alert_regenerate_imagine),
                    LuziaAlertDialogTextDefaults.Description()
                )
            )
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .navigationBarsPadding()
                .padding(top = paddingValues.calculateTopPadding())
                .background(LuziaTheme.palette.surface.background)
                .testTag("containerImagineResult")
        ) {
            Column(
                modifier = Modifier
                    .padding(horizontal = Spacing.X16.dp)
                    .verticalScroll(rememberScrollState())
                    .weight(1f)
            ) {
                if (uiImagineState.isLoading) {
                    ImagineImageContainer(
                        modifier = Modifier.testTag("imageLoading"),
                        type = ImagineImageContainerType.Loading
                    )
                } else if (uiImagineState.uri != null) {
                    ImagineImageContainer(
                        modifier = Modifier.testTag("imageLoaded"),
                        type = ImagineImageContainerType.Image(url = uiImagineState.uri),
                        updateDrawable = { bitmapLoaded = it.toBitmap() }
                    )
                } else {
                    ImagineImageContainer(
                        modifier = Modifier.testTag("imageNotLoaded"),
                        type = ImagineImageContainerType.Error(
                            cancelOnClick = { showExitErrorAlert = true },
                            retryOnClick = { imagineResultViewModel.retryImagine(prompt) },
                            showError = when (uiImagineState.isInError) {
                                is ImagineErrors.CommonError -> true
                                else -> false
                            },
                            errorText = when (uiImagineState.isInError) {
                                is ImagineErrors.CommonError -> stringResource(id = localizationR.string.generic_error)
                                ImagineErrors.LimitReached -> stringResource(
                                    id = localizationR.string.imagine_limit_reached_error
                                )

                                null -> ""
                            }
                        )
                    )
                }

                TextFieldMultiline(
                    modifier = Modifier.padding(top = Spacing.X16.dp),
                    messageText = prompt,
                    readOnly = true,
                    showCounter = false,
                    limitChar = LIMIT_CHAR_DEFAULT
                )

                uiImagineState.showFeedback?.let {
                    FeedbackView(
                        model = FeedbackUiModel(
                            title = localizationR.string.feedback_question_imagine,
                            onLiked = {
                                imagineResultViewModel.onViewAction(
                                    ImagineResultViewActions.OnFeedbackReceived(
                                        FeedbackViewActions.OnLiked(it)
                                    )
                                )
                            },
                            onDisliked = {
                                imagineResultViewModel.onViewAction(
                                    ImagineResultViewActions.OnFeedbackReceived(
                                        FeedbackViewActions.OnDisliked(it)
                                    )
                                )
                            },
                            onDismiss = {
                                imagineResultViewModel.onViewAction(
                                    ImagineResultViewActions.OnFeedbackReceived(
                                        FeedbackViewActions.OnDismiss(it)
                                    )
                                )
                            }
                        )
                    )
                }
            }

            if (uiImagineState.uri != null) {
                var showDialog by remember { mutableStateOf(false) }
                ReportDialog(
                    show = showDialog,
                    onDismiss = {
                        showDialog = false
                    },
                    onReport = {
                        analytics.logEvent(
                            Event.ReportMessage,
                            mapOf(
                                Parameter.Tool to "imagine",
                                Parameter.MessageId to uiImagineState.showFeedback.orEmpty()
                            )
                        )
                    }
                )
                BaseToolBar.ImagineResultToolBar(
                    regenerateOnClick = {
                        if (!uiImagineState.isSaved) {
                            showRegenerateAlert = true
                        } else {
                            imagineResultViewModel.retryImagine(prompt)
                        }
                    },
                    shareOnClick = {
                        bitmapLoaded?.let {
                            scope.launch {
                                val intent = Intent(Intent.ACTION_SEND)
                                intent.type = "image/*"
                                val screenshotUri = saveImage(context, it)
                                intent.putExtra(Intent.EXTRA_STREAM, screenshotUri)
                                try {
                                    analytics.logEvent(
                                        Event.ImagineShare,
                                        mapOf(Parameter.Source to ScreenView.ImagineCreated.screenName)
                                    )
                                    shareLauncher.launch(intent)
                                } catch (expected: ActivityNotFoundException) {
                                    analytics.logFailedEvent(
                                        Event.ImagineShare,
                                        mapOf(Parameter.Source to ScreenView.ImagineCreated.screenName)
                                    )
                                    Toast.makeText(
                                        context,
                                        context.getString(localizationR.string.error_share_image_imagine),
                                        Toast.LENGTH_LONG
                                    ).show()
                                }
                            }
                        } ?: kotlin.run {
                            analytics.logFailedEvent(
                                Event.ImagineShare,
                                mapOf(Parameter.Source to ScreenView.ImagineCreated.screenName)
                            )
                            Toast.makeText(
                                context,
                                context.getString(localizationR.string.error_share_image_imagine),
                                Toast.LENGTH_LONG
                            ).show()
                        }
                    },
                    downloadOnClick = {
                        analytics.logEvent(Event.ImaginePictureSave)
                        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.S_V2) {
                            download(
                                scope,
                                context,
                                uiImagineState,
                                prompt,
                                uiImagineState.showFeedback.orEmpty()
                            )
                            imagineResultViewModel.saveImage()
                        } else {
                            launcher.launch(Manifest.permission.WRITE_EXTERNAL_STORAGE)
                        }
                    },
                    reportOnClick = {
                        showDialog = true
                    }
                )
            }
        }
    }
}

fun download(
    scope: CoroutineScope,
    context: Context,
    uiImagineState: ImagineResultViewModel.UiImagineResultState,
    prompt: String,
    requestId: String,
) {
    scope.launch {
        val result =
            downloadImage(
                context,
                uiImagineState.uri.toString(),
                prompt,
                requestId
            )
        result?.let {
            Toast.makeText(
                context,
                context.getString(localizationR.string.download_done),
                Toast.LENGTH_LONG
            )
                .show()
        } ?: Toast.makeText(
            context,
            context.getString(localizationR.string.download_fail),
            Toast.LENGTH_LONG
        )
            .show()
    }
}

suspend fun downloadImage(
    context: Context,
    imageUrl: String,
    prompt: String,
    requestId: String
): Uri? {
    return withContext(Dispatchers.IO) {
        try {
            val loader =
                ImageLoader.Builder(context)
                    .build()

            val request =
                ImageRequest.Builder(context)
                    .data(imageUrl)
                    .build()

            val result = loader.execute(request)

            if (result is SuccessResult) {
                val contentResolver: ContentResolver = context.contentResolver

                val directory =
                    File(
                        Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES),
                        IMAGINE_LUZIA_FOLDER
                    )
                if (!directory.exists()) {
                    directory.mkdirs()
                }

                val calendar = Calendar.getInstance()
                val dateFormat =
                    SimpleDateFormat("yyyyMMdd", Locale.getDefault())

                var count = 0
                val baseFileName = IMAGE_PREFIX_NAME + dateFormat.format(calendar.time)
                val extension = JPG_EXTENSION
                var countStr = String.format(Locale.getDefault(), "%04d", count)

                while (File(directory, "$baseFileName-$countStr$extension").exists()) {
                    count++
                    countStr = String.format(Locale.getDefault(), "%04d", count)
                }

                val imageName = "$baseFileName-$countStr$extension"

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    val imageCollectionUri: Uri =
                        MediaStore.Images.Media.getContentUri(
                            MediaStore.VOLUME_EXTERNAL_PRIMARY
                        )
                    val values =
                        ContentValues().apply {
                            put(MediaStore.Images.Media.DISPLAY_NAME, imageName)
                            put(MediaStore.Images.Media.MIME_TYPE, IMAGE_JPEG_MIME_TYPE)
                            put(
                                MediaStore.Images.Media.RELATIVE_PATH,
                                "${Environment.DIRECTORY_PICTURES}/$IMAGINE_LUZIA_FOLDER"
                            )
                        }

                    val imageUri = contentResolver.insert(imageCollectionUri, values)

                    imageUri?.let { uri ->
                        contentResolver.openOutputStream(uri)?.use { outputStream ->
                            result.image.asDrawable(context.resources).toBitmap()
                                .compress(Bitmap.CompressFormat.JPEG, IMAGE_QUALITY, outputStream)
                            saveExifComment(context, uri, prompt, requestId)
                        }
                        return@withContext uri
                    }

                    return@withContext contentResolver.insert(imageCollectionUri, values)
                } else {
                    val imageFile = File(directory, imageName)

                    FileOutputStream(imageFile).use { outputStream ->
                        outputStream.write(
                            result.image.asDrawable(context.resources).toBitmap().encodeToJpeg()
                        )
                    }

                    val contentValues =
                        ContentValues().apply {
                            put(MediaStore.Images.Media.DISPLAY_NAME, imageName)
                            put(MediaStore.Images.Media.MIME_TYPE, IMAGE_JPEG_MIME_TYPE)
                            put(MediaStore.Images.Media.DATA, imageFile.absolutePath)
                        }

                    val imageUri =
                        contentResolver.insert(
                            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                            contentValues
                        )
                    imageUri?.let { uri ->
                        saveExifComment(context, uri, prompt, requestId)
                    }

                    return@withContext imageUri
                }
            }
        } catch (expected: Exception) {
            expected.printStackTrace()
        }

        return@withContext null
    }
}

private fun saveExifComment(context: Context, imageUri: Uri, prompt: String, requestId: String) {
    var imagePath: String? = null
    val cursor = context.contentResolver.query(imageUri, null, null, null, null)
    cursor?.use {
        it.moveToFirst()
        val columnIndex = it.getColumnIndex(MediaStore.Images.ImageColumns.DATA)
        imagePath = it.getString(columnIndex)
    }
    imagePath?.let {
        try {
            val bytesUtf8 = prompt.toByteArray(Charsets.UTF_8)
            val encodedPrompt = Base64.encodeToString(bytesUtf8, Base64.DEFAULT)
            val encodedRequestId =
                Base64.encodeToString(requestId.toByteArray(Charsets.UTF_8), Base64.DEFAULT)
            val exif = ExifInterface(it)
            exif.setAttribute(
                ExifInterface.TAG_IMAGE_DESCRIPTION,
                encodedPrompt
            )
            exif.setAttribute(
                ExifInterface.TAG_IMAGE_UNIQUE_ID,
                encodedRequestId
            )
            exif.saveAttributes()
        } catch (expected: Exception) {
            expected.printStackTrace()
        }
    }
}

fun Bitmap.encodeToJpeg(): ByteArray {
    val stream = ByteArrayOutputStream()
    compress(Bitmap.CompressFormat.JPEG, IMAGE_QUALITY, stream)
    return stream.toByteArray()
}
