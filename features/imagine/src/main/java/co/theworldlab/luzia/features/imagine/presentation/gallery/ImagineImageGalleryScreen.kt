@file:Suppress("ReturnCount")

package co.theworldlab.luzia.features.imagine.presentation.gallery

import android.Manifest
import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.ContentResolver
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.provider.Settings
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.core.app.ActivityCompat
import androidx.hilt.navigation.compose.hiltViewModel
import co.thewordlab.luzia.foundation.analytics.Event
import co.thewordlab.luzia.foundation.analytics.LocalAnalytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.analytics.ScreenView
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.common.extensions.ImageUtils.getDecodedPrompt
import co.thewordlab.luzia.foundation.common.extensions.ImageUtils.getDecodedRequestId
import co.thewordlab.luzia.foundation.common.extensions.ImageUtils.getImageContentUri
import co.thewordlab.luzia.foundation.common.extensions.ImageUtils.saveImage
import co.thewordlab.luzia.foundation.common.extensions.shareText
import co.theworldlab.luzia.features.imagine.domain.MyImagineImage
import co.theworldlab.luzia.features.imagine.presentation.result.PACKAGE_URI
import co.theworldlab.luzia.foundation.design.system.components.navbar.LuziaNavBarDefaults
import co.theworldlab.luzia.foundation.design.system.components.navbar.NavigationAction
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBar
import co.theworldlab.luzia.foundation.design.system.components.navbar.TopNavigationBarModel
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.BaseContextualMenuOptions
import co.theworldlab.luzia.foundation.design.system.legacy.composables.BaseToolBar
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ImagineImageContainer
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ImagineImageContainerType
import co.theworldlab.luzia.foundation.design.system.legacy.composables.LuziaContextualMenu
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.BaseAlertDialog
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialog
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogButton
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogTextDefaults
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun ImagineImageGalleryDeleteDialog(onConfirm: () -> Unit, onDismiss: () -> Unit) {
    LuziaAlertDialog(
        confirmButton =
        LuziaAlertDialogButton(
            stringResource(id = localizationR.string.delete),
            action = onConfirm,
            isPrimaryAction = true
        ),
        dismissButton =
        LuziaAlertDialogButton(
            stringResource(id = localizationR.string.cancel),
            action = onDismiss
        ),
        title =
        LuziaAlertDialogText(
            stringResource(id = localizationR.string.text_title_delete_this_image_imagine_gallery),
            LuziaAlertDialogTextDefaults.Title()
        ),
        text =
        LuziaAlertDialogText(
            stringResource(id = localizationR.string.text_description_delete_imagine_gallery),
            LuziaAlertDialogTextDefaults.Description()
        )
    )
}

@Suppress("CyclomaticComplexMethod", "LongMethod")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun ImagineImageGalleryScreen(
    selectedImageIndex: Int,
    onNavigateBack: () -> Unit
) {
    val imagineImageGalleryViewModel: ImagineImageGalleryViewModel = hiltViewModel()
    val analytics = LocalAnalytics.current
    val context = LocalContext.current
    val density = LocalDensity.current
    val activity = LocalView.current.context as Activity
    val contentResolver: ContentResolver = LocalContext.current.contentResolver

    var loadingContent by remember { mutableStateOf(true) }
    val list by remember { mutableStateOf(mutableListOf<MyImagineImage>()) }
    var deletedImage by remember { mutableStateOf(false) }
    var isGoingBack by remember { mutableStateOf(false) }

    var isDeleteDialogVisible by remember { mutableStateOf(false) }
    var isPermissionDialogVisible by remember { mutableStateOf(false) }
    var permissionState by remember { mutableStateOf(false) }

    val scope = rememberCoroutineScope()

    val shareLauncher =
        rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) {}
    val requestPermissionLauncher =
        rememberLauncherForActivityResult(
            ActivityResultContracts.RequestPermission()
        ) { isGranted ->
            permissionState = isGranted
            if (isGranted) {
                isDeleteDialogVisible = true
            } else {
                if (!ActivityCompat.shouldShowRequestPermissionRationale(
                        activity,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE
                    )
                ) {
                    isPermissionDialogVisible = true
                }
            }
        }

    var isContextMenuVisible by rememberSaveable {
        mutableStateOf(false)
    }
    var pressOffset by remember {
        mutableStateOf(DpOffset.Zero)
    }
    var itemHeight by remember {
        mutableStateOf(0.dp)
    }
    val onDismissContextualMenu = {
        isContextMenuVisible = false
    }

    var bitmap: Bitmap? by remember {
        mutableStateOf(null)
    }
    LaunchedEffect(Dispatchers.IO) {
        withContext(Dispatchers.Main) {
            if (list.isNotEmpty()) {
                bitmap = BitmapFactory.decodeFile(list[selectedImageIndex].file?.path)
            }
        }
    }
    val imageBitmap = bitmap?.asImageBitmap()

    lateinit var intentSenderLauncher: ActivityResultLauncher<IntentSenderRequest>

    intentSenderLauncher =
        rememberLauncherForActivityResult(ActivityResultContracts.StartIntentSenderForResult()) {
            if (it.resultCode == Activity.RESULT_OK) {
                list.clear()
                deletedImage = true
                if (!isGoingBack) {
                    onNavigateBack()
                    isGoingBack = true
                }
            } else {
                Toast.makeText(
                    context,
                    context.getString(localizationR.string.error_delete_image_imagine_gallery),
                    Toast.LENGTH_LONG
                )
                    .show()
            }
        }

    if (isDeleteDialogVisible) {
        ImagineImageGalleryDeleteDialog(
            onConfirm = {
                val deleted =
                    deleteImage(
                        list = list,
                        selectedImageIndex = selectedImageIndex,
                        contentResolver = contentResolver,
                        intentSenderLauncher = intentSenderLauncher
                    )
                if (deleted == false) {
                    Toast.makeText(
                        context,
                        context.getString(localizationR.string.error_delete_image_imagine_gallery),
                        Toast.LENGTH_LONG
                    ).show()
                } else if (deleted == true) {
                    list.clear()
                    deletedImage = true
                    if (!isGoingBack) {
                        onNavigateBack()
                        isGoingBack = true
                    }
                }
                isDeleteDialogVisible = false
            },
            onDismiss = {
                isDeleteDialogVisible = false
            }
        )
    }

    if (isPermissionDialogVisible) {
        BaseAlertDialog.PermissionAlertDialog(
            onConfirm = {
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                val uri = Uri.fromParts(PACKAGE_URI, context.packageName, null)
                intent.data = uri
                context.startActivity(intent)
                isPermissionDialogVisible = false
            },
            onDismiss = {
                isPermissionDialogVisible = false
            }
        )
    }

    LaunchedEffect(loadingContent) {
        if (loadingContent) {
            list.clear()
            val imageFiles = imagineImageGalleryViewModel.getImages()
            if (imageFiles != null) {
                val imageFileListSorted = imageFiles.sortedByDescending { it.lastModified() }
                if (imageFileListSorted.isNotEmpty()) {
                    var index = 1
                    for (image in imageFileListSorted) {
                        val prompt = getDecodedPrompt(image)
                        val requestId = getDecodedRequestId(image)
                        list.add(
                            MyImagineImage(
                                name = image.name,
                                file = image,
                                isChecked = null,
                                index = index,
                                prompt = prompt,
                                requestId = requestId
                            )
                        )
                        index += 1
                    }
                }
            }
            imagineImageGalleryViewModel.setImages(list, selectedImageIndex)
            loadingContent = false
        }
    }

    LaunchedEffect(Unit) {
        analytics.logScreenView(ScreenView.PictureDetail)
    }

    Scaffold(
        topBar = {
            TopNavigationBar(
                TopNavigationBarModel(
                    title = stringResource(
                        id = localizationR.string.x_of_y,
                        selectedImageIndex + 1,
                        list.size
                    ),
                    navigationAction = NavigationAction.Icon(designR.drawable.ic_back_arrow) {
                        if (!isGoingBack) {
                            onNavigateBack()
                            isGoingBack = true
                        }
                    },
                    colors = LuziaNavBarDefaults.colors(containerColor = LuziaTheme.palette.surface.background)
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier =
            Modifier
                .fillMaxSize()
                .navigationBarsPadding()
                .padding(top = paddingValues.calculateTopPadding())
                .background(LuziaTheme.palette.surface.background)
        ) {
            // TODO This will be a carousel
            // TODO View of images no 1:1
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.Center
            ) {
                if (!deletedImage && !loadingContent && imageBitmap != null) {
                    ImagineImageContainer(
                        ImagineImageContainerType.DownloadedImage(
                            imageBitmap = imageBitmap,
                            prompt = list[selectedImageIndex].prompt
                        )
                    )
                    Spacer(modifier = Modifier.height(24.dp))
                    Box(
                        modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(all = 16.dp)
                            .background(color = LuziaTheme.palette.surface.background.copy(alpha = 0.4f))
                            .onSizeChanged {
                                itemHeight = with(density) { it.height.toDp() }
                            }
                            .pointerInput(true) {
                                detectTapGestures(
                                    onLongPress = {
                                        if (list[selectedImageIndex].prompt.isNotEmpty()) {
                                            isContextMenuVisible = true
                                            pressOffset = DpOffset(it.x.toDp(), it.y.toDp())
                                        }
                                    }
                                )
                            }
                    ) {
                        LuziaText(
                            list[selectedImageIndex].prompt,
                            style = LuziaTheme.typography.body.regular.small,
                            color = LuziaTheme.palette.text.primary,
                            modifier =
                            Modifier
                                .fillMaxWidth()
                                .verticalScroll(rememberScrollState())
                        )
                        LuziaContextualMenu(
                            isVisible = isContextMenuVisible,
                            pressOffset = pressOffset,
                            itemHeight = itemHeight,
                            onDismiss = onDismissContextualMenu,
                            dropDownItemList =
                            BaseContextualMenuOptions.messageDropDownList(
                                text = list[selectedImageIndex].prompt,
                                isAi = true,
                                onDismiss = onDismissContextualMenu,
                                onTextCopied = {
                                    analytics.logEvent(
                                        Event.CopyMessage,
                                        mapOf(Parameter.Tool to "image_creation")
                                    )
                                },
                                onTextShared = {
                                    context.shareText(it)
                                    analytics.logEvent(
                                        Event.ShareMessage,
                                        mapOf(
                                            Parameter.Tool to "image_creation",
                                            Parameter.Count to 1
                                        )
                                    )
                                },
                                onTextReported = {
                                    analytics.logEvent(
                                        Event.ReportMessage,
                                        mapOf(
                                            Parameter.Text to it,
                                            Parameter.Tool to "image_creation",
                                            Parameter.MessageId to list[selectedImageIndex].requestId.orEmpty()
                                        )
                                    )
                                },
                                enableFavoriteActions = false,
                                isFavorite = false,
                                onTextFavorite = { DO_NOTHING },
                                enableMessagingActions = false,
                                onTextEdited = { DO_NOTHING },
                                enableTextToSpeech = false
                            )
                        )
                    }
                }
            }

            BaseToolBar.GalleryToolBar(
                shareOnClick = {
                    analytics.logEvent(
                        Event.ImagineShare,
                        mapOf(Parameter.Source to ScreenView.PictureDetail.screenName)
                    )
                    bitmap?.let {
                        scope.launch {
                            val intent = Intent(Intent.ACTION_SEND)
                            intent.type = "image/*"
                            val screenshotUri = saveImage(context, it)
                            intent.putExtra(Intent.EXTRA_STREAM, screenshotUri)
                            try {
                                shareLauncher.launch(intent)
                            } catch (expected: ActivityNotFoundException) {
                                Toast.makeText(
                                    context,
                                    context.getString(localizationR.string.error_share_image_imagine),
                                    Toast.LENGTH_LONG
                                ).show()
                            }
                        }
                    } ?: kotlin.run {
                        Toast.makeText(
                            context,
                            context.getString(localizationR.string.error_share_image_imagine),
                            Toast.LENGTH_LONG
                        ).show()
                    }
                },
                deleteOnClick = {
                    analytics.logEvent(Event.ImaginePictureDelete)
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S_V2) {
                        isDeleteDialogVisible = true
                    } else {
                        requestPermissionLauncher.launch(Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    }
                }
            )
        }
    }
}

@Suppress("NestedBlockDepth", "InstanceOfCheckForException")
private fun deleteImage(
    list: List<MyImagineImage>,
    selectedImageIndex: Int,
    contentResolver: ContentResolver,
    intentSenderLauncher: ActivityResultLauncher<IntentSenderRequest>
): Boolean? {
    return list[selectedImageIndex].file?.let {
        if (it.exists()) {
            val imageUri = getImageContentUri(it, contentResolver)
            try {
                if (imageUri != null) {
                    val rowsDeleted = contentResolver.delete(imageUri, null, null)
                    if (rowsDeleted > 0) {
                        if (it.exists()) {
                            it.delete()
                        }
                        true
                    } else {
                        false
                    }
                } else {
                    false
                }
            } catch (expected: Exception) {
                if (expected is SecurityException) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        val intentSender =
                            MediaStore.createDeleteRequest(
                                contentResolver,
                                listOf(imageUri)
                            ).intentSender
                        intentSenderLauncher.launch(
                            IntentSenderRequest.Builder(intentSender).build()
                        )
                        return null
                    } else {
                        return false
                    }
                } else {
                    return false
                }
            }
        } else {
            false
        }
    } ?: false
}
