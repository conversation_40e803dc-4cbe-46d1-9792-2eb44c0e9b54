package co.theworldlab.luzia.features.imagine.presentation.gallery

import android.os.Environment
import androidx.lifecycle.ViewModel
import co.theworldlab.luzia.features.imagine.domain.MyImagineImage
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject

const val IMAGINE_LUZIA_FOLDER = "Imagine Luzia"
const val JPG_TYPE = "jpg"

@HiltViewModel
class ImagineGalleryViewModel
@Inject
constructor() : ViewModel() {
    data class UiImagineGalleryState(
        val allImagesList: List<MyImagineImage> = listOf(),
        val isViewStateImagineGalleryType: Boolean = true,
        val selectedItems: Int? = null
    )

    private val _uiImagineGalleryState = MutableStateFlow(UiImagineGalleryState())
    val uiImagineGalleryState: StateFlow<UiImagineGalleryState> =
        _uiImagineGalleryState.asStateFlow()

    fun addOrRemoveImage(image: MyImagineImage, index: Int) {
        image.isChecked = !(image.isChecked ?: false)
        val all =
            _uiImagineGalleryState.value.allImagesList.mapIndexed { i, item ->
                if (_uiImagineGalleryState.value.isViewStateImagineGalleryType) {
                    item.copy(isChecked = i == index)
                } else if (i == index) {
                    item.copy(isChecked = image.isChecked)
                } else {
                    item.copy()
                }
            }

        val selectedItems = all.count { it.isChecked == true }

        _uiImagineGalleryState.value =
            UiImagineGalleryState(
                isViewStateImagineGalleryType = false,
                allImagesList = all,
                selectedItems = selectedItems
            )
    }

    fun unselectAll() {
        val all = _uiImagineGalleryState.value.allImagesList.map { it.copy(isChecked = null) }
        _uiImagineGalleryState.value =
            UiImagineGalleryState(
                isViewStateImagineGalleryType = true,
                allImagesList = all,
                selectedItems = null
            )
    }

    fun selectAll() {
        val all = _uiImagineGalleryState.value.allImagesList.map { it.copy(isChecked = true) }
        _uiImagineGalleryState.value =
            UiImagineGalleryState(
                isViewStateImagineGalleryType = false,
                allImagesList = all,
                selectedItems = all.size
            )
    }

    fun setImages(list: List<MyImagineImage>) {
        _uiImagineGalleryState.value =
            UiImagineGalleryState(
                allImagesList = list
            )
    }

    suspend fun getImages(): Array<File>? {
        return withContext(Dispatchers.IO) {
            val folder =
                File(
                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES),
                    IMAGINE_LUZIA_FOLDER
                )
            if (!folder.exists()) {
                folder.mkdirs()
            }
            val imageFiles =
                folder.listFiles { file ->
                    file.isFile && file.extension == JPG_TYPE
                }
            return@withContext imageFiles
        }
    }
}
