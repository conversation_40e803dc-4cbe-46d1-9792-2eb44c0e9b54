package co.theworldlab.luzia.features.imagine.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.thewordlab.fouundation.persistence.chat.MessageType
import co.thewordlab.luzia.core.tools.domain.repository.ToolsRepository
import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.AnalyticsScreens
import co.thewordlab.luzia.foundation.analytics.Event
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject

@HiltViewModel
class ImagineViewModel @Inject constructor(
    private val dispatcher: CoroutineDispatcher,
    private val toolsRepository: ToolsRepository,
    private val analytics: Analytics
) : ViewModel() {

    data class UiImagineState(
        val error: Boolean = false,
        val prompt: String = "",
        val isChristmasMode: Boolean = false,
    )

    private val _uiImagineState = MutableStateFlow(UiImagineState())
    val uiImagineState: StateFlow<UiImagineState> = _uiImagineState.asStateFlow()

    fun errorShowed() {
        _uiImagineState.value = _uiImagineState.value.copy(error = false)
    }

    fun sendAudio(audioFile: File) {
        trackSendAudio()
        viewModelScope.launch(dispatcher) {
            when (val result = toolsRepository.sendAudioRecorder(audioFile, "", null)) {
                is ResultOf.Failure -> {
                    _uiImagineState.update { it.copy(prompt = "", error = true) }
                }
                is ResultOf.Success -> {
                    _uiImagineState.update { it.copy(prompt = result.data.content) }
                }
            }
        }
    }

    private fun trackSendAudio() {
        val properties = mutableMapOf<Parameter, Any>(
            Parameter.Type to MessageType.AudioRecord.property,
            Parameter.Source to IMAGINE
        )
        analytics.logEvent(Event.VoiceChat, properties)
    }

    fun savePrompt(prompt: String) {
        _uiImagineState.value = _uiImagineState.value.copy(prompt = prompt)
    }

    fun onStart(imagineId: String) {
        val isChristmasMode = imagineId == CHRISTMAS_ID
        _uiImagineState.value =
            _uiImagineState.value.copy(isChristmasMode = isChristmasMode)
        analytics.logScreen(
            AnalyticsScreens(
                if (isChristmasMode) {
                    CHRISTMAS_SCREEN
                } else {
                    IMAGINE
                }
            )
        )
    }

    companion object {
        private const val IMAGINE = "imagine"
        private const val CHRISTMAS_SCREEN = "christmas_postcard"
        private const val CHRISTMAS_ID = "christmas_postcard"
    }
}
