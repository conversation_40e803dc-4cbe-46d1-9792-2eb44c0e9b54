package co.theworldlab.luzia.features.imagine.data.api

import co.thewordlab.luzia.foundation.networking.model.ErrorDto
import co.theworldlab.luzia.features.imagine.data.api.models.ContentDeferredMediaDTO
import co.theworldlab.luzia.features.imagine.data.api.models.CreateImageGenerationResponse
import com.slack.eithernet.ApiResult
import com.slack.eithernet.DecodeErrorBody
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Query

interface ImagineApi {

    /**
     * Creates an image given a prompt and an image variation.
     * * Responses:
     *  - 200: OK
     *  - 0: Unexpected error
     *
     * @param createImageGenerationResponse * @param acceptLanguage Client&#39;s preferred
     * language for response content following ISO-639-1 (optional)
     * @param personalityId  (optional)
     * @return [kotlin.collections.List<ContentDeferredMediaResponse>]
     */
    @DecodeErrorBody
    @POST(IMAGINE_IMAGE_URL)
    suspend fun postImageGenerations(
        @Query("postcard") postCard: Boolean? = null,
        @Body createImageGenerationResponse: CreateImageGenerationResponse
    ): ApiResult<ContentDeferredMediaDTO, ErrorDto>

    companion object {
        const val IMAGINE_IMAGE_URL = "image-generations"
    }
}
