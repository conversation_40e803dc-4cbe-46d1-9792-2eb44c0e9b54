---
description: ResultOf Error Handling Pattern
globs: ["**/*Repository*.kt", "**/*UseCase*.kt", "**/domain/**/*.kt", "**/data/**/*.kt"]
alwaysApply: false
---
# ResultOf Error Handling Pattern

Use the custom `ResultOf<T, E>` sealed interface for error handling, NOT <PERSON>tlin's built-in `Result`.

## Basic Pattern

```kotlin
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.thewordlab.luzia.foundation.networking.model.Error
import co.thewordlab.luzia.foundation.networking.model.CommonErrors

// Repository methods return ResultOf
suspend fun getData(id: String): ResultOf<DataModel, Error> {
    return try {
        val response = api.getData(id)
        ResultOf.Success(response)
    } catch (e: NetworkException) {
        ResultOf.Failure(CommonErrors.NetworkError(e.message))
    } catch (e: Exception) {
        ResultOf.Failure(CommonErrors.UnknownError(e.message))
    }
}
```

## Usage in ViewModels

```kotlin
private fun loadData(id: String) = viewModelScope.launch {
    updateState { it.copy(isLoading = true) }
    
    when (val result = repository.getData(id)) {
        is ResultOf.Success -> {
            updateState { 
                it.copy(
                    data = result.value,
                    isLoading = false,
                    error = null
                )
            }
            sendEvent(DataLoadedEvent)
        }
        is ResultOf.Failure -> {
            updateState { 
                it.copy(
                    isLoading = false,
                    error = result.error.message
                )
            }
            sendEvent(ShowErrorEvent(result.error.message))
        }
    }
}
```

## UseCase Pattern

```kotlin
class GetUserDataUseCase @Inject constructor(
    private val repository: UserRepository
) {
    suspend operator fun invoke(userId: String): ResultOf<UserModel, Error> {
        return repository.getUserData(userId)
    }
}

// For multiple operations
class ComplexDataUseCase @Inject constructor(
    private val userRepository: UserRepository,
    private val profileRepository: ProfileRepository
) {
    suspend operator fun invoke(userId: String): ResultOf<ComplexData, Error> {
        return when (val userResult = userRepository.getUser(userId)) {
            is ResultOf.Success -> {
                when (val profileResult = profileRepository.getProfile(userId)) {
                    is ResultOf.Success -> {
                        val complexData = ComplexData(
                            user = userResult.value,
                            profile = profileResult.value
                        )
                        ResultOf.Success(complexData)
                    }
                    is ResultOf.Failure -> profileResult
                }
            }
            is ResultOf.Failure -> userResult
        }
    }
}
```

## Error Types

Define custom error types extending the base `Error` interface:

```kotlin
sealed class UserError : Error {
    object UserNotFound : UserError() {
        override val message = "User not found"
    }
    
    data class ValidationError(override val message: String) : UserError()
    
    data class NetworkError(override val message: String) : UserError()
}

// Usage
ResultOf.Failure(UserError.UserNotFound)
ResultOf.Failure(UserError.ValidationError("Invalid email format"))
```

## Mapping Results

```kotlin
// Transform success value
val mappedResult = originalResult.map { data ->
    data.toUiModel()
}

// Transform error
val transformedResult = originalResult.mapError { error ->
    CustomError(error.message)
}
```

## Testing with ResultOf

```kotlin
@Test
fun `Given valid data When getData Then returns success`() = runTest {
    // Given
    val expectedData = DataModel(id = "1", name = "Test")
    coEvery { api.getData(any()) } returns expectedData

    // When
    val result = repository.getData("1")

    // Then
    assertThat(result).isInstanceOf(ResultOf.Success::class.java)
    val success = result as ResultOf.Success
    assertThat(success.value).isEqualTo(expectedData)
}

@Test
fun `Given network error When getData Then returns failure`() = runTest {
    // Given
    coEvery { api.getData(any()) } throws NetworkException("Network error")

    // When
    val result = repository.getData("1")

    // Then
    assertThat(result).isInstanceOf(ResultOf.Failure::class.java)
    val failure = result as ResultOf.Failure
    assertThat(failure.error).isInstanceOf(CommonErrors.NetworkError::class.java)
}
```

## Common Errors

```kotlin
// From foundation/networking
CommonErrors.NetworkError(message)
CommonErrors.UnknownError(message)
CommonErrors.TimeoutError
CommonErrors.AuthenticationError
```

❌ **Don't use Kotlin's Result:**
```kotlin
// Avoid this
suspend fun getData(): Result<DataModel> // ❌
```

✅ **Use ResultOf:**
```kotlin
// Use this
suspend fun getData(): ResultOf<DataModel, Error> // ✅
```

@foundation/networking/src/main/java/co/thewordlab/luzia/foundation/networking/model/ResultOf.kt
