---
description: Naming Conventions and Code Style
globs: []
alwaysApply: true
---
# Naming Conventions and Code Style

Follow consistent naming patterns across the codebase.

## Class Naming

```kotlin
// ViewModels
class ProfileViewModel          // Feature ViewModels
class ProfileEditViewModel      // Specific action ViewModels

// ViewStates/Actions/Events
data class ProfileViewState : ViewState
sealed class ProfileViewActions : ViewAction
sealed class ProfileViewEvents : ViewEvent

// Repositories
interface ProfileRepository
class ProfileRepositoryImpl

// Use Cases
class GetUserProfileUseCase
class UpdateUserProfileUseCase

// Composables
@Composable
fun ProfileScreen()             // Main screens
@Composable
fun ProfileContent()            // Content composables
@Composable
fun ProfileHeader()             // Component composables
```

## Package Organization

Base package: `co.thewordlab.luzia` (some older modules use `co.theworldlab.luzia`)

```
features/profile/
├── presentation/
│   ├── profile/
│   │   ├── ProfileViewModel.kt
│   │   ├── ProfileViewState.kt
│   │   ├── ProfileViewActions.kt
│   │   ├── ProfileViewEvents.kt
│   │   └── ProfileScreen.kt
│   └── edit/
│       ├── ProfileEditViewModel.kt
│       └── ProfileEditScreen.kt
└── navigation/
    └── ProfileNavigation.kt
```

## File Naming

- `*ViewModel.kt` - ViewModels
- `*ViewState.kt` - State classes (can be in same file as ViewModel)
- `*ViewActions.kt` - Action classes
- `*ViewEvents.kt` - Event classes
- `*Screen.kt` - Main screen composables
- `*Repository.kt` - Repository interfaces
- `*RepositoryImpl.kt` - Repository implementations

## Resource Naming

All resources MUST have module prefixes:

```kotlin
// Strings
foundation_localization_error_network
features_profile_title_edit_profile
core_chat_message_send_error

// Drawables
foundation_design_system_ic_close
features_home_bg_gradient
core_gamification_ic_star

// Layouts (if any)
foundation_design_system_item_message
features_profile_screen_edit
```

## Constants

```kotlin
// In companion objects or top-level const
private companion object {
    const val DEFAULT_TIMEOUT = 5000L
    const val MAX_RETRY_COUNT = 3
    const val PREF_KEY_USER_ID = "user_id"
}

// Or top-level
const val DEFAULT_PAGE_SIZE = 20
```

## Analytics Events

```kotlin
// Event names
object ProfileAnalytics {
    const val PROFILE_SCREEN = "profile_screen"
    const val PROFILE_EDIT_CLICKED = "profile_edit_clicked"
    const val PROFILE_SAVE_SUCCESS = "profile_save_success"
}

// Parameters
object Parameter {
    const val USER_ID = "user_id"
    const val PROFILE_TYPE = "profile_type"
    const val ERROR_MESSAGE = "error_message"
}
```

## Method Naming

```kotlin
// ViewActions handler
override fun onViewAction(action: ProfileViewActions)

// Private methods - descriptive
private fun loadUserProfile()
private fun updateProfileData(data: ProfileData)
private fun handleNetworkError(error: NetworkError)

// Use Cases - action-oriented
suspend operator fun invoke(userId: String): ResultOf<Profile, Error>
```

## Boolean Properties

```kotlin
// Use positive naming
val isLoading: Boolean = false
val isEnabled: Boolean = true
val hasPermission: Boolean = false

// Avoid negative naming
val isNotLoading: Boolean = true  // ❌ Avoid
val isDisabled: Boolean = false   // ❌ Avoid
```
