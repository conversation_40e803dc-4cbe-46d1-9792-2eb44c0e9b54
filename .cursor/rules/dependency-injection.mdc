---
description: Hilt Dependency Injection Patterns
globs: ["**/*Module.kt", "**/*ViewModel.kt", "**/di/**/*.kt", "**/*Repository*.kt", "**/*UseCase*.kt"]
alwaysApply: false
---
# Hilt Dependency Injection Patterns

Use Hilt for dependency injection with proper scoping and module organization.

## ViewModel Injection

```kotlin
@HiltViewModel
class ExampleViewModel @Inject constructor(
    private val repository: ExampleRepository,
    private val analytics: Analytics,
    private val featureFlagManager: FeatureFlagManager
) : ViewModel(),
    ViewModelActions<ExampleViewActions>,
    ViewModelStates<ExampleViewState> by ViewModelStatesImpl(ExampleViewState()),
    ViewModelEvents<ExampleViewEvents> by ViewModelEventsImpl() {
    
    // ViewModel implementation
}
```

## Repository Pattern

```kotlin
interface ExampleRepository {
    suspend fun getData(): ResultOf<List<ExampleModel>, Error>
}

class ExampleRepositoryImpl @Inject constructor(
    private val api: ExampleApi,
    private val dao: ExampleDao
) : ExampleRepository {
    
    override suspend fun getData(): ResultOf<List<ExampleModel>, Error> {
        return try {
            val result = api.getData()
            dao.saveData(result)
            ResultOf.Success(result)
        } catch (e: Exception) {
            ResultOf.Failure(CommonErrors.NetworkError(e.message))
        }
    }
}
```

## Hilt Modules

```kotlin
@Module
@InstallIn(SingletonComponent::class)
abstract class ExampleModule {

    @Binds
    abstract fun bindExampleRepository(
        implementation: ExampleRepositoryImpl
    ): ExampleRepository

    companion object {
        @Provides
        @Singleton
        fun provideExampleApi(
            retrofit: Retrofit
        ): ExampleApi = retrofit.create(ExampleApi::class.java)
    }
}
```

## UseCase Pattern

```kotlin
class GetExampleDataUseCase @Inject constructor(
    private val repository: ExampleRepository
) {
    suspend operator fun invoke(id: String): ResultOf<ExampleModel, Error> {
        return repository.getData(id)
    }
}
```

## Assisted Injection (for ViewModels with runtime parameters)

```kotlin
@HiltViewModel(assistedFactory = ExampleViewModelFactory::class)
class ExampleViewModel @AssistedInject constructor(
    @Assisted private val itemId: String,
    private val repository: ExampleRepository,
    private val analytics: Analytics
) : ViewModel(),
    ViewModelActions<ExampleViewActions>,
    ViewModelStates<ExampleViewState> by ViewModelStatesImpl(ExampleViewState()) {

    @AssistedFactory
    interface ExampleViewModelFactory {
        fun create(itemId: String): ExampleViewModel
    }
}
```

## Scoping Rules

- `@Singleton` - App-wide single instance
- `@ActivityScoped` - Activity lifecycle
- `@ViewModelScoped` - ViewModel lifecycle
- `@InstallIn(SingletonComponent::class)` - App-wide modules
- `@InstallIn(ActivityComponent::class)` - Activity-scoped modules

## Required Imports

```kotlin
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.components.SingletonComponent
import javax.inject.Inject
import javax.inject.Singleton
```

## Common Patterns

✅ **Use `@Binds` for interface implementations**
✅ **Use `@Provides` for external dependencies (Retrofit, Room, etc.)**
✅ **Keep modules focused on single responsibility**
✅ **Use proper scoping to avoid memory leaks**
