import co.thewordlab.luzia.LuziaFlavor

plugins {
	alias(libs.plugins.luzia.android.library)
	alias(libs.plugins.luzia.android.library.jacoco)
	alias(libs.plugins.luzia.android.hilt)
}

android {
	namespace = "co.thewordlab.luzia.foundation.networking"
	productFlavors {
		getByName(LuziaFlavor.dev.name) {
			buildConfigField(
				"String",
				"BASE_GOOGLE_URL",
				"\"https://www.googleapis.com/oauth2/v1/\""
			)
			buildConfigField(
				"String",
				"BASE_API_URL",
				"\"https://antonia.staging.thewordlab.net/api/v4.0/\""
			)
			buildConfigField(
				"String",
				"PROACTIVE_MESSAGING_API_URL",
				"\"https://proactive-messaging.staging.thewordlab.net/api/v1.0/\""
			)
			buildConfigField(
				"String",
				"API_KEY",
				"\"r5tig@bn7oi3!9v8rRfq6k7dr!vmE8\""
			)
			buildConfigField(
				"String",
				"ORGANIZATION_KEY",
				"\"66dd9263-b005-43fd-993b-3626ed6b6bf3\""
			)
		}
		getByName(LuziaFlavor.pro.name) {
			buildConfigField(
				"String",
				"BASE_GOOGLE_URL",
				"\"https://www.googleapis.com/oauth2/v1/\""
			)
			buildConfigField(
				"String",
				"BASE_API_URL",
				"\"https://luzia.thewordlab.net/api/v4.0/\""
			)
			buildConfigField(
				"String",
				"PROACTIVE_MESSAGING_API_URL",
				"\"https://proactive-messaging.prod.thewordlab.net/api/v1.0/\""
			)
			buildConfigField(
				"String",
				"API_KEY",
				"\"fXuk7lHv2WaTMbVNqu@vXEDtHfz&IJ0SW\""
			)
			buildConfigField(
				"String",
				"ORGANIZATION_KEY",
				"\"a7bd74a8-7247-42c3-ab3e-fe1015cd2849\""
			)
		}
	}
}

dependencies {
	implementation(libs.coil.kt)
	implementation(libs.coil.kt.svg)
	implementation(libs.okhttp.logging)
	implementation(libs.retrofit.core)
	implementation(libs.retrofit.moshi)
	implementation(libs.slack.eithernet)
	implementation(libs.moshi.core)
	implementation(libs.moshi.kotlin)
	implementation(libs.moshi.adapters)
	implementation(libs.google.adid)
	ksp(libs.moshi.codegen)
	devImplementation(libs.chucker.main)
	proImplementation(libs.chucker.none)
	implementation(libs.androidx.dataStore.core)
	implementation(libs.androidx.dataStore.preferences)
	implementation(projects.foundation.persistence)
	implementation(projects.foundation.analytics)
	testImplementation(projects.foundation.testing)
}
