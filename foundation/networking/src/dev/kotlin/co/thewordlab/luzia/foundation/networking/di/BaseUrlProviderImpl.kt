package co.thewordlab.luzia.foundation.networking.di

import androidx.datastore.preferences.core.stringPreferencesKey
import co.thewordlab.fouundation.persistence.LuziaDataStore
import co.thewordlab.luzia.foundation.networking.model.environments.DevEnvironment
import kotlinx.coroutines.runBlocking
import javax.inject.Inject

class BaseUrlProviderImpl @Inject constructor(
    private val luziaDataStore: LuziaDataStore
) : BaseUrlProvider {

    override fun provideBaseUrl(): String {
        val environment =
            runBlocking { DevEnvironment.fromString(luziaDataStore.getData(environmentPreference)) }
        return environment.baseUrl
    }

    private companion object {
        const val KEY_SELECTED_ENVIRONMENT = "selected_environment"
        val environmentPreference = stringPreferencesKey(KEY_SELECTED_ENVIRONMENT)
    }
}