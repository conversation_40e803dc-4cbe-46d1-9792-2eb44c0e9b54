package co.thewordlab.luzia.foundation.networking.interceptor

import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.networking.interceptor.model.ApiFailureAnalyticsEvents
import okhttp3.Interceptor
import okhttp3.Response
import javax.inject.Inject

class FailureInterceptor @Inject constructor(
    private val analytics: Analytics
) : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)
        if (!response.isSuccessful) {
            val url = request.url.toString()
            val code = response.code
            val body = response.peekBody(Long.MAX_VALUE).string()
            analytics.logEvent(
                ApiFailureAnalyticsEvents,
                mapOf(
                    Parameter.ApiEndpoint to url,
                    Parameter.ApiResponseCode to code,
                    Parameter.ApiResponseBody to body,
                    Parameter.CorrelationId to response.request.header(Headers.CORRELATION_ID).orEmpty(),
                )
            )
        }
        return response
    }
}
