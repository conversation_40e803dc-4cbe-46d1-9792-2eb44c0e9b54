package co.thewordlab.luzia.foundation.networking.auth

import co.thewordlab.luzia.foundation.networking.interceptor.Headers
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import okhttp3.Authenticator
import okhttp3.Request
import okhttp3.Response
import okhttp3.Route

class LuziaAuthenticator(private val sessionManager: UserSessionManager) : Authenticator {

    override fun authenticate(route: Route?, response: Response): Request? {
        var request: Request?
        val token = response.request.header(Headers.HEADER_TOKEN_NAME)?.replace("Bearer ", "")
        synchronized(this) {
            val newSession = sessionManager.refreshSession(token)
            request = if (newSession != null) {
                response.request.newBuilder()
                    .header(Headers.HEADER_TOKEN_NAME, "Bearer ${newSession.accessToken}")
                    .header(Headers.DEVICE_KEY, newSession.deviceKey)
                    .build()
            } else {
                null
            }
        }
        return request
    }
}
