/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

package co.thewordlab.luzia.foundation.networking.session.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

data class AppConfigData(
    val versionAppConfig: Int?,
    val appVersionData: AppVersionData
)

data class AppVersionData(
    val minimumSupportedVersion: String,
    val latestVersion: String,
    val bannedVersions: List<String>
) {
    fun isForceUpdateApp(currentVersion: String): Boolean {
        return isCurrentVersionMinor(currentVersion, minimumSupportedVersion) ||
            currentVersion in bannedVersions
    }

    fun isUpdateAvailable(currentVersion: String): Boolean =
        isCurrentVersionMinor(currentVersion, latestVersion)

    private fun isCurrentVersionMinor(currentVersion: String, appConfigVersion: String): Boolean {
        val minimumVersion = appConfigVersion.split(".")
        val minimumVersionMajor = minimumVersion[0].toInt()
        val minimumVersionMinor = minimumVersion[1].toInt()
        val minimumVersionPatch = minimumVersion[2].toInt()

        val currentAppVersion = currentVersion.split(".")
        val currentAppVersionMajor = currentAppVersion[0].toInt()
        val currentAppVersionMinor = currentAppVersion[1].toInt()
        val currentAppVersionPatch = currentAppVersion[2].toInt()

        return when {
            currentAppVersionMajor > minimumVersionMajor -> false
            currentAppVersionMajor < minimumVersionMajor -> true
            currentAppVersionMinor > minimumVersionMinor -> false
            currentAppVersionMinor < minimumVersionMinor -> true
            currentAppVersionPatch > minimumVersionPatch -> false
            currentAppVersionPatch < minimumVersionPatch -> true
            else -> false
        }
    }
}

@JsonClass(generateAdapter = true)
data class RetrieveConfigResponse(
    // Version number of the configuration.
    @Json(name = "version")
    val versionAppConfig: Int?,
    // Localized registration message.
    @Json(name = "settings")
    val settings: VersionControlResponse
) {
    fun toDomain() = AppConfigData(
        versionAppConfig = versionAppConfig,
        appVersionData = settings.versionAppConfig.toDomain()
    )
}

@JsonClass(generateAdapter = true)
data class VersionControlResponse(
    @Json(name = "versionControl")
    val versionAppConfig: AndroidVersionResponse
)

@JsonClass(generateAdapter = true)
data class AndroidVersionResponse(
    @Json(name = "android")
    val versionAppConfig: AppVersionResponse
) {
    fun toDomain() = AppVersionData(
        minimumSupportedVersion = versionAppConfig.minimumSupportedVersion,
        latestVersion = versionAppConfig.latestVersion,
        bannedVersions = versionAppConfig.bannedVersions
    )
}

@JsonClass(generateAdapter = true)
data class AppVersionResponse(
    // The minimum app version that is supported, in semantic versioning format (X.Y.Z).
    @Json(name = "minimumSupportedVersion")
    val minimumSupportedVersion: String,
    // The latest available version of the app that is supported, in semantic versioning format (X.Y.Z).
    @Json(name = "latestVersion")
    val latestVersion: String,
    // Banned version of the app, in semantic versioning format (X.Y.Z).
    @Json(name = "bannedVersions")
    val bannedVersions: List<String>
)
