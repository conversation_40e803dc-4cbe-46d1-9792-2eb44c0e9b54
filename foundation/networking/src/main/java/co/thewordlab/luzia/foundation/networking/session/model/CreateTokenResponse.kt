package co.thewordlab.luzia.foundation.networking.session.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class CreateTokenResponse(
    @<PERSON><PERSON>(name = "refreshToken")
    val refreshToken: String,
    @<PERSON><PERSON>(name = "token")
    val token: String,
    @J<PERSON>(name = "userSessionId")
    val userSessionId: String?,
    @Json(name = "masterUserId")
    val masterUserId: String?,
    @Json(name = "metadata")
    val metadata: MetadataResponse?
)

@JsonClass(generateAdapter = true)
data class MetadataResponse(
    @<PERSON><PERSON>(name = "featureFlags")
    val featureFlags: Map<String, Any>?,
    @<PERSON><PERSON>(name = "requestId")
    val requestId: String,
    @<PERSON><PERSON>(name = "isNewAdId")
    val isNewAdId: Boolean?
)
