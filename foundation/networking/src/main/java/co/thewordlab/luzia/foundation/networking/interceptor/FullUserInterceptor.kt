package co.thewordlab.luzia.foundation.networking.interceptor

import co.thewordlab.luzia.foundation.analytics.Analytics
import co.thewordlab.luzia.foundation.networking.headers.FullUserNeeded
import co.thewordlab.luzia.foundation.networking.model.ErrorDetailDto
import co.thewordlab.luzia.foundation.networking.model.ErrorDto
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import co.thewordlab.luzia.foundation.networking.session.UserType
import com.squareup.moshi.Moshi
import okhttp3.Interceptor
import okhttp3.Protocol
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import retrofit2.Invocation
import javax.inject.Inject

class FullUserInterceptor @Inject constructor(
    private val userSessionManager: UserSessionManager,
    private val moshi: Moshi,
    private val analytics: Analytics
) : Interceptor {

    private val error by lazy {
        moshi.adapter(ErrorDto::class.java).toJson(ERROR)
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val method = chain.request().tag(Invocation::class.java)?.method()
        val annotation = method?.annotations?.filterIsInstance<FullUserNeeded>()?.firstOrNull()
        if (annotation == null) {
            return chain.proceed(chain.request())
        }
        val isFullUser =
            userSessionManager.getUserSessionImmediate()?.userType == UserType.FULL_USER
        return if (!isFullUser) {
            val request = chain.request()
            analytics.reportException("Full user expected check endpoint ${request.url}")
            Response.Builder()
                .protocol(Protocol.HTTP_2)
                .request(request)
                .body(error.toResponseBody())
                .code(HTTP_CODE_UNAUTHENTICATED)
                .message(ERROR_MESSAGE)
                .build()
        } else {
            chain.proceed(chain.request())
        }
    }

    private companion object {
        const val HTTP_CODE_UNAUTHENTICATED = 403
        const val ERROR_MESSAGE = "Only full users can access this endpoint"
        val ERROR = ErrorDto(
            ErrorDetailDto(
                code = HTTP_CODE_UNAUTHENTICATED.toString(),
                message = ERROR_MESSAGE,
                extraData = null
            )
        )
    }
}
