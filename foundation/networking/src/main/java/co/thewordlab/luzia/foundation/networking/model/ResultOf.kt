package co.thewordlab.luzia.foundation.networking.model

import com.slack.eithernet.ApiResult
import com.squareup.moshi.JsonDataException
import com.squareup.moshi.Moshi
import retrofit2.Response
import java.io.IOException

interface Error

private const val HTTP_LOCKED = 423
private const val HTTP_ENTITY_TOO_LARGE = 413
private const val HTTP_UNPROCESSABLE_CONTENT = 422
private const val HTTP_TOO_MANY_REQUESTS = 429

sealed interface ResultOf<out T, out E : Error> {
    data class Success<out T, out E : Error>(val data: T) : ResultOf<T, E>

    data class Failure<out T, out E : Error>(
        val error: E,
        val extraData: ExtraData? = null,
        val throwable: Throwable? = null
    ) : ResultOf<T, E>
}

fun <T : Any> ApiResult<T, ErrorDto>.asResult(): ResultOf<T, AppErrors> {
    return when (this) {
        is ApiResult.Failure.ApiFailure -> ResultOf.Failure(
            error.asAppError(),
            error.extractExtraData(),
            error?.let { Throwable("$it") }
        )

        is ApiResult.Failure.HttpFailure -> ResultOf.Failure(
            asAppError(),
            error.extractExtraData(),
            error?.let { Throwable("$it") }
        )

        is ApiResult.Failure.NetworkFailure -> ResultOf.Failure(AppErrors.NoNetwork, null, error)
        is ApiResult.Failure.UnknownFailure -> {
            ResultOf.Failure(AppErrors.Unknown, null, error)
        }

        is ApiResult.Success -> ResultOf.Success(value)
    }
}

fun ErrorDto?.extractExtraData(): ExtraData =
    ExtraData(
        dataErrors = this?.error?.extraData?.dataErrors?.map {
            ExtraErrorDetail(
                it.key,
                it.message
            )
        }.orEmpty()
    )

fun <T : Any, E : Error> ApiResult<T, ErrorDto>.asResult(
    mapError: (ApiResult.Failure<ErrorDto>) -> ResultOf.Failure<T, E>
): ResultOf<T, E> {
    return when (this) {
        is ApiResult.Failure -> mapError(this)
        is ApiResult.Success -> ResultOf.Success(value)
    }
}

fun <T, E : Error> T.asSuccess() = ResultOf.Success<T, E>(this)

fun <T, E : Error> ResultOf<T, E>.isSuccess() = this is ResultOf.Success

fun <T, E : Error> ResultOf<T, E>.isFailure() = this is ResultOf.Failure

fun <T, E : Error> ResultOf<T, E>.getDataOrNull(): T? = when (this) {
    is ResultOf.Failure -> null
    is ResultOf.Success -> data
}

fun <T, E : Error> E.asFailure() = ResultOf.Failure<T, E>(this)

fun <T, R> ResultOf<T, AppErrors>.mapTo(mapper: T.() -> R): ResultOf<R, AppErrors> {
    return when (this) {
        is ResultOf.Failure -> ResultOf.Failure(error)
        is ResultOf.Success -> ResultOf.Success(mapper.invoke(data))
    }
}

fun <T, R, E : Error> ResultOf<T, E>.map(mapper: T.() -> R): ResultOf<R, E> {
    return when (this) {
        is ResultOf.Failure -> ResultOf.Failure(error)
        is ResultOf.Success -> ResultOf.Success(mapper.invoke(data))
    }
}

private fun ErrorDto?.asAppError(): AppErrors {
    return when (this?.error?.code) {
        else -> AppErrors.Unknown
    }
}

private fun mapHttpErrorToAppError(code: Int): AppErrors {
    return when (code) {
        HTTP_LOCKED -> AppErrors.AuthRequired
        HTTP_TOO_MANY_REQUESTS -> AppErrors.LimitReached
        HTTP_ENTITY_TOO_LARGE, HTTP_UNPROCESSABLE_CONTENT -> AppErrors.FileTooBig
        else -> AppErrors.Unknown
    }
}

private fun ApiResult.Failure.HttpFailure<ErrorDto>.asAppError(): AppErrors {
    return mapHttpErrorToAppError(this.code)
}

fun <T> Response<T>.asResult(moshi: Moshi): ResultOf<T, AppErrors> {
    return when {
        isSuccessful -> {
            val body = body()
            if (body != null) {
                ResultOf.Success(body)
            } else {
                ResultOf.Failure(AppErrors.MediaNotFound)
            }
        }

        else -> {
            val errorBody = errorBody()
            if (errorBody != null) {
                val result: ResultOf<T, AppErrors> = try {
                    val jsonAdapter = moshi.adapter(ErrorDto::class.java)
                    val errorDto = jsonAdapter.fromJson(errorBody.source())
                    ResultOf.Failure(
                        errorDto.asAppError(),
                        errorDto.extractExtraData(),
                        errorDto?.let { Throwable("$it") }
                    )
                } catch (e: JsonDataException) {
                    ResultOf.Failure(AppErrors.Unknown, null, e)
                } catch (e: IOException) {
                    ResultOf.Failure(AppErrors.Unknown, null, e)
                }
                result
            } else {
                ResultOf.Failure(mapHttpErrorToAppError(code()))
            }
        }
    }
}
