package co.thewordlab.luzia.foundation.networking.model

/**
 * TODO Ruy: This class should be moved to foundations/common along with the Error interface
 */
sealed class CommonErrors(val description: String) : Error {

    data object NetworkError : CommonErrors("Not connected to the internet")
    data object UnspecifiedError : CommonErrors("Unspecified api error")
    data class ValidationError(val id: String, val message: String) : CommonErrors("Field $id: $message")
}
