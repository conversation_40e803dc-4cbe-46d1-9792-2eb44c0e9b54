package co.thewordlab.luzia.foundation.networking.interceptor

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import co.thewordlab.luzia.foundation.networking.BuildConfig
import co.thewordlab.luzia.foundation.networking.device.DeviceManagement
import co.thewordlab.luzia.foundation.networking.interceptor.language.LanguageHandler
import dagger.hilt.android.qualifiers.ApplicationContext
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import java.util.Locale
import javax.inject.Inject

class EssentialsInterceptor
@Inject
constructor(
    @ApplicationContext private val context: Context,
    private val languageHandler: LanguageHandler,
    private val deviceManagement: DeviceManagement
) : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val builder = request.newBuilder()
        val adId = deviceManagement.getAdvertisingId()
        Headers.AD_ID.include(builder, adId)
        Headers.HEADER_USER_AGENT.include(builder, getUserAgent())
        Headers.API_KEY.include(builder, BuildConfig.API_KEY)
        Headers.ORGANIZATION_KEY.include(builder, BuildConfig.ORGANIZATION_KEY)
        Headers.ACCEPT_LANGUAGE.include(builder, languageHandler.languageAndCountryCode())
        Headers.COUNTRY_CODE.include(builder, Locale.getDefault().country)
        return chain.proceed(builder.build())
    }

    private fun String.include(builder: Request.Builder, value: String) {
        builder.header(this, value)
    }

    private fun getUserAgent(): String {
        return "${Build.DEVICE}/${Build.MODEL} Android/${Build.VERSION.RELEASE} LuziaApp/${getVersionName(context)}"
    }

    private fun getVersionName(context: Context): String {
        return try {
            val packageName = context.packageName
            val packageInfo = context.packageManager.getPackageInfo(packageName, 0)
            packageInfo.versionName.orEmpty()
        } catch (expected: PackageManager.NameNotFoundException) {
            Log.d("LuziaApp", "App name not found: " + expected.message)
            "-"
        }
    }
}
