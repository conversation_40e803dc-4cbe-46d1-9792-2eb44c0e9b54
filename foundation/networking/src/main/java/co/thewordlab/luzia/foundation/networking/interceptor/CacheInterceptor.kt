package co.thewordlab.luzia.foundation.networking.interceptor

import co.thewordlab.luzia.foundation.networking.headers.CacheEnabled
import okhttp3.CacheControl
import okhttp3.Interceptor
import okhttp3.Response
import retrofit2.Invocation
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class CacheInterceptor @Inject constructor() : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val method = chain.request().tag(Invocation::class.java)?.method()
        val cacheEnabled = method?.annotations?.filterIsInstance<CacheEnabled>()?.firstOrNull()
        return if (cacheEnabled == null) {
            chain.proceed(chain.request())
        } else {
            val hours = cacheEnabled.expiry.hour
            val response: Response = chain.proceed(chain.request())
            val cacheControl = CacheControl.Builder()
                .maxAge(hours, TimeUnit.HOURS)
                .build()
            return response.newBuilder()
                .header(Headers.CACHE_CONTROL, cacheControl.toString())
                .build()
        }
    }
}
