package co.thewordlab.luzia.foundation.networking.di

import android.content.Context
import co.thewordlab.luzia.foundation.analytics.providers.AnalyticsDeviceIdProvider
import co.thewordlab.luzia.foundation.networking.BuildConfig
import co.thewordlab.luzia.foundation.networking.auth.LuziaAuthenticator
import co.thewordlab.luzia.foundation.networking.converters.GamificationConverterFactory
import co.thewordlab.luzia.foundation.networking.device.DeviceManagement
import co.thewordlab.luzia.foundation.networking.interceptor.CacheInterceptor
import co.thewordlab.luzia.foundation.networking.interceptor.CorrelationIdInterceptor
import co.thewordlab.luzia.foundation.networking.interceptor.EssentialsInterceptor
import co.thewordlab.luzia.foundation.networking.interceptor.FailureInterceptor
import co.thewordlab.luzia.foundation.networking.interceptor.FullUserInterceptor
import co.thewordlab.luzia.foundation.networking.interceptor.UserSessionInterceptor
import co.thewordlab.luzia.foundation.networking.session.UserSessionManager
import co.thewordlab.luzia.foundation.networking.session.api.SessionApi
import com.chuckerteam.chucker.api.ChuckerInterceptor
import com.slack.eithernet.ApiResultCallAdapterFactory
import com.slack.eithernet.ApiResultConverterFactory
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.moshi.MoshiConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Singleton

private const val TIMEOUT_IN_SECONDS = 60L

@Suppress("TooManyFunctions", "LongParameterList")
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    @Singleton
    @Provides
    fun provideMoshi(): Moshi = Moshi.Builder()
        .add(KotlinJsonAdapterFactory())
        .build()

    @Suppress("LongParameterList")
    @Provides
    fun provideHttpClient(
        @ApplicationContext context: Context,
        essentialsInterceptor: EssentialsInterceptor,
        failureInterceptor: FailureInterceptor,
        correlationIdInterceptor: CorrelationIdInterceptor
    ): OkHttpClient.Builder {
        return OkHttpClient.Builder()
            .readTimeout(TIMEOUT_IN_SECONDS, TimeUnit.SECONDS)
            .connectTimeout(TIMEOUT_IN_SECONDS, TimeUnit.SECONDS)
            .addInterceptor(essentialsInterceptor)
            .addInterceptor(failureInterceptor)
            .addInterceptor(correlationIdInterceptor)
            .apply {
                if (BuildConfig.FLAVOR == "dev") {
                    addInterceptor(ChuckerInterceptor(context))
                }
            }
    }

    @Singleton
    @Provides
    fun provideLuziaAuthenticator(
        userSessionManager: UserSessionManager
    ): LuziaAuthenticator = LuziaAuthenticator(userSessionManager)

    @BaseHost
    @Singleton
    @Provides
    fun provideRetrofit(
        okHttpClientBuilder: OkHttpClient.Builder,
        moshi: Moshi,
        cacheInterceptor: CacheInterceptor,
        fullUserInterceptor: FullUserInterceptor,
        authenticator: LuziaAuthenticator,
        sessionInterceptor: UserSessionInterceptor,
        baseUrlProvider: BaseUrlProvider,
        gamificationConverterFactory: GamificationConverterFactory
    ): Retrofit {
        val client = okHttpClientBuilder
            .addInterceptor(fullUserInterceptor)
            .addInterceptor(cacheInterceptor)
            .addInterceptor(sessionInterceptor)
            .includeLogging()
            .authenticator(authenticator)
            .build()
        return Retrofit.Builder()
            .baseUrl(baseUrlProvider.provideBaseUrl())
            .client(client)
            .addConverterFactory(ApiResultConverterFactory)
            .addCallAdapterFactory(ApiResultCallAdapterFactory)
            .addConverterFactory(gamificationConverterFactory)
            .addConverterFactory(MoshiConverterFactory.create(moshi).asLenient())
            .build()
    }

    @BaseHostNoSession
    @Singleton
    @Provides
    fun provideRetrofitNoSession(
        okHttpClientBuilder: OkHttpClient.Builder,
        moshi: Moshi,
        baseUrlProvider: BaseUrlProvider
    ): Retrofit {
        return Retrofit.Builder()
            .baseUrl(baseUrlProvider.provideBaseUrl())
            .client(okHttpClientBuilder.includeLogging().build())
            .addConverterFactory(ApiResultConverterFactory)
            .addCallAdapterFactory(ApiResultCallAdapterFactory)
            .addConverterFactory(MoshiConverterFactory.create(moshi).asLenient())
            .build()
    }

    @GoogleHost
    @Singleton
    @Provides
    fun provideRetrofitForGoogle(
        moshi: Moshi
    ): Retrofit {
        val client = OkHttpClient.Builder()
            .includeLogging()
            .build()
        return Retrofit.Builder()
            .baseUrl(BuildConfig.BASE_GOOGLE_URL)
            .client(client)
            .addConverterFactory(ApiResultConverterFactory)
            .addCallAdapterFactory(ApiResultCallAdapterFactory)
            .addConverterFactory(MoshiConverterFactory.create(moshi).asLenient())
            .build()
    }

    @ProactiveMessagingHost
    @Singleton
    @Provides
    fun provideRetrofitForProactiveMessaging(
        okHttpClientBuilder: OkHttpClient.Builder,
        moshi: Moshi,
        sessionInterceptor: UserSessionInterceptor
    ): Retrofit {
        val client = okHttpClientBuilder
            .addInterceptor(sessionInterceptor)
            .includeLogging()
            .build()

        return Retrofit.Builder()
            .baseUrl(BuildConfig.PROACTIVE_MESSAGING_API_URL)
            .client(client)
            .addConverterFactory(ApiResultConverterFactory)
            .addCallAdapterFactory(ApiResultCallAdapterFactory)
            .addConverterFactory(MoshiConverterFactory.create(moshi).asLenient())
            .build()
    }

    @Provides
    fun provideSessionApi(
        @BaseHostNoSession retrofit: Retrofit
    ): SessionApi {
        return retrofit.create(SessionApi::class.java)
    }

    @Provides
    fun provideAnalyticsDeviceIdProvider(
        deviceManagement: DeviceManagement
    ): AnalyticsDeviceIdProvider {
        return deviceManagement
    }

    private fun OkHttpClient.Builder.includeLogging(): OkHttpClient.Builder {
        if (BuildConfig.DEBUG) {
            return addInterceptor(
                HttpLoggingInterceptor().apply {
                    level = HttpLoggingInterceptor.Level.BODY
                }
            )
        }
        return this
    }
}
