package co.thewordlab.luzia.foundation.networking.session

import co.thewordlab.luzia.foundation.networking.session.model.MetadataResponse
import com.squareup.moshi.Moshi
import javax.inject.Inject

class UserSessionMapper @Inject constructor(
    private val moshi: <PERSON><PERSON>
) {

    fun mapToJson(userSession: UserSession): String {
        return moshi.adapter(UserSession::class.java).toJson(userSession)
    }

    fun mapToUserSession(json: String?): UserSession? {
        return json?.let { moshi.adapter(UserSession::class.java).fromJson(json) }
    }

    fun mapToMetadata(metadata: MetadataResponse?): Metadata? =
        metadata?.let {
            Metadata(
                featureFlags = it.featureFlags?.toMap().orEmpty(),
                requestId = it.requestId,
                isNewAdId = it.isNewAdId ?: false
            )
        }
}
