package co.thewordlab.luzia.foundation.networking.session

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class UserSession(
    @Json(name = "accessToken")
    val accessToken: String,
    @<PERSON><PERSON>(name = "refreshToken")
    val refreshToken: String,
    @<PERSON><PERSON>(name = "deviceKey")
    val deviceKey: String,
    @<PERSON><PERSON>(name = "sessionId")
    val sessionId: String?,
    @<PERSON><PERSON>(name = "masterUserId")
    val masterUserId: String?,
    @<PERSON>son(name = "userType")
    val userType: UserType?
)

@JsonClass(generateAdapter = true)
data class Metadata(
    @Json(name = "featureFlags")
    val featureFlags: Map<String, Any>,
    @<PERSON><PERSON>(name = "requestId")
    val requestId: String,
    @<PERSON><PERSON>(name = "isNewAdId")
    val isNewAdId: Boolean
)

@JsonClass(generateAdapter = false)
enum class UserType(val value: String) {

    @<PERSON><PERSON>(name = "guest")
    GUEST("guest"),

    @<PERSON><PERSON>(name = "guest_plus")
    GUEST_PLUS("guest_plus"),

    @<PERSON><PERSON>(name = "full_user")
    FULL_USER("full_user"),

    @<PERSON><PERSON>(name = "vip")
    VIP("vip")
}
