package co.thewordlab.luzia.foundation.networking.model.environments

enum class DevEnvironment(val baseUrl: String) {
    ANTONIA("https://antonia.staging.thewordlab.net/api/v4.0/"),
    MATIAS("https://matias.staging.thewordlab.net/api/v4.0/"),
    EUGENIA("https://eugenia.staging.thewordlab.net/api/v4.0/");

    companion object {
        fun fromString(value: String?): DevEnvironment = when (value) {
            ANTONIA.name -> ANTONIA
            MATIAS.name -> MATIAS
            EUGENIA.name -> EUGENIA
            else -> ANTONIA
        }
    }
}
