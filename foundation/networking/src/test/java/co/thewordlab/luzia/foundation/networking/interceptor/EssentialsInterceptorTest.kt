package co.thewordlab.luzia.foundation.networking.interceptor

import android.content.Context
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import co.thewordlab.luzia.foundation.networking.device.DeviceManagement
import co.thewordlab.luzia.foundation.networking.interceptor.language.LanguageHandler
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import okhttp3.Interceptor
import okhttp3.Request
import kotlin.test.Test

class EssentialsInterceptorTest {

    private val context: Context = mockk()

    private val deviceManagement: DeviceManagement = mockk()

    private val languageHandler: LanguageHandler = mockk()

    private val interceptor = EssentialsInterceptor(
        context = context,
        languageHandler = languageHandler,
        deviceManagement = deviceManagement
    )

    @Test
    fun `intercepted header values are correct`() {
        mockContext()
        val builder: Request.Builder = mockk(relaxed = true)
        val chain: Interceptor.Chain = mockk(relaxed = true)
        val request: Request = mockk()
        every { deviceManagement.getAdvertisingId() } returns AD_ID
        every { languageHandler.languageAndCountryCode() } returns LANGUAGE
        every { chain.request() } returns request
        every { request.newBuilder() } returns builder

        interceptor.intercept(chain)

        verify {
            builder.header(Headers.ACCEPT_LANGUAGE, LANGUAGE)
        }
    }

    private fun mockContext() {
        val info: PackageInfo = mockk()
        val manager: PackageManager = mockk()
        every { context.packageName } returns "package"
        every { manager.getPackageInfo("package", 0) } returns info
        every { context.packageManager } returns manager
    }

    private companion object {
        const val LANGUAGE = "es-ES"
        const val AD_ID = "adId"
    }
}
