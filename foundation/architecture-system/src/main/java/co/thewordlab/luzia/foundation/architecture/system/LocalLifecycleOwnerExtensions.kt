package co.thewordlab.luzia.foundation.architecture.system

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.navigation.NavBackStackEntry

/**
 * This lambda will be invoked after the LifecycleOwner's onCreate method returns.
 *
 * @param onCreateAction the lambda to be invoked
 * @param lifecycleOwner of which lifecycle will be listened
 * @param key used to prevent multiple invocations for OnCreate
 * @see LocalLifecycleOwner
 * @see LifecycleOwner
 * @see DefaultLifecycleObserver
 * @see DefaultLifecycleObserver.onCreate
 */
@Composable
fun OnCreate(
    screenName: String,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
    onCreateAction: () -> Unit
) {
    val currentAction by rememberUpdatedState(onCreateAction)

    DisposableEffect(lifecycleOwner) {
        val entry = lifecycleOwner as? NavBackStackEntry
        val observer = object : DefaultLifecycleObserver {
            override fun onCreate(owner: LifecycleOwner) {
                // Use process PID as session identifier to handle process death
                val processId = android.os.Process.myPid()
                val sessionKey = "${processId}_$screenName"

                if (entry?.savedStateHandle?.get<Boolean>(sessionKey) != true) {
                    currentAction()
                    entry?.savedStateHandle?.set(sessionKey, true)
                }
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}

/**
 * This lambda will be invoked after the LifecycleOwner's onStart method returns.
 *
 * @param onStartAction the lambda to be invoked
 * @see LocalLifecycleOwner
 * @see LifecycleOwner
 * @see DefaultLifecycleObserver
 * @see DefaultLifecycleObserver.onCreate
 */
@Composable
fun OnStart(
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
    onStartAction: () -> Unit
) {
    val currentAction by rememberUpdatedState(onStartAction)

    DisposableEffect(lifecycleOwner) {
        val observer = object : DefaultLifecycleObserver {
            override fun onStart(owner: LifecycleOwner) {
                currentAction()
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}

/**
 * This lambda will be invoked after the LifecycleOwner's onResume method returns.
 *
 * @param onResumeAction the lambda to be invoked
 * @see LocalLifecycleOwner
 * @see LifecycleOwner
 * @see DefaultLifecycleObserver
 * @see DefaultLifecycleObserver.onCreate
 */
@Composable
fun OnResume(
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
    onResumeAction: () -> Unit
) {
    val currentAction by rememberUpdatedState(onResumeAction)

    DisposableEffect(lifecycleOwner) {
        val observer = object : DefaultLifecycleObserver {
            override fun onResume(owner: LifecycleOwner) {
                currentAction()
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}

/**
 * This lambda will be invoked after the LifecycleOwner's onPause method returns.
 *
 * @param onPauseAction the lambda to be invoked
 * @see LocalLifecycleOwner
 * @see LifecycleOwner
 * @see DefaultLifecycleObserver
 * @see DefaultLifecycleObserver.onCreate
 */
@Composable
fun OnPause(
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
    onPauseAction: () -> Unit
) {
    val currentAction by rememberUpdatedState(onPauseAction)

    DisposableEffect(lifecycleOwner) {
        val observer = object : DefaultLifecycleObserver {
            override fun onPause(owner: LifecycleOwner) {
                currentAction()
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}

/**
 * This lambda will be invoked after the LifecycleOwner's onStop method returns.
 *
 * @param onStopAction the lambda to be invoked
 * @see LocalLifecycleOwner
 * @see LifecycleOwner
 * @see DefaultLifecycleObserver
 * @see DefaultLifecycleObserver.onCreate
 */
@Composable
fun OnStop(lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current, onStopAction: () -> Unit) {
    val currentAction by rememberUpdatedState(onStopAction)

    DisposableEffect(lifecycleOwner) {
        val observer = object : DefaultLifecycleObserver {
            override fun onStop(owner: LifecycleOwner) {
                currentAction()
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}

/**
 * This lambda will be invoked after the LifecycleOwner's onDestroy method returns.
 *
 * @param onDestroyAction the lambda to be invoked
 * @see LocalLifecycleOwner
 * @see LifecycleOwner
 * @see DefaultLifecycleObserver
 * @see DefaultLifecycleObserver.onCreate
 */
@Composable
fun OnDestroy(
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
    onDestroyAction: () -> Unit
) {
    val currentAction by rememberUpdatedState(onDestroyAction)

    DisposableEffect(lifecycleOwner) {
        val observer = object : DefaultLifecycleObserver {
            override fun onDestroy(owner: LifecycleOwner) {
                currentAction()
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}
