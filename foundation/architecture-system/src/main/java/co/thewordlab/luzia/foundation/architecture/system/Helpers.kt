package co.thewordlab.luzia.foundation.architecture.system

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.flowWithLifecycle
import kotlinx.coroutines.flow.Flow

@Composable
fun <VE : ViewEvent> ViewModelEventEffect(events: ViewModelEvents<VE>, onEvent: suspend (VE) -> Unit) {
    events.viewEvent.FlowLifecycleEffect { event ->
        events.consumeEvent(event) { onEvent(it) }
    }
}

@Composable
private fun <T> Flow<T>.FlowLifecycleEffect(block: suspend (event: T) -> Unit) {
    val lifecycleAwareFlow = rememberFlowWithLifecycle(flow = this)
    LaunchedEffect(Unit) {
        lifecycleAwareFlow.collect(collector = block)
    }
}

@Composable
private fun <T> rememberFlowWithLifecycle(
    flow: Flow<T>,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current
): Flow<T> = remember(
    key1 = flow,
    key2 = lifecycleOwner
) { flow.flowWithLifecycle(lifecycleOwner.lifecycle, Lifecycle.State.STARTED) }
