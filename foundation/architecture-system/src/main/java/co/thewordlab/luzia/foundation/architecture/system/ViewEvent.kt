package co.thewordlab.luzia.foundation.architecture.system

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.update

interface ViewEvent

interface ViewModelEvents<VE : ViewEvent> {

    val viewEvent: Flow<VE>

    suspend fun consumeEvent(event: VE, eventToConsumeCallback: suspend (VE) -> Unit)

    fun sendEvent(event: VE)
}

class ViewModelEventsImpl<VE : ViewEvent> : ViewModelEvents<VE> {

    private val mutableViewEvents: MutableStateFlow<List<VE>> = MutableStateFlow(listOf())
    override val viewEvent: Flow<VE>
        get() = mutableViewEvents.mapNotNull { events -> events.firstOrNull() }

    override suspend fun consumeEvent(event: VE, eventToConsumeCallback: suspend (VE) -> Unit) {
        with(event) {
            eventToConsumeCallback(this)
            removeEvent(this)
        }
    }

    override fun sendEvent(event: VE) {
        mutableViewEvents.update { it.plus(event) }
    }

    private fun removeEvent(event: VE) {
        mutableViewEvents.update { it.minus(event) }
    }
}
