package co.thewordlab.luzia.foundation.messages.data

import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.fouundation.persistence.chat.MessageType
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

class InMemoryChatDataSourceTest {

    private lateinit var dataSource: InMemoryChatDataSource
    private val personalityId = "test_personality"
    private val testMessage = MessageEntity(
        messageId = 1L,
        personalityId = personalityId,
        text = "Test message",
        fileName = null,
        isAi = false,
        isRead = true,
        isLoading = false,
        messageType = MessageType.Text,
        error = null,
        timeStamp = System.currentTimeMillis(),
        feedbackId = null,
        proactivePayload = null,
        requestId = null,
        deleted = false,
        isFavorite = false,
        maxPromptLength = null,
        contentInjection = null,
        webSearchData = null,
        webViewData = null
    )

    @Before
    fun setup() {
        dataSource = InMemoryChatDataSource()
    }

    @Test
    fun `insertMessage should add message to the list`() = runTest {
        val messageId = dataSource.insertMessage(testMessage)

        assertEquals(1L, messageId)

        val pagingSource = dataSource.getMessagesPaginated(personalityId)
        assertNotNull(pagingSource)
    }

    @Test
    fun `insertMessage should update existing message if messageId matches`() = runTest {
        // Insert original message
        dataSource.insertMessage(testMessage)

        // Create updated message with same ID but different text
        val updatedMessage = testMessage.copy(text = "Updated message")
        dataSource.insertMessage(updatedMessage)

        // Get messages and verify the text was updated
        val messages = dataSource.getLastMessagesWithoutError(personalityId, 10)
        assertEquals(1, messages.size)
        assertEquals("Updated message", messages[0].text)
    }

    @Test
    fun `readAllMessages should mark all messages as read`() = runTest {
        // Insert unread message
        val unreadMessage = testMessage.copy(isRead = false)
        dataSource.insertMessage(unreadMessage)

        // Mark all as read
        dataSource.readAllMessages(personalityId)

        // Verify message is now read
        val messages = dataSource.getLastMessagesWithoutError(personalityId, 10)
        assertEquals(1, messages.size)
        assertTrue(messages[0].isRead)
    }

    @Test
    fun `getUnreadMessageCount should return correct count`() = runTest {
        // Insert unread message
        val unreadMessage = testMessage.copy(isRead = false)
        dataSource.insertMessage(unreadMessage)

        // Check unread count
        val count = dataSource.getUnreadMessageCount(personalityId).first()
        assertEquals(1L, count)

        // Mark all as read
        dataSource.readAllMessages(personalityId)

        // Check unread count again
        val updatedCount = dataSource.getUnreadMessageCount(personalityId).first()
        assertEquals(0L, updatedCount)
    }

    @Test
    fun `findLastAiMessage should return the last AI message`() = runTest {
        // Insert user message
        dataSource.insertMessage(testMessage)

        // Insert AI message
        val aiMessage = testMessage.copy(messageId = 2L, isAi = true)
        dataSource.insertMessage(aiMessage)

        // Find last AI message
        val lastAiMessage = dataSource.findLastAiMessage(personalityId)
        assertNotNull(lastAiMessage)
        assertEquals(2L, lastAiMessage?.messageId)
        assertTrue(lastAiMessage?.isAi == true)
    }

    @Test
    fun `deleteFailedUserMessagesAfter should remove failed user messages after timestamp`() = runTest {
        // Insert normal message
        dataSource.insertMessage(testMessage)

        // Insert failed message after timestamp
        val failedMessage = testMessage.copy(
            messageId = 2L,
            timeStamp = testMessage.timeStamp + 1000,
            error = "error_code"
        )
        dataSource.insertMessage(failedMessage)

        // Delete failed messages
        dataSource.deleteFailedUserMessagesAfter(personalityId, testMessage.timeStamp)

        // Verify failed message was deleted
        val messages = dataSource.getLastMessagesWithoutError(personalityId, 10)
        assertEquals(1, messages.size)
        assertEquals(1L, messages[0].messageId)
    }

    @Test
    fun `deleteLoadingUserMessagesAfter should remove loading user messages in time range`() = runTest {
        // Insert normal message
        dataSource.insertMessage(testMessage)

        val baseTime = testMessage.timeStamp

        // Insert loading message in time range
        val loadingMessage = testMessage.copy(
            messageId = 2L,
            timeStamp = baseTime + 500,
            isLoading = true
        )
        dataSource.insertMessage(loadingMessage)

        // Delete loading messages
        dataSource.deleteLoadingUserMessagesAfter(personalityId, baseTime, baseTime + 1000)

        // Verify loading message was deleted
        val messages = dataSource.getLastMessagesWithoutError(personalityId, 10)
        assertEquals(1, messages.size)
        assertEquals(1L, messages[0].messageId)
    }

    @Test
    fun `clearMessages should remove all messages for personality`() = runTest {
        // Insert message
        dataSource.insertMessage(testMessage)

        // Clear messages
        dataSource.clearMessages(personalityId)

        // Verify no messages remain
        val messages = dataSource.getLastMessagesWithoutError(personalityId, 10)
        assertTrue(messages.isEmpty())
    }

    @Test
    fun `getMessageById should return message with matching ID`() = runTest {
        // Insert message
        dataSource.insertMessage(testMessage)

        // Get message by ID
        val message = dataSource.getMessageById(1L)
        assertNotNull(message)
        assertEquals(1L, message?.messageId)

        // Try to get non-existent message
        val nonExistentMessage = dataSource.getMessageById(999L)
        assertNull(nonExistentMessage)
    }

    @Test
    fun `searchMessages should return messages containing query`() = runTest {
        // Insert message with specific text
        val message1 = testMessage.copy(text = "Hello world")
        dataSource.insertMessage(message1)

        // Insert another message
        val message2 = testMessage.copy(messageId = 2L, text = "Goodbye")
        dataSource.insertMessage(message2)

        // Search for messages containing "world"
        val results = dataSource.searchMessages(personalityId, "world")
        assertEquals(1, results.size)
        assertEquals("Hello world", results[0].text)

        // Search for messages containing "o" (should match both)
        val results2 = dataSource.searchMessages(personalityId, "o")
        assertEquals(2, results2.size)
    }

    @Test
    fun `countAllMessages should return correct count`() = runTest {
        // Insert message
        dataSource.insertMessage(testMessage)

        // Insert message for different personality
        val otherMessage = testMessage.copy(messageId = 2L, personalityId = "other_personality")
        dataSource.insertMessage(otherMessage)

        // Count all messages
        val totalCount = dataSource.countAllMessages(null)
        assertEquals(2, totalCount)

        // Count messages for specific personality
        val personalityCount = dataSource.countAllMessages(personalityId)
        assertEquals(1, personalityCount)
    }

    @Test
    fun `getChatMetadata should return null when no messages exist`() = runTest {
        // When no messages exist
        val metadata = dataSource.getChatMetadata(personalityId).first()

        // Then metadata should be null
        assertNull(metadata)
    }

    @Test
    fun `getChatMetadata should return correct metadata when messages exist`() = runTest {
        // Given messages exist
        val message1 = testMessage.copy(messageId = 1L, isAi = false)
        val message2 = testMessage.copy(messageId = 2L, isAi = true)
        dataSource.insertMessage(message1)
        dataSource.insertMessage(message2)

        // When getting metadata
        val metadata = dataSource.getChatMetadata(personalityId).first()

        // Then metadata should contain correct information
        assertNotNull(metadata)
        assertEquals(2L, metadata?.lastMessageId)
        assertTrue(metadata?.lastMessageIsAi == true)
        assertEquals(2L, metadata?.messageCount)
    }
}
