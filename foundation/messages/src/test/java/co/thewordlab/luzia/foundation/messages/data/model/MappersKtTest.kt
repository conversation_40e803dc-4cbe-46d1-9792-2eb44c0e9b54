package co.thewordlab.luzia.foundation.messages.data.model

import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.fouundation.persistence.chat.MessageType
import junit.framework.TestCase.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Test
import java.util.Date

class MappersKtTest {

    @Test
    fun `test math expression with markdown is marked as latex view with two new lines`() {
        val isLatex = """
            1. **Quadratic Equation**
               \[
               ax^2 + bx + c = 0 \quad \text{where } a = 2, b = 5, c = -3
               \]
               The solutions can be found using the quadratic formula:
               \[
               x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}
               \]

            ### Supermarket List

            - **Fruits & Vegetables**
              - Apples (6)
              - Bananas (1 bunch)
              - Spinach (1 bag)
              - Carrots (500g)
        """.trimIndent().isLatexText()
        assertTrue(isLatex)
    }

    @Test
    fun `test math expression with markdown is marked as latex view`() {
        val isLatex = """
            Ejemplo de fracciones	Claro, aquí tienes un ejemplo de fracciones:
            \[
            \frac{3}{4} + \frac{2}{5} = \frac{15}{20} + \frac{8}{20} = \frac{23}{20}
            \]
            ¿Quieres que te ayude con operaciones con fracciones?
            Ponme un ejemplo de integral	Claro, aquí tienes un ejemplo sencillo:
            \[
            \int x^2 \, dx = \frac{x^3}{3} + C
            \]
            ¿Quieres que te explique cómo resolverla?
        """.trimIndent().isLatexText()
        assertTrue(isLatex)
    }

    @Test
    fun `test math expression with markdown is marked as latex view with one new lines at end`() {
        val isLatex = """
            2. **Exponential Growth**
               \[ P(t) = P_0 e^{rt} \quad \text{where } P_0 = 1000, r = 0.05, t = 10
               \]
               This represents the population growth over time.

            ### Supermarket List
            - **Dairy**
              - Milk (1 liter)
              - Yogurt (4 cups)
              - Cheddar cheese (200g)
        """.trimIndent().isLatexText()
        assertTrue(isLatex)
    }

    @Test
    fun `test math expression with markdown is marked as latex view with one new lines at start`() {
        val isLatex = """
            4. **Newton's Second Law**
               \[
               F = ma + m\frac{dv}{dt} \quad \text{where } F = 50\,N, m = 10\,kg, a = ?\]
               This equation helps calculate acceleration based on force and mass.
            ### Supermarket List
            - **Grains**
              - Brown rice (1kg)
              - Whole grain bread (1 loaf)
        """.trimIndent().isLatexText()
        assertTrue(isLatex)
    }

    @Test
    fun `GIVEN text WHEN map as user message THEN returns a valid entity`() {
        val expected = MessageEntity(
            messageId = 0L,
            text = "luzia test",
            personalityId = "LuzIA",
            isAi = false,
            isRead = true,
            isLoading = false,
            messageType = MessageType.Text,
            error = null,
            fileName = "",
            timeStamp = Date().time,
            feedbackId = null,
            proactivePayload = null,
            requestId = null,
            deleted = false,
            isFavorite = false,
            maxPromptLength = null,
            contentInjection = null,
            webSearchData = null,
            webViewData = null
        )
        val message = "luzia test"

        val sut = message.asUserMessage(personalityId = "LuzIA")

        assertEquals(expected.text, sut.text)
        assertEquals(expected.personalityId, sut.personalityId)
        assertEquals(expected.messageType, sut.messageType)
    }

    @Test
    fun `GIVEN image text WHEN map as AI message THEN returns AI image entity with extracted URL`() {
        val imageMarkdown = "![image](https://example.com/generated_image.png)"
        val personalityId = "LuzIA"

        val sut = imageMarkdown.asAiMessage(personalityId = personalityId)

        assertEquals("https://example.com/generated_image.png", sut.fileName)
        assertEquals("", sut.text)
        assertEquals(personalityId, sut.personalityId)
        assertEquals(MessageType.Image, sut.messageType)
        assertEquals(true, sut.isAi)
        assertEquals(true, sut.isRead)
        assertEquals(false, sut.isLoading)
        assertEquals(null, sut.error)
    }

    @Test
    fun `GIVEN image text broken WHEN map as AI message THEN returns AI text entity`() {
        val imageMarkdown = "![image](https://example.com/generated_image.png"
        val personalityId = "LuzIA"

        val sut = imageMarkdown.asAiMessage(personalityId = personalityId)

        assertEquals(imageMarkdown, sut.text)
        assertEquals(null, sut.fileName)
        assertEquals(personalityId, sut.personalityId)
        assertEquals(MessageType.Text, sut.messageType)
        assertEquals(true, sut.isAi)
        assertEquals(true, sut.isRead)
        assertEquals(false, sut.isLoading)
        assertEquals(null, sut.error)
    }

    @Test
    fun `GIVEN markdown WHEN map as AI message THEN returns AI text entity`() {
        val personalityId = "LuzIA"

        val sut1 = "Text before ![image](https://example.com/generated_image.png)"
            .asAiMessage(personalityId = personalityId)
        val sut2 = "![image](https://example.com/generated_image.png) extra"
            .asAiMessage(personalityId = personalityId)
        val sut3 = "![image](https://example.com/generated_image.png"
            .asAiMessage(personalityId = personalityId)
        val sut4 = "![image](https://example.com/generated_image.png)"
            .asAiMessage(personalityId = personalityId)

        assertEquals(MessageType.Text, sut1.messageType)
        assertEquals(MessageType.Text, sut2.messageType)
        assertEquals(MessageType.Text, sut3.messageType)
        assertEquals(MessageType.Image, sut4.messageType)
    }

    @Test
    fun `GIVEN regular text WHEN map as AI message THEN returns AI text entity`() {
        val text = "This is a regular text response"
        val personalityId = "LuzIA"

        val sut = text.asAiMessage(personalityId = personalityId)

        assertEquals(text, sut.text)
        assertEquals(null, sut.fileName)
        assertEquals(personalityId, sut.personalityId)
        assertEquals(MessageType.Text, sut.messageType)
        assertEquals(true, sut.isAi)
        assertEquals(true, sut.isRead)
        assertEquals(false, sut.isLoading)
        assertEquals(null, sut.error)
    }

    @Test
    fun `GIVEN math latex text WHEN map as AI message THEN returns AI math entity`() {
        val mathText = "The equation is \\[x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}\\]"
        val personalityId = "MATH_AI"

        val sut = mathText.asAiMessage(personalityId = personalityId)

        assertEquals(mathText, sut.text)
        assertEquals(null, sut.fileName)
        assertEquals(personalityId, sut.personalityId)
        assertEquals(MessageType.Math, sut.messageType)
        assertEquals(true, sut.isAi)
    }

    @Test
    fun `GIVEN AI message with web search data WHEN map as AI message THEN includes web search data`() {
        val text = "Search results show that..."
        val personalityId = "SEARCH_AI"
        val webSearchData = "{\"query\": \"test\", \"results\": []}"

        val sut = text.asAiMessage(personalityId = personalityId, webSearchPayload = webSearchData)

        assertEquals(text, sut.text)
        assertEquals(personalityId, sut.personalityId)
        assertEquals(webSearchData, sut.webSearchData)
        assertEquals(MessageType.Text, sut.messageType)
        assertEquals(true, sut.isAi)
    }
}
