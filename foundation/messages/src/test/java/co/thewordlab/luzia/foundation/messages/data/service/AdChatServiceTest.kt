package co.thewordlab.luzia.foundation.messages.data.service

import co.thewordlab.fouundation.persistence.chat.MessageType
import co.thewordlab.luzia.foundation.messages.data.mapper.AdContentMapper
import co.thewordlab.luzia.foundation.messages.data.model.AdContentDto
import co.thewordlab.luzia.foundation.messages.domain.model.AdContent
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Test

class AdChatServiceTest {

    private val adContentMapper = mockk<AdContentMapper>(relaxed = true)
    private val adChatService = AdChatService(adContentMapper)

    @Test
    fun `GIVEN ad content with headline WHEN formatMessageWithAdContent THEN returns formatted message`() {
        // GIVEN
        val userText = "Tell me about smartphones"
        val adContent = AdContent(
            headline = "Latest iPhone",
            body = "Amazing features and performance",
            advertiser = "Apple Inc",
            callToAction = "Buy now",
            icon = "icon_url",
            store = "App Store"
        )

        // WHEN
        val result = adChatService.formatMessageWithAdContent(userText, adContent)

        // THEN
        val expected = """
            Tell me about smartphones
            [AD_CONTENT_START]
            Advertiser: Apple Inc
            Title: Latest iPhone
            Body: Amazing features and performance
            [AD_CONTENT_END]
        """.trimIndent()
        assertEquals(expected, result)
    }

    @Test
    fun `GIVEN ad content with null headline WHEN formatMessageWithAdContent THEN returns original user text`() {
        // GIVEN
        val userText = "Tell me about smartphones"
        val adContent = AdContent(
            headline = null,
            body = "Amazing features",
            advertiser = "Apple Inc",
            callToAction = "Buy now",
            icon = "icon_url",
            store = "App Store"
        )

        // WHEN
        val result = adChatService.formatMessageWithAdContent(userText, adContent)

        // THEN
        assertEquals(userText, result)
    }

    @Test
    fun `GIVEN null ad content WHEN formatMessageWithAdContent THEN returns original user text`() {
        // GIVEN
        val userText = "Tell me about smartphones"

        // WHEN
        val result = adChatService.formatMessageWithAdContent(userText, null)

        // THEN
        assertEquals(userText, result)
    }

    @Test
    fun `GIVEN has ad content is true WHEN getTargetPersonalityId THEN returns luzia ads personality id`() {
        // GIVEN
        val defaultPersonalityId = "default_personality"

        // WHEN
        val result = adChatService.getTargetPersonalityId(hasAdContent = true, defaultPersonalityId)

        // THEN
        assertEquals(AdChatService.LUZIA_ADS_ID_PERSONALITY, result)
    }

    @Test
    fun `GIVEN has ad content is false WHEN getTargetPersonalityId THEN returns default personality id`() {
        // GIVEN
        val defaultPersonalityId = "default_personality"

        // WHEN
        val result =
            adChatService.getTargetPersonalityId(hasAdContent = false, defaultPersonalityId)

        // THEN
        assertEquals(defaultPersonalityId, result)
    }

    @Test
    fun `GIVEN valid json content WHEN parseAdContent THEN returns response and description pair`() {
        // GIVEN
        val content = """{"response": "test response", "description": "test description"}"""
        val adContentDto = AdContentDto(
            response = "test response",
            description = "test description"
        )
        every { adContentMapper.fromJson(content) } returns adContentDto

        // WHEN
        val result = adChatService.parseAdContent(content)

        // THEN
        assertEquals("test response", result.first)
        assertEquals("test description", result.second)
        verify { adContentMapper.fromJson(content) }
    }

    @Test
    fun `GIVEN invalid json content WHEN parseAdContent THEN returns null pair`() {
        // GIVEN
        val content = "invalid json"
        every { adContentMapper.fromJson(content) } returns null

        // WHEN
        val result = adChatService.parseAdContent(content)

        // THEN
        assertEquals(null, result.first)
        assertEquals(null, result.second)
        verify { adContentMapper.fromJson(content) }
    }

    @Test
    fun `GIVEN personality id WHEN createAdSignalMessage THEN returns ad message entity with correct properties`() {
        // GIVEN
        val personalityId = "test_personality"

        // WHEN
        val result = adChatService.createAdSignalMessage(personalityId)

        // THEN
        assertEquals(-1L, result.messageId)
        assertEquals(personalityId, result.personalityId)
        assertEquals("", result.text)
        assertEquals(null, result.fileName)
        assertTrue(result.isAi)
        assertTrue(result.isRead)
        assertFalse(result.isLoading)
        assertEquals(MessageType.Ad, result.messageType)
        assertEquals(null, result.error)
        assertTrue(result.timeStamp > 0)
        assertEquals(null, result.feedbackId)
        assertEquals(null, result.proactivePayload)
        assertEquals(null, result.requestId)
        assertFalse(result.deleted)
        assertFalse(result.isFavorite)
        assertEquals(null, result.maxPromptLength)
        assertEquals(null, result.contentInjection)
        assertEquals(null, result.webSearchData)
    }

    @Test
    fun `GIVEN ad content with null advertiser WHEN formatMessageWithAdContent THEN handles null gracefully`() {
        // GIVEN
        val userText = "Tell me about smartphones"
        val adContent = AdContent(
            headline = "Latest iPhone",
            body = "Amazing features and performance",
            advertiser = null,
            callToAction = "Buy now",
            icon = "icon_url",
            store = "App Store"
        )

        // WHEN
        val result = adChatService.formatMessageWithAdContent(userText, adContent)

        // THEN
        val expected = """
            Tell me about smartphones
            [AD_CONTENT_START]
            Advertiser: null
            Title: Latest iPhone
            Body: Amazing features and performance
            [AD_CONTENT_END]
        """.trimIndent()
        assertEquals(expected, result)
    }

    @Test
    fun `GIVEN ad content with empty body WHEN formatMessageWithAdContent THEN includes empty body`() {
        // GIVEN
        val userText = "Tell me about smartphones"
        val adContent = AdContent(
            headline = "Latest iPhone",
            body = "",
            advertiser = "Apple Inc",
            callToAction = "Buy now",
            icon = "icon_url",
            store = "App Store"
        )

        // WHEN
        val result = adChatService.formatMessageWithAdContent(userText, adContent)

        // THEN
        val expected = """
            Tell me about smartphones
            [AD_CONTENT_START]
            Advertiser: Apple Inc
            Title: Latest iPhone
            Body: 
            [AD_CONTENT_END]
        """.trimIndent()
        assertEquals(expected, result)
    }

    @Test
    fun `GIVEN ad content dto with only response WHEN parseAdContent THEN returns response and null description`() {
        // GIVEN
        val content = """{"response": "only response"}"""
        val adContentDto = AdContentDto(
            response = "only response",
            description = null
        )
        every { adContentMapper.fromJson(content) } returns adContentDto

        // WHEN
        val result = adChatService.parseAdContent(content)

        // THEN
        assertEquals("only response", result.first)
        assertEquals(null, result.second)
    }

    @Test
    fun `GIVEN ad content dto with only description WHEN parseAdContent THEN returns null response and description`() {
        // GIVEN
        val content = """{"description": "only description"}"""
        val adContentDto = AdContentDto(
            response = null,
            description = "only description"
        )
        every { adContentMapper.fromJson(content) } returns adContentDto

        // WHEN
        val result = adChatService.parseAdContent(content)

        // THEN
        assertEquals(null, result.first)
        assertEquals("only description", result.second)
    }
}
