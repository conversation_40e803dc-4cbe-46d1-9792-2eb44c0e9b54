package co.thewordlab.luzia.foundation.messages.data.mapper

import co.thewordlab.luzia.foundation.messages.data.model.WebViewDataDto
import co.thewordlab.luzia.foundation.messages.domain.model.WebviewData
import com.squareup.moshi.Moshi
import javax.inject.Inject

class WebviewDataMapper @Inject constructor(
    private val moshi: <PERSON><PERSON>
) {

    fun mapToDomain(json: String): WebviewData? {
        return runCatching {
            val jsonAdapter = moshi.adapter(WebviewData::class.java)
            return jsonAdapter.fromJson(json)
        }.getOrNull()
    }

    fun mapToString(dto: WebViewDataDto): String? {
        return runCatching {
            val jsonAdapter = moshi.adapter(WebviewData::class.java)
            val domain = WebviewData(
                title = dto.title,
                html = dto.html
            )
            return jsonAdapter.toJson(domain)
        }.getOrNull()
    }
}
