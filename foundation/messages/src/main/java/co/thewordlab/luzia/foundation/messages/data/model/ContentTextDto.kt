package co.thewordlab.luzia.foundation.messages.data.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class ContentTextDto(
    @Json(name = "content")
    val content: String,
    @Json(name = "context")
    val context: String? = null,
    @Json(name = "metadata")
    val metadata: ContentMetadataDto,
    @<PERSON><PERSON>(name = "attachmentId")
    val attachmentId: String? = null,
    @<PERSON><PERSON>(name = "webSearchDisplayData")
    val webSearchData: WebSearchDataDto? = null,
    @<PERSON>son(name = "webview")
    val webviewData: WebViewDataDto? = null
)

@JsonClass(generateAdapter = true)
data class WebSearchDataDto(
    @Json(name = "items")
    val items: List<WebSearchSourceDto> = emptyList()
)

@JsonClass(generateAdapter = true)
data class WebViewDataDto(
    @Json(name = "title")
    val title: String = "",
    @<PERSON><PERSON>(name = "html")
    val html: String = ""
)

@JsonClass(generateAdapter = true)
data class WebSearchSourceDto(
    @Json(name = "title")
    val title: String? = null,
    @Json(name = "snippet")
    val snippet: String? = null,
    @Json(name = "link")
    val link: String? = null,
    @Json(name = "favicon")
    val favicon: String? = null,
)

@JsonClass(generateAdapter = true)
data class AdContentDto(
    @Json(name = "response")
    val response: String? = null,
    @Json(name = "description")
    val description: String? = null,
    @Json(name = "advertiser")
    val advertiser: String? = null,
    @Json(name = "title")
    val title: String? = null,
    @Json(name = "body")
    val body: String? = null,
    @Json(name = "confidence")
    val confidence: String? = null
)

@JsonClass(generateAdapter = true)
data class ContentMetadataDto(
    @Json(name = "requestId")
    val requestId: String,
    @Json(name = "maxPromptLength")
    val maxPromptLength: Int? = null
)
