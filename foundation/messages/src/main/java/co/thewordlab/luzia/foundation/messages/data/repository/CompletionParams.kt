package co.thewordlab.luzia.foundation.messages.data.repository

import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.luzia.foundation.messages.data.model.CompletionContextMessage

data class CompletionParams(
    val userMessage: MessageEntity,
    val contextMessages: List<CompletionContextMessage>,
    val responseStyleId: String?,
    val personalityId: String,
    val content: String,
    val isWebSearch: <PERSON>olean
)
