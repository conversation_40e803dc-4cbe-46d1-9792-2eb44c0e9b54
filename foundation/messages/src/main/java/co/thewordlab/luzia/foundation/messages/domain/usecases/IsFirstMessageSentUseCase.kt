package co.thewordlab.luzia.foundation.messages.domain.usecases

import androidx.datastore.preferences.core.booleanPreferencesKey
import co.thewordlab.fouundation.persistence.LuziaDataStore
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.analytics.helpers.parseValues
import co.thewordlab.luzia.foundation.analytics.providers.BrazeAnalyticsProvider
import co.thewordlab.luzia.foundation.messages.domain.repository.ChatRepository
import javax.inject.Inject

class IsFirstMessageSentUseCase @Inject constructor(
    private val analytics: BrazeAnalyticsProvider,
    private val dataStore: LuziaDataStore,
    private val chatRepository: ChatRepository
) {

    suspend operator fun invoke(): Boolean {
        val isFirstMessageSent = dataStore.getData(firstMessageSent)
        if (isFirstMessageSent != true) {
            val existMessages = chatRepository.countAllMessages() > 0
            dataStore.saveData(firstMessageSent, existMessages)
            analytics.setUserProperties(
                mapOf(Parameter.FirstMessageSent to parseValues(existMessages))
            )
            return existMessages
        }
        return true
    }

    private companion object {
        const val FIRST_MESSAGE_SENT = "first_message_sent"
        val firstMessageSent = booleanPreferencesKey(FIRST_MESSAGE_SENT)
    }
}
