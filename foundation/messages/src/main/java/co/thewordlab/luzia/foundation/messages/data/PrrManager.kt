package co.thewordlab.luzia.foundation.messages.data

import androidx.datastore.preferences.core.longPreferencesKey
import co.thewordlab.fouundation.persistence.LuziaDataStore
import co.thewordlab.fouundation.persistence.chat.ChatDataSource
import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.luzia.foundation.messages.data.model.ContentMetadataDto
import java.util.Calendar
import kotlin.random.Random

class PrrManager(
    private val chatDataSource: ChatDataSource,
    private val luziaDataStore: LuziaDataStore
) {

    private fun shouldNotSkipPrrLogic(message: MessageEntity): Boolean =
        launchProcessOn20Percent() && message.text.length > PRR_MESSAGE_MIN_TEXT_LENGTH

    private fun launchProcessOn20Percent() = Random.nextInt(1, MAX_NUMBER_FOR_RANDOM) == 1

    suspend fun enrichWithFeedback(
        userMessage: MessageEntity,
        responseMessage: MessageEntity,
        metadata: ContentMetadataDto
    ): MessageEntity {
        if (!shouldNotSkipPrrLogic(userMessage)) return responseMessage
        val lastTimePrr = luziaDataStore.getData(keyLastTimePrr)
        return if (shouldShowPrr(lastTimePrr)) {
            luziaDataStore.saveData(keyLastTimePrr, responseMessage.timeStamp)
            responseMessage.copy(feedbackId = metadata.requestId)
        } else {
            responseMessage
        }
    }

    suspend fun deleteExpiredPrrFeedbacks() {
        val timeStamp = Calendar.getInstance().apply {
            add(Calendar.HOUR_OF_DAY, -PRR_MESSAGE_HOURS_THRESHOLD)
        }.timeInMillis
        chatDataSource.deleteFeedbackBefore(timeStamp)
    }

    suspend fun deletePrrFeedbacks(feedbackId: String) {
        chatDataSource.deleteFeedback(feedbackId)
    }

    private fun shouldShowPrr(timestamp: Long?): Boolean {
        if (timestamp == null) return true
        val lastTimePrrShown = Calendar.getInstance().apply {
            timeInMillis = timestamp
        }
        val thresholdDate = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_MONTH, -PRR_MESSAGE_DAYS_THRESHOLD)
        }
        return lastTimePrrShown.before(thresholdDate)
    }

    private companion object {
        const val MAX_NUMBER_FOR_RANDOM = 6
        const val PRR_MESSAGE_MIN_TEXT_LENGTH = 5
        const val PRR_MESSAGE_HOURS_THRESHOLD = 6
        const val PRR_MESSAGE_DAYS_THRESHOLD = 15
        const val KEY_LAST_PRR_TIMESTAMP = "timeStampLastTimePrr"
        val keyLastTimePrr = longPreferencesKey(KEY_LAST_PRR_TIMESTAMP)
    }
}
