package co.thewordlab.luzia.foundation.messages.data.mapper

import android.util.Log
import co.thewordlab.luzia.foundation.messages.data.model.AdContentDto
import com.squareup.moshi.JsonDataException
import com.squareup.moshi.JsonEncodingException
import com.squareup.moshi.Moshi
import java.io.IOException
import javax.inject.Inject

private const val TAG = "AdContentMapper"

class AdContentMapper @Inject constructor(
    private val moshi: <PERSON>shi
) {
    fun fromJson(json: String): AdContentDto? {
        return try {
            moshi.adapter(AdContentDto::class.java).fromJson(json)
        } catch (e: JsonDataException) {
            Log.e(TAG, "Failed to parse AdContent from JSON: ${e.message}")
            null
        } catch (e: JsonEncodingException) {
            Log.e(TAG, "JSON encoding error for AdContent: ${e.message}")
            null
        } catch (e: IOException) {
            Log.e(TAG, "IO error while parsing AdContent: ${e.message}")
            null
        }
    }

    fun toJson(adContent: AdContentDto): String? {
        return try {
            moshi.adapter(AdContentDto::class.java).toJson(adContent)
        } catch (e: JsonDataException) {
            Log.e(TAG, "Failed to convert AdContent to JSON: ${e.message}")
            null
        } catch (e: JsonEncodingException) {
            Log.e(TAG, "JSON encoding error for AdContent: ${e.message}")
            null
        } catch (e: IOException) {
            Log.e(TAG, "IO error while creating AdContent JSON: ${e.message}")
            null
        }
    }
}
