package co.thewordlab.luzia.foundation.messages.data

import androidx.paging.PagingSource
import co.thewordlab.fouundation.persistence.chat.ChatDataSource
import co.thewordlab.fouundation.persistence.chat.ChatMetadata
import co.thewordlab.fouundation.persistence.chat.ChatView
import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject

@Suppress("TooManyFunctions")
class InMemoryChatDataSource @Inject constructor() : ChatDataSource {
    private val nextId = AtomicLong(1)
    private val messages: MutableStateFlow<List<MessageEntity>> = MutableStateFlow(emptyList())
    private var inMemoryChatPagingSource: InMemoryChatPagingSource? = null

    override fun getMessagesPaginated(personalityId: String): PagingSource<Int, MessageEntity> {
        val source = InMemoryChatPagingSource(messages.value.filterNot { it.deleted })
        inMemoryChatPagingSource = source
        return source
    }

    override fun getMessagesPaginatedAfter(
        personalityId: String,
        timeStamp: Long
    ): PagingSource<Int, MessageEntity> {
        val source = InMemoryChatPagingSource(
            messages.value.filterNot { it.deleted }.filter { it.timeStamp > timeStamp }
        )
        inMemoryChatPagingSource = source
        return source
    }

    override fun getFavoritesPaginated(personalityId: String): PagingSource<Int, MessageEntity> {
        return InMemoryChatPagingSource(messages.value)
    }

    override suspend fun insertMessage(message: MessageEntity): Long {
        val entity =
            if (message.messageId == 0L) message.copy(messageId = nextId.getAndIncrement()) else message
        messages.update { list ->
            if (list.any { it.messageId == entity.messageId }) {
                list.map { if (it.messageId == entity.messageId) entity else it }
            } else {
                list.plus(entity)
            }
        }
        inMemoryChatPagingSource?.invalidate()
        return entity.messageId
    }

    override suspend fun readAllMessages(personalityId: String) {
        messages.update { messages -> messages.map { it.copy(isRead = true) } }
        inMemoryChatPagingSource?.invalidate()
    }

    override fun getUnreadMessageCount(personalityId: String): Flow<Long> {
        return messages.map { messages -> messages.count { !it.isRead }.toLong() }
    }

    override fun getLastChats(): Flow<List<ChatView>> = flowOf(emptyList())

    override suspend fun findLastAiMessage(personalityId: String): MessageEntity? {
        return messages.first().lastOrNull { it.isAi }
    }

    override suspend fun deleteFailedUserMessagesAfter(personalityId: String, timeStamp: Long) {
        messages.update { messages ->
            messages.filterNot { it.timeStamp > timeStamp && it.error != null && !it.isAi }
        }
        inMemoryChatPagingSource?.invalidate()
    }

    override suspend fun deleteLoadingUserMessagesAfter(
        personalityId: String,
        timeStampStart: Long,
        timeStampEnd: Long
    ) {
        messages.update { messages ->
            messages.filterNot { it.timeStamp in (timeStampStart + 1)..<timeStampEnd && it.isLoading && !it.isAi }
        }
        inMemoryChatPagingSource?.invalidate()
    }

    override suspend fun getFailedMessages(errorCode: String): List<MessageEntity> {
        return messages.first().filter { it.error == errorCode }
    }

    override suspend fun getLastMessagesWithoutError(
        personalityId: String,
        takeLast: Int,
    ): List<MessageEntity> {
        return messages.first()
            .filter { it.error == null }
            .sortedByDescending { it.timeStamp }
            .take(takeLast)
    }

    override suspend fun clearMessages(personalityId: String) {
        messages.update { emptyList() }
        inMemoryChatPagingSource?.invalidate()
    }

    override suspend fun deleteFeedback(feedbackId: String) {
        messages.update { messages ->
            messages.map { message ->
                if (message.feedbackId == feedbackId) message.copy(feedbackId = null) else message
            }
        }
        inMemoryChatPagingSource?.invalidate()
    }

    override suspend fun deleteMessage(personalityId: String, messageId: Long) {
        messages.update { messages ->
            messages.filterNot { it.personalityId == personalityId && it.messageId == messageId }
        }
        inMemoryChatPagingSource?.invalidate()
    }

    override suspend fun deleteFollowingMessagesFrom(personalityId: String, timestamp: Long) {
        messages.update { messages ->
            messages.filterNot { it.personalityId == personalityId && it.timeStamp > timestamp }
        }
        inMemoryChatPagingSource?.invalidate()
    }

    override suspend fun setDeletedMessagesWith(
        personalityId: String,
        messageIds: List<Long>,
        toBeDeleted: Boolean,
    ) {
        messages.update { messages ->
            messages.map {
                if (it.personalityId == personalityId && messageIds.contains(it.messageId)) {
                    it.copy(deleted = toBeDeleted)
                } else {
                    it
                }
            }
        }
        inMemoryChatPagingSource?.invalidate()
    }

    override suspend fun setDeletedPersonalityWith(personalityId: String, toBeDeleted: Boolean) {
        messages.update { messages ->
            messages.map {
                if (it.personalityId == personalityId) {
                    it.copy(deleted = toBeDeleted)
                } else {
                    it
                }
            }
        }
        inMemoryChatPagingSource?.invalidate()
    }

    override suspend fun deletePendingMessages(personalityId: String) {
        messages.update { messages ->
            messages.filterNot { it.personalityId == personalityId && it.deleted }
        }
        inMemoryChatPagingSource?.invalidate()
    }

    override suspend fun deleteFeedbackBefore(timestamp: Long) {
        messages.update { messages ->
            messages.map { message ->
                if (message.timeStamp < timestamp) message.copy(feedbackId = null) else message
            }
        }
        inMemoryChatPagingSource?.invalidate()
    }

    override suspend fun searchMessages(personalityId: String, query: String): List<MessageEntity> =
        messages.value.filter { it.personalityId == personalityId && it.text.contains(query, true) }

    override suspend fun markFavorite(messageId: Long) {
        DO_NOTHING
    }

    override suspend fun unmarkFavorite(messageId: Long) {
        DO_NOTHING
    }

    override suspend fun getMessageById(messageId: Long): MessageEntity? {
        return messages.value.find { it.messageId == messageId }
    }

    override fun getLastUserMessage(personalityId: String): Flow<MessageEntity?> =
        messages.map { messages -> messages.lastOrNull { !it.isAi } }

    override fun getChatMetadata(personalityId: String): Flow<ChatMetadata?> =
        messages.map { messages ->
            messages.lastOrNull()?.run {
                ChatMetadata(
                    lastMessageId = messageId,
                    lastMessageIsAi = isAi,
                    messageCount = messages.size.toLong()
                )
            }
        }

    override suspend fun countAllMessages(personalityId: String?): Int = when (personalityId) {
        null -> messages.value.size
        else -> messages.value.count { it.personalityId == personalityId }
    }

    override suspend fun countAllUserMessages(personalityId: String): Int =
        messages.value.filter { it.personalityId == personalityId && !it.isAi }.size
}
