package co.thewordlab.luzia.foundation.messages.domain.repository

import android.net.Uri
import androidx.paging.PagingData
import co.thewordlab.fouundation.persistence.chat.ChatMetadata
import co.thewordlab.fouundation.persistence.chat.ChatView
import co.thewordlab.fouundation.persistence.chat.MessageEntity
import co.thewordlab.luzia.foundation.messages.data.model.ContentTextDto
import co.thewordlab.luzia.foundation.messages.data.repository.TextMessageResultWrapper
import co.thewordlab.luzia.foundation.messages.domain.model.AdContent
import co.thewordlab.luzia.foundation.networking.model.AppErrors
import co.thewordlab.luzia.foundation.networking.model.ResultOf
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageModel
import kotlinx.coroutines.flow.Flow
import java.io.File

@Suppress("TooManyFunctions")
interface ChatRepository {

    fun getMessages(personalityId: String): Flow<PagingData<MessageEntity>>

    fun getMessagesAfter(personalityId: String, timestamp: Long): Flow<PagingData<MessageEntity>>

    fun getFavoritesMessages(personalityId: String): Flow<PagingData<MessageEntity>>

    fun getUnreadMessageCount(personalityId: String): Flow<Long>

    fun getChats(): Flow<List<ChatView>>

    suspend fun clearFailedUserMessages(personalityId: String)

    suspend fun resendAllSignupRequiredMessages()

    suspend fun saveRemoteMessage(personalityId: String, text: String)

    suspend fun sendTextMessage(
        personalityId: String,
        text: String,
        isWebSearch: Boolean
    ): ResultOf<ContentTextDto, AppErrors>

    suspend fun sendTextMessageAndSignalAd(
        personalityId: String,
        text: String,
        adContent: AdContent?,
        isWebSearch: Boolean
    ): ResultOf<TextMessageResultWrapper, AppErrors>

    suspend fun resendMessage(
        message: MessageEntity,
        isWebSearch: Boolean
    ): ResultOf<ContentTextDto, AppErrors>

    suspend fun sendAudioRecording(
        personalityId: String,
        file: File,
    ): ResultOf<ContentTextDto, AppErrors>

    suspend fun sendAudioImported(
        personalityId: String,
        file: File,
    ): ResultOf<ContentTextDto, AppErrors>

    suspend fun sendVisionImage(
        file: File,
        prompt: String,
        personalityId: String,
    ): ResultOf<ContentTextDto, AppErrors>

    suspend fun readAllMessages(personalityId: String)

    suspend fun updateMessage(messageId: Long, textToUpdate: String, isWebSearch: Boolean)

    suspend fun setMessagesAsPendingToDelete(personalityId: String, messageIds: List<Long>)

    suspend fun setMessagesAsNotPendingToDelete(personalityId: String, messageIds: List<Long>)

    suspend fun setPersonalityAsPendingToDelete(personalityId: String)

    suspend fun setPersonalityAsNotPendingToDelete(personalityId: String)

    suspend fun deletePendingMessages(personalityId: String)

    suspend fun searchMessages(personalityId: String, query: String): List<MessageEntity>

    suspend fun dismissFeedback(feedbackId: String)

    suspend fun shouldShowBestiePointsBanner(): Boolean

    suspend fun markBestiePointsBannerAsShown()

    suspend fun countAllMessages(): Int

    suspend fun countAllLuziaMessages(): Int

    suspend fun countAllUserMessagesWithLuzia(): Int

    suspend fun toggleFavorite(message: MessageModel)

    suspend fun unFavoriteMessages(messageIds: List<Long>)

    fun getLastUserMessageMaxPromptLength(personalityId: String): Flow<Int?>

    suspend fun textToSpeech(
        text: String
    ): ResultOf<Uri, AppErrors>

    fun getChatMetadata(personalityId: String): Flow<ChatMetadata?>
}
