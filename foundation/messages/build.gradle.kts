plugins {
	alias(libs.plugins.luzia.android.library)
	alias(libs.plugins.luzia.android.library.jacoco)
	alias(libs.plugins.luzia.android.hilt)
}

android {
	namespace = "co.thewordlab.luzia.foundation.messages"
}

dependencies {
	implementation(projects.core.feedback)
	implementation(projects.foundation.localization)
	implementation(projects.foundation.persistence)
	implementation(projects.foundation.networking)
	implementation(projects.foundation.common)
	implementation(projects.foundation.files)
	implementation(projects.foundation.config)
	implementation(projects.foundation.designSystem)
	implementation(projects.foundation.analytics)
	implementation(libs.retrofit.core)
	implementation(libs.slack.eithernet)
	implementation(libs.androidx.paging)
	implementation(libs.moshi.core)
	implementation(libs.moshi.kotlin)
	implementation(libs.moshi.adapters)
	ksp(libs.moshi.codegen)
	implementation(libs.androidx.dataStore.core)
	implementation(libs.androidx.dataStore.preferences)

	testImplementation(projects.foundation.testing)
}
