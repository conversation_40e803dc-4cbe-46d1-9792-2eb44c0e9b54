@file:Suppress("MaxLineLength")

package co.thewordlab.luzia.foundation.config.providers.local

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.di.Logger
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

class LocalFeatureConfigTest {

    @MockK
    private lateinit var mockContext: Context

    @MockK
    private lateinit var logger: Logger

    @MockK
    private lateinit var mockSharedPreferences: SharedPreferences

    @MockK
    private lateinit var mockEditor: SharedPreferences.Editor

    private lateinit var sut: LocalFeatureConfig

    @Before
    fun setUp() {
        MockKAnnotations.init(this, relaxed = true)

        every { mockContext.getSharedPreferences("LOCAL_CONFIG", Context.MODE_PRIVATE) } returns mockSharedPreferences
        every { mockSharedPreferences.edit() } returns mockEditor
        every { mockEditor.putBoolean(any(), any()) } returns mockEditor
        every { mockEditor.putString(any(), any()) } returns mockEditor
        every { mockEditor.putInt(any(), any()) } returns mockEditor
        every { mockEditor.apply() } returns Unit

        sut = LocalFeatureConfig(mockContext, logger)
    }

    @Test
    fun `isLocalConfigEnabled should return false by default`() {
        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns false

        assertFalse(sut.isLocalConfigEnabled)
    }

    @Test
    fun `isLocalConfigEnabled should return true when enabled`() {
        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns true

        assertTrue(sut.isLocalConfigEnabled)
    }

    @Test
    fun `get should return null when local config is disabled`() {
        val testFlag = FeatureFlag.GamificationEnabled
        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns false

        val result = runBlocking { sut.get<Boolean>(testFlag) }

        assertNull(result)
    }

    @Test
    fun `getImmediate should return null when local config is disabled`() {
        val testFlag = FeatureFlag.GamificationEnabled
        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns false

        val result = sut.getImmediate<Boolean>(testFlag)

        assertNull(result)
    }

    @Test
    fun `getImmediate should return boolean value when local config is enabled and value exists`() {
        val testFlag = FeatureFlag.GamificationEnabled
        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns true
        every { mockSharedPreferences.getBoolean(testFlag.key, testFlag.defaultValue as Boolean) } returns true

        val result = sut.getImmediate<Boolean>(testFlag)

        assertTrue(result!!)
        verify { logger.log("Local config enabled will return value for ${testFlag.key}") }
    }

    @Test
    fun `getImmediate should return string value when local config is enabled and value exists`() {
        val testFlag = FeatureFlag.WebTools
        val expectedValue = "test_string_value"
        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns true
        every { mockSharedPreferences.getString(testFlag.key, testFlag.defaultValue as String) } returns expectedValue

        val result = sut.getImmediate<String>(testFlag)

        assertEquals(expectedValue, result)
        verify { logger.log("Local config enabled will return value for ${testFlag.key}") }
    }

    @Test
    fun `getImmediate should return null for double type when local config is enabled`() {
        // Test that Double types (like ChatContextMessageLimit) go to the 'else' branch and return null
        val testFlag = FeatureFlag.ChatContextMessageLimit // This has a Double default (20.0)
        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns true

        val result = sut.getImmediate<Double>(testFlag)

        // Since ChatContextMessageLimit has a Double default, it will go to the 'else' branch and return null
        assertNull(result)
        verify { logger.log("Local config enabled will return value for ${testFlag.key}") }
    }

    @Test
    fun `getImmediate should handle float type properly if flag exists`() {
        // Since we don't have actual Float flags in the current FeatureFlag enum,
        // we'll test the behavior by demonstrating that unsupported types return null
        val testFlag = FeatureFlag.DocumentsContextMessageLimit // This has a Double default (-1.0)
        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns true

        // Requesting Float type for a Double flag should return null
        val result = sut.getImmediate<Float>(testFlag)

        assertNull(result)
        verify { logger.log("Local config enabled will return value for ${testFlag.key}") }
    }

    @Test
    fun `getImmediate should handle int type properly if flag exists`() {
        // Since we don't have actual Int flags in the current FeatureFlag enum,
        // we'll test the behavior by demonstrating that unsupported types return null
        val testFlag = FeatureFlag.DocumentsContextMessageLimit // This has a Double default (-1.0)
        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns true

        // Requesting Int type for a Double flag should return null
        val result = sut.getImmediate<Int>(testFlag)

        assertNull(result)
        verify { logger.log("Local config enabled will return value for ${testFlag.key}") }
    }

    @Test
    fun `getImmediate should return null for unsupported type when local config is enabled`() {
        val testFlag = FeatureFlag.DocumentsContextMessageLimit // This has a Double default value
        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns true

        val result = sut.getImmediate<Double>(testFlag)

        assertNull(result)
        verify { logger.log("Local config enabled will return value for ${testFlag.key}") }
    }

    @Test
    fun `getString should return null when local config is disabled`() {
        val testFlag = FeatureFlag.WebTools
        val parameterName = "testParam"
        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns false

        val result = runBlocking { sut.getString(testFlag, parameterName) }

        assertNull(result)
    }

    @Test
    fun `getStringImmediate should return null when local config is disabled`() {
        val testFlag = FeatureFlag.WebTools
        val parameterName = "testParam"
        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns false

        val result = sut.getStringImmediate(testFlag, parameterName)

        assertNull(result)
    }

    @Test
    fun `getStringImmediate should return string value when local config is enabled and payload exists`() {
        val testFlag = FeatureFlag.WebTools
        val parameterName = "testParam"
        val expectedValue = "test_payload_value"
        val payloadKey = "${testFlag.key}_payload_$parameterName"

        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns true
        every { mockSharedPreferences.getString(payloadKey, null) } returns expectedValue

        val result = sut.getStringImmediate(testFlag, parameterName)

        assertEquals(expectedValue, result)
        verify {
            logger.log(
                "Local payload string found for key ${testFlag.key}, parameter $parameterName: $expectedValue"
            )
        }
    }

    @Test
    fun `getStringImmediate should return null when local config is enabled but payload doesn't exist`() {
        val testFlag = FeatureFlag.WebTools
        val parameterName = "testParam"
        val payloadKey = "${testFlag.key}_payload_$parameterName"

        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns true
        every { mockSharedPreferences.getString(payloadKey, null) } returns null

        val result = sut.getStringImmediate(testFlag, parameterName)

        assertNull(result)
    }

    @Test
    fun `getInt should return null when local config is disabled`() {
        val testFlag = FeatureFlag.ChatContextMessageLimit
        val parameterName = "testParam"
        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns false

        val result = runBlocking { sut.getInt(testFlag, parameterName) }

        assertNull(result)
    }

    @Test
    fun `getIntImmediate should return null when local config is disabled`() {
        val testFlag = FeatureFlag.ChatContextMessageLimit
        val parameterName = "testParam"
        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns false

        val result = sut.getIntImmediate(testFlag, parameterName)

        assertNull(result)
    }

    @Test
    fun `getIntImmediate should return int value when local config is enabled and payload exists`() {
        val testFlag = FeatureFlag.ChatContextMessageLimit
        val parameterName = "testParam"
        val expectedValue = 42
        val payloadKey = "${testFlag.key}_payload_$parameterName"

        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns true
        every { mockSharedPreferences.getInt(payloadKey, -1) } returns expectedValue

        val result = sut.getIntImmediate(testFlag, parameterName)

        assertEquals(expectedValue, result)
        verify {
            logger.log(
                "Local payload int found for key ${testFlag.key}, parameter $parameterName: $expectedValue"
            )
        }
    }

    @Test
    fun `getIntImmediate should return null when local config is enabled but payload doesn't exist`() {
        val testFlag = FeatureFlag.ChatContextMessageLimit
        val parameterName = "testParam"
        val payloadKey = "${testFlag.key}_payload_$parameterName"

        every { mockSharedPreferences.getBoolean("LOCAL_CONFIG_ENABLED", false) } returns true
        every { mockSharedPreferences.getInt(payloadKey, -1) } returns -1

        val result = sut.getIntImmediate(testFlag, parameterName)

        assertNull(result)
    }

    @Test
    fun `overWriteConfigValue should enable local config and save values when flags are found`() {
        val bundle = mockk<Bundle>()
        val testStringFlag = FeatureFlag.WebTools
        val testBooleanFlag = FeatureFlag.GamificationEnabled
        val stringValue = "test_value"
        val booleanValue = true

        every { bundle.keySet() } returns setOf(testStringFlag.key, testBooleanFlag.key)
        every { bundle.getString(testStringFlag.key) } returns stringValue
        every { bundle.getBoolean(testBooleanFlag.key) } returns booleanValue

        sut.overWriteConfigValue(bundle)

        verify { mockEditor.putBoolean("LOCAL_CONFIG_ENABLED", true) }
        verify { mockEditor.putString(testStringFlag.key, stringValue) }
        verify { mockEditor.putBoolean(testBooleanFlag.key, booleanValue) }
        verify(exactly = 3) { mockEditor.apply() }
    }

    @Test
    fun `overWriteConfigValue should do nothing when no flags are found`() {
        val bundle = mockk<Bundle>()
        every { bundle.keySet() } returns setOf("unknown_key")

        sut.overWriteConfigValue(bundle)

        verify(exactly = 0) { mockEditor.putBoolean("LOCAL_CONFIG_ENABLED", true) }
    }

    @Test
    fun `save string should store string value in preferences`() {
        val key = "test_key"
        val value = "test_value"

        sut.save(key, value)

        verify { mockEditor.putString(key, value) }
        verify { mockEditor.apply() }
    }

    @Test
    fun `save boolean should store boolean value in preferences`() {
        val key = "test_key"
        val value = true

        sut.save(key, value)

        verify { mockEditor.putBoolean(key, value) }
        verify { mockEditor.apply() }
    }

    @Test
    fun `saveString should store string payload in preferences`() {
        val testFlag = FeatureFlag.WebTools
        val parameterName = "testParam"
        val value = "test_value"
        val expectedKey = "${testFlag.key}_payload_$parameterName"

        sut.saveString(testFlag, parameterName, value)

        verify { mockEditor.putString(expectedKey, value) }
        verify { mockEditor.apply() }
    }

    @Test
    fun `saveInt should store int payload in preferences`() {
        val testFlag = FeatureFlag.ChatContextMessageLimit
        val parameterName = "testParam"
        val value = 42
        val expectedKey = "${testFlag.key}_payload_$parameterName"

        sut.saveInt(testFlag, parameterName, value)

        verify { mockEditor.putInt(expectedKey, value) }
        verify { mockEditor.apply() }
    }

    @Test
    fun `setLocalConfigEnabled should store boolean value in preferences`() {
        val isEnabled = true

        sut.setLocalConfigEnabled(isEnabled)

        verify { mockEditor.putBoolean("LOCAL_CONFIG_ENABLED", isEnabled) }
        verify { mockEditor.apply() }
    }
}
