@file:Suppress("MaxLineLength")

package co.thewordlab.luzia.foundation.config

import android.os.Bundle
import co.thewordlab.luzia.foundation.config.di.Logger
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

@OptIn(ExperimentalCoroutinesApi::class)
class FeatureFlagManagerImplTest {

    @MockK
    private lateinit var mockLogger: Logger

    @MockK
    private lateinit var mockFeatureConfig1: FeatureConfig

    @MockK
    private lateinit var mockFeatureConfig2: FeatureConfig

    @MockK
    private lateinit var mockFeatureConfig3: FeatureConfig

    private val testDispatcher = StandardTestDispatcher()

    private lateinit var featureFlagManager: FeatureFlagManagerImpl

    @Before
    fun setUp() {
        MockKAnnotations.init(this, relaxed = true)

        val configList = listOf(mockFeatureConfig1, mockFeatureConfig2, mockFeatureConfig3)
        featureFlagManager = FeatureFlagManagerImpl(
            configList = configList,
            logger = mockLogger,
            coroutineDispatcher = testDispatcher
        )
    }

    @Test
    fun `initialize should call initialize on all feature configs and log`() {
        featureFlagManager.initialize()

        verify {
            mockLogger.log(
                "Initializing providers [$mockFeatureConfig1, $mockFeatureConfig2, $mockFeatureConfig3]"
            )
        }
        verify { mockFeatureConfig1.initialize() }
        verify { mockFeatureConfig2.initialize() }
        verify { mockFeatureConfig3.initialize() }
    }

    @Test
    fun `overWriteConfigValue should call overWriteConfigValue on all feature configs`() {
        val mockBundle = mockk<Bundle>()

        featureFlagManager.overWriteConfigValue(mockBundle)

        verify { mockFeatureConfig1.overWriteConfigValue(mockBundle) }
        verify { mockFeatureConfig2.overWriteConfigValue(mockBundle) }
        verify { mockFeatureConfig3.overWriteConfigValue(mockBundle) }
    }

    @Test
    fun `get should return value from first provider that has the value`() = runTest(testDispatcher) {
        val testFlag = FeatureFlag.GamificationEnabled
        val expectedValue = true
        coEvery { mockFeatureConfig1.get<Boolean>(testFlag) } returns null
        coEvery { mockFeatureConfig2.get<Boolean>(testFlag) } returns expectedValue
        coEvery { mockFeatureConfig3.get<Boolean>(testFlag) } returns false // Should not be called

        val result = featureFlagManager.get<Boolean>(testFlag)

        assertEquals(expectedValue, result)
        verify { mockLogger.log("Searching config value for ${testFlag.key}") }
        coVerify { mockFeatureConfig1.get<Boolean>(testFlag) }
        coVerify { mockFeatureConfig2.get<Boolean>(testFlag) }
        coVerify(exactly = 0) { mockFeatureConfig3.get<Boolean>(testFlag) }
    }

    @Test
    fun `get should return default value when no provider has the value`() = runTest(testDispatcher) {
        val testFlag = FeatureFlag.GamificationEnabled
        coEvery { mockFeatureConfig1.get<Boolean>(testFlag) } returns null
        coEvery { mockFeatureConfig2.get<Boolean>(testFlag) } returns null
        coEvery { mockFeatureConfig3.get<Boolean>(testFlag) } returns null

        val result = featureFlagManager.get<Boolean>(testFlag)

        assertEquals(testFlag.defaultValue, result)
        verify { mockLogger.log("Searching config value for ${testFlag.key}") }
        verify { mockLogger.log("Providers don't have the value falling back to default for ${testFlag.key}") }
        coVerify { mockFeatureConfig1.get<Boolean>(testFlag) }
        coVerify { mockFeatureConfig2.get<Boolean>(testFlag) }
        coVerify { mockFeatureConfig3.get<Boolean>(testFlag) }
    }

    @Test
    fun `get should return value from first provider when multiple providers have values`() = runTest(testDispatcher) {
        val testFlag = FeatureFlag.WebTools
        val firstProviderValue = "first_value"
        val secondProviderValue = "second_value"
        coEvery { mockFeatureConfig1.get<String>(testFlag) } returns firstProviderValue
        coEvery { mockFeatureConfig2.get<String>(testFlag) } returns secondProviderValue
        coEvery { mockFeatureConfig3.get<String>(testFlag) } returns "third_value"

        val result = featureFlagManager.get<String>(testFlag)

        assertEquals(firstProviderValue, result)
        verify { mockLogger.log("Searching config value for ${testFlag.key}") }
        coVerify { mockFeatureConfig1.get<String>(testFlag) }
        coVerify(exactly = 0) { mockFeatureConfig2.get<String>(testFlag) }
        coVerify(exactly = 0) { mockFeatureConfig3.get<String>(testFlag) }
    }

    @Test
    fun `getImmediate should return value from first provider that has the value`() {
        val testFlag = FeatureFlag.GamificationEnabled
        val expectedValue = true
        every { mockFeatureConfig1.getImmediate<Boolean>(testFlag) } returns null
        every { mockFeatureConfig2.getImmediate<Boolean>(testFlag) } returns expectedValue
        every { mockFeatureConfig3.getImmediate<Boolean>(testFlag) } returns false

        val result = featureFlagManager.getImmediate<Boolean>(testFlag)

        assertEquals(expectedValue, result)
        verify { mockLogger.log("Searching config value for ${testFlag.key}") }
        verify { mockFeatureConfig1.getImmediate<Boolean>(testFlag) }
        verify { mockFeatureConfig2.getImmediate<Boolean>(testFlag) }
        verify(exactly = 0) { mockFeatureConfig3.getImmediate<Boolean>(testFlag) }
    }

    @Test
    fun `getImmediate should return default value when no provider has the value`() {
        val testFlag = FeatureFlag.GamificationEnabled
        every { mockFeatureConfig1.getImmediate<Boolean>(testFlag) } returns null
        every { mockFeatureConfig2.getImmediate<Boolean>(testFlag) } returns null
        every { mockFeatureConfig3.getImmediate<Boolean>(testFlag) } returns null

        val result = featureFlagManager.getImmediate<Boolean>(testFlag)

        assertEquals(testFlag.defaultValue, result)
        verify { mockLogger.log("Searching config value for ${testFlag.key}") }
        verify { mockLogger.log("Providers don't have the value falling back to default for ${testFlag.key}") }
        verify { mockFeatureConfig1.getImmediate<Boolean>(testFlag) }
        verify { mockFeatureConfig2.getImmediate<Boolean>(testFlag) }
        verify { mockFeatureConfig3.getImmediate<Boolean>(testFlag) }
    }

    @Test
    fun `getString should return value from first provider that has the value`() = runTest(testDispatcher) {
        val testFlag = FeatureFlag.WebTools
        val parameterName = "testParam"
        val defaultValue = "default_value"
        val expectedValue = "test_string_value"
        coEvery { mockFeatureConfig1.getString(testFlag, parameterName) } returns null
        coEvery { mockFeatureConfig2.getString(testFlag, parameterName) } returns expectedValue
        coEvery { mockFeatureConfig3.getString(testFlag, parameterName) } returns "other_value"

        val result = featureFlagManager.getString(testFlag, parameterName, defaultValue)

        assertEquals(expectedValue, result)
        verify { mockLogger.log("Searching payload string for ${testFlag.key}, parameter: $parameterName") }
        coVerify { mockFeatureConfig1.getString(testFlag, parameterName) }
        coVerify { mockFeatureConfig2.getString(testFlag, parameterName) }
        coVerify(exactly = 0) { mockFeatureConfig3.getString(testFlag, parameterName) }
    }

    @Test
    fun `getString should return default value when no provider has the value`() = runTest(testDispatcher) {
        val testFlag = FeatureFlag.WebTools
        val parameterName = "testParam"
        val defaultValue = "default_value"
        coEvery { mockFeatureConfig1.getString(testFlag, parameterName) } returns null
        coEvery { mockFeatureConfig2.getString(testFlag, parameterName) } returns null
        coEvery { mockFeatureConfig3.getString(testFlag, parameterName) } returns null

        val result = featureFlagManager.getString(testFlag, parameterName, defaultValue)

        assertEquals(defaultValue, result)
        verify { mockLogger.log("Searching payload string for ${testFlag.key}, parameter: $parameterName") }
        verify {
            mockLogger.log(
                "No provider has the payload string for ${testFlag.key}, parameter: $parameterName, returning default: $defaultValue"
            )
        }
        coVerify { mockFeatureConfig1.getString(testFlag, parameterName) }
        coVerify { mockFeatureConfig2.getString(testFlag, parameterName) }
        coVerify { mockFeatureConfig3.getString(testFlag, parameterName) }
    }

    @Test
    fun `getStringImmediate should return value from first provider that has the value`() {
        val testFlag = FeatureFlag.WebTools
        val parameterName = "testParam"
        val defaultValue = "default_value"
        val expectedValue = "test_string_value"
        every { mockFeatureConfig1.getStringImmediate(testFlag, parameterName) } returns null
        every { mockFeatureConfig2.getStringImmediate(testFlag, parameterName) } returns expectedValue
        every { mockFeatureConfig3.getStringImmediate(testFlag, parameterName) } returns "other_value"

        val result = featureFlagManager.getStringImmediate(testFlag, parameterName, defaultValue)

        assertEquals(expectedValue, result)
        verify { mockLogger.log("Searching immediate payload string for ${testFlag.key}, parameter: $parameterName") }
        verify { mockFeatureConfig1.getStringImmediate(testFlag, parameterName) }
        verify { mockFeatureConfig2.getStringImmediate(testFlag, parameterName) }
        verify(exactly = 0) { mockFeatureConfig3.getStringImmediate(testFlag, parameterName) }
    }

    @Test
    fun `getStringImmediate should return default value when no provider has the value`() {
        val testFlag = FeatureFlag.WebTools
        val parameterName = "testParam"
        val defaultValue = "default_value"
        every { mockFeatureConfig1.getStringImmediate(testFlag, parameterName) } returns null
        every { mockFeatureConfig2.getStringImmediate(testFlag, parameterName) } returns null
        every { mockFeatureConfig3.getStringImmediate(testFlag, parameterName) } returns null

        val result = featureFlagManager.getStringImmediate(testFlag, parameterName, defaultValue)

        assertEquals(defaultValue, result)
        verify { mockLogger.log("Searching immediate payload string for ${testFlag.key}, parameter: $parameterName") }
        verify {
            mockLogger.log(
                "No provider has the immediate payload string for ${testFlag.key}, parameter: $parameterName, returning default: $defaultValue"
            )
        }
        verify { mockFeatureConfig1.getStringImmediate(testFlag, parameterName) }
        verify { mockFeatureConfig2.getStringImmediate(testFlag, parameterName) }
        verify { mockFeatureConfig3.getStringImmediate(testFlag, parameterName) }
    }

    @Test
    fun `getInt should return value from first provider that has the value`() = runTest(testDispatcher) {
        val testFlag = FeatureFlag.ChatContextMessageLimit
        val parameterName = "testParam"
        val defaultValue = 99
        val expectedValue = 42
        coEvery { mockFeatureConfig1.getInt(testFlag, parameterName) } returns null
        coEvery { mockFeatureConfig2.getInt(testFlag, parameterName) } returns expectedValue
        coEvery { mockFeatureConfig3.getInt(testFlag, parameterName) } returns 100

        val result = featureFlagManager.getInt(testFlag, parameterName, defaultValue)

        assertEquals(expectedValue, result)
        verify { mockLogger.log("Searching payload int for ${testFlag.key}, parameter: $parameterName") }
        coVerify { mockFeatureConfig1.getInt(testFlag, parameterName) }
        coVerify { mockFeatureConfig2.getInt(testFlag, parameterName) }
        coVerify(exactly = 0) { mockFeatureConfig3.getInt(testFlag, parameterName) }
    }

    @Test
    fun `getInt should return default value when no provider has the value`() = runTest(testDispatcher) {
        val testFlag = FeatureFlag.ChatContextMessageLimit
        val parameterName = "testParam"
        val defaultValue = 99
        coEvery { mockFeatureConfig1.getInt(testFlag, parameterName) } returns null
        coEvery { mockFeatureConfig2.getInt(testFlag, parameterName) } returns null
        coEvery { mockFeatureConfig3.getInt(testFlag, parameterName) } returns null

        val result = featureFlagManager.getInt(testFlag, parameterName, defaultValue)

        assertEquals(defaultValue, result)
        verify { mockLogger.log("Searching payload int for ${testFlag.key}, parameter: $parameterName") }
        verify {
            mockLogger.log(
                "No provider has the payload int for ${testFlag.key}, parameter: $parameterName, returning default: $defaultValue"
            )
        }
        coVerify { mockFeatureConfig1.getInt(testFlag, parameterName) }
        coVerify { mockFeatureConfig2.getInt(testFlag, parameterName) }
        coVerify { mockFeatureConfig3.getInt(testFlag, parameterName) }
    }

    @Test
    fun `getIntImmediate should return value from first provider that has the value`() {
        val testFlag = FeatureFlag.ChatContextMessageLimit
        val parameterName = "testParam"
        val defaultValue = 99
        val expectedValue = 42
        every { mockFeatureConfig1.getIntImmediate(testFlag, parameterName) } returns null
        every { mockFeatureConfig2.getIntImmediate(testFlag, parameterName) } returns expectedValue
        every { mockFeatureConfig3.getIntImmediate(testFlag, parameterName) } returns 100

        val result = featureFlagManager.getIntImmediate(testFlag, parameterName, defaultValue)

        assertEquals(expectedValue, result)
        verify { mockLogger.log("Searching immediate payload int for ${testFlag.key}, parameter: $parameterName") }
        verify { mockFeatureConfig1.getIntImmediate(testFlag, parameterName) }
        verify { mockFeatureConfig2.getIntImmediate(testFlag, parameterName) }
        verify(exactly = 0) { mockFeatureConfig3.getIntImmediate(testFlag, parameterName) }
    }

    @Test
    fun `getIntImmediate should return default value when no provider has the value`() {
        val testFlag = FeatureFlag.ChatContextMessageLimit
        val parameterName = "testParam"
        val defaultValue = 99
        every { mockFeatureConfig1.getIntImmediate(testFlag, parameterName) } returns null
        every { mockFeatureConfig2.getIntImmediate(testFlag, parameterName) } returns null
        every { mockFeatureConfig3.getIntImmediate(testFlag, parameterName) } returns null

        val result = featureFlagManager.getIntImmediate(testFlag, parameterName, defaultValue)

        assertEquals(defaultValue, result)
        verify { mockLogger.log("Searching immediate payload int for ${testFlag.key}, parameter: $parameterName") }
        verify {
            mockLogger.log(
                "No provider has the immediate payload int for ${testFlag.key}, parameter: $parameterName, returning default: $defaultValue"
            )
        }
        verify { mockFeatureConfig1.getIntImmediate(testFlag, parameterName) }
        verify { mockFeatureConfig2.getIntImmediate(testFlag, parameterName) }
        verify { mockFeatureConfig3.getIntImmediate(testFlag, parameterName) }
    }

    @Test
    fun `provider order should be respected - first provider wins`() = runTest(testDispatcher) {
        val testFlag = FeatureFlag.GamificationEnabled
        val firstProviderValue = true
        val secondProviderValue = false
        val thirdProviderValue = true

        coEvery { mockFeatureConfig1.get<Boolean>(testFlag) } returns firstProviderValue
        coEvery { mockFeatureConfig2.get<Boolean>(testFlag) } returns secondProviderValue
        coEvery { mockFeatureConfig3.get<Boolean>(testFlag) } returns thirdProviderValue

        val result = featureFlagManager.get<Boolean>(testFlag)

        assertEquals(firstProviderValue, result)
        coVerify { mockFeatureConfig1.get<Boolean>(testFlag) }
        coVerify(exactly = 0) { mockFeatureConfig2.get<Boolean>(testFlag) }
        coVerify(exactly = 0) { mockFeatureConfig3.get<Boolean>(testFlag) }
    }

    @Test
    fun `empty config list should always return default values`() {
        val emptyFeatureFlagManager = FeatureFlagManagerImpl(
            configList = emptyList(),
            logger = mockLogger,
            coroutineDispatcher = testDispatcher
        )
        val testFlag = FeatureFlag.GamificationEnabled

        val result = emptyFeatureFlagManager.getImmediate<Boolean>(testFlag)

        assertEquals(testFlag.defaultValue, result)
        verify { mockLogger.log("Searching config value for ${testFlag.key}") }
        verify { mockLogger.log("Providers don't have the value falling back to default for ${testFlag.key}") }
    }

    @Test
    fun `should handle different feature flag types correctly`() = runTest(testDispatcher) {
        val booleanFlag = FeatureFlag.GamificationEnabled
        val booleanValue = true
        coEvery { mockFeatureConfig1.get<Boolean>(booleanFlag) } returns booleanValue

        val stringFlag = FeatureFlag.WebTools
        val stringValue = "test_value"
        coEvery { mockFeatureConfig1.get<String>(stringFlag) } returns stringValue

        val booleanResult = featureFlagManager.get<Boolean>(booleanFlag)
        assertEquals(booleanValue, booleanResult)

        val stringResult = featureFlagManager.get<String>(stringFlag)
        assertEquals(stringValue, stringResult)
    }

    @Test
    fun `empty config list should return default value for payload methods`() {
        val emptyFeatureFlagManager = FeatureFlagManagerImpl(
            configList = emptyList(),
            logger = mockLogger,
            coroutineDispatcher = testDispatcher
        )
        val testFlag = FeatureFlag.WebTools
        val parameterName = "testParam"
        val defaultValue = "expected_default"

        val result = emptyFeatureFlagManager.getStringImmediate(testFlag, parameterName, defaultValue)

        assertEquals(defaultValue, result)
        verify { mockLogger.log("Searching immediate payload string for ${testFlag.key}, parameter: $parameterName") }
        verify {
            mockLogger.log(
                "No provider has the immediate payload string for ${testFlag.key}, parameter: $parameterName, returning default: $defaultValue"
            )
        }
    }
}
