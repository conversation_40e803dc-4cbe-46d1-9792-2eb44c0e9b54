@file:Suppress("MaxLineLength")

package co.thewordlab.luzia.foundation.config.providers.firebase

import android.os.Bundle
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.di.Logger
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.StandardTestDispatcher
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

@OptIn(ExperimentalCoroutinesApi::class)
class FirebaseFeatureConfigTest {

    @MockK
    private lateinit var logger: Logger

    @MockK
    private lateinit var mockRemoteConfigWrapper: RemoteConfigWrapper

    private val testDispatcher = StandardTestDispatcher()

    private lateinit var sut: FirebaseFeatureConfig

    @Before
    fun setUp() {
        MockKAnnotations.init(this, relaxed = true)

        sut = FirebaseFeatureConfig(
            coroutineDispatcher = testDispatcher,
            logger = logger,
            remoteConfigWrapper = mockRemoteConfigWrapper
        )
    }

    @Test
    fun `priority should return 2`() {
        assertEquals(2, sut.priority)
    }

    @Test
    fun `get should return null for non-existent feature flag`() {
        val testFlag = FeatureFlag.GamificationEnabled
        every { mockRemoteConfigWrapper.containsKey(testFlag.key) } returns false

        val result = runBlocking { sut.get<Boolean>(testFlag) }

        assertNull(result)
    }

    @Test
    fun `getImmediate should return null for non-existent feature flag`() {
        val testFlag = FeatureFlag.GamificationEnabled
        every { mockRemoteConfigWrapper.containsKey(testFlag.key) } returns false

        val result = sut.getImmediate<Boolean>(testFlag)

        assertNull(result)
    }

    @Test
    fun `getImmediate should return true for boolean feature flag when value is true`() {
        val testFlag = FeatureFlag.GamificationEnabled
        every { mockRemoteConfigWrapper.containsKey(testFlag.key) } returns true
        every { mockRemoteConfigWrapper.getBoolean(testFlag.key) } returns true

        val result = sut.getImmediate<Boolean>(testFlag)

        assertTrue(result!!)
        verify { logger.log("Key found in firebase for key ${testFlag.key} value: true") }
    }

    @Test
    fun `getImmediate should return false for boolean feature flag when value is false`() {
        val testFlag = FeatureFlag.GamificationEnabled
        every { mockRemoteConfigWrapper.containsKey(testFlag.key) } returns true
        every { mockRemoteConfigWrapper.getBoolean(testFlag.key) } returns false

        val result = sut.getImmediate<Boolean>(testFlag)

        assertFalse(result!!)
        verify { logger.log("Key found in firebase for key ${testFlag.key} value: false") }
    }

    @Test
    fun `getImmediate should return string value for string feature flag`() {
        val testFlag = FeatureFlag.WebTools
        val expectedValue = "test_string_value"
        every { mockRemoteConfigWrapper.containsKey(testFlag.key) } returns true
        every { mockRemoteConfigWrapper.getString(testFlag.key) } returns expectedValue

        val result = sut.getImmediate<String>(testFlag)

        assertEquals(expectedValue, result)
        verify { logger.log("Key found in firebase for key ${testFlag.key} value: $expectedValue") }
    }

    @Test
    fun `getImmediate should return double value for double feature flag`() {
        val testFlag = FeatureFlag.DocumentsContextMessageLimit
        val expectedValue = 42.5
        every { mockRemoteConfigWrapper.containsKey(testFlag.key) } returns true
        every { mockRemoteConfigWrapper.getDouble(testFlag.key) } returns expectedValue

        val result = sut.getImmediate<Double>(testFlag)

        assertEquals(expectedValue, result)
        verify { logger.log("Key found in firebase for key ${testFlag.key} value: $expectedValue") }
    }

    @Test
    fun `getString should return null (not implemented)`() {
        val testFlag = FeatureFlag.WebTools
        val parameterName = "testParam"

        val result = runBlocking { sut.getString(testFlag, parameterName) }

        assertNull(result)
    }

    @Test
    fun `getStringImmediate should return null (not implemented)`() {
        val testFlag = FeatureFlag.WebTools
        val parameterName = "testParam"

        val result = sut.getStringImmediate(testFlag, parameterName)

        assertNull(result)
    }

    @Test
    fun `getInt should return null (not implemented)`() {
        val testFlag = FeatureFlag.ChatContextMessageLimit
        val parameterName = "testParam"

        val result = runBlocking { sut.getInt(testFlag, parameterName) }

        assertNull(result)
    }

    @Test
    fun `getIntImmediate should return null (not implemented)`() {
        val testFlag = FeatureFlag.ChatContextMessageLimit
        val parameterName = "testParam"

        val result = sut.getIntImmediate(testFlag, parameterName)

        assertNull(result)
    }

    @Test
    fun `overWriteConfigValue should not modify bundle`() {
        val bundle = mockk<Bundle>()

        sut.overWriteConfigValue(bundle)

        verify(exactly = 0) { bundle.keySet() }
    }
}
