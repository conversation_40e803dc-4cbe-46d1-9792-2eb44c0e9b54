package co.thewordlab.luzia.foundation.config.providers.firebase

import android.os.Bundle
import android.util.Log
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.config.FeatureConfig
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.R
import co.thewordlab.luzia.foundation.config.di.Logger
import com.google.android.gms.tasks.OnFailureListener
import com.google.firebase.remoteconfig.ConfigUpdate
import com.google.firebase.remoteconfig.ConfigUpdateListener
import com.google.firebase.remoteconfig.FirebaseRemoteConfigException
import com.google.firebase.remoteconfig.ktx.remoteConfigSettings
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withTimeoutOrNull
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FirebaseFeatureConfig @Inject constructor(
    coroutineDispatcher: CoroutineDispatcher,
    private val logger: Logger,
    private val remoteConfigWrapper: RemoteConfigWrapper
) : FeatureConfig, OnFailureListener, ConfigUpdateListener {

    private var fetchJob: Job? = null
    private val exceptionHandler =
        CoroutineExceptionHandler { _, throwable ->
            Log.e(TAG, "Exception in global coroutine handler", throwable)
        }
    private val coroutineScope = CoroutineScope(coroutineDispatcher + exceptionHandler)

    override val priority: Int = 2

    override fun initialize() {
        setConfigurations()
        fetchAndActivate()
        listenForRemoteChanges()
    }

    @Suppress("UNCHECKED_CAST")
    override suspend fun <T> get(featureFlag: FeatureFlag): T? {
        ensureInitialized()
        return getImmediate(featureFlag)
    }

    override fun <T> getImmediate(featureFlag: FeatureFlag): T? {
        val key = featureFlag.key
        return if (remoteConfigWrapper.containsKey(key)) {
            (
                when (featureFlag.defaultValue) {
                    is String -> remoteConfigWrapper.getString(key)
                    is Long -> remoteConfigWrapper.getLong(key)
                    is Double -> remoteConfigWrapper.getDouble(key)
                    is Boolean -> remoteConfigWrapper.getBoolean(key)
                    is Float -> remoteConfigWrapper.getDouble(key).toFloat()
                    else -> null
                } as T?
                ).also {
                logger.log("Key found in firebase for key ${featureFlag.key} value: $it")
            }
        } else {
            null
        }
    }

    private fun listenForRemoteChanges() {
        remoteConfigWrapper.addOnConfigUpdateListener(this)
    }

    private fun setConfigurations() {
        val configSettings =
            remoteConfigSettings { minimumFetchIntervalInSeconds = MINIMUM_FETCH_INTERVAL }
        remoteConfigWrapper.setDefaultsAsync(R.xml.remote_config_defaults)
        remoteConfigWrapper.setConfigSettingsAsync(configSettings)
    }

    private fun fetchAndActivate() {
        fetchJob =
            coroutineScope.launch {
                remoteConfigWrapper.fetchAndActivate()
                    .addOnFailureListener(this@FirebaseFeatureConfig).await()
            }
    }

    private fun activate() {
        coroutineScope.launch {
            remoteConfigWrapper.activate().addOnFailureListener(this@FirebaseFeatureConfig).await()
        }
    }

    override fun onFailure(exception: Exception) {
        Log.e(TAG, "Exception in task failure", exception)
    }

    override fun onUpdate(configUpdate: ConfigUpdate) {
        Log.d(TAG, "Config values changes in the remote, activating...")
        activate()
    }

    override fun onError(error: FirebaseRemoteConfigException) {
        Log.e(TAG, "Error in update listener", error)
    }

    override fun overWriteConfigValue(bundle: Bundle) {
        DO_NOTHING
    }

    override suspend fun getString(featureFlag: FeatureFlag, parameterName: String): String? {
        ensureInitialized()
        return getStringImmediate(featureFlag, parameterName)
    }

    override fun getStringImmediate(featureFlag: FeatureFlag, parameterName: String): String? {
        return null
    }

    override suspend fun getInt(featureFlag: FeatureFlag, parameterName: String): Int? {
        ensureInitialized()
        return getIntImmediate(featureFlag, parameterName)
    }

    override fun getIntImmediate(featureFlag: FeatureFlag, parameterName: String): Int? {
        return null
    }

    private suspend fun ensureInitialized() = fetchJob?.run {
        if (isCompleted == false && isActive == true) {
            withTimeoutOrNull(INIT_TIMEOUT) { join() }
        }
    }

    private companion object {
        const val TAG = "FirebaseFeatureFlag"
        const val MINIMUM_FETCH_INTERVAL = 3600L
        private const val INIT_TIMEOUT = 3000L
    }
}
