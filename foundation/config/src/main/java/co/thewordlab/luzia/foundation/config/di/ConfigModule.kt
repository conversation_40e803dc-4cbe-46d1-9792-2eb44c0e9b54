package co.thewordlab.luzia.foundation.config.di

import android.app.Application
import co.thewordlab.luzia.foundation.analytics.providers.AmplitudeAnalyticsProvider
import co.thewordlab.luzia.foundation.analytics.providers.config.BrazeConfig
import co.thewordlab.luzia.foundation.analytics.providers.config.DataDogConfig
import co.thewordlab.luzia.foundation.config.FeatureConfig
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.FeatureFlagManager
import co.thewordlab.luzia.foundation.config.FeatureFlagManagerImpl
import co.thewordlab.luzia.foundation.config.data.api.ConfigurationApi
import co.thewordlab.luzia.foundation.config.providers.amplitude.AmplitudeFeatureConfig
import co.thewordlab.luzia.foundation.config.providers.amplitude.DefaultVariantProvider
import co.thewordlab.luzia.foundation.config.providers.amplitude.PayloadProcessor
import co.thewordlab.luzia.foundation.config.providers.amplitude.VariantProvider
import co.thewordlab.luzia.foundation.config.providers.firebase.FirebaseFeatureConfig
import co.thewordlab.luzia.foundation.config.providers.firebase.FirebaseRemoteConfigWrapper
import co.thewordlab.luzia.foundation.config.providers.firebase.RemoteConfigWrapper
import co.thewordlab.luzia.foundation.networking.di.BaseHost
import co.thewordlab.luzia.foundation.securelib.SecureKey
import co.thewordlab.luzia.foundation.securelib.SecureStorage
import com.amplitude.experiment.Experiment
import com.amplitude.experiment.ExperimentClient
import com.amplitude.experiment.ExperimentConfig
import com.amplitude.experiment.ServerZone
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import dagger.multibindings.IntoSet
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.runBlocking
import retrofit2.Retrofit
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object ConfigModule {

    @Provides
    fun provideApi(@BaseHost retrofit: Retrofit): ConfigurationApi =
        retrofit.create(ConfigurationApi::class.java)

    @Provides
    fun provideRemoteConfigWrapper(impl: FirebaseRemoteConfigWrapper): RemoteConfigWrapper = impl

    @IntoSet
    @Provides
    fun provideFirebaseFeatureConfig(impl: FirebaseFeatureConfig): FeatureConfig = impl

    @Provides
    fun providePayloadProcessor(logger: Logger): PayloadProcessor = PayloadProcessor(logger)

    @Provides
    fun provideVariantProvider(experimentClient: ExperimentClient): VariantProvider =
        DefaultVariantProvider(experimentClient)

    @IntoSet
    @Provides
    @Suppress("LongParameterList")
    fun provideAmplitudeFeatureConfig(
        coroutineDispatcher: CoroutineDispatcher,
        experimentClient: ExperimentClient,
        variantProvider: VariantProvider,
        logger: Logger,
        amplitudeAnalyticsProvider: AmplitudeAnalyticsProvider,
        payloadProcessor: PayloadProcessor
    ): FeatureConfig = AmplitudeFeatureConfig(
        coroutineDispatcher,
        experimentClient,
        variantProvider,
        logger,
        amplitudeAnalyticsProvider,
        payloadProcessor
    )

    @Provides
    @Singleton
    fun provideExperimentClient(
        application: Application,
        secureStorage: SecureStorage
    ): ExperimentClient {
        val config = ExperimentConfig.Builder().apply {
            serverZone(ServerZone.EU)
        }.build()
        return Experiment.initializeWithAmplitudeAnalytics(
            application = application,
            apiKey = secureStorage.get(SecureKey.KEY_AMPLITUDE_EXPERIMENT),
            config = config
        )
    }

    @Singleton
    @Provides
    fun provideFeatureFlagManager(
        configList: Set<@JvmSuppressWildcards FeatureConfig>,
        logger: Logger,
        coroutineDispatcher: CoroutineDispatcher
    ): FeatureFlagManager = FeatureFlagManagerImpl(
        configList = configList.sortedBy { it.priority },
        logger = logger,
        coroutineDispatcher = coroutineDispatcher
    )

    @Provides
    fun provideBrazeConfig(featureFlagManager: FeatureFlagManager): BrazeConfig {
        return object : BrazeConfig {
            override fun isEnabled(): Boolean {
                return runBlocking {
                    featureFlagManager.getImmediate(FeatureFlag.BrazeEnabled)
                }
            }
        }
    }

    @Provides
    fun provideDataDogConfig(featureFlagManager: FeatureFlagManager): DataDogConfig {
        return object : DataDogConfig {
            override fun isEnabled(): Boolean {
                return runBlocking {
                    featureFlagManager.getImmediate(FeatureFlag.DataDogLogEnabled)
                }
            }

            override fun isRumEnabled(): Boolean {
                return runBlocking {
                    featureFlagManager.getImmediate(FeatureFlag.DataDogRumEnabled)
                }
            }

            override fun sessionSampleRate(): Double {
                return runBlocking {
                    featureFlagManager.getImmediate(FeatureFlag.DataDogSessionSampleRate)
                }
            }
        }
    }
}
