package co.thewordlab.luzia.foundation.config.providers.amplitude

import android.os.Bundle
import androidx.annotation.VisibleForTesting
import co.thewordlab.luzia.foundation.analytics.providers.AmplitudeAnalyticsProvider
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.config.FeatureConfig
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.di.Logger
import com.amplitude.experiment.ExperimentClient
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AmplitudeFeatureConfig @Inject constructor(
    coroutineDispatcher: CoroutineDispatcher,
    private val client: ExperimentClient,
    private val variantProvider: VariantProvider,
    private val logger: Logger,
    private val amplitudeAnalyticsProvider: AmplitudeAnalyticsProvider,
    private val payloadProcessor: PayloadProcessor
) : FeatureConfig {

    private var fetchJob: Job? = null
    private val exceptionHandler =
        CoroutineExceptionHandler { _, throwable ->
            logger.log("Exception in global coroutine handler: ${throwable.message}")
        }
    private val coroutineScope = CoroutineScope(coroutineDispatcher + exceptionHandler)

    override val priority: Int = 1

    override fun initialize() {
        fetchJob = coroutineScope.launch {
            amplitudeAnalyticsProvider.initialize()
            client.start().get()
        }
    }

    @Suppress("UNCHECKED_CAST")
    override suspend fun <T> get(featureFlag: FeatureFlag): T? {
        ensureInitialized()
        return getImmediate(featureFlag)
    }

    override fun <T> getImmediate(featureFlag: FeatureFlag): T? {
        val key = featureFlag.key
        return if (variantProvider.hasVariant(key)) {
            val variant = variantProvider.getVariant(key)
            variant?.let { variantData ->
                (
                    when (featureFlag.defaultValue) {
                        is Boolean -> variantData.value == ON
                        is String -> variantData.value
                        else -> null
                    } as T?
                    ).also {
                    logger.log("Key found in amplitude for key ${featureFlag.key} value: $it")
                }
            }
        } else {
            null
        }
    }

    override suspend fun getString(featureFlag: FeatureFlag, parameterName: String): String? {
        ensureInitialized()
        return getStringImmediate(featureFlag, parameterName)
    }

    override fun getStringImmediate(featureFlag: FeatureFlag, parameterName: String): String? {
        return payloadProcessor.getPayloadValue<String>(featureFlag, parameterName, variantProvider)
    }

    override suspend fun getInt(featureFlag: FeatureFlag, parameterName: String): Int? {
        ensureInitialized()
        return getIntImmediate(featureFlag, parameterName)
    }

    override fun getIntImmediate(featureFlag: FeatureFlag, parameterName: String): Int? {
        return payloadProcessor.getPayloadValue<Int>(featureFlag, parameterName, variantProvider)
    }

    override fun overWriteConfigValue(bundle: Bundle) {
        DO_NOTHING
    }

    @VisibleForTesting
    internal suspend fun ensureInitialized() = fetchJob?.run {
        if (isCompleted == false && isActive == true) {
            withTimeoutOrNull(INIT_TIMEOUT) { join() }
        }
    }

    @VisibleForTesting
    internal fun isInitialized(): Boolean =
        fetchJob?.isCompleted == true || fetchJob == null

    private companion object {
        private const val ON = "on"
        private const val INIT_TIMEOUT = 3000L
    }
}
