package co.thewordlab.luzia.foundation.config

@Suppress("MagicNumber")
enum class FeatureFlag(val key: String, val defaultValue: Any) {
    DynamicToolsEnabled("dynamic_tools_enabled", true),
    ProactiveMessagingEnabled("proactive_messaging_enabled", false),
    WebTools(
        "web_tools",
        "[{\"image_editor\":\"https://luzia-experiments-nextjs.vercel.app?auth=LX1903KE\"}]"
    ),
    GroupChatsEnabled("group_chats_enabled", false),
    ReferralProgramEnabled("referral_program_enabled", false),
    ShipmasProjectEnabled("shipmas_project_enabled", false),
    GroupsJoinSchoolEnabled("groups_join_school_enabled", false),
    DataDogLogEnabled("datadog_logs_enabled", false),
    DataDogRumEnabled("datadog_rum_enabled", false),
    DataDogSessionSampleRate("datadog_logs_session_sample_rate", 0.0),
    TextToSpeechEnabled("luzia_voice_enabled", false),
    ContextualAdsEnabled("contextual_ads_enabled", FeatureFlagTreatment.CONTROL.value),
    GamificationEnabled("gamification_enabled", false),
    DocumentsContextMessageLimit("documents_context_message_limit", -1.0),
    BrazeEnabled("braze_enabled", false),
    ChatContextMessageLimit("chat_context_message_limit", 20.0),
    OnBoardingSelfDescriptionEnabled("onboarding_self_description_enabled", false),
    SignupAnimationTreatment("signup_animations_treatment", FeatureFlagTreatment.CONTROL.value),
    WebSearchEnabled("web_search_enabled", false),
    OverlayEnabled("overlay_enabled", false),
    PrrV2Enabled("prr_v2_enabled", false),
    CustomBestieFlowId("custom_bestie_flow_id", FeatureFlagTreatment.CONTROL.value),
    BannerAdsEnabled("banner_ads_enabled", FeatureFlagTreatment.CONTROL.value),
    ToolsCharactersSortingEnabled("tools_characters_sorting_enabled", false),
    CameraMlKitEnabled("camera_mlkit_enabled", false),
    InterstitialAdsEnabled("interstitial_ads_enabled", false)
}
