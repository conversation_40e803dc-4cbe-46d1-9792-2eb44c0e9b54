package co.thewordlab.luzia.foundation.config.providers.firebase

import com.google.android.gms.tasks.Task
import com.google.firebase.remoteconfig.ConfigUpdateListener
import com.google.firebase.remoteconfig.ConfigUpdateListenerRegistration
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings

interface RemoteConfigWrapper {
    fun setDefaultsAsync(resourceId: Int): Task<Void>
    fun setConfigSettingsAsync(settings: FirebaseRemoteConfigSettings): Task<Void>
    fun fetchAndActivate(): Task<Boolean>
    fun activate(): Task<Boolean>
    fun addOnConfigUpdateListener(listener: ConfigUpdateListener): ConfigUpdateListenerRegistration
    fun containsKey(key: String): Boolean
    fun getString(key: String): String
    fun getBoolean(key: String): Boolean
    fun getLong(key: String): Long
    fun getDouble(key: String): Double
}
