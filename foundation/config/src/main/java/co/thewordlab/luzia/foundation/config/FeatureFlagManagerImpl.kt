package co.thewordlab.luzia.foundation.config

import android.os.Bundle
import co.thewordlab.luzia.foundation.config.di.Logger
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext

class FeatureFlagManagerImpl(
    private val configList: List<FeatureConfig>,
    private val logger: Logger,
    private val coroutineDispatcher: CoroutineDispatcher
) : FeatureFlagManager {

    override fun initialize() {
        logger.log("Initializing providers $configList")
        configList.forEach { config ->
            config.initialize()
        }
    }

    override fun overWriteConfigValue(bundle: Bundle) {
        configList.forEach { config ->
            config.overWriteConfigValue(bundle)
        }
    }

    @Suppress("UNCHECKED_CAST")
    override suspend fun <T> get(featureFlag: FeatureFlag): T = withContext(coroutineDispatcher) {
        logger.log("Searching config value for ${featureFlag.key}")
        configList.forEach { config ->
            val value = config.get<T>(featureFlag)
            if (value != null) return@withContext value
        }
        logger.log("Providers don't have the value falling back to default for ${featureFlag.key}")
        featureFlag.defaultValue as T
    }

    override fun <T> getImmediate(featureFlag: FeatureFlag): T {
        logger.log("Searching config value for ${featureFlag.key}")
        configList.forEach { config ->
            val value = config.getImmediate<T>(featureFlag)
            if (value != null) return value
        }
        logger.log("Providers don't have the value falling back to default for ${featureFlag.key}")
        return featureFlag.defaultValue as T
    }

    override suspend fun getString(
        featureFlag: FeatureFlag,
        parameterName: String,
        defaultValue: String
    ): String = withContext(coroutineDispatcher) {
        logger.log("Searching payload string for ${featureFlag.key}, parameter: $parameterName")
        configList.forEach { config ->
            val value = config.getString(featureFlag, parameterName)
            if (value != null) return@withContext value
        }
        logger.log(
            "No provider has the payload string for ${featureFlag.key}, " +
                "parameter: $parameterName, returning default: $defaultValue"
        )
        defaultValue
    }

    override fun getStringImmediate(
        featureFlag: FeatureFlag,
        parameterName: String,
        defaultValue: String
    ): String {
        logger.log("Searching immediate payload string for ${featureFlag.key}, parameter: $parameterName")
        configList.forEach { config ->
            val value = config.getStringImmediate(featureFlag, parameterName)
            if (value != null) return value
        }
        logger.log(
            "No provider has the immediate payload string for ${featureFlag.key}, " +
                "parameter: $parameterName, returning default: $defaultValue"
        )
        return defaultValue
    }

    override suspend fun getInt(
        featureFlag: FeatureFlag,
        parameterName: String,
        defaultValue: Int
    ): Int = withContext(coroutineDispatcher) {
        logger.log("Searching payload int for ${featureFlag.key}, parameter: $parameterName")
        configList.forEach { config ->
            val value = config.getInt(featureFlag, parameterName)
            if (value != null) return@withContext value
        }
        logger.log(
            "No provider has the payload int for ${featureFlag.key}, " +
                "parameter: $parameterName, returning default: $defaultValue"
        )
        defaultValue
    }

    override fun getIntImmediate(
        featureFlag: FeatureFlag,
        parameterName: String,
        defaultValue: Int
    ): Int {
        logger.log("Searching immediate payload int for ${featureFlag.key}, parameter: $parameterName")
        configList.forEach { config ->
            val value = config.getIntImmediate(featureFlag, parameterName)
            if (value != null) return value
        }
        logger.log(
            "No provider has the immediate payload int for ${featureFlag.key}, " +
                "parameter: $parameterName, returning default: $defaultValue"
        )
        return defaultValue
    }
}
