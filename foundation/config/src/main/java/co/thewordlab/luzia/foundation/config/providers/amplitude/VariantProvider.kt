package co.thewordlab.luzia.foundation.config.providers.amplitude

import com.amplitude.experiment.ExperimentClient

/**
 * Interface that wraps Amplitude ExperimentClient to make it more testable.
 * This allows us to easily mock the behavior without dealing with Amplitude's final classes.
 */
interface VariantProvider {
    fun hasVariant(flagKey: String): Boolean
    fun getVariant(flagKey: String): VariantData?
}

/**
 * Default implementation that delegates to the actual Amplitude ExperimentClient.
 */
class DefaultVariantProvider(private val experimentClient: ExperimentClient) : VariantProvider {

    override fun hasVariant(flagKey: String): Boolean {
        return experimentClient.all().containsKey(flagKey)
    }

    override fun getVariant(flagKey: String): VariantData? {
        return if (hasVariant(flagKey)) {
            val amplitudeVariant = experimentClient.variant(flagKey)
            VariantData(
                value = amplitudeVariant.value,
                payload = amplitudeVariant.payload
            )
        } else {
            null
        }
    }
}
