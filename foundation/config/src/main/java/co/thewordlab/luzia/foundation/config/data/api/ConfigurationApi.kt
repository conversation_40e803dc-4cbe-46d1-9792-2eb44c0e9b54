package co.thewordlab.luzia.foundation.config.data.api

import co.thewordlab.luzia.foundation.networking.headers.ExcludeTokens
import co.thewordlab.luzia.foundation.networking.model.ErrorDto
import co.thewordlab.luzia.foundation.networking.session.model.RetrieveConfigResponse
import com.slack.eithernet.ApiResult
import com.slack.eithernet.DecodeErrorBody
import retrofit2.http.GET

interface ConfigurationApi {
    companion object {
        const val APP_CONFIG_URL = "config"
    }

    @DecodeErrorBody
    @ExcludeTokens
    @GET(APP_CONFIG_URL)
    suspend fun getAppConfig(): ApiResult<RetrieveConfigResponse, ErrorDto>
}
