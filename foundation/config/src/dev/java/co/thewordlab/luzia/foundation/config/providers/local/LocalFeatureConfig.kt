package co.thewordlab.luzia.foundation.config.providers.local

import android.content.Context
import android.os.Bundle
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.config.FeatureConfig
import co.thewordlab.luzia.foundation.config.FeatureFlag
import co.thewordlab.luzia.foundation.config.di.Logger
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class LocalFeatureConfig @Inject constructor(
    @ApplicationContext private val context: Context,
    private val logger: Logger
) : FeatureConfig {

    private val prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)

    val isLocalConfigEnabled: Boolean get() = prefs.getBoolean(KEY_LOCAL_ENABLED, false)

    override val priority: Int = 0

    override fun initialize() = DO_NOTHING

    @Suppress("UNCHECKED_CAST")
    override suspend fun <T> get(featureFlag: FeatureFlag): T? {
        return getImmediate(featureFlag)
    }

    override fun <T> getImmediate(featureFlag: FeatureFlag): T? {
        return if (isLocalConfigEnabled) {
            logger.log("Local config enabled will return value for ${featureFlag.key}")
            return when (featureFlag.defaultValue) {
                is Boolean -> prefs.getBoolean(featureFlag.key, featureFlag.defaultValue)
                is String -> prefs.getString(featureFlag.key, featureFlag.defaultValue)
                is Long -> prefs.getLong(featureFlag.key, featureFlag.defaultValue)
                is Float -> prefs.getFloat(featureFlag.key, featureFlag.defaultValue)
                is Int -> prefs.getInt(featureFlag.key, featureFlag.defaultValue)
                else -> null
            } as T?
        } else {
            null
        }
    }

    override fun overWriteConfigValue(bundle: Bundle) {
        val flags = bundle.keySet().mapNotNull { key -> FeatureFlag.entries.find { it.key == key } }
        if (flags.isNotEmpty()) {
            setLocalConfigEnabled(true)
            flags.forEach { flag ->
                when (flag.defaultValue) {
                    is String -> save(flag.key, bundle.getString(flag.key)!!)
                    is Boolean -> save(flag.key, bundle.getBoolean(flag.key))
                    else -> DO_NOTHING
                }
            }
        }
    }

    override suspend fun getString(featureFlag: FeatureFlag, parameterName: String): String? {
        return getStringImmediate(featureFlag, parameterName)
    }

    override fun getStringImmediate(featureFlag: FeatureFlag, parameterName: String): String? {
        return if (isLocalConfigEnabled) {
            val payloadKey = "${featureFlag.key}_payload_$parameterName"
            prefs.getString(payloadKey, null)?.also {
                logger.log("Local payload string found for key ${featureFlag.key}, parameter $parameterName: $it")
            }
        } else {
            null
        }
    }

    override suspend fun getInt(featureFlag: FeatureFlag, parameterName: String): Int? {
        return getIntImmediate(featureFlag, parameterName)
    }

    override fun getIntImmediate(featureFlag: FeatureFlag, parameterName: String): Int? {
        return if (isLocalConfigEnabled) {
            val payloadKey = "${featureFlag.key}_payload_$parameterName"
            val defaultValue = -1
            val value = prefs.getInt(payloadKey, defaultValue)
            if (value != defaultValue) {
                logger.log("Local payload int found for key ${featureFlag.key}, parameter $parameterName: $value")
                value
            } else {
                null
            }
        } else {
            null
        }
    }

    fun save(key: String, value: String) {
        prefs.edit().putString(key, value).apply()
    }

    fun save(key: String, value: Boolean) {
        prefs.edit().putBoolean(key, value).apply()
    }

    fun saveString(featureFlag: FeatureFlag, parameterName: String, value: String) {
        val payloadKey = "${featureFlag.key}_payload_$parameterName"
        prefs.edit().putString(payloadKey, value).apply()
    }

    fun saveInt(featureFlag: FeatureFlag, parameterName: String, value: Int) {
        val payloadKey = "${featureFlag.key}_payload_$parameterName"
        prefs.edit().putInt(payloadKey, value).apply()
    }

    fun setLocalConfigEnabled(isEnabled: Boolean) {
        prefs.edit().putBoolean(KEY_LOCAL_ENABLED, isEnabled).apply()
    }

    private companion object {
        const val PREF_NAME = "LOCAL_CONFIG"
        const val KEY_LOCAL_ENABLED = "LOCAL_CONFIG_ENABLED"
    }
}