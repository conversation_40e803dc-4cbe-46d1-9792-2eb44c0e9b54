package co.thewordlab.luzia.foundation.config.di

import co.thewordlab.luzia.foundation.config.FeatureConfig
import co.thewordlab.luzia.foundation.config.providers.local.LocalFeatureConfig
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import dagger.multibindings.IntoSet

@Module
@InstallIn(SingletonComponent::class)
object DevConfigModule {

    @IntoSet
    @Provides
    fun provideLocalFeatureConfig(impl: LocalFeatureConfig): FeatureConfig = impl
}
