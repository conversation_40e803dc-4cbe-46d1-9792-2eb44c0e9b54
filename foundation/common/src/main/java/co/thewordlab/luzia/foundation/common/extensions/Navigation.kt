package co.thewordlab.luzia.foundation.common.extensions

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log

fun Context.onNavigateToGooglePlayStore() {
    try {
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$packageName"))
        startActivity(intent)
    } catch (e: ActivityNotFoundException) {
        Log.d("LuziaApp", "App not found: " + e.message)
        val intent =
            Intent(
                Intent.ACTION_VIEW,
                Uri.parse("https://play.google.com/store/apps/details?id=$packageName")
            )
        startActivity(intent)
    }
}
