package co.thewordlab.luzia.foundation.common.date

import android.content.Context
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import co.thewordlab.luzia.foundation.localization.R as localizationR

fun formatDateForChats(
    context: Context,
    locale: Locale = Locale.getDefault(),
    calendar: Calendar = Calendar.getInstance(),
    now: Calendar = Calendar.getInstance()
): String {
    return if (calendar.isDateInYesterday(now)) {
        context.getString(localizationR.string.yesterday)
    } else if (calendar.isDateInToday(now)) {
        SimpleDateFormat(HourPattern.HOURS_MINUTES.pattern, locale).format(calendar.time)
    } else {
        SimpleDateFormat(DatePattern.SLASHES.pattern, locale).format(calendar.time)
    }
}

private fun Calendar.isDateInYesterday(now: Calendar): Boolean =
    this.get(Calendar.DAY_OF_YEAR) == now.get(Calendar.DAY_OF_YEAR) - 1

private fun Calendar.isDateInToday(now: Calendar): Boolean =
    this.get(Calendar.DAY_OF_YEAR) == now.get(Calendar.DAY_OF_YEAR)
