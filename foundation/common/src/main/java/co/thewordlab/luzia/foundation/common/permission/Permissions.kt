package co.thewordlab.luzia.foundation.common.permission

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.provider.Settings
import androidx.core.app.ActivityCompat.shouldShowRequestPermissionRationale
import androidx.core.content.ContextCompat.checkSelfPermission

fun Activity.notificationPermissionState(): PermissionState {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        val permission = Manifest.permission.POST_NOTIFICATIONS
        val state = checkSelfPermission(this, permission)
        val isGranted = state == PackageManager.PERMISSION_GRANTED
        if (isGranted) {
            PermissionState.Granted
        } else {
            PermissionState.Denied(shouldShowRequestPermissionRationale(this, permission))
        }
    } else {
        PermissionState.Granted
    }
}

fun Activity.openNotificationSettings() {
    Intent(Settings.ACTION_APP_NOTIFICATION_SETTINGS).apply {
        putExtra(Settings.EXTRA_APP_PACKAGE, packageName)
        startActivity(this)
    }
}
