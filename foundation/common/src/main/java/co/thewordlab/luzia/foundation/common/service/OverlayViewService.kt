package co.thewordlab.luzia.foundation.common.service

import android.app.Service
import android.content.Intent
import android.graphics.PixelFormat
import android.os.IBinder
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.platform.ComposeView
import androidx.lifecycle.HasDefaultViewModelProviderFactory
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.SavedStateViewModelFactory
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStore
import androidx.lifecycle.ViewModelStoreOwner
import androidx.lifecycle.setViewTreeLifecycleOwner
import androidx.lifecycle.viewmodel.compose.LocalViewModelStoreOwner
import androidx.savedstate.SavedStateRegistry
import androidx.savedstate.SavedStateRegistryController
import androidx.savedstate.SavedStateRegistryOwner
import androidx.savedstate.setViewTreeSavedStateRegistryOwner

open class OverlayViewService :
    Service(),
    LifecycleOwner,
    SavedStateRegistryOwner,
    ViewModelStoreOwner,
    HasDefaultViewModelProviderFactory {

    private var overlayView: View? = null
    private lateinit var windowManager: WindowManager
    lateinit var params: WindowManager.LayoutParams
    lateinit var layoutInflater: LayoutInflater

    private val registry = LifecycleRegistry(this)
    private val registryController = SavedStateRegistryController.create(this)

    override val savedStateRegistry: SavedStateRegistry = registryController.savedStateRegistry

    override val lifecycle: Lifecycle = registry

    override fun onCreate() {
        super.onCreate()
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater
        registryController.performAttach()
        registryController.performRestore(null)
        registry.handleLifecycleEvent(Lifecycle.Event.ON_CREATE)
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    protected fun updateLayout() {
        runCatching {
            windowManager.updateViewLayout(overlayView, params)
        }
    }

    protected fun showLegacyView(
        view: View,
        width: Int = WindowManager.LayoutParams.WRAP_CONTENT,
        height: Int = WindowManager.LayoutParams.WRAP_CONTENT,
    ) {
        removeOverlay()
        overlayView = view
        params = createLayoutParams(
            width = width,
            height = height,
            isDraggable = true,
            initialX = if (::params.isInitialized) params.x else null,
            initialY = if (::params.isInitialized) params.y else null
        )
        windowManager.addView(overlayView, params)
    }

    protected fun showComposeView(
        content: @Composable () -> Unit,
        backgroundColor: Int? = null
    ) {
        removeOverlay()
        params = createLayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            isDraggable = false,
            initialX = if (::params.isInitialized) params.x else null,
            initialY = if (::params.isInitialized) params.y else null
        )

        backgroundColor?.let {
            // Add flags to make the window draw under system bars
            params.flags = params.flags or
                WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR or
                WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS or
                WindowManager.LayoutParams.FLAG_DIM_BEHIND
            val alpha = (it shr ALPHA_SHIFT) and ALPHA_MASK
            params.dimAmount = alpha / ALPHA_MAX_VALUE
        }

        registry.handleLifecycleEvent(Lifecycle.Event.ON_START)
        registry.handleLifecycleEvent(Lifecycle.Event.ON_RESUME)
        overlayView = ComposeView(this).apply {
            setViewTreeLifecycleOwner(this@OverlayViewService)
            setViewTreeSavedStateRegistryOwner(this@OverlayViewService)
            setContent {
                CompositionLocalProvider(LocalViewModelStoreOwner provides this@OverlayViewService) {
                    content()
                }
            }
        }
        windowManager.addView(overlayView, params)
    }

    protected fun removeOverlay() {
        runCatching { overlayView?.let { windowManager.removeView(it) } }
    }

    private fun createLayoutParams(
        width: Int,
        height: Int,
        isDraggable: Boolean,
        initialX: Int? = null,
        initialY: Int? = null
    ): WindowManager.LayoutParams {
        val overlayType = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        var flags = if (isDraggable) WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE else 0

        // For full-screen overlay (not draggable), add flags to extend under system bars
        if (!isDraggable) {
            flags = 0 or
                WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS
        }

        return WindowManager.LayoutParams(
            width,
            height,
            overlayType,
            flags,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
            x = initialX ?: (if (::params.isInitialized) params.x else DEFAULT_COORDINATE)
            y = initialY ?: (if (::params.isInitialized) params.y else DEFAULT_COORDINATE)
            // Set soft input mode to resize the window when keyboard appears
            if (!isDraggable) {
                softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        registry.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY)
        overlayView?.let {
            if (it.windowToken != null) {
                runCatching { windowManager.removeView(it) }
            }
            overlayView = null
        }
    }

    override val viewModelStore: ViewModelStore = ViewModelStore()
    override val defaultViewModelProviderFactory: ViewModelProvider.Factory
        get() = SavedStateViewModelFactory(application, this)

    private companion object {
        const val ALPHA_SHIFT = 24
        const val ALPHA_MASK = 0xFF
        const val ALPHA_MAX_VALUE = 255f
        const val DEFAULT_COORDINATE = 0
    }
}
