package co.thewordlab.luzia.foundation.common.extensions

import android.app.Activity
import android.app.Activity.OVERRIDE_TRANSITION_OPEN
import android.os.Build
import androidx.annotation.AnimRes

fun Activity?.animateActivityTransition(@AnimRes enterAnim: Int, @AnimRes exitAnim: Int) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
        this?.overrideActivityTransition(
            OVERRIDE_TRANSITION_OPEN,
            enterAnim,
            exitAnim
        )
    } else {
        this?.overridePendingTransition(enterAnim, exitAnim)
    }
}
