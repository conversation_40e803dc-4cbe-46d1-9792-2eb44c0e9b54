package co.thewordlab.luzia.foundation.common.di

import android.util.Log
import co.thewordlab.luzia.foundation.common.BuildConfig
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob

@Module
@InstallIn(SingletonComponent::class)
object CommonModule {
    @Provides
    fun provideDispatcher(): CoroutineDispatcher = Dispatchers.IO

    @Provides
    fun provideCoroutineExceptionHandler(): CoroutineExceptionHandler =
        CoroutineExceptionHandler { _, throwable ->
            if (BuildConfig.DEBUG) {
                Log.e(
                    "CoroutineExceptionHandler",
                    "CoroutineExceptionHandler: ${throwable.message}"
                )
            }
        }

    @Provides
    fun provideCoroutineScope(
        dispatcher: CoroutineDispatcher,
        exceptionHandler: CoroutineExceptionHandler
    ): CoroutineScope = CoroutineScope(dispatcher + SupervisorJob() + exceptionHandler)
}
