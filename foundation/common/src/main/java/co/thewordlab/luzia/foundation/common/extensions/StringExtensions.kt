package co.thewordlab.luzia.foundation.common.extensions

import java.util.Locale

fun String.capitalize() = replaceFirstChar {
    if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString()
}

fun String.capitalizeWords(skipLabels: List<String> = emptyList()): String {
    val lowercase = this.lowercase()
    return lowercase.replace(Regex("\\b(\\w)")) { matchResult ->
        if (skipLabels.contains(matchResult.groupValues[1])) {
            matchResult.groupValues[1]
        } else {
            matchResult.groupValues[1].uppercase()
        }
    }
}

fun removeQueryParameter(url: String, keyToRemove: String): String {
    val regex = Regex("([?&])$keyToRemove(?:=[^&]*)?(?:&|$)")
    val modifiedUrl =
        url.replace(regex) {
            if (it.value.endsWith("&")) {
                "?"
            } else {
                ""
            }
        }
    return modifiedUrl
}

/**
 * Converts markdown text to HTML.
 *
 * @return HTML representation of the markdown text
 */
@Suppress("LongMethod")
fun String.markdownToHTML(): String {
    var html = this

    // Handle code blocks first (to avoid processing markdown inside them)
    html = html.replace(
        Regex("(?s)```(\\w+)?\\n?(.*?)```"),
        "<pre><code>$2</code></pre>"
    )

    // Handle inline code
    html = html.replace(
        Regex("`([^`]+)`"),
        "<code>$1</code>"
    )

    // Handle headers (from h6 to h1 to avoid conflicts)
    html = html.replace(Regex("(?m)^###### (.+)$"), "<h6>$1</h6>")
    html = html.replace(Regex("(?m)^##### (.+)$"), "<h5>$1</h5>")
    html = html.replace(Regex("(?m)^#### (.+)$"), "<h4>$1</h4>")
    html = html.replace(Regex("(?m)^### (.+)$"), "<h3>$1</h3>")
    html = html.replace(Regex("(?m)^## (.+)$"), "<h2>$1</h2>")
    html = html.replace(Regex("(?m)^# (.+)$"), "<h1>$1</h1>")

    // Handle bold and italic
    html = html.replace(
        Regex("\\*\\*\\*(.+?)\\*\\*\\*"),
        "<strong><em>$1</em></strong>"
    )
    html = html.replace(
        Regex("\\*\\*(.+?)\\*\\*"),
        "<strong>$1</strong>"
    )
    html = html.replace(
        Regex("\\*(.+?)\\*"),
        "<em>$1</em>"
    )

    // Process lists before paragraph processing

    // First, mark list items with special tags
    html = html.replace(
        Regex("(?m)^[*\\-+] (.+)$"),
        "@@UNORDERED_LIST_ITEM@@$1@@/UNORDERED_LIST_ITEM@@"
    )

    html = html.replace(
        Regex("(?m)^\\d+\\. (.+)$"),
        "@@ORDERED_LIST_ITEM@@$1@@/ORDERED_LIST_ITEM@@"
    )

    // Group consecutive unordered list items
    html = html.replace(
        Regex("(@@UNORDERED_LIST_ITEM@@.*?@@/UNORDERED_LIST_ITEM@@\\s*)+"),
        { matchResult ->
            val content = matchResult.value
                .replace("@@UNORDERED_LIST_ITEM@@", "<li>")
                .replace("@@/UNORDERED_LIST_ITEM@@", "</li>")
                .replace(Regex("\\s*<li>"), "<li>") // Remove whitespace before <li>
            "<ul>$content</ul>"
        }
    )

    // Group consecutive ordered list items
    html = html.replace(
        Regex("(@@ORDERED_LIST_ITEM@@.*?@@/ORDERED_LIST_ITEM@@\\s*)+"),
        { matchResult ->
            val content = matchResult.value
                .replace("@@ORDERED_LIST_ITEM@@", "<li>")
                .replace("@@/ORDERED_LIST_ITEM@@", "</li>")
                .replace(Regex("\\s*<li>"), "<li>") // Remove whitespace before <li>
            "<ol>$content</ol>"
        }
    )

    // Handle blockquotes
    html = html.replace(
        Regex("(?m)^> (.+)$"),
        "<blockquote>$1</blockquote>"
    )

    // Handle links
    html = html.replace(
        Regex("\\[([^\\]]+)\\]\\(([^\\)]+)\\)"),
        "<a href=\"$2\">$1</a>"
    )

    // Define block-level HTML elements that shouldn't be wrapped in paragraphs
    val blockElements = listOf(
        "h1", "h2", "h3", "h4", "h5", "h6",
        "ul", "ol", "blockquote", "pre", "table", "div"
    )

    // Check if a string is a standalone block-level HTML element
    fun isBlockElement(text: String): Boolean {
        // Special case for headers - they should always be treated as block elements
        if (text.matches(Regex("<h[1-6][^>]*>.*?</h[1-6]>", RegexOption.DOT_MATCHES_ALL))) {
            return true
        }

        // Check if the text matches any of the block element patterns
        return blockElements.any { element ->
            text.matches(Regex("<$element[^>]*>.*?</$element>", RegexOption.DOT_MATCHES_ALL))
        }
    }

    // Check if a string contains any block-level HTML elements
    fun containsBlockElement(text: String): Boolean {
        // Special case for headers - they should always be treated as block elements
        if (text.contains(Regex("<h[1-6][^>]*>.*?</h[1-6]>", RegexOption.DOT_MATCHES_ALL))) {
            return true
        }

        return blockElements.any { element ->
            text.contains(Regex("<$element[^>]*>.*?</$element>", RegexOption.DOT_MATCHES_ALL))
        }
    }

    // Convert double line breaks to paragraph breaks
    val paragraphs = html.split("\n\n")
        .map { paragraph ->
            val trimmed = paragraph.trim()
            if (trimmed.isEmpty()) {
                ""
            } else if (isBlockElement(trimmed)) {
                // Don't wrap standalone block-level elements
                trimmed
            } else if (!containsBlockElement(trimmed)) {
                // No block elements, wrap the whole content in paragraph
                "<p>$trimmed</p>"
            } else {
                // Mixed content with block elements and text
                // We need to handle this case specially

                // First, handle the case where the content starts with a block element
                var result = trimmed
                for (element in blockElements) {
                    val pattern = Regex("^(<$element[^>]*>.*?</$element>)(.*)", RegexOption.DOT_MATCHES_ALL)
                    if (pattern.matches(result)) {
                        result = pattern.replace(result) { matchResult ->
                            val blockElement = matchResult.groupValues[1]
                            val remainingText = matchResult.groupValues[2].trim()
                            if (remainingText.isEmpty()) {
                                blockElement
                            } else {
                                "$blockElement\n<p>$remainingText</p>"
                            }
                        }
                        // We've handled this case, so return the result
                        return@map result
                    }
                }

                // If we get here, the content doesn't start with a block element
                // Wrap it in paragraph tags
                "<p>$trimmed</p>"
            }
        }
        .filter { it.isNotEmpty() }

    html = paragraphs.joinToString("\n")

    // Clean up multiple consecutive HTML tags
    html = html.replace(
        Regex("</ul>\\s*<ul>"),
        ""
    )
    html = html.replace(
        Regex("</ol>\\s*<ol>"),
        ""
    )

    return html
}
