package co.thewordlab.luzia.foundation.common.extensions

import android.content.ContentResolver
import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.provider.MediaStore
import android.util.Base64
import android.util.Log
import androidx.core.content.FileProvider
import androidx.exifinterface.media.ExifInterface
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

private const val IMG_QUALITY = 90

object ImageUtils {
    fun getImageContentUri(imageFile: File, contentResolver: ContentResolver): Uri? {
        val filePath = imageFile.absolutePath
        val projection = arrayOf(MediaStore.Images.Media._ID)
        val selection = "${MediaStore.Images.Media.DATA} = ?"
        val selectionArgs = arrayOf(filePath)

        contentResolver.query(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            projection,
            selection,
            selectionArgs,
            null
        )?.use { cursor ->
            if (cursor.moveToFirst()) {
                val columnIndex = cursor.getColumnIndex(MediaStore.Images.Media._ID)
                if (columnIndex != -1) {
                    val imageId = cursor.getLong(columnIndex)
                    return Uri.withAppendedPath(
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                        imageId.toString()
                    )
                }
            }
        }
        return null
    }

    fun saveImage(context: Context, bitmap: Bitmap, index: Int? = null): Uri? {
        val imagesFolder = File(context.cacheDir, "images")
        var uri: Uri? = null
        try {
            imagesFolder.mkdirs()
            val i =
                if (index != null) {
                    "_$index"
                } else {
                    ""
                }
            val file = File(imagesFolder, "shared_image$i.png")
            val stream = FileOutputStream(file)
            bitmap.compress(Bitmap.CompressFormat.PNG, IMG_QUALITY, stream)
            stream.flush()
            stream.close()
            uri = FileProvider.getUriForFile(context, "${context.packageName}.fileprovider", file)
        } catch (e: IOException) {
            Log.d("Error", "IOException while trying to write file for sharing: " + e.message)
        }
        return uri
    }

    fun getDecodedPrompt(image: File): String {
        return try {
            val exif = ExifInterface(image.absolutePath)
            val encodedPrompt = exif.getAttribute(ExifInterface.TAG_IMAGE_DESCRIPTION) ?: ""
            try {
                val recoveredBytes = Base64.decode(encodedPrompt, Base64.DEFAULT)
                String(recoveredBytes, Charsets.UTF_8)
            } catch (expected: Exception) {
                Log.d("LuziaApp", "Error decoding prompt: " + expected.message)
                exif.getAttribute(ExifInterface.TAG_IMAGE_DESCRIPTION) ?: ""
            }
        } catch (e: IOException) {
            Log.d("LuziaApp", "Error decoding prompt: " + e.message)
            ""
        }
    }

    fun getDecodedRequestId(image: File): String {
        return try {
            val exif = ExifInterface(image.absolutePath)
            val encodedRequestId = exif.getAttribute(ExifInterface.TAG_IMAGE_UNIQUE_ID) ?: ""
            try {
                val recoveredBytes = Base64.decode(encodedRequestId, Base64.DEFAULT)
                String(recoveredBytes, Charsets.UTF_8)
            } catch (expected: Exception) {
                Log.d("LuziaApp", "Error decoding request id: " + expected.message)
                exif.getAttribute(ExifInterface.TAG_IMAGE_UNIQUE_ID) ?: ""
            }
        } catch (e: IOException) {
            Log.d("LuziaApp", "Error decoding request id: " + e.message)
            ""
        }
    }
}
