package co.thewordlab.luzia.foundation.common.extensions

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.content.Intent
import android.content.Intent.FLAG_ACTIVITY_NEW_TASK
import co.thewordlab.luzia.foundation.localization.R

fun Context.findActivity(): Activity {
    var context = this
    while (context is ContextWrapper) {
        if (context is Activity) return context
        context = context.baseContext
    }
    error("No activity found")
}

fun Context.shareText(text: String) {
    val intent = Intent(Intent.ACTION_SEND)
    intent.type = "text/plain"
    intent.putExtra(Intent.EXTRA_TEXT, text)
    val chooserIntent =
        Intent.createChooser(intent, getString(R.string.contextual_menu_share_option))
    chooserIntent.setFlags(FLAG_ACTIVITY_NEW_TASK)
    startActivity(chooserIntent)
}
