plugins {
    alias(libs.plugins.luzia.android.library)
    alias(libs.plugins.luzia.android.hilt)
    alias(libs.plugins.luzia.android.library.compose)
    alias(libs.plugins.luzia.android.library.jacoco)
}

android {
    namespace = "co.thewordlab.luzia.foundation.common"
}

dependencies {
    implementation(libs.androidx.dataStore.preferences)
    implementation(libs.androidx.media.exif)
    implementation(libs.google.review)
    implementation(libs.google.review.ktx)
    implementation(projects.foundation.localization)
    implementation(projects.foundation.persistence)

    testImplementation(libs.mockk)
}
