<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="20dp"
    android:height="21dp"
    android:viewportWidth="20"
    android:viewportHeight="21">
  <group>
    <clip-path
        android:pathData="M0,0.772h20v20h-20z"/>
    <path
        android:pathData="M10,13.272C11.381,13.272 12.5,12.153 12.5,10.772C12.5,9.391 11.381,8.272 10,8.272C8.619,8.272 7.5,9.391 7.5,10.772C7.5,12.153 8.619,13.272 10,13.272Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.62996"
        android:fillColor="#00000000"
        android:strokeColor="#121344"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M16.167,13.272C16.056,13.523 16.023,13.802 16.072,14.073C16.121,14.343 16.25,14.592 16.442,14.789L16.492,14.839C16.647,14.993 16.77,15.177 16.854,15.38C16.938,15.582 16.981,15.799 16.981,16.018C16.981,16.237 16.938,16.454 16.854,16.656C16.77,16.858 16.647,17.042 16.492,17.197C16.337,17.352 16.153,17.475 15.951,17.559C15.749,17.643 15.532,17.686 15.313,17.686C15.094,17.686 14.877,17.643 14.674,17.559C14.472,17.475 14.288,17.352 14.134,17.197L14.083,17.147C13.887,16.955 13.638,16.826 13.367,16.777C13.097,16.728 12.818,16.761 12.567,16.872C12.32,16.978 12.11,17.153 11.962,17.377C11.814,17.6 11.735,17.862 11.733,18.13V18.272C11.733,18.714 11.558,19.138 11.245,19.451C10.933,19.763 10.509,19.939 10.067,19.939C9.625,19.939 9.201,19.763 8.888,19.451C8.576,19.138 8.4,18.714 8.4,18.272V18.197C8.394,17.921 8.304,17.654 8.144,17.429C7.983,17.205 7.759,17.034 7.5,16.939C7.249,16.828 6.97,16.795 6.7,16.844C6.429,16.893 6.18,17.021 5.984,17.214L5.933,17.264C5.779,17.419 5.595,17.542 5.393,17.625C5.19,17.709 4.973,17.752 4.754,17.752C4.535,17.752 4.318,17.709 4.116,17.625C3.914,17.542 3.73,17.419 3.575,17.264C3.42,17.109 3.297,16.925 3.213,16.723C3.13,16.52 3.086,16.303 3.086,16.084C3.086,15.865 3.13,15.649 3.213,15.446C3.297,15.244 3.42,15.06 3.575,14.905L3.625,14.855C3.817,14.659 3.946,14.41 3.995,14.139C4.044,13.869 4.011,13.59 3.9,13.339C3.795,13.092 3.619,12.882 3.396,12.734C3.172,12.586 2.91,12.506 2.642,12.505H2.5C2.058,12.505 1.634,12.33 1.322,12.017C1.009,11.705 0.833,11.281 0.833,10.839C0.833,10.397 1.009,9.973 1.322,9.66C1.634,9.348 2.058,9.172 2.5,9.172H2.575C2.851,9.166 3.118,9.076 3.343,8.916C3.567,8.755 3.738,8.531 3.833,8.272C3.944,8.021 3.978,7.742 3.928,7.471C3.879,7.201 3.751,6.952 3.559,6.755L3.509,6.705C3.354,6.551 3.231,6.367 3.147,6.164C3.063,5.962 3.02,5.745 3.02,5.526C3.02,5.307 3.063,5.09 3.147,4.888C3.231,4.686 3.354,4.502 3.509,4.347C3.663,4.192 3.847,4.069 4.049,3.985C4.252,3.901 4.469,3.858 4.688,3.858C4.907,3.858 5.124,3.901 5.326,3.985C5.528,4.069 5.712,4.192 5.867,4.347L5.917,4.397C6.113,4.589 6.363,4.718 6.633,4.767C6.903,4.816 7.182,4.783 7.433,4.672H7.5C7.747,4.566 7.957,4.391 8.105,4.167C8.253,3.944 8.332,3.682 8.333,3.414V3.272C8.333,2.83 8.509,2.406 8.822,2.093C9.134,1.781 9.558,1.605 10,1.605C10.442,1.605 10.866,1.781 11.179,2.093C11.491,2.406 11.667,2.83 11.667,3.272V3.347C11.668,3.615 11.747,3.877 11.895,4.101C12.043,4.324 12.254,4.5 12.5,4.605C12.752,4.716 13.03,4.749 13.301,4.7C13.571,4.651 13.82,4.522 14.017,4.33L14.067,4.28C14.222,4.125 14.405,4.002 14.608,3.919C14.81,3.835 15.027,3.792 15.246,3.792C15.465,3.792 15.682,3.835 15.884,3.919C16.087,4.002 16.27,4.125 16.425,4.28C16.58,4.435 16.703,4.619 16.787,4.821C16.871,5.024 16.914,5.24 16.914,5.459C16.914,5.679 16.871,5.895 16.787,6.098C16.703,6.3 16.58,6.484 16.425,6.639L16.375,6.689C16.183,6.885 16.054,7.134 16.005,7.405C15.956,7.675 15.989,7.954 16.1,8.205V8.272C16.206,8.518 16.381,8.729 16.605,8.877C16.828,9.025 17.09,9.104 17.358,9.105H17.5C17.942,9.105 18.366,9.281 18.679,9.593C18.991,9.906 19.167,10.33 19.167,10.772C19.167,11.214 18.991,11.638 18.679,11.95C18.366,12.263 17.942,12.439 17.5,12.439H17.425C17.157,12.44 16.895,12.519 16.671,12.667C16.448,12.815 16.272,13.026 16.167,13.272V13.272Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.62996"
        android:fillColor="#00000000"
        android:strokeColor="#121344"
        android:strokeLineCap="round"/>
  </group>
</vector>
