<vector xmlns:android="http://schemas.android.com/apk/res/android" android:height="23.04dp" android:viewportHeight="24" android:viewportWidth="25" android:width="24dp">
      
    <path android:fillColor="#00000000" android:fillType="evenOdd" android:pathData="M14.875,7.375C14.875,8.687 13.812,9.75 12.5,9.75C11.188,9.75 10.125,8.687 10.125,7.375C10.125,6.063 11.188,5 12.5,5C13.812,5 14.875,6.063 14.875,7.375Z" android:strokeColor="#000000" android:strokeLineCap="round" android:strokeLineJoin="round" android:strokeWidth="1.5"/>
      
    <path android:fillColor="#00000000" android:fillType="evenOdd" android:pathData="M17.25,15.775C17.25,17.575 15.123,19.042 12.5,19.042C9.877,19.042 7.75,17.579 7.75,15.775C7.75,13.971 9.877,12.509 12.5,12.509C15.123,12.509 17.25,13.971 17.25,15.775Z" android:strokeColor="#000000" android:strokeLineCap="round" android:strokeLineJoin="round" android:strokeWidth="1.5"/>
      
    <path android:fillColor="#00000000" android:fillType="evenOdd" android:pathData="M19.9,9.553C19.91,10.132 19.569,10.659 19.038,10.887C18.506,11.116 17.889,11 17.476,10.594C17.064,10.189 16.937,9.574 17.156,9.039C17.375,8.503 17.896,8.153 18.475,8.153C19.255,8.146 19.893,8.773 19.9,9.553V9.553Z" android:strokeColor="#000000" android:strokeLineCap="round" android:strokeLineJoin="round" android:strokeWidth="1.5"/>
      
    <path android:fillColor="#00000000" android:fillType="evenOdd" android:pathData="M5.1,9.553C5.09,10.132 5.431,10.659 5.962,10.887C6.494,11.116 7.111,11 7.523,10.594C7.936,10.189 8.063,9.574 7.844,9.039C7.625,8.503 7.104,8.153 6.525,8.153C5.745,8.146 5.107,8.773 5.1,9.553Z" android:strokeColor="#000000" android:strokeLineCap="round" android:strokeLineJoin="round" android:strokeWidth="1.5"/>
      
    <path android:fillColor="#000000" android:pathData="M19.217,17.362C18.804,17.325 18.44,17.629 18.403,18.042C18.366,18.455 18.67,18.819 19.083,18.856L19.217,17.362ZM22,15.775L22.746,15.857C22.751,15.802 22.751,15.747 22.746,15.693L22,15.775ZM19.083,12.695C18.67,12.732 18.366,13.096 18.403,13.509C18.44,13.922 18.804,14.226 19.217,14.189L19.083,12.695ZM5.917,18.856C6.329,18.819 6.634,18.455 6.597,18.042C6.56,17.629 6.196,17.325 5.783,17.362L5.917,18.856ZM3,15.775L2.254,15.693C2.248,15.747 2.248,15.802 2.254,15.857L3,15.775ZM5.783,14.189C6.196,14.226 6.56,13.922 6.597,13.509C6.634,13.096 6.329,12.732 5.917,12.695L5.783,14.189ZM19.083,18.856C20.917,19.02 22.545,17.687 22.746,15.857L21.254,15.693C21.143,16.712 20.237,17.453 19.217,17.362L19.083,18.856ZM22.746,15.693C22.544,13.863 20.916,12.531 19.083,12.695L19.217,14.189C20.237,14.098 21.142,14.839 21.254,15.857L22.746,15.693ZM5.783,17.362C4.763,17.453 3.857,16.712 3.746,15.693L2.254,15.857C2.455,17.687 4.083,19.02 5.917,18.856L5.783,17.362ZM3.745,15.857C3.857,14.839 4.763,14.098 5.783,14.189L5.917,12.695C4.083,12.531 2.456,13.863 2.254,15.693L3.745,15.857Z"/>
    
</vector>
