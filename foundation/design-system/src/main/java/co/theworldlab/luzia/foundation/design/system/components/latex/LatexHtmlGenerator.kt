package co.theworldlab.luzia.foundation.design.system.components.latex

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import co.thewordlab.luzia.foundation.common.extensions.markdownToHTML
import co.thewordlab.luzia.foundation.common.extensions.toHex

/**
 * Generator for HTML content that renders LaTeX using MathJax.
 */
@Suppress("LongMethod", "MagicNumber")
object LatexHtmlGenerator {

    /**
     * Generates HTML for rendering LaTeX content with MathJax.
     *
     * @param latex The LaTeX content to render
     * @param textColor The color to use for the text
     * @return HTML string that can be loaded in a WebView
     */
    fun generateHTML(
        latex: String,
        textColor: Color,
        highlightColor: Color,
    ): String {
        val htmlBody = latex.markdownToHTML()
        val textColorHex = textColor.toArgb().toHex()
        val highlightColorHex = highlightColor.toArgb().toHex()
        return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
                <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
                <!-- MathJax configuration matching Luzia web implementation -->
                <script>
                    window.MathJax = {
                      tex: {
                        inlineMath: [
                          ['${'$'}', '${'$'}'],       // ${'$'}...${'$'} for inline math
                          ['\\\\(', '\\\\)'],    // \\(...\\) for inline math
                          ['\\(', '\\)'],    // \(...\) for inline math
                        ],
                        displayMath: [
                          ['${'$'}${'$'}', '${'$'}${'$'}'],     // ${'$'}${'$'}...${'$'}${'$'} for display math
                          ['\\\\[', '\\\\]'],    // \\[...\\] for display math
                          ['\\[', '\\]']    //\[...\] for display math
                        ],
                        processEscapes: true,
                        processEnvironments: true
                      },
                      options: {
                        skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'code'],
                        processHtmlClass: 'tex2jax_process'
                      },
                      startup: {
                        pageReady() {
                          return MathJax.startup.defaultPageReady().then(() => {
                            // Force typeset after page is ready
                            return MathJax.typesetPromise();
                          });
                        }
                      }
                    };
                </script>
                <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
            
                <!-- CSS styles optimized for mobile -->
                <style>
                    @font-face {
                        font-family: 'Plus Jakarta Regular';
                        src: url(file:///android_asset/plus_jakarta_regular.ttf);
                        font-weight: 400;
                        font-style: normal
                    }
                    body {
                      margin: 0;
                      padding: 8px;
                      font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
                      font-size: 17px;
                      color: $textColorHex;
            
                      line-height: 1.2;
                      word-wrap: break-word;
                      overflow-wrap: break-word;
                    }
            
                    mark {
                        background-color: $highlightColorHex;
                        color: $textColorHex
                    }
            
                    /* MathJax container styling for CHTML output */
                    mjx-container[jax="CHTML"] {
                      display: inline-block !important;
                      overflow-x: auto !important;
                      overflow-y: hidden !important;
                      max-width: 100% !important;
                      vertical-align: middle;
                      font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
                    }
            
                    /* Display math styling */
                    mjx-container[display="true"] {
                      display: block !important;
                      text-align: center;
                      margin: 1em 0;
                      overflow-x: auto !important;
                      font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
                    }
            
                    /* Ensure math elements inherit text color */
                    mjx-container *,
                    mjx-container[jax="CHTML"] * {
                      color: inherit !important;
                    }
            
                    /* Markdown elements styling */
                    h1, h2, h3, h4, h5, h6 {
                      color: inherit;
                      margin: 1em 0 0.5em 0;
                      font-weight: 600;
                      line-height: 1.2;
                      font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
                    }
            
                    h1 { font-size: 1.8em; font-weight: 700; }
                    h2 { font-size: 1.5em; font-weight: 700; }
                    h3 { font-size: 1.3em; font-weight: 600; }
                    h4 { font-size: 1.1em; font-weight: 600; }
                    h5 { font-size: 1em; font-weight: 600; }
                    h6 { font-size: 0.9em; font-weight: 600; }
            
                    p {
                      margin: 0.8em 0;
                      line-height: 1.2;
                      font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
                    }
            
                    strong, b {
                      font-weight: 600;
                      font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
                    }
            
                    em, i {
                      font-style: italic;
                      font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
                    }
            
                    /* Code blocks */
                    pre {
                      padding: 12px;
                      border-radius: 6px;
                      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
                      font-size: 14px;
                      background-color: rgba(128, 128, 128, 0.1);
                      overflow-x: auto;
                      white-space: pre-wrap;
                      margin: 1em 0;
                      line-height: 1.2;
                    }
            
                    code {
                      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
                      font-size: 0.9em;
                      background-color: rgba(128, 128, 128, 0.1);
                      padding: 2px 4px;
                      border-radius: 3px;
                    }
            
                    /* Lists */
                    ul, ol {
                      margin: 0.8em 0;
                      padding-left: 1.5em;
                      font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
                    }
            
                    li {
                      margin: 0.3em 0;
                      font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
                    }
            
                    /* Blockquotes */
                    blockquote {
                      margin: 1em 0;
                      padding: 0 1em;
                      border-left: 3px solid rgba(128, 128, 128, 0.3);
                      color: $textColorHex;
                      font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
                      font-style: italic;
                    }
            
                    /* Links */
                    a {
                      color: #007AFF;
                      text-decoration: none;
                      font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
                    }
            
                    a:hover {
                      text-decoration: underline;
                    }
            
                    /* Tables */
                    table {
                      border-collapse: collapse;
                      width: 100%;
                      margin: 1em 0;
                      font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
                    }
            
                    th, td {
                      border: 1px solid rgba(128, 128, 128, 0.3);
                      padding: 8px 12px;
                      text-align: left;
                      font-family: 'Plus Jakarta Regular', 'Helvetica Neue', sans-serif;
                    }
            
                    th {
                      background-color: rgba(128, 128, 128, 0.1);
                      font-weight: 600;
                    }
            
                    /* Hide scrollbars */
                    * {
                      scrollbar-width: none;
                      -ms-overflow-style: none;
                    }
            
                    *::-webkit-scrollbar {
                      display: none;
                    }
            
                    /* Responsive adjustments */
                    @media (max-width: 480px) {
                      body {
                        font-size: 16px;
                        padding: 6px;
                      }
            
                      h1 { font-size: 1.6em; }
                      h2 { font-size: 1.4em; }
                      h3 { font-size: 1.2em; }
                    }
                </style>
            </head>
            <body class="tex2jax_process">
            $htmlBody
            <!-- Ensure MathJax processes the content -->
            <script>
                if (window.MathJax && window.MathJax.typesetPromise) {
                  window.MathJax.typesetPromise().catch(function (err) {
                    console.log('MathJax typeset failed: ' + err.message);
                  });
                }
            </script>
            </body>
            </html>
        """.trimIndent()
    }
}
