package co.theworldlab.luzia.foundation.design.system.legacy.composables

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun ItemSearchList(
    modifier: Modifier = Modifier,
    code: String,
    nameDisplay: String,
    isSelected: Boolean = false,
    onClickListener: (String) -> Unit = {},
    showRightIcon: Boolean = true
) {
    Box(
        modifier =
        modifier
            .fillMaxWidth()
            .click { onClickListener(code) }
            .padding(horizontal = Spacing.X16.dp)
    ) {
        Row(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp)
        ) {
            LuziaText(
                modifier =
                Modifier
                    .height(height = 24.dp)
                    .weight(1f),
                text = nameDisplay,
                color = LuziaTheme.palette.text.primary,
                style =
                if (isSelected) {
                    LuziaTheme.typography.body.semiBold.default
                } else {
                    LuziaTheme.typography.body.regular.default
                }
            )
            if (isSelected && showRightIcon) {
                Box(modifier = Modifier.padding(horizontal = 16.dp)) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_check),
                        contentDescription = null,
                        modifier =
                        Modifier
                            .size(24.dp),
                        tint = LuziaTheme.palette.interactive.brand
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true, widthDp = 600, heightDp = 600)
@Composable
fun ItemSearchListPreview() {
    Column {
        ItemSearchList(
            code = "ES",
            nameDisplay = "+34 (Spain)",
            isSelected = false
        )
        ItemSearchList(
            code = "ES",
            nameDisplay = "+34 (Spain)",
            isSelected = true
        )
        ItemSearchList(
            code = "ES",
            nameDisplay = "+34 (Spain)",
            isSelected = true,
            showRightIcon = false
        )
    }
}
