package co.theworldlab.luzia.foundation.design.system.components.scaffold

import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.compositionLocalOf
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers

val LocalAppState = compositionLocalOf {
    AppState(
        coroutineScope = CoroutineScope(Dispatchers.Main),
        snackBar = SnackbarHostState(),
        onActivityResultRequest = {}
    )
}
