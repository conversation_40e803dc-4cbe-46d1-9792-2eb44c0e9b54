package co.theworldlab.luzia.foundation.design.system.components.message

import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.unit.dp

fun Modifier.chatBubble(isAi: <PERSON><PERSON><PERSON>, color: Color): Modifier {
    return this.then(
        Modifier.drawBehind {
            val width = drawContext.size.width
            val height = drawContext.size.height
            val triangleSize = 8.dp.toPx()
            val cornerRadius = 12.dp.toPx()
            val path =
                Path().apply {
                    if (isAi) {
                        moveTo(-triangleSize, 0f)
                        lineTo(width - cornerRadius, 0f)
                        arcTo(
                            rect = Rect(width - cornerRadius * 2, 0f, width, cornerRadius * 2),
                            startAngleDegrees = 270f,
                            sweepAngleDegrees = 90f,
                            forceMoveTo = false
                        )
                        lineTo(width, height - cornerRadius)
                        arcTo(
                            rect = Rect(width - cornerRadius * 2, height - cornerRadius * 2, width, height),
                            startAngleDegrees = 0f,
                            sweepAngleDegrees = 90f,
                            forceMoveTo = false
                        )
                        lineTo(cornerRadius, height)
                        arcTo(
                            rect = Rect(0f, height - cornerRadius * 2, cornerRadius * 2, height),
                            startAngleDegrees = 90f,
                            sweepAngleDegrees = 90f,
                            forceMoveTo = false
                        )
                        lineTo(0f, triangleSize)
                        close()
                    } else {
                        moveTo(0f, cornerRadius)
                        arcTo(
                            rect = Rect(0f, 0f, cornerRadius * 2, cornerRadius * 2),
                            startAngleDegrees = 180f,
                            sweepAngleDegrees = 90f,
                            forceMoveTo = false
                        )
                        lineTo(width + triangleSize, 0f)
                        lineTo(width, triangleSize)
                        lineTo(width, height - cornerRadius)
                        arcTo(
                            rect = Rect(width - cornerRadius * 2, height - cornerRadius * 2, width, height),
                            startAngleDegrees = 0f,
                            sweepAngleDegrees = 90f,
                            forceMoveTo = false
                        )
                        lineTo(cornerRadius, height)
                        arcTo(
                            rect = Rect(0f, height - cornerRadius * 2, cornerRadius * 2, height),
                            startAngleDegrees = 90f,
                            sweepAngleDegrees = 90f,
                            forceMoveTo = false
                        )
                        close()
                    }
                }
            drawPath(path, color)
        }
    )
}
