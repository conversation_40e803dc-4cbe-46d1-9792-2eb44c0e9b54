package co.theworldlab.luzia.foundation.design.system.components.lds.list

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import co.theworldlab.luzia.foundation.design.system.components.image.IconTint
import co.theworldlab.luzia.foundation.design.system.components.image.LuziaImageUiModel
import co.theworldlab.luzia.foundation.design.system.components.image.LuziaImageView
import co.theworldlab.luzia.foundation.design.system.components.selection.LuziaRadioIcon
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR

@Composable
fun LuziaListItemView(type: LuziaListItemType, modifier: Modifier = Modifier) {
    when (type) {
        is LuziaListItemType.BottomSheet -> BottomSheetListItemView(type, modifier)
        is LuziaListItemType.Icon -> IconListItemView(type, modifier)
        is LuziaListItemType.Cta -> CtaListItemView(type, modifier)
        is LuziaListItemType.Alternate -> AlternateListItemView(type, modifier)
        is LuziaListItemType.Radio -> RadioListItemView(type, modifier)
        is LuziaListItemType.Time -> TimeListItemView(type, modifier)
    }
}

@Composable
private fun BottomSheetListItemView(
    type: LuziaListItemType.BottomSheet,
    modifier: Modifier = Modifier
) {
    LuziaInternalListItem(
        modifier = modifier,
        horizontalSpacing = Spacing.X4.dp,
        headlineContent = {
            LuziaText(
                text = type.title,
                style = LuziaTheme.typography.body.semiBold.default,
                color = LuziaTheme.palette.text.primary
            )
        },
        supportingContent = {},
        leadingContent = { ListItemLeadingImageView(type.image, Spacing.X48.dp) },
        trailingContent = {}
    )
}

@Composable
private fun IconListItemView(
    type: LuziaListItemType.Icon,
    modifier: Modifier = Modifier
) {
    LuziaInternalListItem(
        modifier = modifier,
        horizontalSpacing = Spacing.X8.dp,
        verticalSpacing = Spacing.X2.dp,
        headlineContent = {
            LuziaText(
                text = type.title,
                style = LuziaTheme.typography.body.semiBold.default,
                color = LuziaTheme.palette.text.primary
            )
        },
        supportingContent = {
            LuziaText(
                text = type.description,
                style = LuziaTheme.typography.body.regular.small,
                color = LuziaTheme.palette.text.secondary
            )
        },
        leadingContent = {
            ListItemLeadingImageView(
                model = type.image,
                size = Spacing.X48.dp,
                modifier = Modifier
                    .clip(CircleShape)
                    .background(LuziaTheme.palette.interactive.brandLight)
            )
        },
        trailingContent = {}
    )
}

@Composable
private fun CtaListItemView(
    type: LuziaListItemType.Cta,
    modifier: Modifier = Modifier
) {
    LuziaListItemWithTrailingView(
        modifier = modifier,
        title = type.title,
        description = type.description,
        leadingImage = type.leadingImage,
        trailingContent = {
            LuziaImageView(
                model = type.trailingImage,
                modifier = Modifier.size(Spacing.X24.dp),
            )
        }
    )
}

@Composable
private fun AlternateListItemView(
    type: LuziaListItemType.Alternate,
    modifier: Modifier = Modifier
) {
    LuziaInternalListItem(
        modifier = modifier,
        horizontalSpacing = Spacing.X16.dp,
        verticalSpacing = Spacing.X2.dp,
        headlineContent = {
            LuziaText(
                text = type.title,
                style = LuziaTheme.typography.body.regular.footnote,
                color = LuziaTheme.palette.text.secondary
            )
        },
        supportingContent = {
            LuziaText(
                text = type.description,
                style = LuziaTheme.typography.body.semiBold.small,
                color = LuziaTheme.palette.text.primary
            )
        },
        leadingContent = {
            ListItemLeadingImageView(
                model = type.leadingImage,
                size = Spacing.X40.dp,
                modifier = Modifier
                    .clip(CircleShape)
                    .background(LuziaTheme.palette.surface.content)
            )
        },
        trailingContent = {
            LuziaImageView(
                model = type.trailingImage,
                modifier = Modifier.size(Spacing.X24.dp)
            )
        }
    )
}

@Composable
private fun RadioListItemView(
    type: LuziaListItemType.Radio,
    modifier: Modifier = Modifier
) {
    LuziaListItemWithTrailingView(
        modifier = modifier,
        title = type.title,
        description = type.description,
        leadingImage = type.leadingImage,
        trailingContent = {
            LuziaRadioIcon(selected = type.isSelected)
        }
    )
}

@Composable
private fun TimeListItemView(
    type: LuziaListItemType.Time,
    modifier: Modifier = Modifier
) {
    LuziaListItemWithTrailingView(
        modifier = modifier,
        title = type.title,
        description = type.description,
        leadingImage = type.leadingImage,
        trailingContent = {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
            ) {
                LuziaText(
                    text = type.time,
                    style = LuziaTheme.typography.body.semiBold.caption,
                    color = LuziaTheme.palette.text.helper
                )
                if (type.badgeCount > 0) {
                    Box(
                        modifier = Modifier
                            .height(Spacing.X16.dp)
                            .widthIn(min = Spacing.X16.dp)
                            .background(LuziaTheme.palette.interactive.brand, CircleShape),
                        contentAlignment = Alignment.Center
                    ) {
                        LuziaText(
                            text = type.badgeCount.toString(),
                            style = LuziaTheme.typography.body.semiBold.caption,
                            color = LuziaTheme.palette.text.contrast
                        )
                    }
                }
            }
        }
    )
}

@Composable
private fun LuziaInternalListItem(
    modifier: Modifier = Modifier,
    headlineContent: @Composable () -> Unit,
    supportingContent: @Composable () -> Unit,
    leadingContent: @Composable () -> Unit,
    trailingContent: @Composable () -> Unit,
    horizontalSpacing: Dp = 0.dp,
    verticalSpacing: Dp = 0.dp
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(horizontalSpacing),
        verticalAlignment = Alignment.CenterVertically
    ) {
        leadingContent()
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(verticalSpacing)
        ) {
            headlineContent()
            supportingContent()
        }
        trailingContent()
    }
}

@Composable
private fun ListItemLeadingImageView(
    model: LuziaImageUiModel,
    size: Dp,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.size(size),
        contentAlignment = Alignment.Center
    ) {
        LuziaImageView(
            modifier = Modifier.size(Spacing.X24.dp),
            model = model
        )
    }
}

@Composable
private fun LuziaListItemWithTrailingView(
    title: String,
    description: String,
    leadingImage: LuziaImageUiModel,
    trailingContent: @Composable () -> Unit,
    modifier: Modifier = Modifier
) {
    LuziaInternalListItem(
        modifier = modifier,
        horizontalSpacing = Spacing.X16.dp,
        verticalSpacing = Spacing.X2.dp,
        headlineContent = {
            LuziaText(
                text = title,
                style = LuziaTheme.typography.body.semiBold.small,
                color = LuziaTheme.palette.text.primary
            )
        },
        supportingContent = {
            LuziaText(
                text = description,
                style = LuziaTheme.typography.body.regular.footnote,
                color = LuziaTheme.palette.text.secondary
            )
        },
        leadingContent = {
            ListItemLeadingImageView(
                model = leadingImage,
                size = Spacing.X40.dp,
                modifier = Modifier
                    .clip(CircleShape)
                    .background(LuziaTheme.palette.surface.content)
            )
        },
        trailingContent = trailingContent
    )
}

@PreviewLightDark
@Composable
private fun Preview(@PreviewParameter(LuziaListTypeProvider::class) listType: LuziaListItemType) {
    LuziaTheme {
        Surface(color = LuziaTheme.palette.surface.background) {
            LuziaListItemView(listType)
        }
    }
}

private class LuziaListTypeProvider : PreviewParameterProvider<LuziaListItemType> {
    override val values: Sequence<LuziaListItemType> = sequenceOf(
        LuziaListItemType.BottomSheet(
            title = "Lorem ipsum",
            image = LuziaImageUiModel.Drawable(
                resourceId = designR.drawable.ic_home_24,
                tint = IconTint.InteractivePrimary
            )
        ),
        LuziaListItemType.Icon(
            title = "Tile ipsum",
            description = "Description ipsum",
            image = LuziaImageUiModel.Drawable(
                resourceId = designR.drawable.ic_home_24,
                tint = IconTint.InteractiveBrand
            )
        ),
        LuziaListItemType.Cta(
            title = "John Doe",
            description = "@johndoe1",
            trailingImage = LuziaImageUiModel.Drawable(
                designR.drawable.ic_send,
                IconTint.InteractivePrimary
            ),
            leadingImage = LuziaImageUiModel.Url("https://image.com"),
        ),
        LuziaListItemType.Alternate(
            title = "John Doe",
            description = "@johndoe1",
            leadingImage = LuziaImageUiModel.Url("https://image.com"),
            trailingImage = LuziaImageUiModel.Drawable(
                resourceId = designR.drawable.ic_chevron_right_24,
                tint = IconTint.InteractivePrimary
            )
        ),
        LuziaListItemType.Radio(
            title = "John Doe",
            description = "@johndoe1",
            leadingImage = LuziaImageUiModel.Url("https://image.com"),
            isSelected = false
        ),
        LuziaListItemType.Time(
            title = "John Doe",
            description = "@johndoe1",
            leadingImage = LuziaImageUiModel.Url("https://image.com"),
            time = "23:48",
            badgeCount = 5
        )
    )
}
