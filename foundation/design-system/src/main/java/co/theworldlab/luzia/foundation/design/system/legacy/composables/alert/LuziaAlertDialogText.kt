package co.theworldlab.luzia.foundation.design.system.legacy.composables.alert

import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme

sealed class LuziaAlertDialogTextDefaults(
    val textStyle: TextStyle
) {
    class Description : LuziaAlertDialogTextDefaults(
        LuziaTheme.typography.body.regular.small
    )

    class Title : LuziaAlertDialogTextDefaults(
        LuziaTheme.typography.body.semiBold.default
    )
}

class LuziaAlertDialogText(
    private val title: String,
    private val styleDefaults: LuziaAlertDialogTextDefaults,
    private val modifier: Modifier = Modifier
) {
    @Composable
    fun View() {
        Text(
            text = title,
            modifier = modifier,
            color =
            if (styleDefaults is LuziaAlertDialogTextDefaults.Description) {
                LuziaTheme.palette.text.primary
            } else {
                LuziaTheme.palette.text.primary
            },
            style = styleDefaults.textStyle
        )
    }
}
