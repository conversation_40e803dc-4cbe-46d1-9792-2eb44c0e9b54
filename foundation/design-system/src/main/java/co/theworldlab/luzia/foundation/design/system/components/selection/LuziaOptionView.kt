package co.theworldlab.luzia.foundation.design.system.components.selection

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun LuziaOptionView(
    modifier: Modifier = Modifier,
    title: String,
    selected: Boolean,
    onClick: () -> Unit = {}
) {
    Row(
        modifier = modifier
            .click(action = onClick)
            .height(Spacing.X48.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        LuziaText(
            modifier = Modifier.weight(1f),
            text = title,
            style = LuziaTheme.typography.body.regular.default,
            color = LuziaTheme.palette.text.primary
        )
        LuziaRadioIcon(selected = selected, onClick = onClick)
    }
}
