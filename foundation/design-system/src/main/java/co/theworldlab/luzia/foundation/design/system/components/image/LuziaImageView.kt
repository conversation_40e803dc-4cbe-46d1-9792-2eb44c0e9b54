package co.theworldlab.luzia.foundation.design.system.components.image

import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import coil3.compose.AsyncImage

@Composable
fun LuziaImageView(
    model: LuziaImageUiModel,
    modifier: Modifier = Modifier
) {
    when (model) {
        is LuziaImageUiModel.Drawable -> Icon(
            painter = painterResource(id = model.resourceId),
            contentDescription = null,
            tint = model.tint.toColor(),
            modifier = modifier
        )

        is LuziaImageUiModel.Url -> AsyncImage(
            model = model.imageUrl,
            contentDescription = null,
            modifier = modifier,
            contentScale = ContentScale.Crop
        )
    }
}

@Composable
private fun IconTint.toColor(): Color {
    return when (this) {
        IconTint.InteractivePrimary -> LuziaTheme.palette.interactive.primary
        IconTint.InteractiveBrand -> LuziaTheme.palette.interactive.brand
    }
}
