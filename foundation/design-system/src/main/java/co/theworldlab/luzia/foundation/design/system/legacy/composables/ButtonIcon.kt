package co.theworldlab.luzia.foundation.design.system.legacy.composables

import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.PressInteraction
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.LocalViewConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun ButtonIcon(
    icon: Painter,
    onClick: () -> Unit,
    contentDescription: String,
    modifier: Modifier = Modifier,
    iconModifier: Modifier = Modifier,
    enabled: Boolean = true,
    containerColor: Color = LuziaTheme.palette.interactive.brand,
    containerOnPressedColor: Color = LuziaTheme.palette.interactive.brandLight,
    contentColor: Color = LuziaTheme.palette.text.contrast,
    disabledContainerColor: Color = LuziaTheme.palette.interactive.brand.copy(alpha = 0.5f),
    disabledContentColor: Color = LuziaTheme.palette.text.contrast,
    tint: Color = LuziaTheme.palette.text.contrast,
    onLongClick: (() -> Unit)? = null,
    onLongClickRelease: (() -> Unit)? = null,
    iconSize: Int = 24,
    iconContainerSize: Int = 40
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    val backgroundColor =
        if (isPressed) containerOnPressedColor else containerColor

    if (onLongClick != null || onLongClickRelease != null) {
        val viewConfiguration = LocalViewConfiguration.current
        LaunchedEffect(interactionSource) {
            var isLongClick = false
            interactionSource.interactions.collectLatest { interaction ->
                when (interaction) {
                    is PressInteraction.Press -> {
                        isLongClick = false
                        delay(viewConfiguration.longPressTimeoutMillis)
                        isLongClick = true
                        onLongClick?.invoke()
                    }

                    is PressInteraction.Release -> {
                        if (isLongClick) {
                            onLongClickRelease?.invoke()
                        }
                    }
                }
            }
        }
    }

    IconButton(
        onClick = onClick,
        interactionSource = interactionSource,
        modifier =
        modifier
            .clip(CircleShape)
            .size(iconContainerSize.dp),
        enabled = enabled,
        colors =
        IconButtonDefaults.iconButtonColors(
            containerColor = backgroundColor,
            contentColor = contentColor,
            disabledContainerColor = disabledContainerColor,
            disabledContentColor = disabledContentColor
        )
    ) {
        Icon(
            modifier = iconModifier.size(size = iconSize.dp),
            painter = icon,
            tint = tint,
            contentDescription = contentDescription
        )
    }
}

@Preview
@Composable
fun ExampleButtonIconPreview() {
    Box(
        modifier = Modifier.size(70.dp),
        contentAlignment = Alignment.Center
    ) {
        ButtonIcon(
            icon = painterResource(id = R.drawable.ic_more),
            onClick = ({}),
            contentDescription = stringResource(id = localizationR.string.button_circle_description)
        )
    }
}
