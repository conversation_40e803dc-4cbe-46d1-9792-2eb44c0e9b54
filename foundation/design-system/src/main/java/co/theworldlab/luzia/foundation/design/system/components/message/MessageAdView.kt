package co.theworldlab.luzia.foundation.design.system.components.message

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageContainerData
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageModel

@Composable
fun MessageAdView(
    modifier: Modifier = Modifier,
    model: MessageModel.Ad,
    adContent: @Composable () -> Unit
) {
    val containerData = MessageContainerData(
        isAi = true,
        isLoading = false,
        error = null,
        isFavorite = false,
        timestamp = model.messageTimeStamp,
        feedback = null,
        animateItemSelection = false,
        tutdFeedback = null,
        isOffline = false
    )

    MessageContainerView(
        modifier = modifier,
        data = containerData
    ) {
        adContent()
    }
}
