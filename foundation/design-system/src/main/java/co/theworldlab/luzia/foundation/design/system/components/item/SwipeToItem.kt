package co.theworldlab.luzia.foundation.design.system.components.item

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Icon
import androidx.compose.material3.SwipeToDismissBox
import androidx.compose.material3.SwipeToDismissBoxState
import androidx.compose.material3.SwipeToDismissBoxValue
import androidx.compose.material3.rememberSwipeToDismissBoxState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialog
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogButton
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogTextDefaults
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import kotlinx.coroutines.launch
import co.thewordlab.luzia.foundation.localization.R as localizationR

data class SwipeToModel<T>(
    val data: T,
    val endContent: SwipeActionContent,
    val content: @Composable () -> Unit,
    val onAction: (SwipeToActions<T>) -> Unit
)

data class SwipeActionContent(
    val enableSwipe: Boolean,
    val enableConfirmation: Boolean,
    val confirmationTitle: String,
    val confirmationDesc: String,
    val confirmationButton: String,
    @DrawableRes val icon: Int,
)

sealed class SwipeToActions<T> {
    data class OnSwiped<T>(val data: T) : SwipeToActions<T>()
    data class OnStartEndAction<T>(val data: T) : SwipeToActions<T>()
    data class OnEndActionCancelled<T>(val data: T) : SwipeToActions<T>()
}

@Composable
fun <T> SwipeToItem(
    model: SwipeToModel<T>
) {
    val coroutineScope = rememberCoroutineScope()
    var showEndConfirmation by remember { mutableStateOf(false) }
    var alreadySwiped = false
    val dismissState = rememberSwipeToDismissBoxState(
        initialValue = SwipeToDismissBoxValue.Settled,
        confirmValueChange = {
            if (!alreadySwiped) {
                alreadySwiped = true
                model.onAction(SwipeToActions.OnSwiped(model.data))
            }
            when (it) {
                SwipeToDismissBoxValue.EndToStart -> showEndConfirmation = true
                SwipeToDismissBoxValue.StartToEnd,
                SwipeToDismissBoxValue.Settled -> {
                    alreadySwiped = false
                    return@rememberSwipeToDismissBoxState false
                }
            }
            return@rememberSwipeToDismissBoxState false
        },
        positionalThreshold = { it * .5f }
    )

    if (showEndConfirmation && model.endContent.enableConfirmation) {
        EndConfirmationPopup(
            model.data,
            model.endContent.confirmationTitle,
            model.endContent.confirmationDesc,
            model.endContent.confirmationButton,
            model.onAction
        ) {
            showEndConfirmation = false
            coroutineScope.launch { dismissState.reset() }
        }
    }

    SwipeToDismissBox(
        state = dismissState,
        modifier = Modifier,
        backgroundContent = { DismissBackground(dismissState, model.endContent.icon) },
        enableDismissFromEndToStart = model.endContent.enableSwipe,
        enableDismissFromStartToEnd = false,
        content = { model.content() }
    )
}

@Composable
private fun DismissBackground(dismissState: SwipeToDismissBoxState, @DrawableRes endIcon: Int) {
    val color = when (dismissState.dismissDirection) {
        SwipeToDismissBoxValue.EndToStart -> LuziaTheme.palette.accents.red.error90
        SwipeToDismissBoxValue.StartToEnd,
        SwipeToDismissBoxValue.Settled -> Color.Transparent
    }

    Row(
        modifier = Modifier
            .fillMaxSize()
            .background(color)
            .padding(Spacing.X16.dp, Spacing.X8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.End
    ) {
        Icon(
            painter = painterResource(id = endIcon),
            contentDescription = "delete",
            tint = Color.White
        )
    }
}

@Composable
private fun <T> EndConfirmationPopup(
    data: T,
    title: String,
    desc: String,
    button: String,
    onViewActions: (SwipeToActions<T>) -> Unit,
    onDismiss: () -> Unit
) {
    LuziaAlertDialog(
        confirmButton = LuziaAlertDialogButton(
            button,
            action = {
                onDismiss()
                onViewActions(SwipeToActions.OnStartEndAction(data))
            },
            isPrimaryAction = true
        ),
        dismissButton = LuziaAlertDialogButton(
            stringResource(id = localizationR.string.cancel),
            action = {
                onDismiss()
                onViewActions(SwipeToActions.OnEndActionCancelled(data))
            }
        ),
        title = LuziaAlertDialogText(
            title = title,
            LuziaAlertDialogTextDefaults.Description()
        ),
        text = LuziaAlertDialogText(
            title = desc,
            LuziaAlertDialogTextDefaults.Title()
        )
    )
}
