package co.theworldlab.luzia.foundation.design.system.legacy.composables

import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import kotlinx.coroutines.delay

@Composable
fun LuziaCircularProgressIndicator(
    modifier: Modifier = Modifier,
    strokeWidth: Int = 6,
    color: Color = LuziaTheme.palette.interactive.brand,
    delay: Long = 40
) {
    var progress by remember { mutableFloatStateOf(0f) }

    LaunchedEffect(Unit) {
        while (true) {
            for (i in 0..100) {
                progress = i / 100f
                delay(delay)
            }
        }
    }

    CircularProgressIndicator(
        progress = progress,
        modifier = modifier.size(48.dp),
        color = color,
        strokeWidth = strokeWidth.dp
    )
}
