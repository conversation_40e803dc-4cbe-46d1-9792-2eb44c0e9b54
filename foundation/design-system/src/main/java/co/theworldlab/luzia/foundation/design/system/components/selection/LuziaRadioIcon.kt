package co.theworldlab.luzia.foundation.design.system.components.selection

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.unit.dp
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun LuziaRadioIcon(
    modifier: Modifier = Modifier,
    selected: Boolean,
    isEnabled: Boolean = true,
    onClick: () -> Unit = {},
    isVisible: Boolean = true
) {
    val borderColor = if (selected) LuziaTheme.palette.border.primary else LuziaTheme.palette.interactive.brand
    Box(
        modifier = modifier
            .graphicsLayer { alpha = if (isVisible) 1f else 0f }
            .size(Spacing.X24.dp)
            .clip(CircleShape)
            .border(width = 1.dp, color = borderColor, shape = CircleShape)
            .background(if (selected) LuziaTheme.palette.interactive.brand else Color.Transparent)
            .click(action = onClick, isEnabled = isEnabled),
        contentAlignment = Alignment.Center
    ) {
        if (selected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "Selected",
                tint = Color.White,
                modifier = Modifier.width(Spacing.X12.dp)
            )
        }
    }
}
