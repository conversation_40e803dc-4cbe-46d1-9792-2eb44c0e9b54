package co.theworldlab.luzia.foundation.design.system.components.camera.permission

import android.Manifest
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.animation.slideOutVertically
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import co.thewordlab.luzia.foundation.localization.R
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialog
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogButton
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.alert.LuziaAlertDialogTextDefaults
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.PermissionStatus
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import com.google.accompanist.permissions.shouldShowRationale

private const val STIFFNESS_VERTICAL_ANIM = 8

@Stable
@OptIn(ExperimentalPermissionsApi::class)
class CameraPermissionState internal constructor(
    private val onGranted: () -> Unit,
    private val onShowRationale: () -> Unit,
    private val onRequestPermission: () -> Unit,
) {

    internal val status: MutableState<PermissionStatus> = mutableStateOf(PermissionStatus.Granted)

    val isGranted: Boolean get() = status.value.isGranted

    fun launchCamera() {
        if (status.value.isGranted) {
            onGranted()
        } else {
            if (status.value.shouldShowRationale) {
                onShowRationale()
            } else {
                onRequestPermission()
            }
        }
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun rememberCameraPermissionState(
    onGranted: () -> Unit
): CameraPermissionState {
    val permissionState = rememberPermissionState(permission = Manifest.permission.CAMERA)
    var showRationale by remember { mutableStateOf(false) }
    val wasPermissionGranted = remember { permissionState.status.isGranted }
    val cameraState = remember {
        CameraPermissionState(
            onGranted = onGranted,
            onShowRationale = {
                showRationale = true
            },
            onRequestPermission = {
                permissionState.launchPermissionRequest()
            }
        )
    }
    LaunchedEffect(permissionState.status) {
        cameraState.status.value = permissionState.status
    }
    LaunchedEffect(wasPermissionGranted, permissionState.status.isGranted) {
        if (!wasPermissionGranted && permissionState.status.isGranted) {
            onGranted()
        }
    }
    CameraPermissionRationale(
        show = showRationale,
        onDismiss = { showRationale = false }
    )
    return cameraState
}

@Composable
private fun CameraPermissionRationale(show: Boolean, onDismiss: () -> Unit) {
    AnimatedVisibility(
        visible = show,
        enter =
        fadeIn(spring(stiffness = Spring.StiffnessHigh)) +
            scaleIn(
                initialScale = .8f,
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessMediumLow
                )
            ),
        exit = slideOutVertically { it / STIFFNESS_VERTICAL_ANIM } + fadeOut() + scaleOut(
            targetScale = .95f
        )
    ) {
        val context = LocalContext.current
        LuziaAlertDialog(
            confirmButton = LuziaAlertDialogButton(
                title = stringResource(id = R.string.go_to_settings),
                action = {
                    onDismiss.invoke()
                    val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                    val uri = Uri.fromParts("package", context.packageName, null)
                    intent.data = uri
                    context.startActivity(intent)
                },
                isPrimaryAction = true
            ),
            dismissButton = LuziaAlertDialogButton(
                title = stringResource(id = R.string.cancel),
                action = { onDismiss.invoke() }
            ),
            title = LuziaAlertDialogText(
                stringResource(id = R.string.vision_camera_permission_denied_title),
                LuziaAlertDialogTextDefaults.Title()
            ),
            text = LuziaAlertDialogText(
                stringResource(id = R.string.vision_camera_permission_denied_description),
                LuziaAlertDialogTextDefaults.Description()
            )
        )
    }
}
