package co.theworldlab.luzia.foundation.design.system.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.staticCompositionLocalOf

val LocalPalette = staticCompositionLocalOf { lightPalette() }
val LocalDarkMode = staticCompositionLocalOf { false }

@Composable
fun LuziaTheme(darkTheme: Boolean = isSystemInDarkTheme(), content: @Composable () -> Unit) {
    val palette = if (darkTheme) darkPalette() else lightPalette()

    CompositionLocalProvider(
        LocalDarkMode provides darkTheme,
        LocalPalette provides palette,
        content = content
    )
}

object LuziaTheme {

    val palette: Palette
        @Composable
        get() = LocalPalette.current

    val isDarkTheme: Boolean
        @Composable
        get() = LocalDarkMode.current

    val typography = luziaTypography()
}
