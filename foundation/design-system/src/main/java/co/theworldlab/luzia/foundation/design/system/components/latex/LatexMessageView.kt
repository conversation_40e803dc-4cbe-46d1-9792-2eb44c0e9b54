package co.theworldlab.luzia.foundation.design.system.components.latex

import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import co.theworldlab.luzia.foundation.design.system.components.message.MessageContainerView
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageModel
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageViewActions
import co.theworldlab.luzia.foundation.design.system.legacy.composables.BaseContextualMenuOptions
import co.theworldlab.luzia.foundation.design.system.legacy.composables.LuziaContextualMenu

private val heights = HashMap<Long, Dp>()

@Composable
fun LatexMessageView(
    modifier: Modifier = Modifier,
    model: MessageModel.Math,
    actions: MessageViewActions,
) {
    MessageContainerView(
        modifier = modifier,
        data = model.containerData
    ) {
        var contextMenuPosition: DpOffset? by remember { mutableStateOf(null) }
        val density = LocalDensity.current
        val itemHeight = heights[model.id] ?: 0.dp

        // Track the position of the LatexInlineView in the Compose hierarchy
        var latexViewPosition by remember { mutableStateOf(Offset.Zero) }

        LatexInlineView(
            modifier = modifier
                .onGloballyPositioned { coordinates ->
                    // Store the position of the LatexInlineView in the Compose hierarchy
                    latexViewPosition = coordinates.positionInRoot()
                }
                .onSizeChanged {
                    if (it.height > 0) {
                        heights[model.id] = with(density) { it.height.toDp() }
                    }
                }
                .height(heights[model.id] ?: Dp.Unspecified),
            message = model.text,
            query = model.query,
            onLongClick = { x, y ->
                val onLongClick = actions.onLongClick
                if (onLongClick != null) {
                    onLongClick(model)
                } else {
                    // Convert WebView coordinates to Compose coordinates
                    // Add the WebView's position in the Compose hierarchy to the touch coordinates
                    contextMenuPosition = with(density) {
                        DpOffset(
                            (latexViewPosition.x + x).toDp(),
                            (latexViewPosition.y + y).toDp()
                        )
                    }
                }
            }
        )
        contextMenuPosition?.let {
            LuziaContextualMenu(
                isVisible = true,
                pressOffset = it,
                itemHeight = itemHeight,
                onDismiss = { contextMenuPosition = null },
                dropDownItemList =
                BaseContextualMenuOptions.messageDropDownList(
                    text = model.text,
                    isAi = model.containerData.isAi,
                    onDismiss = { contextMenuPosition = null },
                    onTextCopied = { actions.onTextCopied(model) },
                    onTextShared = { actions.onTextShared(model) },
                    onTextReported = { actions.onTextReported(model) },
                    enableFavoriteActions = model.capabilities.enableFavoriteActions,
                    enableMessagingActions = model.capabilities.enableMessagingActions,
                    enableTextToSpeech = model.capabilities.enableTextToSpeech,
                    onTextDeleted = { actions.onTextDeleted(model) },
                    onTextFavorite = { actions.onTextFavorite(model) },
                    isFavorite = model.containerData.isFavorite
                )
            )
        }
    }
}
