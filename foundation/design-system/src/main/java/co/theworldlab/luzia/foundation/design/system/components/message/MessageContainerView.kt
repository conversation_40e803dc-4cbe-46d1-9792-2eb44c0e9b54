package co.theworldlab.luzia.foundation.design.system.components.message

import androidx.compose.animation.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.lds.buttons.LuziaTextButton
import co.theworldlab.luzia.foundation.design.system.components.message.feedback.FeedbackView
import co.theworldlab.luzia.foundation.design.system.components.message.model.FeedbackState
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageContainerData
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageDecoration
import co.theworldlab.luzia.foundation.design.system.components.message.model.SourceItem
import co.theworldlab.luzia.foundation.design.system.components.message.model.TuTdFeedback
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.legacy.composables.LuziaAnimationText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import coil.compose.AsyncImage
import kotlinx.coroutines.delay
import co.thewordlab.luzia.foundation.localization.R as localizationR
import co.theworldlab.luzia.foundation.design.system.components.lds.feedback.FeedbackView as TuTdView

private const val BLINK_DURATION = 750
private const val BLINK_DELAY = 1000L

@Composable
fun MessageContainerView(
    modifier: Modifier,
    data: MessageContainerData,
    backgroundColor: Color = getDefaultBackground(data.isAi),
    content: @Composable ColumnScope.() -> Unit,
) {
    Column(modifier = modifier.fillMaxWidth()) {
        ChatBubbleView(data, backgroundColor, content)
        if (data.tutdFeedback != null) {
            Spacing.X4.Vertical()
            TuTdView(
                state = data.tutdFeedback.state,
                isEnabled = !data.isOffline,
                onFeedbackClicked = { data.tutdFeedback.onFeedbackProvided(it) }
            )
        }
        if (data.isLoading) {
            Spacing.X12.Vertical()
            LuziaAnimationText()
        }
        if (data.feedback != null) {
            Spacing.X12.Vertical()
            FeedbackView(model = data.feedback)
        }
    }
}

@Composable
private fun ColumnScope.ChatBubbleView(
    data: MessageContainerData,
    backgroundColor: Color,
    content: @Composable (ColumnScope.() -> Unit)
) {
    val yellow90 = LuziaTheme.palette.accents.yellow.yellow90
    val color = remember { Animatable(backgroundColor) }
    LaunchedEffect(data.animateItemSelection) {
        if (data.animateItemSelection) {
            color.animateTo(
                yellow90,
                animationSpec = tween(BLINK_DURATION)
            )
            delay(BLINK_DELAY)
            color.animateTo(backgroundColor, animationSpec = tween(BLINK_DURATION))
        }
    }
    val paddingValues =
        if (data.isAi) PaddingValues(end = Spacing.X40.dp) else PaddingValues(start = Spacing.X40.dp)
    val alignment: Alignment.Horizontal = if (data.isAi) Alignment.Start else Alignment.End
    Column(
        modifier = Modifier
            .align(alignment)
            .padding(paddingValues)
            .chatBubble(data.isAi, color.value)
    ) {
        Column(
            modifier = Modifier.padding(
                end = Spacing.X12.dp,
                start = Spacing.X12.dp,
                top = Spacing.X12.dp,
                bottom = Spacing.X8.dp,
            )
        ) {
            content.invoke(this)
        }
        if (data.error != null) MessageErrorView(data.error)
        MessageDecorationView(Modifier, data)
    }
}

@Composable
private fun MessageDecorationView(
    modifier: Modifier,
    data: MessageContainerData
) {
    Column(modifier) {
        data.decoration?.sources?.takeIf { it.isNotEmpty() }?.let { sources ->
            SourcesView(
                items = sources,
                onSourcesTapped = data.decoration.onSourcesTapped
            )
        }
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = Spacing.X12.dp,
                    end = Spacing.X12.dp,
                    bottom = Spacing.X4.dp
                ),
            verticalAlignment = Alignment.CenterVertically
        ) {
            data.onTextToSpeech?.let {
                IconButton(
                    onClick = it,
                    modifier = Modifier.size(Spacing.X32.dp)
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_sound),
                        tint = LuziaTheme.palette.interactive.secondary,
                        contentDescription = null,
                        modifier = Modifier.size(Spacing.X24.dp)
                    )
                }
            }
            Spacer(Modifier.weight(1f))
            if (data.isFavorite) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_star_12),
                    contentDescription = null,
                    tint = LuziaTheme.palette.accents.yellow.yellow30
                )
                Spacing.X4.Horizontal()
            }
            LuziaText(
                text = data.timestamp,
                style = LuziaTheme.typography.body.regular.footnote,
                color = LuziaTheme.palette.text.secondary
            )
        }
    }
}

@Composable
private fun SourcesView(items: List<SourceItem>, onSourcesTapped: () -> Unit) {
    Row(
        modifier = Modifier
            .padding(horizontal = Spacing.X12.dp)
            .clip(CircleShape)
            .background(LuziaTheme.palette.primitives.neutral.neutral7)
            .click(action = onSourcesTapped)
            .padding(horizontal = Spacing.X12.dp, vertical = Spacing.X4.dp),
        horizontalArrangement = Arrangement.spacedBy(Spacing.X4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        val width = Spacing.X8.dp * (items.size + 1)
        Box(modifier = Modifier.width(width)) {
            items.forEachIndexed { index, item ->
                AsyncImage(
                    modifier = Modifier
                        .offset(x = Spacing.X8.dp * index)
                        .border(Spacing.X1.dp, LuziaTheme.palette.border.primary, CircleShape)
                        .size(Spacing.X16.dp)
                        .padding(Spacing.X1.dp)
                        .clip(CircleShape),
                    contentScale = ContentScale.Crop,
                    model = item.thumbnail,
                    contentDescription = null
                )
            }
        }
        LuziaText(
            text = stringResource(localizationR.string.web_search_all_sources),
            style = LuziaTheme.typography.body.semiBold.small,
            color = LuziaTheme.palette.text.helper
        )
    }
}

@Composable
private fun getDefaultBackground(isAi: Boolean) =
    if (isAi) LuziaTheme.palette.surface.content else LuziaTheme.palette.surface.chat

@Composable
private fun MessageErrorView(error: MessageError) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(Spacing.X12.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(Spacing.X4.dp)
        ) {
            val errorMessage = stringResource(id = error.errorMessageRes)
            Icon(
                modifier = Modifier
                    .padding(top = Spacing.X4.dp)
                    .size(Spacing.X12.dp),
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_warning),
                contentDescription = errorMessage,
                tint = LuziaTheme.palette.text.primary
            )
            LuziaText(
                modifier = Modifier.weight(1f),
                text = errorMessage,
                style = LuziaTheme.typography.body.regular.small,
                color = LuziaTheme.palette.text.primary
            )
        }
        if (error.showResendButton) {
            LuziaTextButton(
                modifier = Modifier.align(Alignment.End),
                onClick = error.onResend,
                text = stringResource(localizationR.string.chat_resend)
            )
        }
    }
}

@PreviewLightDark
@Composable
private fun Preview(@PreviewParameter(MessageContainerDataProvider::class) data: MessageContainerData) {
    LuziaTheme {
        MessageContainerView(modifier = Modifier, data = data) {
            LuziaText(
                text = "Lorem ipsum door sit amet...",
                color = LuziaTheme.palette.text.primary,
                style = LuziaTheme.typography.body.semiBold.default
            )
        }
    }
}

private class MessageContainerDataProvider : PreviewParameterProvider<MessageContainerData> {
    override val values: Sequence<MessageContainerData> = sequenceOf(
        MessageContainerData(
            isAi = false,
            isLoading = false,
            error = MessageError(
                localizationR.string.error_audio_message,
                true,
                {}
            ),
            isFavorite = false,
            feedback = null,
            timestamp = "12:00",
            animateItemSelection = false,
            tutdFeedback = null,
            isOffline = false,
            onTextToSpeech = {},
            decoration = null
        ),
        MessageContainerData(
            isAi = true,
            isLoading = false,
            error = MessageError(
                localizationR.string.error_audio_message,
                true,
                {}
            ),
            isFavorite = false,
            feedback = null,
            timestamp = "12:00",
            animateItemSelection = false,
            tutdFeedback = TuTdFeedback(
                messageServerId = "",
                state = FeedbackState.Positive,
                onFeedbackProvided = {}
            ),
            isOffline = false,
            onTextToSpeech = {},
            decoration = MessageDecoration(
                onSourcesTapped = {},
                sources = listOf(
                    SourceItem(
                        "Kemal",
                        "https://upload.wikimedia.org/wikipedia/commons/f/fb/Cnn_logo_red_background.png",
                        ""
                    ),
                    SourceItem(
                        "Ahmet",
                        "https://upload.wikimedia.org/wikipedia/commons/f/fb/Cnn_logo_red_background.png",
                        ""
                    ),
                    SourceItem(
                        "Mehmet",
                        "https://upload.wikimedia.org/wikipedia/commons/f/fb/Cnn_logo_red_background.png",
                        ""
                    ),
                )
            )
        )
    )
}
