package co.theworldlab.luzia.foundation.design.system.components.scaffold

import android.content.Intent
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.SnackbarResult
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import co.theworldlab.luzia.foundation.design.system.components.snackbar.LuziaSnackBarVisual
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

data class AppState(
    internal val coroutineScope: CoroutineScope,
    internal val snackBar: SnackbarHostState,
    internal val onActivityResultRequest: (Intent) -> Unit
) {

    internal val onActivityResultResponse: MutableState<((Intent?) -> Unit)?> = mutableStateOf(null)

    fun showSnackBar(message: String) {
        coroutineScope.launch {
            snackBar.showSnackbar(message)
        }
    }

    fun showLuziaSnackBar(message: String) {
        coroutineScope.launch {
            snackBar.showSnackbar(LuziaSnackBarVisual(message))
        }
    }

    suspend fun suspendShowSnackBar(message: String) {
        snackBar.showSnackbar(message)
    }

    fun showSnackBarWithAction(
        message: String,
        actionLabel: String,
        duration: SnackbarDuration,
        onActionReceived: (SnackbarResult) -> Unit
    ) = coroutineScope.launch {
        onActionReceived(
            snackBar.showSnackbar(
                message = message,
                actionLabel = actionLabel,
                duration = duration,
                withDismissAction = false
            )
        )
    }

    fun launchActivityResult(intent: Intent) {
        onActivityResultRequest(intent)
    }

    fun setOnActivityResult(onResult: ((Intent?) -> Unit)?) {
        onActivityResultResponse.value = onResult
    }
}
