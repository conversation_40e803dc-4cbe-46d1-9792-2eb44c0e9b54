package co.theworldlab.luzia.foundation.design.system.legacy.composables

import androidx.activity.compose.BackHandler
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateIntOffsetAsState
import androidx.compose.animation.core.updateTransition
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.DefaultAlpha
import androidx.compose.ui.graphics.FilterQuality
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Popup
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import coil3.compose.AsyncImage
import coil3.compose.AsyncImagePainter
import kotlin.math.abs
import kotlin.math.roundToInt

@Composable
fun ExpandableImageView(
    model: Any?,
    description: String,
    modifier: Modifier = Modifier,
    transform: (AsyncImagePainter.State) -> AsyncImagePainter.State = AsyncImagePainter.DefaultTransform,
    onState: ((AsyncImagePainter.State) -> Unit)? = null,
    alignment: Alignment = Alignment.Center,
    contentScale: ContentScale = ContentScale.Fit,
    alpha: Float = DefaultAlpha,
    colorFilter: ColorFilter? = null,
    filterQuality: FilterQuality = DrawScope.DefaultFilterQuality
) {
    var maximizing by remember { mutableStateOf(false) }
    var showOverlay by remember { mutableStateOf(false) }
    val density = LocalDensity.current
    val transition = updateTransition(targetState = maximizing, label = "Show overlay")

    val alphaAnimated =
        transition.animateFloat(
            label = "Alpha",
            targetValueByState = {
                if (it) 1f else 0f
            }
        )

    val isRunning by remember(transition) {
        derivedStateOf { transition.isRunning }
    }
    val dragThreshold = with(density) { 30.dp.toPx() }

    BackHandler(maximizing) {
        maximizing = false
    }
    LaunchedEffect(isRunning, transition.currentState) {
        if (!isRunning && !transition.currentState) {
            showOverlay = false
        }
    }
    AsyncImage(
        modifier =
        modifier
            .click {
                showOverlay = true
                maximizing = true
            },
        model = model,
        contentDescription = description,
        transform = transform,
        onState = onState,
        alignment = alignment,
        contentScale = contentScale,
        alpha = alpha,
        colorFilter = colorFilter,
        filterQuality = filterQuality
    )
    if (showOverlay) {
        Popup(offset = IntOffset.Zero) {
            var offsetX by remember { mutableFloatStateOf(0f) }
            var offsetY by remember { mutableFloatStateOf(0f) }
            val dragOffset by remember(offsetX, offsetY) {
                derivedStateOf { IntOffset(offsetX.roundToInt(), offsetY.roundToInt()) }
            }
            val animateOffset = animateIntOffsetAsState(targetValue = dragOffset, label = "Offset")
            Box(
                modifier =
                Modifier
                    .offset { animateOffset.value }
                    .graphicsLayer { this.alpha = alphaAnimated.value }
                    .pointerInput(Unit) {
                        detectDragGestures(
                            onDragEnd = {
                                if (abs(offsetX) > dragThreshold || abs(offsetY) > dragThreshold) maximizing = false
                                offsetX = 0f
                                offsetY = 0f
                            },
                            onDrag = { change, dragAmount ->
                                change.consume()
                                offsetX += dragAmount.x
                                offsetY += dragAmount.y
                            }
                        )
                    }
                    .background(Color.Black)
                    .fillMaxWidth()
                    .fillMaxHeight()
            ) {
                AsyncImage(
                    modifier =
                    Modifier
                        .fillMaxWidth()
                        .align(Alignment.Center),
                    model = model,
                    contentDescription = description,
                    contentScale = ContentScale.Fit
                )
                if (!isRunning && maximizing) {
                    CameraAction(
                        modifier =
                        Modifier
                            .padding(16.dp)
                            .clip(CircleShape),
                        drawableRes = R.drawable.ic_back_arrow,
                        onClick = { maximizing = false }
                    )
                    LuziaText(
                        modifier =
                        Modifier
                            .align(Alignment.BottomCenter)
                            .fillMaxWidth()
                            .background(Color.Black.copy(alpha = 0.8f))
                            .padding(16.dp),
                        text = description,
                        color = Color.White,
                        style = LuziaTheme.typography.body.semiBold.small
                    )
                }
            }
        }
    }
}
