package co.theworldlab.luzia.foundation.design.system.components.navbar

import androidx.annotation.DrawableRes
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.constraintlayout.compose.ConstraintLayout
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun TopCenteredNavigationBar(
    modifier: Modifier = Modifier,
    title: String,
    titleStyle: TextStyle = LuziaTheme.typography.headlines.h4,
    @DrawableRes navigationIconRes: Int = R.drawable.ic_back_arrow,
    onNavigationIconClick: () -> Unit,
    actionBackground: Color = Color.Transparent,
    endActions: List<Pair<Int, () -> Unit>> = emptyList()
) {
    ConstraintLayout(
        modifier = modifier
            .statusBarsPadding()
            .padding(vertical = Spacing.X8.dp)
            .fillMaxWidth()
    ) {
        val (startRef, centerRef, endRef) = createRefs()
        IconButton(
            modifier = Modifier
                .size(IconSizes.X32.dp)
                .clip(CircleShape)
                .constrainAs(startRef) {
                    start.linkTo(parent.start)
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                }
                .testTag("buttonBack"),
            onClick = onNavigationIconClick,
            colors = IconButtonDefaults.iconButtonColors(
                containerColor = actionBackground,
                contentColor = LuziaTheme.palette.text.primary
            )
        ) {
            Icon(
                modifier = Modifier.size(size = IconSizes.X20.dp),
                painter = painterResource(navigationIconRes),
                contentDescription = null
            )
        }
        LuziaText(
            modifier = Modifier.constrainAs(centerRef) {
                start.linkTo(parent.start)
                end.linkTo(parent.end)
                top.linkTo(parent.top)
                bottom.linkTo(parent.bottom)
            },
            text = title,
            style = titleStyle,
            color = LuziaTheme.palette.text.primary
        )
        Row(
            modifier = Modifier.constrainAs(endRef) {
                end.linkTo(parent.end)
                top.linkTo(parent.top)
                bottom.linkTo(parent.bottom)
            },
            verticalAlignment = Alignment.CenterVertically
        ) {
            endActions.forEach { (resId, action) ->
                IconButton(
                    modifier = Modifier
                        .size(IconSizes.X32.dp)
                        .clip(CircleShape),
                    onClick = action,
                    colors = IconButtonDefaults.iconButtonColors(
                        containerColor = actionBackground,
                        contentColor = LuziaTheme.palette.text.primary
                    )
                ) {
                    Icon(
                        modifier = Modifier.size(size = IconSizes.X20.dp),
                        painter = painterResource(resId),
                        contentDescription = null
                    )
                }
            }
        }
    }
}
