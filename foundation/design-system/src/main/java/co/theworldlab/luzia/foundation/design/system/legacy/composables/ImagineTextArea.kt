package co.theworldlab.luzia.foundation.design.system.legacy.composables

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.legacy.extensions.isSmallScreen
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun ImagineTextArea(
    modifier: Modifier = Modifier,
    onDoneButtonKBListener: (String) -> Unit,
    onTextChanged: (String) -> Unit,
    text: String = "",
    hintText: String = "",
    limitChar: Int = 500,
    title: String = "",
    enabled: Boolean = true,
    onFocused: (Boolean) -> Unit = {},
    readOnly: Boolean = false
) {
    val configuration = LocalConfiguration.current
    val maxLines = 5
    val maxLinesSmallScreen = 4

    Column(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight(Alignment.Top)
            .padding(
                start = 16.dp,
                end = 16.dp,
                top =
                if (configuration.isSmallScreen()) {
                    4.dp
                } else {
                    16.dp
                },
                bottom =
                if (configuration.isSmallScreen()) {
                    4.dp
                } else {
                    16.dp
                }
            )
    ) {
        LuziaText(
            text = title.takeIf { it.isNotEmpty() } ?: stringResource(id = localizationR.string.imagine_title_textarea),
            style = LuziaTheme.typography.body.semiBold.small,
            color = LuziaTheme.palette.text.primary
        )
        TextFieldMultiline(
            modifier = Modifier.testTag("inputBar"),
            onDoneButtonKBListener = { onDoneButtonKBListener(it) },
            onTextChanged = { onTextChanged(it) },
            messageText = text,
            hintText = hintText,
            contentDescriptionText = stringResource(localizationR.string.describe_imagine),
            enabled = enabled,
            readOnly = readOnly,
            onFocused = { onFocused(it) },
            maxLines = if (configuration.isSmallScreen()) {
                maxLinesSmallScreen
            } else {
                maxLines
            },
            limitChar = limitChar
        )
    }
}

@Preview(showBackground = true, widthDp = 500, heightDp = 500)
@Composable
fun ImagineTextAreaPreview() {
    ImagineTextArea(
        onDoneButtonKBListener = ({}),
        onTextChanged = {},
        text = "text transcription"
    )
}
