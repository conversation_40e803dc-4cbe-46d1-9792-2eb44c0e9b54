package co.theworldlab.luzia.foundation.design.system.components.lds.cards

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun CardSelection(
    modifier: Modifier = Modifier,
    title: String,
    isSelected: Boolean,
    state: CardState = CardState.ENABLED,
    onSelected: (isSelected: Boolean) -> Unit
) {
    val (borderColor, checkColor, textColor) = if (state == CardState.ENABLED) {
        listOf(
            if (isSelected) LuziaTheme.palette.interactive.brand else LuziaTheme.palette.border.primary,
            LuziaTheme.palette.interactive.brand,
            LuziaTheme.palette.text.primary
        )
    } else {
        listOf(
            LuziaTheme.palette.text.helper,
            LuziaTheme.palette.text.helper,
            LuziaTheme.palette.text.helper
        )
    }
    ConstraintLayout(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(Corners.X2.dp))
            .border(width = 1.dp, color = borderColor, shape = RoundedCornerShape(Corners.X2.dp))
            .click { onSelected(!isSelected) }
            .padding(Spacing.X12.dp)
    ) {
        val (startContainer, endContainer) = createRefs()
        Column(
            modifier = Modifier
                .constrainAs(startContainer) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(endContainer.start)
                    width = Dimension.fillToConstraints
                }
        ) {
            LuziaText(
                text = title,
                style = LuziaTheme.typography.body.regular.small,
                color = textColor
            )
        }
        Box(
            modifier = Modifier
                .wrapContentSize()
                .constrainAs(endContainer) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    end.linkTo(parent.end)
                },
            contentAlignment = Alignment.Center
        ) {
            SelectionIcon(isSelected = isSelected, color = checkColor)
        }
    }
}

@Composable
private fun SelectionIcon(isSelected: Boolean, color: Color) {
    Box(
        modifier = Modifier
            .size(IconSizes.X24.dp)
            .border(
                width = 1.dp,
                color = color,
                shape = CircleShape
            )
    ) {
        if (isSelected) {
            Icon(
                modifier = Modifier
                    .size(IconSizes.X24.dp)
                    .background(color = color, shape = CircleShape),
                painter = painterResource(id = R.drawable.ic_check),
                contentDescription = null,
                tint = Color.Unspecified
            )
        }
    }
}

@Composable
@PreviewLightDark
private fun Preview() {
    LuziaTheme {
        Column(
            modifier = Modifier
                .background(LuziaTheme.palette.surface.background)
                .padding(Spacing.X8.dp),
            verticalArrangement = Arrangement.spacedBy(Spacing.X8.dp)
        ) {
            Text(text = "Card Selection Enabled", color = LuziaTheme.palette.text.primary)
            CardSelection(
                title = "Title",
                isSelected = true,
                onSelected = { DO_NOTHING }
            )
            CardSelection(
                title = "Title",
                isSelected = false,
                onSelected = { DO_NOTHING }
            )

            Text(text = "Card Selection Disabled")
            CardSelection(
                title = "Title",
                isSelected = true,
                onSelected = { DO_NOTHING },
                state = CardState.DISABLED
            )
            CardSelection(
                title = "Title",
                isSelected = false,
                onSelected = { DO_NOTHING },
                state = CardState.DISABLED
            )
        }
    }
}
