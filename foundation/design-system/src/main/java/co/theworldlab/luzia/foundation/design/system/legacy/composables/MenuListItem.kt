package co.theworldlab.luzia.foundation.design.system.legacy.composables

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme

@Composable
fun MenuListItem(
    modifier: Modifier = Modifier,
    headlineText: String,
    headlineModifier: Modifier = Modifier,
    headlineTextInBold: Boolean = false,
    supportingText: String? = null,
    supportingTextMaxLines: Int = Int.MAX_VALUE,
    supportingModifier: Modifier = Modifier,
    supportingTextOverflow: TextOverflow = TextOverflow.Ellipsis,
    leadingItem: @Composable (() -> Unit)? = null,
    trailingItem: @Composable (() -> Unit)? = null
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        leadingItem?.invoke()
        Column(
            modifier =
            Modifier
                .weight(1f)
                .fillMaxWidth()
                .padding(horizontal = 4.dp)
        ) {
            MenuListItemHeadlineContent(
                headlineText = headlineText,
                modifier = headlineModifier,
                hasSupportingText = supportingText?.isEmpty() == false,
                isBold = headlineTextInBold
            )
            supportingText?.let {
                MenuListItemSupportingContent(
                    supportingText = it,
                    modifier = supportingModifier,
                    overflow = supportingTextOverflow,
                    maxLines = supportingTextMaxLines
                )
            }
        }
        trailingItem?.invoke()
    }
}

@Composable
private fun MenuListItemHeadlineContent(
    headlineText: String,
    hasSupportingText: Boolean,
    isBold: Boolean,
    modifier: Modifier = Modifier
) {
    LuziaText(
        text = headlineText,
        modifier = modifier,
        style =
        when {
            isBold || hasSupportingText -> LuziaTheme.typography.body.semiBold.default
            else -> LuziaTheme.typography.body.regular.default
        },
        color = LuziaTheme.palette.text.primary
    )
}

@Composable
private fun MenuListItemSupportingContent(
    supportingText: String,
    maxLines: Int,
    overflow: TextOverflow,
    modifier: Modifier = Modifier
) {
    LuziaText(
        text = supportingText,
        modifier = modifier,
        style = LuziaTheme.typography.body.regular.small,
        color = LuziaTheme.palette.text.primary,
        maxLines = maxLines,
        overflow = overflow
    )
}

@Preview(showBackground = true)
@Composable
private fun LuziaListItemPreview() {
    Column {
        MenuListItem(
            headlineText = "headlineText"
        )
        MenuListItem(
            headlineText = "headlineText",
            leadingItem = {
                LuziaListItemLeadingTrailingImageContent(
                    painter = painterResource(id = R.drawable.ic_friends),
                    contentDescription = null
                ).View()
            }
        )
        MenuListItem(
            headlineText = "headlineText",
            trailingItem = {
                LuziaListItemLeadingTrailingIconContent(
                    painter = painterResource(id = R.drawable.ic_chevron_right),
                    contentDescription = null
                ).View()
            }
        )
        MenuListItem(
            headlineText = "headlineText",
            leadingItem = {
                LuziaListItemLeadingTrailingImageContent(
                    painter = painterResource(id = R.drawable.ic_friends),
                    contentDescription = null
                ).View()
            },
            trailingItem = {
                LuziaListItemLeadingTrailingIconContent(
                    painter = painterResource(id = R.drawable.ic_chevron_right),
                    contentDescription = null
                ).View()
            }
        )
    }
}
