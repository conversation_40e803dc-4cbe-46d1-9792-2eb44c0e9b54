package co.theworldlab.luzia.foundation.design.system.components.input

import android.net.Uri
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.audio.AudioRecordState
import co.theworldlab.luzia.foundation.design.system.components.input.model.CameraResult
import co.theworldlab.luzia.foundation.design.system.components.lds.tooltip.Tooltip
import java.io.File

data class InputToolbarConfig(
    val isSendEnabled: Boolean,
    val actionsEnabled: Boolean,
    val addTooltip: Tooltip?,
    val addResourcesConfig: AddResourcesConfig = AddResourcesConfig.All,
    val onSendMessage: (InputMessage) -> Unit,
    val onTextChanged: (String) -> Unit = { DO_NOTHING },
    val onRecordingState: (AudioRecordState) -> Unit = { DO_NOTHING },
    val onAddResources: (ResourceType) -> Unit = { DO_NOTHING },
    val onMetricsAction: (MetricsActions) -> Unit = { DO_NOTHING },
    val cameraResult: CameraResult?,
    val onClearCameraResult: () -> Unit
)

sealed class AddResourcesConfig {
    data object None : AddResourcesConfig()
    data object All : AddResourcesConfig()
    data object OnlyPhotos : AddResourcesConfig()
    data object OnlyDocuments : AddResourcesConfig()
}

sealed class MetricsActions {
    data object PlusButtonTapped : MetricsActions()
}

sealed class InputMessage {
    data class Text(val text: String, val imageUri: Uri?) : InputMessage()
    data class Audio(val file: File) : InputMessage()
}

sealed class ResourceType {
    data object Camera : ResourceType()
    data object Gallery : ResourceType()
    data object File : ResourceType()
}
