package co.theworldlab.luzia.foundation.design.system.components.navbar

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.model.UiImage
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import coil3.compose.AsyncImage
import co.thewordlab.luzia.foundation.design.system.R as designR

private val DOT_SIZE = 10.dp

@Composable
fun NavBarMainView(
    modifier: Modifier = Modifier,
    title: String,
    image: UiImage?,
    onLeftIconClicked: () -> Unit,
    onNavBarContentClicked: () -> Unit = { DO_NOTHING },
    leftIconDrawableRes: Int = designR.drawable.ic_back_arrow,
    showLeftIconBackground: Boolean = false,
    showRedDot: Boolean = false,
    endSection: (@Composable () -> Unit)? = null,
) {
    Row(
        modifier = modifier
            .background(LuziaTheme.palette.surface.background)
            .fillMaxWidth()
            .click { onNavBarContentClicked() }
            .statusBarsPadding()
            .padding(vertical = Spacing.X12.dp)
            .padding(start = Spacing.X8.dp, end = Spacing.X12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(
            modifier = Modifier
                .clip(CircleShape)
                .size(Spacing.X48.dp)
                .testTag("buttonNavBack")
                .then(
                    if (showLeftIconBackground) {
                        Modifier.background(
                            LuziaTheme.palette.interactive.brandLight
                        )
                    } else {
                        Modifier
                    }
                ),
            onClick = onLeftIconClicked
        ) {
            Icon(
                painter = painterResource(id = leftIconDrawableRes),
                contentDescription = null,
                tint = LuziaTheme.palette.text.primary
            )
        }
        image?.let {
            Box {
                AsyncImage(
                    modifier = Modifier
                        .clip(CircleShape)
                        .size(Spacing.X32.dp),
                    contentScale = ContentScale.Inside,
                    model =
                    when (image) {
                        is UiImage.Resource -> image.resourceId
                        is UiImage.Plain -> image.url
                    },
                    contentDescription = null
                )
                if (showRedDot) {
                    Box(
                        modifier = Modifier
                            .size(DOT_SIZE)
                            .background(color = LuziaTheme.palette.accents.red.error90, shape = CircleShape)
                            .align(Alignment.BottomEnd)
                    )
                }
            }
            Spacer(Modifier.width(Spacing.X12.dp))
        } ?: Spacer(Modifier.width(Spacing.X8.dp))
        LuziaText(
            modifier = Modifier.weight(1f),
            text = title,
            style = LuziaTheme.typography.headlines.h4,
            color = LuziaTheme.palette.text.primary
        )
        endSection?.invoke()
    }
}

@Preview
@Composable
fun Preview() {
    LuziaTheme {
        NavBarMainView(
            title = "Test",
            image = UiImage.Resource(designR.drawable.luzia_with_background),
            onLeftIconClicked = {}
        )
    }
}
