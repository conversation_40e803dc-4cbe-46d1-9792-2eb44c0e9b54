package co.theworldlab.luzia.foundation.design.system.components.dialog

import androidx.annotation.StringRes
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme

@Composable
fun AlertDialogMandatory(
    @StringRes titleRes: Int,
    @StringRes messageRes: Int,
    @StringRes ctaRes: Int,
    onCtaClicked: () -> Unit
) {
    AlertDialog(
        title = {
            LuziaText(
                text = stringResource(titleRes),
                style = LuziaTheme.typography.body.semiBold.default
            )
        },
        text = {
            LuziaText(
                text = stringResource(messageRes),
                style = LuziaTheme.typography.body.semiBold.small
            )
        },
        onDismissRequest = { },
        confirmButton = {},
        dismissButton = {
            TextButton(onClick = onCtaClicked) {
                Text(stringResource(ctaRes))
            }
        }
    )
}
