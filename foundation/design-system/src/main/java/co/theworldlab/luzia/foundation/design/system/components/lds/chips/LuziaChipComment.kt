package co.theworldlab.luzia.foundation.design.system.components.lds.chips

import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.components.message.chatBubble
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun LuziaChipComment(
    modifier: Modifier = Modifier,
    title: String,
    subtitle: String,
    state: ChipState = ChipState.ENABLED,
    indicatorPosition: ChipIndicatorPosition = ChipIndicatorPosition.START,
    onClick: () -> Unit
) {
    val interactionSource: MutableInteractionSource = remember { MutableInteractionSource() }
    val containerColor = containerColor(state)
    val contentColor = when (state) {
        ChipState.DISABLED -> LuziaTheme.palette.text.helper
        ChipState.ENABLED -> LuziaTheme.palette.text.primary
        ChipState.SELECTED -> LuziaTheme.palette.text.contrast
    }
    val (indicator, padding, alignment) = when (indicatorPosition) {
        ChipIndicatorPosition.START -> Triple(
            true,
            PaddingValues(
                top = Spacing.X12.dp,
                bottom = Spacing.X12.dp,
                start = Spacing.X12.dp,
                end = Spacing.X32.dp
            ),
            Alignment.Start
        )

        ChipIndicatorPosition.END -> Triple(
            false,
            PaddingValues(
                top = Spacing.X12.dp,
                bottom = Spacing.X12.dp,
                start = Spacing.X32.dp,
                end = Spacing.X12.dp
            ),
            Alignment.End
        )
    }

    Surface(
        onClick = onClick,
        modifier = modifier
            .chatBubble(indicator, containerColor)
            .semantics { role = Role.RadioButton },
        enabled = state != ChipState.DISABLED,
        shape = RoundedCornerShape(Corners.X2.dp),
        color = containerColor,
        contentColor = contentColor,
        shadowElevation = 0.dp,
        border = null,
        interactionSource = interactionSource
    ) {
        Column(
            Modifier.padding(padding),
            horizontalAlignment = alignment,
            verticalArrangement = Arrangement.spacedBy(Spacing.X12.dp)
        ) {
            Text(
                text = title,
                style = LuziaTheme.typography.body.semiBold.small,
                color = contentColor
            )
            Text(
                text = subtitle,
                style = LuziaTheme.typography.body.regular.small,
                color = contentColor
            )
        }
    }
}

@Composable
private fun containerColor(state: ChipState): Color {
    return when (state) {
        ChipState.ENABLED,
        ChipState.DISABLED -> LuziaTheme.palette.surface.content
        ChipState.SELECTED -> LuziaTheme.palette.primitives.brand.brand70
    }
}

@Composable
@PreviewLightDark
private fun Preview() {
    LuziaTheme {
        Column(
            modifier = Modifier
                .background(LuziaTheme.palette.surface.background)
                .padding(Spacing.X16.dp),
            verticalArrangement = Arrangement.spacedBy(Spacing.X16.dp),
        ) {
            Text(
                text = "Chip Comment - Start Indicator",
                style = LuziaTheme.typography.headlines.h4,
                color = LuziaTheme.palette.text.primary
            )

            LuziaChipComment(
                title = "✉\uFE0F Short & Sweet",
                subtitle = "K, see you there.",
                state = ChipState.ENABLED
            ) { DO_NOTHING }

            LuziaChipComment(
                title = "✉\uFE0F Short & Sweet",
                subtitle = "K, see you there.",
                state = ChipState.DISABLED
            ) { DO_NOTHING }

            LuziaChipComment(
                title = "✉\uFE0F Short & Sweet",
                subtitle = "K, see you there.",
                state = ChipState.SELECTED
            ) { DO_NOTHING }

            Text(
                text = "Chip Comment - End Indicator",
                style = LuziaTheme.typography.headlines.h4,
                color = LuziaTheme.palette.text.primary
            )

            LuziaChipComment(
                title = "✉\uFE0F Short & Sweet",
                subtitle = "K, see you there.",
                state = ChipState.ENABLED,
                indicatorPosition = ChipIndicatorPosition.END
            ) { DO_NOTHING }

            LuziaChipComment(
                title = "✉\uFE0F Short & Sweet",
                subtitle = "K, see you there.",
                state = ChipState.DISABLED,
                indicatorPosition = ChipIndicatorPosition.END
            ) { DO_NOTHING }

            LuziaChipComment(
                title = "✉\uFE0F Short & Sweet",
                subtitle = "K, see you there.",
                state = ChipState.SELECTED,
                indicatorPosition = ChipIndicatorPosition.END
            ) { DO_NOTHING }
        }
    }
}
