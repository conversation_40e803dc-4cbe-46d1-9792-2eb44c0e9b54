package co.theworldlab.luzia.foundation.design.system.components.message

import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.audio.WaveformsView
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageModel
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageViewActions
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.legacy.composables.BaseContextualMenuOptions
import co.theworldlab.luzia.foundation.design.system.legacy.composables.LuziaContextualMenu
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun MessageAudioView(
    modifier: Modifier = Modifier,
    model: MessageModel.Audio,
    actions: MessageViewActions,
) {
    var contextMenuPosition: DpOffset? by remember { mutableStateOf(null) }
    val density = LocalDensity.current
    val itemHeight = remember { mutableStateOf(0.dp) }

    MessageContainerView(
        modifier = modifier
            .onSizeChanged { itemHeight.value = with(density) { it.height.toDp() } }
            .pointerInput(Unit) {
                detectTapGestures(
                    onLongPress = { contextMenuPosition = DpOffset(it.x.toDp(), it.y.toDp()) }
                )
            },
        data = model.containerData
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(2.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            val label =
                if (model.isImported) {
                    localizationR.string.audio_transcription_imported
                } else {
                    localizationR.string.audio_transcribing
                }
            Icon(
                painter = painterResource(id = R.drawable.ic_imported),
                contentDescription = null,
                tint = LuziaTheme.palette.text.secondary
            )
            LuziaText(
                text = stringResource(id = label),
                style = LuziaTheme.typography.body.regular.small,
                color = LuziaTheme.palette.text.secondary
            )
        }
        Spacer(modifier = Modifier.height(8.dp))
        WaveformsView(shouldAnimate = false)
        if (model.text.isNotEmpty()) {
            MarkdownTextView(
                text = model.text,
                style = rememberRichTextStyle(),
                onLinkClicked = actions.onLinkClicked
            )
        }
    }

    contextMenuPosition?.let {
        LuziaContextualMenu(
            isVisible = true,
            pressOffset = it,
            itemHeight = itemHeight.value,
            onDismiss = { contextMenuPosition = null },
            dropDownItemList =
            BaseContextualMenuOptions.messageOnlyDeleteDropDownList(
                text = model.text,
                onDismiss = { contextMenuPosition = null },
                enableMessagingActions = model.capabilities.enableMessagingActions,
                onTextDeleted = { actions.onTextDeleted(model) }
            )
        )
    }
}
