package co.theworldlab.luzia.foundation.design.system.helpers

import android.annotation.SuppressLint
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

class ScreenSizeHandler(
    private val modifier: Modifier,
    private val contentAlignment: Alignment
) {

    @SuppressLint("UnusedBoxWithConstraintsScope")
    @Composable
    fun Build(
        contentSmall: @Composable () -> Unit,
        contentDefault: @Composable () -> Unit,
    ) {
        BoxWithConstraints(
            modifier = modifier.fillMaxSize(),
            contentAlignment = contentAlignment
        ) {
            when (determineContentVersion(maxHeight, maxWidth)) {
                ScreenContentVersion.Small -> contentSmall()
                ScreenContentVersion.Default -> contentDefault()
            }
        }
    }
}

internal fun determineContentVersion(
    maxHeight: Dp,
    maxWidth: Dp
): ScreenContentVersion = when {
    maxHeight <= 640.dp || maxWidth <= 320.dp -> ScreenContentVersion.Small
    else -> ScreenContentVersion.Default
}

internal sealed class ScreenContentVersion {
    data object Small : ScreenContentVersion()
    data object Default : ScreenContentVersion()
}
