package co.theworldlab.luzia.foundation.design.system.components.message.model

import co.theworldlab.luzia.foundation.design.system.components.message.MessageError
import co.theworldlab.luzia.foundation.design.system.components.message.feedback.FeedbackUiModel
import co.theworldlab.luzia.foundation.design.system.components.message.proactive.ProActiveMessageUiModel

sealed class MessageModel(
    val messageId: Long,
    val messageType: MessageType,
    val messageText: String,
    val messagePersonalityId: String,
    val messageTime: Long,
    val messageTimeStamp: String,
    val messageAi: Boolean,
    val messageFavorite: Boolean,
    val messageRequestId: String?
) {

    data class Text(
        val id: Long,
        val text: String,
        val requestId: String?,
        val time: Long,
        val personalityId: String,
        val query: String,
        val containerData: MessageContainerData,
        val capabilities: MessageCapabilities
    ) : MessageModel(
        messageId = id,
        messageText = text,
        messageType = MessageType.Text,
        messagePersonalityId = personalityId,
        messageTime = time,
        messageAi = containerData.isAi,
        messageTimeStamp = containerData.timestamp,
        messageFavorite = containerData.isFavorite,
        messageRequestId = requestId
    )

    data class Math(
        val id: Long,
        val text: String,
        val personalityId: String,
        val time: Long,
        val query: String,
        val requestId: String?,
        val containerData: MessageContainerData,
        val capabilities: MessageCapabilities,
    ) : MessageModel(
        messageId = id,
        messageText = text,
        messageType = MessageType.Math,
        messagePersonalityId = personalityId,
        messageTime = time,
        messageAi = containerData.isAi,
        messageTimeStamp = containerData.timestamp,
        messageFavorite = containerData.isFavorite,
        messageRequestId = requestId
    )

    data class Image(
        val id: Long,
        val fileName: String,
        val isImported: Boolean,
        val text: String,
        val personalityId: String,
        val time: Long,
        val requestId: String?,
        val containerData: MessageContainerData,
        val capabilities: MessageCapabilities,
    ) : MessageModel(
        messageId = id,
        messageText = text,
        messageType = MessageType.Image,
        messagePersonalityId = personalityId,
        messageTime = time,
        messageAi = containerData.isAi,
        messageTimeStamp = containerData.timestamp,
        messageFavorite = containerData.isFavorite,
        messageRequestId = requestId
    )

    data class File(
        val id: Long,
        val fileName: String,
        val fileType: String,
        val isImported: Boolean,
        val text: String,
        val personalityId: String,
        val time: Long,
        val requestId: String?,
        val containerData: MessageContainerData,
        val capabilities: MessageCapabilities,
    ) : MessageModel(
        messageId = id,
        messageText = text,
        messageType = MessageType.File,
        messagePersonalityId = personalityId,
        messageTime = time,
        messageAi = containerData.isAi,
        messageTimeStamp = containerData.timestamp,
        messageFavorite = containerData.isFavorite,
        messageRequestId = requestId
    )

    data class Audio(
        val id: Long,
        val isImported: Boolean,
        val text: String,
        val personalityId: String,
        val time: Long,
        val requestId: String?,
        val containerData: MessageContainerData,
        val capabilities: MessageCapabilities,
    ) : MessageModel(
        messageId = id,
        messageText = text,
        messageType = MessageType.Math,
        messagePersonalityId = personalityId,
        messageTime = time,
        messageAi = containerData.isAi,
        messageTimeStamp = containerData.timestamp,
        messageFavorite = containerData.isFavorite,
        messageRequestId = requestId
    )

    data class Proactive(
        val id: Long,
        val text: String,
        val personalityId: String,
        val time: Long,
        val requestId: String?,
        val containerData: MessageContainerData,
        val capabilities: MessageCapabilities,
        val proactive: List<ProActiveMessageUiModel>
    ) : MessageModel(
        messageId = id,
        messageText = text,
        messageType = MessageType.Proactive,
        messagePersonalityId = personalityId,
        messageTime = time,
        messageAi = containerData.isAi,
        messageTimeStamp = containerData.timestamp,
        messageFavorite = containerData.isFavorite,
        messageRequestId = requestId
    )

    data class Ad(
        val id: Long,
        val requestId: String?,
        val time: Long,
        val personalityId: String,
        val containerData: MessageContainerData,
        val capabilities: MessageCapabilities
    ) : MessageModel(
        messageId = id,
        messageText = "",
        messageType = MessageType.Ad,
        messagePersonalityId = personalityId,
        messageTime = time,
        messageAi = true,
        messageTimeStamp = containerData.timestamp,
        messageFavorite = false,
        messageRequestId = requestId
    )
}

data class MessageContainerData(
    val isAi: Boolean,
    val isLoading: Boolean,
    val error: MessageError?,
    val isFavorite: Boolean,
    val timestamp: String,
    val feedback: FeedbackUiModel?,
    val tutdFeedback: TuTdFeedback?,
    val animateItemSelection: Boolean,
    val isOffline: Boolean,
    val onTextToSpeech: (() -> Unit)? = null,
    val decoration: MessageDecoration? = null
)

data class MessageCapabilities(
    val enableMessagingActions: Boolean,
    val enableFavoriteActions: Boolean,
    val enableTextToSpeech: Boolean
)

enum class MessageType(val property: String) {
    Text("text"),
    AudioRecord("audio"),
    AudioImport("audio_transcription"),
    Image("image"),
    File("file"),
    Math("text"),
    Proactive("proactive"),
    Ad("ad")
}
