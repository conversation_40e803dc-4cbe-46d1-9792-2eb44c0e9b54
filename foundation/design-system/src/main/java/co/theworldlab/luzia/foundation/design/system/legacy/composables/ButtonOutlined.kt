package co.theworldlab.luzia.foundation.design.system.legacy.composables

import androidx.annotation.DrawableRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes

@Composable
fun ButtonOutlined(
    onClick: () -> Unit,
    buttonText: String,
    modifier: Modifier = Modifier,
    textModifier: Modifier = Modifier,
    enabled: Boolean = true,
    containerColor: Color = Color.Unspecified,
    contentColor: Color = LuziaTheme.palette.interactive.brand,
    containerBorderColor: Color = Color.Unspecified,
    containerOnPressedColor: Color = LuziaTheme.palette.interactive.brandLight,
    disabledContainerColor: Color = LuziaTheme.palette.interactive.brand.copy(alpha = 0.5f),
    disabledContentColor: Color = LuziaTheme.palette.interactive.brand,
    textStyle: TextStyle = LuziaTheme.typography.body.semiBold.small,
    @DrawableRes iconRes: Int? = null,
    tintIcon: Boolean = true,
    isLoading: Boolean = false
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    val backgroundColor = if (isPressed) containerOnPressedColor else containerColor
    val borderColor =
        if (containerBorderColor == Color.Unspecified) {
            if (enabled) contentColor else disabledContentColor
        } else {
            containerBorderColor
        }

    OutlinedButton(
        modifier =
        Modifier
            .height(40.dp)
            .wrapContentWidth()
            .then(modifier),
        onClick = { onClick() },
        interactionSource = interactionSource,
        enabled = enabled && !isLoading,
        border = BorderStroke(1.dp, borderColor),
        shape = CircleShape,
        colors =
        ButtonDefaults.outlinedButtonColors(
            containerColor = backgroundColor,
            contentColor = contentColor,
            disabledContentColor = disabledContentColor,
            disabledContainerColor = disabledContainerColor
        ),
        contentPadding = PaddingValues(horizontal = 24.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    color = LuziaTheme.palette.interactive.brand,
                    modifier = Modifier
                        .size(IconSizes.X16.dp)
                        .align(Alignment.CenterVertically)
                )
            } else {
                iconRes?.let {
                    Icon(
                        painter = painterResource(id = iconRes),
                        contentDescription = null,
                        tint = if (!tintIcon) Color.Unspecified else contentColor
                    )
                }
                Text(
                    text = buttonText,
                    modifier = textModifier,
                    style = textStyle,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Preview
@Composable
fun ExampleButtonOutlinedPreview() {
    Box(
        modifier = Modifier.size(400.dp),
        contentAlignment = Alignment.Center
    ) {
        ButtonOutlined(
            onClick = ({}),
            "Button",
            enabled = true,
            containerColor = LuziaTheme.palette.surface.background,
            contentColor = LuziaTheme.palette.interactive.brand
        )
    }
}

@Preview
@Composable
fun ExampleButtonOutlinedWithIconPreview() {
    Box(
        modifier = Modifier.size(400.dp),
        contentAlignment = Alignment.Center
    ) {
        ButtonOutlined(
            modifier = Modifier.fillMaxWidth(),
            onClick = ({}),
            buttonText = "Button",
            enabled = true,
            containerColor = LuziaTheme.palette.surface.background,
            contentColor = LuziaTheme.palette.interactive.brand,
            iconRes = R.drawable.ic_mic,
            tintIcon = true,
            textModifier = Modifier.wrapContentWidth().padding(start = 4.dp)
        )
    }
}
