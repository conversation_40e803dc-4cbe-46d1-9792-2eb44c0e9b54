package co.theworldlab.luzia.foundation.design.system.legacy.composables

import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Column
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchColors
import androidx.compose.material3.SwitchDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme

class LuziaSwitchDefaultProperties(
    val thumbColor: Color,
    val trackColor: Color,
    val borderColor: Color,
    val iconColor: Color
)

object LuziaSwitchDefaults {
    @Composable
    fun colors(
        checkedProperties: LuziaSwitchDefaultProperties =
            LuziaSwitchDefaultProperties(
                thumbColor = Color.White,
                trackColor = LuziaTheme.palette.interactive.brand,
                borderColor = LuziaTheme.palette.interactive.brand.copy(alpha = 0.5f),
                iconColor = LuziaTheme.palette.text.contrast
            ),
        uncheckedProperties: LuziaSwitchDefaultProperties =
            LuziaSwitchDefaultProperties(
                thumbColor = Color.White,
                trackColor = LuziaTheme.palette.interactive.brandLight,
                borderColor = LuziaTheme.palette.interactive.brandLight.copy(alpha = 0.5f),
                iconColor = LuziaTheme.palette.text.contrast
            )
    ) = SwitchDefaults.colors(
        checkedThumbColor = checkedProperties.thumbColor,
        checkedTrackColor = checkedProperties.trackColor,
        checkedBorderColor = checkedProperties.borderColor,
        checkedIconColor = checkedProperties.iconColor,
        uncheckedThumbColor = uncheckedProperties.thumbColor,
        uncheckedTrackColor = uncheckedProperties.trackColor,
        uncheckedBorderColor = uncheckedProperties.borderColor,
        uncheckedIconColor = uncheckedProperties.iconColor
    )
}

@Composable
fun LuziaSwitch(
    checked: Boolean,
    onCheckedChange: ((Boolean) -> Unit)?,
    modifier: Modifier = Modifier,
    thumbContent: (@Composable () -> Unit)? = null,
    enabled: Boolean = true,
    colors: SwitchColors = LuziaSwitchDefaults.colors(),
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() }
) {
    Switch(checked, onCheckedChange, modifier, thumbContent, enabled, colors, interactionSource)
}

@Preview(showBackground = true)
@Composable
private fun LuziaListItemPreview() {
    var checked1 by remember { mutableStateOf(false) }
    var checked2 by remember { mutableStateOf(true) }
    Column {
        LuziaSwitch(
            checked1,
            { checked1 = it }
        )
        LuziaSwitch(
            checked2,
            { checked2 = it }
        )
    }
}
