package co.theworldlab.luzia.foundation.design.system.components.message

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalUriHandler
import androidx.compose.ui.platform.UriHandler
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.style.TextDecoration
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import com.halilibo.richtext.commonmark.Markdown
import com.halilibo.richtext.ui.BasicRichText
import com.halilibo.richtext.ui.BlockQuoteGutter
import com.halilibo.richtext.ui.RichTextStyle
import com.halilibo.richtext.ui.string.RichTextStringStyle

@Composable
fun MarkdownTextView(
    modifier: Modifier = Modifier,
    text: String,
    style: RichTextStyle,
    onLinkClicked: (String) -> Unit
) {
    val context = LocalContext.current
    val uriHandler = remember(context, onLinkClicked) {
        object : UriHandler {
            override fun openUri(uri: String) {
                onLinkClicked(uri)
            }
        }
    }
    CompositionLocalProvider(LocalUriHandler provides uriHandler) {
        BasicRichText(
            modifier = modifier,
            style = style
        ) {
            Markdown(content = text)
        }
    }
}

@Composable
fun rememberRichTextStyle(): RichTextStyle {
    val colorQuote = LuziaTheme.palette.surface.content
    val boldStyle = LuziaTheme.typography.body.semiBold.default.toSpanStyle()
    val italicStyle =
        LuziaTheme.typography.body.regular.default.copy(fontStyle = FontStyle.Italic).toSpanStyle()
    val brandPrimary = LuziaTheme.palette.interactive.brand
    val yellow30 = LuziaTheme.palette.accents.yellow.yellow30
    return remember {
        RichTextStyle(
            stringStyle =
            RichTextStringStyle(
                codeStyle = SpanStyle(background = yellow30),
                linkStyle = TextLinkStyles(
                    SpanStyle(
                        textDecoration = TextDecoration.Underline,
                        color = brandPrimary
                    )
                ),
                boldStyle = boldStyle,
                italicStyle = italicStyle
            ),
            blockQuoteGutter = BlockQuoteGutter.BarGutter().copy(color = { colorQuote }),
        )
    }
}
