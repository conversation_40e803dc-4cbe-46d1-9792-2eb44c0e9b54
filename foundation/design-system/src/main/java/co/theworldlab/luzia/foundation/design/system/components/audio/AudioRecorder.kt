package co.theworldlab.luzia.foundation.design.system.components.audio

import android.content.Context
import android.media.AudioManager
import android.media.MediaRecorder
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import java.io.File
import java.io.FileOutputStream

class AudioRecorder(private val context: Context) {

    private var recorder: MediaRecorder? = null
    private var stopRecordingRetryCount = 0

    @Suppress("DEPRECATION")
    private fun createRecorder(): MediaRecorder =
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            MediaRecorder(context)
        } else {
            MediaRecorder()
        }

    fun startRecorder(outputFile: File): Boolean {
        stopRecordingRetryCount = 0
        return try {
            recorder = createRecorder().apply {
                setAudioSource(MediaRecorder.AudioSource.VOICE_RECOGNITION)
                setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
                setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
                setAudioEncodingBitRate(ENCODING_BIT_RATE)
                setAudioSamplingRate(SAMPLING_RATE)
                setOutputFile(FileOutputStream(outputFile).fd)
            }
            adjustMicrophoneGain()
            recorder?.run {
                prepare()
                start()
            }
            true
        } catch (expected: Exception) {
            Log.d("AudioRecorder", expected.message ?: "Exception starting recorder")
            false
        }
    }

    fun stopRecorder(): Boolean =
        if (stopRecordingRetryCount < MAX_RETRY_COUNT) {
            try {
                internalStopRecorder()
                true
            } catch (expected: Exception) {
                Log.d("AudioRecorder", expected.message ?: "Exception stopping recorder")
                Handler(Looper.getMainLooper()).postDelayed({ stopRecorder() }, RETRY_DELAY)
                false
            }
        } else {
            false
        }

    private fun internalStopRecorder() {
        recorder?.run {
            stop()
            reset()
            release()
        }
        recorder = null
    }

    private fun adjustMicrophoneGain() {
        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        audioManager.setParameters("input_gain=10")
    }

    private companion object {
        const val RETRY_DELAY = 500L
        const val MAX_RETRY_COUNT = 5
        const val SAMPLING_RATE = 44100
        const val ENCODING_BIT_RATE = 16 * SAMPLING_RATE
    }
}
