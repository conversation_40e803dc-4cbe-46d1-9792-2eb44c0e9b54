package co.theworldlab.luzia.foundation.design.system.legacy.composables

import android.graphics.drawable.Drawable
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.graphics.BlendModeColorFilterCompat
import androidx.core.graphics.BlendModeCompat
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import coil3.asDrawable
import coil3.compose.AsyncImage
import com.airbnb.lottie.LottieProperty
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.airbnb.lottie.compose.rememberLottieDynamicProperties
import com.airbnb.lottie.compose.rememberLottieDynamicProperty
import co.thewordlab.luzia.foundation.localization.R as localizationR

sealed class ImagineImageContainerType(
    val type: ImageTypes,
    val imageUrl: String? = null,
    val errorCancelOnClick: () -> Unit = {},
    val errorRetryOnClick: () -> Unit = {},
    val imageBitmap: ImageBitmap? = null,
    val prompt: String? = null,
    val showError: Boolean = false,
    val errorText: String? = null
) {
    data object Loading : ImagineImageContainerType(ImageTypes.LOADING)

    class Error(
        showError: Boolean = false,
        errorText: String? = null,
        cancelOnClick: () -> Unit,
        retryOnClick: () -> Unit
    ) : ImagineImageContainerType(
        ImageTypes.ERROR,
        errorCancelOnClick = cancelOnClick,
        errorRetryOnClick = retryOnClick,
        showError = showError,
        errorText = errorText
    )

    class Image(
        url: String
    ) : ImagineImageContainerType(
        ImageTypes.IMAGE,
        imageUrl = url
    )

    class DownloadedImage(
        imageBitmap: ImageBitmap,
        prompt: String
    ) : ImagineImageContainerType(
        ImageTypes.DOWNLOADED_IMAGE,
        imageBitmap = imageBitmap,
        prompt = prompt
    )
}

enum class ImageTypes {
    LOADING,
    ERROR,
    IMAGE,
    DOWNLOADED_IMAGE
}

@Composable
fun ImagineImageContainer(
    type: ImagineImageContainerType,
    modifier: Modifier = Modifier,
    updateDrawable: (Drawable) -> Unit = {}
) {
    val padding = if (type.type == ImageTypes.ERROR) Spacing.X32.dp else 0.dp
    Box(
        modifier =
        modifier
            .fillMaxWidth()
            .aspectRatio(1f)
            .clip(RoundedCornerShape(if (type.type == ImageTypes.DOWNLOADED_IMAGE) 0.dp else Corners.X4.dp))
            .background(color = LuziaTheme.palette.surface.content)
            .padding(all = padding)
    ) {
        when (type.type) {
            ImageTypes.LOADING -> {
                val dynamicProperties =
                    rememberLottieDynamicProperties(
                        rememberLottieDynamicProperty(
                            property = LottieProperty.COLOR_FILTER,
                            value = BlendModeColorFilterCompat.createBlendModeColorFilterCompat(
                                LuziaTheme.palette.text.primary.hashCode(),
                                BlendModeCompat.SRC_ATOP
                            ),
                            keyPath = arrayOf("**")
                        )
                    )
                val composition by rememberLottieComposition(
                    LottieCompositionSpec.RawRes(R.raw.imagine_loading_animation)
                )
                Column(
                    modifier = Modifier
                        .background(LuziaTheme.palette.surface.content)
                        .fillMaxHeight(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    LottieAnimation(
                        modifier = Modifier
                            .size(width = 220.dp, height = 202.dp),
                        composition = composition,
                        iterations = LottieConstants.IterateForever,
                        dynamicProperties = dynamicProperties
                    )
                    LuziaText(
                        modifier = Modifier
                            .padding(top = Spacing.X16.dp)
                            .fillMaxWidth(),
                        text = stringResource(id = localizationR.string.text_loading_imagine),
                        textAlign = TextAlign.Center,
                        style = LuziaTheme.typography.body.semiBold.default,
                        color = LuziaTheme.palette.text.primary
                    )
                }
            }

            ImageTypes.ERROR -> {
                Column(
                    modifier = Modifier.align(Alignment.Center),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    LuziaText(
                        text = type.errorText
                            ?: stringResource(id = localizationR.string.imagine_image_generation_error_title),
                        textAlign = TextAlign.Center,
                        style = LuziaTheme.typography.body.semiBold.default,
                        color = LuziaTheme.palette.text.primary
                    )
                    if (type.showError) {
                        Spacing.X24.Vertical()
                        LuziaText(
                            text = stringResource(id = localizationR.string.imagine_image_generation_error_text),
                            textAlign = TextAlign.Center,
                            style = LuziaTheme.typography.body.regular.small,
                            color = LuziaTheme.palette.text.primary
                        )
                        Spacing.X24.Vertical()
                        Row(
                            modifier = Modifier.wrapContentWidth(),
                            horizontalArrangement = Arrangement.spacedBy(Spacing.X24.dp)
                        ) {
                            ButtonText(
                                onClick = type.errorCancelOnClick,
                                buttonText = stringResource(id = localizationR.string.cancel),
                                contentColor = LuziaTheme.palette.text.primary,
                            )
                            ButtonText(
                                onClick = type.errorRetryOnClick,
                                buttonText = stringResource(id = localizationR.string.try_again),
                                contentColor = LuziaTheme.palette.text.primary,
                            )
                        }
                    }
                }
            }

            ImageTypes.IMAGE -> {
                val context = LocalContext.current
                AsyncImage(
                    model = type.imageUrl,
                    contentDescription = stringResource(id = localizationR.string.imagine_luzia_image),
                    modifier = Modifier.fillMaxSize(),
                    onSuccess = {
                        updateDrawable(it.result.image.asDrawable(context.resources))
                    }
                )
            }

            ImageTypes.DOWNLOADED_IMAGE -> {
                Image(
                    bitmap = type.imageBitmap!!,
                    contentDescription = type.prompt,
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
    }
}

@Preview(heightDp = 1500, widthDp = 360)
@Composable
fun ImagineImageContainerPreview() {
    Column {
        ImagineImageContainer(ImagineImageContainerType.Loading)
        Spacing.X16.Vertical()
        ImagineImageContainer(
            ImagineImageContainerType.Error(
                cancelOnClick = {},
                retryOnClick = {}
            )
        )
    }
}
