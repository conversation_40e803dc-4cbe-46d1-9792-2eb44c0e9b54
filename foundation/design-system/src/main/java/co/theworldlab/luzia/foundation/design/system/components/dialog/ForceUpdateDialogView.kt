package co.theworldlab.luzia.foundation.design.system.components.dialog

import android.content.Intent
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.core.net.toUri
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun ForceUpdateDialogView() {
    val context = LocalContext.current
    AlertDialogMandatory(
        titleRes = localizationR.string.title_alert_update_required,
        messageRes = localizationR.string.updating_required,
        ctaRes = localizationR.string.button_alert_update_required,
    ) {
        runCatching {
            val intent =
                Intent(Intent.ACTION_VIEW, "market://details?id=${context.packageName}".toUri())
            context.startActivity(intent)
        }.onFailure {
            val intent =
                Intent(
                    Intent.ACTION_VIEW,
                    "https://play.google.com/store/apps/details?id=${context.packageName}".toUri()
                )
            context.startActivity(intent)
        }
    }
}
