package co.theworldlab.luzia.foundation.design.system.components.navbar

import androidx.annotation.DrawableRes
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarColors
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.TopAppBarScrollBehavior
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.PreviewLightDark
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes

@Immutable
@OptIn(ExperimentalMaterial3Api::class)
data class TopNavigationBarModel(
    val title: String? = null,
    val colors: TopAppBarColors,
    val scrollBehavior: TopAppBarScrollBehavior? = null,
    val navigationAction: NavigationAction? = null,
    val actions: List<NavigationAction> = emptyList()
)

sealed class NavigationAction {
    data class Icon(
        @DrawableRes val icon: Int,
        val action: () -> Unit
    ) : NavigationAction()

    data class Text(
        val text: String,
        val action: () -> Unit
    ) : NavigationAction()
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TopNavigationBar(
    model: TopNavigationBarModel
) {
    TopAppBar(
        modifier = Modifier.fillMaxWidth(),
        scrollBehavior = model.scrollBehavior,
        navigationIcon = { model.navigationAction?.let { ActionContent(it) } },
        title = {
            model.title?.let {
                LuziaText(
                    text = it,
                    style = LuziaTheme.typography.headlines.h4,
                    color = LuziaTheme.palette.text.primary
                )
            }
        },
        colors = model.colors,
        actions = { Row { model.actions.forEach { ActionContent(it) } } }
    )
}

@Composable
private fun ActionContent(model: NavigationAction) {
    when (model) {
        is NavigationAction.Icon -> IconButton(
            modifier = Modifier
                .size(IconSizes.X48.dp)
                .clip(CircleShape),
            onClick = model.action,
            colors = IconButtonDefaults.iconButtonColors(
                contentColor = LuziaTheme.palette.text.primary
            )
        ) {
            Icon(
                modifier = Modifier.size(size = IconSizes.X24.dp),
                painter = painterResource(model.icon),
                contentDescription = null
            )
        }

        is NavigationAction.Text ->
            TextButton(
                onClick = model.action,
                content = {
                    LuziaText(
                        model.text,
                        LuziaTheme.typography.body.semiBold.footnote,
                        color = LuziaTheme.palette.interactive.brand
                    )
                }
            )
    }
}

object LuziaNavBarDefaults {
    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun colors(
        containerColor: Color = LuziaTheme.palette.surface.background,
        scrolledContainerColor: Color = LuziaTheme.palette.surface.background,
        navigationIconContentColor: Color = LuziaTheme.palette.text.primary,
        titleContentColor: Color = LuziaTheme.palette.text.primary
    ) = TopAppBarDefaults.centerAlignedTopAppBarColors(
        containerColor = containerColor,
        scrolledContainerColor = scrolledContainerColor,
        navigationIconContentColor = navigationIconContentColor,
        titleContentColor = titleContentColor
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@PreviewLightDark
@Composable
private fun PreviewTopNavigationBar() {
    TopNavigationBar(
        TopNavigationBarModel(
            title = "Test",
            navigationAction = NavigationAction.Icon(R.drawable.ic_back_arrow) { },
            actions = listOf(
                NavigationAction.Icon(R.drawable.ic_close) { },
                NavigationAction.Icon(R.drawable.ic_search) { }
            ),
            colors = LuziaNavBarDefaults.colors(containerColor = LuziaTheme.palette.surface.background),
            scrollBehavior = TopAppBarDefaults.enterAlwaysScrollBehavior()
        )
    )
}
