package co.theworldlab.luzia.foundation.design.system.components.lds.buttons

import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun LuziaPrimaryButton(
    onClick: () -> Unit,
    text: String,
    modifier: Modifier = Modifier,
    size: ButtonSize = ButtonSize.LARGE,
    state: ButtonState = ButtonState.ENABLED,
    icon: Painter? = null,
    contentDescription: String? = null,
    buttonPadding: PaddingValues? = null,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() }
) {
    val isPressed = interactionSource.collectIsPressedAsState()
    val containerColor = containerColor(state, isPressed.value)
    val contentColor = when (state) {
        ButtonState.DISABLED -> LuziaTheme.palette.primitives.neutral.neutral10
        else -> LuziaTheme.palette.text.contrast
    }
    val iconTint = when (state) {
        ButtonState.DISABLED -> LuziaTheme.palette.primitives.neutral.neutral10
        else -> LuziaTheme.palette.interactive.contrast
    }
    val textStyle = when (size) {
        ButtonSize.LARGE -> LuziaTheme.typography.body.semiBold.default
        ButtonSize.SMALL -> LuziaTheme.typography.body.semiBold.small
    }
    val iconSize = when (size) {
        ButtonSize.LARGE -> Spacing.X24.dp
        ButtonSize.SMALL -> Spacing.X20.dp
    }
    val contentPadding = when (size) {
        ButtonSize.LARGE -> PaddingValues(horizontal = Spacing.X16.dp, vertical = Spacing.X8.dp)
        ButtonSize.SMALL -> PaddingValues(horizontal = Spacing.X12.dp, vertical = Spacing.X6.dp)
    }
    Surface(
        onClick = onClick,
        modifier = modifier.semantics { role = Role.Button },
        enabled = state != ButtonState.DISABLED,
        shape = CircleShape,
        color = containerColor,
        contentColor = contentColor,
        shadowElevation = 0.dp,
        border = null,
        interactionSource = interactionSource
    ) {
        Row(
            Modifier.padding(buttonPadding ?: contentPadding),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (icon != null) {
                Icon(
                    painter = icon,
                    contentDescription = contentDescription,
                    tint = iconTint,
                    modifier = Modifier.size(iconSize)
                )
                Spacing.X4.Horizontal()
            }
            Text(
                text = text,
                style = textStyle,
                color = contentColor
            )
        }
    }
}

@Composable
private fun containerColor(state: ButtonState, isPressed: Boolean): Color {
    return when (state) {
        ButtonState.ENABLED -> enabledContainerColor(isPressed)
        ButtonState.DISABLED -> LuziaTheme.palette.interactive.disabled
        ButtonState.DANGER -> LuziaTheme.palette.accents.red.error50
        ButtonState.TOGGLED -> LuziaTheme.palette.accents.blue.blue10
    }
}

@Composable
private fun enabledContainerColor(isPressed: Boolean): Color {
    return when {
        !isPressed -> LuziaTheme.palette.interactive.brand
        else -> LuziaTheme.palette.primitives.brand.brand70
    }
}

@Composable
@PreviewLightDark
private fun LuziaPrimaryButtonAllVariantsPreview() {
    LuziaTheme {
        Column(
            modifier = Modifier
                .background(LuziaTheme.palette.surface.background)
                .padding(Spacing.X16.dp),
            verticalArrangement = Arrangement.spacedBy(Spacing.X16.dp),
        ) {
            Text(
                text = "Large Buttons",
                style = LuziaTheme.typography.headlines.h4,
                color = LuziaTheme.palette.text.primary
            )

            LuziaPrimaryButton(
                onClick = {},
                text = "Enabled Button",
                size = ButtonSize.LARGE,
                state = ButtonState.ENABLED
            )

            LuziaPrimaryButton(
                onClick = {},
                text = "Disabled Button",
                size = ButtonSize.LARGE,
                state = ButtonState.DISABLED
            )

            LuziaPrimaryButton(
                onClick = {},
                text = "Danger Button",
                size = ButtonSize.LARGE,
                state = ButtonState.DANGER
            )

            LuziaPrimaryButton(
                onClick = {},
                text = "With Icon",
                size = ButtonSize.LARGE,
                state = ButtonState.ENABLED,
                icon = rememberVectorPainter(Icons.Filled.Add),
                contentDescription = "Add"
            )

            Spacing.X16.Vertical()

            Text(
                text = "Small Buttons",
                style = LuziaTheme.typography.headlines.h4,
                color = LuziaTheme.palette.text.primary
            )

            LuziaPrimaryButton(
                onClick = {},
                text = "Enabled Button",
                size = ButtonSize.SMALL,
                state = ButtonState.ENABLED
            )

            LuziaPrimaryButton(
                onClick = {},
                text = "Disabled Button",
                size = ButtonSize.SMALL,
                state = ButtonState.DISABLED
            )

            LuziaPrimaryButton(
                onClick = {},
                text = "Danger Button",
                size = ButtonSize.SMALL,
                state = ButtonState.DANGER
            )

            LuziaPrimaryButton(
                onClick = {},
                text = "With Icon",
                size = ButtonSize.SMALL,
                state = ButtonState.ENABLED,
                icon = rememberVectorPainter(Icons.Filled.Add),
                contentDescription = "Add"
            )
        }
    }
}
