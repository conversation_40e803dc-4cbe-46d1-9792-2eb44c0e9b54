package co.theworldlab.luzia.foundation.design.system.legacy.composables

import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.PressInteraction
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme

@Suppress("LongMethod", "CyclomaticComplexMethod")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TextFieldForm(
    modifier: Modifier = Modifier,
    tag: String? = null,
    placeholderText: String? = null,
    onDoneButtonKBListener: (String) -> Unit = {},
    onTextChanged: (String) -> Unit = {},
    messageText: String = "",
    contentDescriptionText: String = "",
    limitChar: Int? = null,
    enabled: Boolean = true,
    onFocused: (Boolean) -> Unit = {},
    isSelector: Boolean = false,
    isError: Boolean = false,
    textError: String? = null,
    supportingText: String? = null,
    onClickButtonListener: () -> Unit = {},
    isNumericTextField: Boolean = false,
    singleLine: Boolean = true,
    focusRequester: FocusRequester,
    isEditableType: Boolean = false,
    onClickEditable: () -> Unit = {},
    disabledBackgroundColor: Color = LuziaTheme.palette.text.helper.copy(alpha = 0.3f),
    disabledErrorBackgroundColor: Color = Color.Transparent,
    disabledSupportingTextColor: Color = LuziaTheme.palette.text.helper
) {
    val keyboard = LocalSoftwareKeyboardController.current
    var isFocusedTextForm by remember {
        mutableStateOf(false)
    }
    val trailingIcon: @Composable (() -> Unit)? =
        if (isSelector) {
            {
                Icon(
                    painter = painterResource(id = R.drawable.ic_chevron_right),
                    contentDescription = null,
                    modifier = Modifier.padding(end = 16.dp),
                    tint = LuziaTheme.palette.text.primary
                )
            }
        } else if (isEditableType) {
            if (!enabled) {
                {
                    ButtonIcon(
                        modifier = Modifier
                            .padding(end = 16.dp)
                            .testTag("buttonEdit"),
                        icon = painterResource(id = R.drawable.ic_edit),
                        contentDescription = "Icono de edición",
                        tint = LuziaTheme.palette.text.secondary,
                        containerColor = Color.Transparent,
                        onClick = {
                            onClickEditable()
                        }
                    )
                }
            } else {
                null
            }
        } else {
            null
        }

    Column(
        modifier =
        modifier
            .fillMaxWidth()
            .wrapContentHeight(Alignment.Top)
            .padding(
                top = 8.dp
            )
    ) {
        OutlinedTextField(
            modifier =
            Modifier
                .testTag(tag ?: "inputField")
                .wrapContentHeight()
                .fillMaxWidth()
                .semantics { contentDescription = contentDescriptionText }
                .onFocusChanged {
                    if (!it.isFocused) {
                        keyboard?.hide()
                    }
                    isFocusedTextForm = it.isFocused
                    onFocused(it.isFocused)
                }
                .focusRequester(focusRequester),
            interactionSource =
            remember { MutableInteractionSource() }
                .also { interactionSource ->
                    LaunchedEffect(interactionSource) {
                        interactionSource.interactions.collect {
                            if (it is PressInteraction.Release) {
                                if (isSelector) {
                                    onClickButtonListener()
                                }
                            }
                        }
                    }
                },
            visualTransformation = VisualTransformation.None,
            value = messageText,
            onValueChange = {
                val text = limitChar?.let { limit -> it.take(limit) } ?: it
                onTextChanged.invoke(text)
            },
            singleLine = singleLine,
            placeholder = {
                placeholderText?.let {
                    LuziaText(
                        text = it,
                        style = LuziaTheme.typography.body.regular.default,
                        color = LuziaTheme.palette.text.helper
                    )
                }
            },
            textStyle = LuziaTheme.typography.body.regular.default,
            keyboardOptions =
            KeyboardOptions(
                imeAction = ImeAction.Done,
                capitalization = KeyboardCapitalization.Sentences,
                keyboardType =
                if (isNumericTextField) {
                    KeyboardType.Number
                } else {
                    KeyboardType.Text
                }
            ),
            keyboardActions =
            KeyboardActions(
                onDone = {
                    keyboard?.hide()
                    onDoneButtonKBListener(messageText)
                }
            ),
            colors = OutlinedTextFieldDefaults.colors().copy(
                focusedContainerColor = LuziaTheme.palette.surface.background,
                unfocusedContainerColor = LuziaTheme.palette.surface.background,
                disabledContainerColor = disabledBackgroundColor,
                cursorColor = LuziaTheme.palette.text.primary,
                focusedSupportingTextColor = LuziaTheme.palette.text.secondary,
                unfocusedSupportingTextColor = LuziaTheme.palette.text.helper,
                disabledSupportingTextColor = disabledSupportingTextColor,
                errorTextColor = LuziaTheme.palette.accents.red.error90,
                errorSupportingTextColor = LuziaTheme.palette.accents.red.error90,
                errorContainerColor = disabledErrorBackgroundColor,
                focusedTextColor = LuziaTheme.palette.text.primary,
                unfocusedTextColor = LuziaTheme.palette.text.secondary,
                disabledTextColor = LuziaTheme.palette.text.helper
            ),
            enabled = enabled,
            isError = isError,
            trailingIcon = trailingIcon,
            readOnly = isSelector,
            supportingText = {
                if (isError && !textError.isNullOrEmpty()) {
                    LuziaText(
                        text = textError,
                        style = LuziaTheme.typography.body.regular.footnote
                    )
                } else if (!supportingText.isNullOrEmpty()) {
                    LuziaText(
                        text = supportingText,
                        style = LuziaTheme.typography.body.regular.footnote
                    )
                }
            }
        )
    }
}

@Preview(showBackground = true, widthDp = 600, heightDp = 600)
@Composable
fun TextFieldFormPreview() {
    val focusRequester: FocusRequester = remember { FocusRequester() }
    var isEnabled by remember {
        mutableStateOf(false)
    }
    var isError by remember {
        mutableStateOf(false)
    }
    Column {
        TextFieldForm(
            placeholderText = "Country code",
            onDoneButtonKBListener = ({}),
            onTextChanged = {},
            isSelector = true,
            focusRequester = focusRequester
        )
        TextFieldForm(
            placeholderText = "Phone number",
            onDoneButtonKBListener = ({}),
            onTextChanged = {},
            focusRequester = focusRequester
        )
        TextFieldForm(
            placeholderText = "Disabled",
            onDoneButtonKBListener = ({}),
            onTextChanged = {},
            enabled = false,
            focusRequester = focusRequester
        )
        TextFieldForm(
            placeholderText = "Phone number",
            onDoneButtonKBListener = ({}),
            onTextChanged = {},
            isError = true,
            focusRequester = focusRequester
        )
        TextFieldForm(
            onDoneButtonKBListener = {},
            onTextChanged = {
                isError = it.isEmpty() || it.isBlank()
            },
            isError = isError,
            focusRequester = focusRequester,
            enabled = true,
            supportingText = "We use this name to address you.",
            textError = "This field is required",
            isEditableType = true,
            onClickEditable = {
                isEnabled = true
            },
            messageText = "Jorge"
        )
    }
}
