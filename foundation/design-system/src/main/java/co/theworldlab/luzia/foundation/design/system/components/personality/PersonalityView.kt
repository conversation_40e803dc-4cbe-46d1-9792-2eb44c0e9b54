package co.theworldlab.luzia.foundation.design.system.components.personality

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import co.theworldlab.luzia.foundation.design.system.components.message.UnreadMessageView
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.model.UiImage
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import coil3.compose.AsyncImage
import co.thewordlab.luzia.foundation.design.system.R as designR
import co.thewordlab.luzia.foundation.localization.R as localizationR

@Composable
fun AddPersonalityCompactView(
    onAddClicked: () -> Unit
) {
    Column(
        modifier = Modifier
            .clip(RoundedCornerShape(Corners.X4.dp))
            .click { onAddClicked() }
            .padding(Spacing.X8.dp)
            .testTag("buttonCreateBestie"),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            modifier = Modifier
                .clip(CircleShape)
                .background(color = LuziaTheme.palette.interactive.brand, CircleShape)
                .size(IconSizes.X64.dp)
                .padding(Spacing.X20.dp),
            painter = painterResource(designR.drawable.ic_plus),
            tint = LuziaTheme.palette.interactive.contrast,
            contentDescription = null
        )
        Spacer(Modifier.height(Spacing.X8.dp))
        LuziaText(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(localizationR.string.custom_bestie_text),
            color = LuziaTheme.palette.text.primary,
            style = LuziaTheme.typography.body.semiBold.footnote,
            textAlign = TextAlign.Center,
            maxLines = 2,
            overflow = TextOverflow.Clip
        )
    }
}

@Composable
fun AddPersonalityView(onAddClicked: () -> Unit) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .click(action = onAddClicked)
            .padding(vertical = Spacing.X8.dp, horizontal = Spacing.X16.dp)
            .testTag("buttonCreateBestie"),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Spacing.X16.dp)
    ) {
        Icon(
            modifier = Modifier
                .clip(CircleShape)
                .background(color = LuziaTheme.palette.interactive.brand, CircleShape)
                .size(IconSizes.X48.dp)
                .padding(Spacing.X16.dp),
            painter = painterResource(designR.drawable.ic_plus),
            tint = LuziaTheme.palette.interactive.contrast,
            contentDescription = null
        )
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(Spacing.X4.dp)
        ) {
            LuziaText(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(localizationR.string.custom_bestie_create_title),
                color = LuziaTheme.palette.text.primary,
                style = LuziaTheme.typography.body.semiBold.small
            )
            LuziaText(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(localizationR.string.custom_bestie_create_desc),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                color = LuziaTheme.palette.text.secondary,
                style = LuziaTheme.typography.body.regular.footnote
            )
        }
    }
}

@Composable
fun PersonalityCompactView(
    id: String,
    isCustomBestie: Boolean,
    title: String,
    image: UiImage,
    onNavigateToPersonality: (id: String, isCustomBestie: Boolean) -> Unit = { _, _ -> }
) {
    Column(
        modifier = Modifier
            .clip(RoundedCornerShape(Corners.X4.dp))
            .click { onNavigateToPersonality(id, isCustomBestie) }
            .padding(Spacing.X8.dp)
            .testTag("item/$id"),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        AsyncImage(
            modifier = Modifier
                .clip(CircleShape)
                .size(IconSizes.X64.dp),
            model = when (image) {
                is UiImage.Resource -> image.resourceId
                is UiImage.Plain -> image.url
            },
            contentDescription = null
        )
        Spacer(Modifier.height(Spacing.X4.dp))
        LuziaText(
            modifier = Modifier.fillMaxWidth(),
            text = title,
            color = LuziaTheme.palette.text.primary,
            style = LuziaTheme.typography.body.semiBold.footnote,
            textAlign = TextAlign.Center,
            maxLines = 2,
            overflow = TextOverflow.Clip
        )
    }
}

@Composable
fun PersonalityView(
    modifier: Modifier,
    title: String,
    description: String,
    image: UiImage,
    avatarSize: Dp,
    showUnreadBadge: Boolean,
    lastMessageDate: String = ""
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = Spacing.X8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Spacing.X16.dp)
    ) {
        AsyncImage(
            modifier = Modifier
                .clip(CircleShape)
                .size(avatarSize),
            model = when (image) {
                is UiImage.Resource -> image.resourceId
                is UiImage.Plain -> image.url
            },
            contentDescription = null
        )
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(Spacing.X4.dp)
        ) {
            LuziaText(
                modifier = Modifier.fillMaxWidth(),
                text = title,
                color = LuziaTheme.palette.text.primary,
                style = LuziaTheme.typography.body.semiBold.small
            )
            LuziaText(
                modifier = Modifier.fillMaxWidth(),
                text = description,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                color = LuziaTheme.palette.text.secondary,
                style = LuziaTheme.typography.body.regular.footnote
            )
        }
        if (showUnreadBadge) {
            UnreadMessageView()
        }

        if (lastMessageDate.isNotEmpty()) {
            LuziaText(
                text = lastMessageDate,
                style = LuziaTheme.typography.body.semiBold.caption,
                color = LuziaTheme.palette.text.secondary
            )
        }
    }
}
