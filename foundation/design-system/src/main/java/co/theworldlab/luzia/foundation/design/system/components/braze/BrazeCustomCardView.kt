package co.theworldlab.luzia.foundation.design.system.components.braze

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Card
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Velocity
import androidx.compose.ui.unit.dp
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.message.MarkdownTextView
import co.theworldlab.luzia.foundation.design.system.components.message.rememberRichTextStyle
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.helpers.pxToDp
import co.theworldlab.luzia.foundation.design.system.legacy.composables.LuziaCardDefaults
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import coil3.compose.AsyncImage
import com.braze.jetpackcompose.contentcards.ContentCardsList
import com.braze.jetpackcompose.contentcards.styling.ContentCardListStyling
import com.halilibo.richtext.ui.RichTextThemeProvider

private const val CONTENT_CARD_MIN_HEIGHT = 90

@Composable
fun BrazeCustomCard(
    location: BrazeCardLocation,
    onDataReceived: (size: Int) -> Unit,
    onLinkClicked: (url: String) -> Unit
) {
    var cardContainerSize by remember { mutableIntStateOf(CONTENT_CARD_MIN_HEIGHT) }
    ContentCardsList(
        emptyComposable = { onDataReceived(0) },
        customCardComposer = { card ->
            BuildCard(
                model = card.buildCardModel(),
                onLinkClicked = onLinkClicked,
                onSizeChanged = {
                    cardContainerSize = it
                    onDataReceived(it)
                },
                onCardDismissed = { onDataReceived(0) },
                onDataReceived = onDataReceived
            )
            true
        },
        cardUpdateHandler = { cards ->
            cards
                .filter { card -> !card.isDismissed && card.isValid(location) }
                .also { items -> onDataReceived(items.size) }
        },
        style = ContentCardListStyling(
            modifier = Modifier
                .fillMaxWidth()
                .height(cardContainerSize.pxToDp() + Spacing.X8.dp)
                .disabledVerticalScroll()
                .disabledHorizontalScroll(),
            listPadding = 0.dp,
            listBackgroundColor = Color.Transparent
        ),
        onCardDismissed = { onDataReceived(0) }
    )
}

@Composable
private fun BuildCard(
    model: BrazeCardContentModel,
    onLinkClicked: (String) -> Unit,
    onSizeChanged: (Int) -> Unit,
    onCardDismissed: () -> Unit,
    onDataReceived: (Int) -> Unit
) {
    when (model.type) {
        BrazeCardType.BASIC -> BasicCardContent(
            model,
            onLinkClicked,
            onSizeChanged,
            onCardDismissed
        )

        BrazeCardType.IMAGE_ONLY -> ImageCardContent(model, onLinkClicked, onSizeChanged)
        BrazeCardType.EMPTY -> onDataReceived(0)
    }
}

@Composable
private fun ImageCardContent(
    model: BrazeCardContentModel,
    onLinkClicked: (String) -> Unit,
    onSizeChanged: (Int) -> Unit
) {
    model.imageUrl.takeIf { it.isNotEmpty() }?.let {
        BaseCardContent(model, onLinkClicked, onSizeChanged) {
            Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.TopEnd) {
                AsyncImage(
                    modifier = Modifier.fillMaxWidth(),
                    model = it,
                    contentScale = ContentScale.FillWidth,
                    contentDescription = null
                )
            }
        }
    } ?: onSizeChanged(0)
}

@Composable
private fun BasicCardContent(
    model: BrazeCardContentModel,
    onLinkClicked: (String) -> Unit,
    onSizeChanged: (Int) -> Unit,
    onCardDismissed: () -> Unit
) {
    val contentCardColor =
        if (LuziaTheme.isDarkTheme) {
            LuziaTheme.palette.text.primary
        } else {
            LuziaTheme.palette.text.contrast
        }
    if (model.description.isNotEmpty() || model.title.isNotEmpty()) {
        BaseCardContent(model, onLinkClicked, onSizeChanged) {
            Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.TopEnd) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(Spacing.X12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    model.imageUrl.takeIf { it.isNotEmpty() }?.let {
                        AsyncImage(
                            modifier = Modifier.size(IconSizes.X48.dp),
                            model = it,
                            contentDescription = null
                        )
                        Spacer(modifier = Modifier.width(Spacing.X16.dp))
                    }
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.Center
                    ) {
                        model.title.takeIf { it.isNotEmpty() }?.let {
                            LuziaText(
                                text = it,
                                style = LuziaTheme.typography.body.semiBold.default,
                                color = contentCardColor
                            )
                            Spacer(modifier = Modifier.height(Spacing.X8.dp))
                        }
                        RichTextThemeProvider(
                            textStyleProvider = { LuziaTheme.typography.body.semiBold.small },
                            contentColorProvider = { contentCardColor }
                        ) {
                            MarkdownTextView(
                                modifier = Modifier.fillMaxWidth(),
                                text = model.description,
                                style = rememberRichTextStyle(),
                                onLinkClicked = { DO_NOTHING }
                            )
                        }
                    }
                }

                IconButton(
                    modifier = Modifier
                        .padding(Spacing.X8.dp)
                        .size(IconSizes.X32.dp),
                    onClick = {
                        model.markAs(BrazeCardViewedAction.ON_CLOSE)
                        onCardDismissed()
                    }
                ) {
                    Icon(
                        modifier = Modifier.size(IconSizes.X24.dp),
                        painter = painterResource(id = R.drawable.ic_close),
                        contentDescription = null,
                        tint = contentCardColor
                    )
                }
            }
        }
    } else {
        onSizeChanged(0)
    }
}

@Composable
private fun BaseCardContent(
    model: BrazeCardContentModel,
    onLinkClicked: (String) -> Unit,
    onSizeChanged: (Int) -> Unit,
    content: @Composable () -> Unit
) {
    Box(modifier = Modifier.padding(vertical = Spacing.X8.dp, horizontal = Spacing.X16.dp)) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .onSizeChanged { onSizeChanged(it.height) }
                .click {
                    model.markAs(BrazeCardViewedAction.ON_CLICK)
                    onLinkClicked(model.link)
                },
            colors = LuziaCardDefaults.cardColors(
                containerColor = model.backgroundColorName.extractColor(),
                contentColor = model.backgroundColorName.extractColor()
            ),
        ) {
            content()
        }
    }
}

private val verticalScrollConsumer = object : NestedScrollConnection {
    override fun onPreScroll(available: Offset, source: NestedScrollSource) =
        available.copy(x = 0f)

    override suspend fun onPreFling(available: Velocity) = available.copy(x = 0f)
}

private val horizontalScrollConsumer = object : NestedScrollConnection {
    override fun onPreScroll(available: Offset, source: NestedScrollSource) =
        available.copy(y = 0f)

    override suspend fun onPreFling(available: Velocity) = available.copy(y = 0f)
}

fun Modifier.disabledVerticalScroll(disabled: Boolean = true) =
    if (disabled) this.nestedScroll(verticalScrollConsumer) else this

fun Modifier.disabledHorizontalScroll(disabled: Boolean = true) =
    if (disabled) this.nestedScroll(horizontalScrollConsumer) else this
