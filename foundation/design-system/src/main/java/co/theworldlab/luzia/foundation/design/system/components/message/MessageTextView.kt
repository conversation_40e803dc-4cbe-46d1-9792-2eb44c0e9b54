package co.theworldlab.luzia.foundation.design.system.components.message

import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageModel
import co.theworldlab.luzia.foundation.design.system.components.message.model.MessageViewActions
import co.theworldlab.luzia.foundation.design.system.legacy.composables.BaseContextualMenuOptions
import co.theworldlab.luzia.foundation.design.system.legacy.composables.LuziaContextualMenu

const val ORIGIN_LONG_PRESS = "longpressmenu"
const val ORIGIN_ICON = "icon"

@Composable
fun MessageTextView(
    modifier: Modifier = Modifier,
    model: MessageModel.Text,
    actions: MessageViewActions,
) {
    val containerData = model.containerData.copy(
        onTextToSpeech = if (model.capabilities.enableTextToSpeech && model.containerData.isAi) {
            { actions.onTextToSpeech(model, ORIGIN_ICON) }
        } else {
            null
        }
    )

    MessageContainerView(
        modifier = modifier,
        data = containerData
    ) {
        var contextMenuPosition: DpOffset? by remember { mutableStateOf(null) }
        val density = LocalDensity.current
        val itemHeight = remember { mutableStateOf(0.dp) }
        MarkdownTextView(
            modifier = Modifier
                .onSizeChanged { itemHeight.value = with(density) { it.height.toDp() } }
                .pointerInput(Unit) {
                    detectTapGestures(
                        onLongPress = {
                            val onLongClick = actions.onLongClick
                            if (onLongClick != null) {
                                onLongClick(model)
                            } else {
                                contextMenuPosition = DpOffset(it.x.toDp(), it.y.toDp())
                            }
                        }
                    )
                },
            text = enrichWithHighlight(model.text, model.query),
            style = rememberRichTextStyle(),
            onLinkClicked = { actions.onLinkClicked(it) }
        )
        contextMenuPosition?.let {
            LuziaContextualMenu(
                isVisible = true,
                pressOffset = it,
                itemHeight = itemHeight.value,
                onDismiss = { contextMenuPosition = null },
                dropDownItemList = BaseContextualMenuOptions.messageDropDownList(
                    text = model.text,
                    isAi = model.containerData.isAi,
                    onDismiss = { contextMenuPosition = null },
                    isFavorite = model.containerData.isFavorite,
                    enableFavoriteActions = model.capabilities.enableFavoriteActions,
                    enableMessagingActions = model.capabilities.enableMessagingActions,
                    enableTextToSpeech = model.capabilities.enableTextToSpeech,
                    onTextCopied = { actions.onTextCopied(model) },
                    onTextShared = { actions.onTextShared(model) },
                    onTextReported = { actions.onTextReported(model) },
                    onTextEdited = { actions.onTextEdited(model) },
                    onTextDeleted = { actions.onTextDeleted(model) },
                    onTextFavorite = { actions.onTextFavorite(model) },
                    onTextToSpeech = { actions.onTextToSpeech(model, ORIGIN_LONG_PRESS) }
                )
            )
        }
    }
}

private fun enrichWithHighlight(text: String, query: String): String {
    if (query.isEmpty()) return text
    return text.replace(query, "`$query`", true)
}
