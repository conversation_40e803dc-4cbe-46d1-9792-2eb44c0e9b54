package co.theworldlab.luzia.foundation.design.system.components.button

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import co.theworldlab.luzia.foundation.design.system.helpers.addShadow
import co.theworldlab.luzia.foundation.design.system.legacy.composables.ButtonFilled
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun CtaContainerView(
    modifier: Modifier,
    ctaText: String,
    ctaEnabled: <PERSON>olean,
    ctaLoading: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = modifier
            .addShadow(elevation = Spacing.X20.dp, corners = Corners.X0)
            .fillMaxWidth()
            .background(LuziaTheme.palette.surface.content)
            .padding(Spacing.X16.dp)
    ) {
        ButtonFilled(
            modifier = Modifier.fillMaxWidth(),
            onClick = onClick,
            enabled = ctaEnabled,
            isLoading = ctaLoading,
            buttonText = ctaText
        )
    }
}
