package co.theworldlab.luzia.foundation.design.system.components.input

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.updateTransition
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.awaitDragOrCancellation
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.awaitLongPressOrCancellation
import androidx.compose.foundation.gestures.awaitTouchSlopOrCancellation
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.pointer.AwaitPointerEventScope
import androidx.compose.ui.input.pointer.PointerInputChange
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.input.pointer.positionChange
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.thewordlab.luzia.foundation.localization.R
import co.theworldlab.luzia.foundation.design.system.components.audio.AudioRecordState
import co.theworldlab.luzia.foundation.design.system.components.audio.v2.AudioRecordingViewV2
import co.theworldlab.luzia.foundation.design.system.components.input.model.CameraResult
import co.theworldlab.luzia.foundation.design.system.components.lds.tooltip.LuziaTooltipComposer
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import coil.compose.AsyncImage
import java.io.File
import java.util.concurrent.TimeUnit
import kotlin.math.abs
import co.thewordlab.luzia.foundation.design.system.R as designR

private const val RANGE_TOLERANCE = 1.1f
val pinRange = 112.dp

@Suppress("LongMethod")
@Composable
fun InputToolbar(
    modifier: Modifier = Modifier,
    initialInput: String = "",
    openKeyboard: Boolean,
    config: InputToolbarConfig,
    hint: String = stringResource(R.string.input_chat_placeholder),
    startSection: (@Composable RowScope.() -> Unit) = { DO_NOTHING },
    topSection: (@Composable ColumnScope.() -> Unit) = { DO_NOTHING }
) {
    val message = remember { mutableStateOf("") }
    val isRecording = remember { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() }

    LaunchedEffect(initialInput) {
        if (message.value.isEmpty() && initialInput.isNotEmpty()) {
            message.value = initialInput
        }
    }

    LaunchedEffect(openKeyboard) {
        if (openKeyboard) {
            focusRequester.requestFocus()
        }
    }

    LaunchedEffect(config.cameraResult?.prompt) {
        config.cameraResult?.prompt?.let { message.value = it }
    }

    Column(
        modifier = modifier
            .testTag("inputBar")
            .background(LuziaTheme.palette.surface.content)
            .fillMaxWidth()
            .padding(vertical = Spacing.X8.dp)
            .navigationBarsPadding()
    ) {
        topSection()
        CameraResultView(
            result = config.cameraResult,
            onClearResult = config.onClearCameraResult
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = Spacing.X8.dp)
                .border(
                    color = LuziaTheme.palette.border.primary,
                    shape = RoundedCornerShape(Corners.X4.dp),
                    width = Spacing.X1.dp
                )
                .imePadding()
        ) {
            AnimatedVisibility(visible = !isRecording.value) {
                InputChat(
                    modifier = Modifier
                        .fillMaxWidth()
                        .testTag("inputBar"),
                    text = message.value,
                    drawBorders = false,
                    hint = hint,
                    onTextChanged = {
                        message.value = it
                        config.onTextChanged(message.value)
                    },
                    focusRequester = focusRequester
                )
            }
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = Spacing.X8.dp)
            ) {
                StartSectionContainer(
                    modifier = Modifier
                        .align(Alignment.BottomStart)
                        .padding(start = Spacing.X8.dp),
                    startSection = startSection,
                    config = config
                )
                BottomActionsContent(
                    modifier = Modifier.fillMaxWidth(),
                    isSendEnabled = config.isSendEnabled && config.actionsEnabled,
                    canRecord = message.value.isEmpty(),
                    onTextSubmit = {
                        val inputMessage = InputMessage.Text(
                            text = message.value,
                            imageUri = config.cameraResult?.uri
                        )
                        config.onClearCameraResult()
                        config.onSendMessage(inputMessage)
                        config.onTextChanged("")
                        message.value = ""
                    },
                    onAudioSubmit = {
                        config.onSendMessage(InputMessage.Audio(it))
                    },
                    onRecordingState = {
                        when (it) {
                            AudioRecordState.STARTED,
                            AudioRecordState.PINNED -> isRecording.value = true

                            else -> isRecording.value = false
                        }
                        config.onRecordingState(it)
                    }
                )
            }
        }
    }
}

@Composable
private fun CameraResultView(result: CameraResult?, onClearResult: () -> Unit) {
    if (result != null) {
        Box(
            modifier = Modifier
                .padding(Spacing.X8.dp)
                .size(Spacing.X56.dp)
                .clip(RoundedCornerShape(Corners.X3.dp)),
        ) {
            AsyncImage(
                modifier = Modifier.fillMaxSize(),
                model = result.uri,
                contentScale = ContentScale.Crop,
                contentDescription = result.prompt
            )
            Icon(
                modifier = Modifier
                    .padding(Spacing.X4.dp)
                    .size(Spacing.X20.dp)
                    .align(Alignment.TopEnd)
                    .clip(CircleShape)
                    .background(LuziaTheme.palette.surface.content)
                    .click(action = onClearResult)
                    .padding(Spacing.X2.dp),
                painter = painterResource(designR.drawable.ic_close),
                contentDescription = stringResource(R.string.close_icon_content_description),
                tint = LuziaTheme.palette.text.primary
            )
        }
    }
}

@Composable
private fun StartSectionContainer(
    modifier: Modifier,
    startSection: @Composable (RowScope.() -> Unit),
    config: InputToolbarConfig
) {
    Row(
        modifier = modifier.wrapContentWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (config.addResourcesConfig != AddResourcesConfig.None) {
            LuziaTooltipComposer(tooltip = config.addTooltip) {
                AddResourcesButton(
                    onButtonClicked = { config.onMetricsAction(MetricsActions.PlusButtonTapped) },
                    onAddResources = config.onAddResources,
                    addResourcesConfig = config.addResourcesConfig,
                    enabled = config.actionsEnabled
                )
            }
            Spacing.X8.Horizontal()
        }
        startSection()
    }
}

@Composable
private fun BottomActionsContent(
    modifier: Modifier = Modifier,
    isSendEnabled: Boolean,
    canRecord: Boolean,
    onTextSubmit: () -> Unit,
    onAudioSubmit: (File) -> Unit,
    onRecordingState: (AudioRecordState) -> Unit
) {
    Box(
        modifier = modifier.fillMaxWidth(),
        contentAlignment = Alignment.CenterStart
    ) {
        val offsetX = remember { mutableFloatStateOf(0f) }
        val offsetY = remember { mutableFloatStateOf(0f) }
        val recordState = remember { mutableStateOf(AudioRecordState.NONE) }
        LaunchedEffect(recordState.value) { onRecordingState(recordState.value) }

        val cancelRange = LocalConfiguration.current.screenWidthDp.dp / 2
        var recordingStartedAt = 0L

        val sendButtonTransition =
            updateTransition(targetState = recordState, label = "Send Button")
        val scale = sendButtonTransition.animateFloat(
            label = "Scale",
            targetValueByState = { if (it.value.scaleButton) 3f else 1f }
        )
        AudioRecordingViewV2(
            modifier = Modifier.fillMaxWidth(),
            onCleared = {
                recordingStartedAt = 0
                recordState.value = AudioRecordState.NONE
            },
            onCancelled = {
                recordingStartedAt = 0
                recordState.value = AudioRecordState.CANCELLED
            },
            onFileRecorded = { audioFile ->
                recordingStartedAt = 0
                audioFile?.let { onAudioSubmit(it) }
                recordState.value = AudioRecordState.NONE
            },
            audioRecordState = recordState
        )
        SendButton(
            enabled = isSendEnabled,
            scale = scale,
            recordState = recordState,
            isMicrophone = canRecord && recordState.value != AudioRecordState.PINNED,
            modifier = Modifier
                .testTag("buttonSend")
                .padding(end = Spacing.X8.dp)
                .align(Alignment.BottomEnd),
            iconModifier = Modifier
                .offset { IntOffset(offsetX.floatValue.toInt(), offsetY.floatValue.toInt()) }
                .then(
                    if (canRecord) {
                        Modifier.pointerInput(Unit) {
                            awaitEachGesture {
                                val down = awaitFirstDown(false)
                                val longPressChange = awaitLongPressOrCancellation(down.id)
                                if (longPressChange != null) {
                                    recordingStartedAt = System.currentTimeMillis()
                                    recordState.value = AudioRecordState.STARTED
                                    moveItem(
                                        down = down,
                                        cancelRange = cancelRange,
                                        offsetX = offsetX,
                                        offsetY = offsetY,
                                        onCancelled = {
                                            recordingStartedAt = 0L
                                            recordState.value = AudioRecordState.CANCELLED
                                        },
                                        onPinned = {
                                            recordingStartedAt = System.currentTimeMillis()
                                            recordState.value = AudioRecordState.PINNED
                                        },
                                        onCompleted = {
                                            recordState.value =
                                                recordingStartedAt.validateRecordFinished()
                                            recordingStartedAt = 0L
                                        }
                                    )
                                }
                            }
                        }
                    } else {
                        Modifier
                    }
                ),
            onClick = {
                if (recordState.value.isRecording) {
                    recordState.value = recordingStartedAt.validateRecordFinished()
                    recordingStartedAt = 0L
                }
                onTextSubmit()
            }
        )
    }
}

private fun Long.validateRecordFinished(): AudioRecordState {
    val time = System.currentTimeMillis() - this
    return if (TimeUnit.MILLISECONDS.toSeconds(time) > 1) {
        AudioRecordState.DONE
    } else {
        AudioRecordState.NONE
    }
}

@Suppress("NestedBlockDepth", "LongParameterList")
private suspend fun AwaitPointerEventScope.moveItem(
    cancelRange: Dp,
    down: PointerInputChange,
    offsetX: MutableState<Float>,
    offsetY: MutableState<Float>,
    onCancelled: () -> Unit,
    onPinned: () -> Unit,
    onCompleted: () -> Unit
) {
    var change =
        awaitTouchSlopOrCancellation(down.id) { change, over ->
            val original = Offset(offsetX.value, offsetY.value)
            val summed = original + over
            change.consume()
            offsetX.value = summed.x
            offsetY.value = summed.y
        }
    var shouldCancel = false
    while (change != null && change.pressed && !shouldCancel) {
        change = awaitDragOrCancellation(change.id)
        if (change != null && change.pressed) {
            val original = Offset(offsetX.value, offsetY.value)
            val summed = original + change.positionChange()
            val deltaDelete = abs(summed.x)
            val deltaPin = abs(summed.y)
            val maxHeight = pinRange.toPx()
            val maxWidth = cancelRange.toPx()
            val dragHeight = maxHeight * RANGE_TOLERANCE
            val dragWidth = maxWidth * RANGE_TOLERANCE
            if (deltaPin > deltaDelete) {
                if (deltaPin <= dragHeight) {
                    change.consume()
                    offsetX.value = 0f
                    offsetY.value = summed.y.coerceAtMost(0f)
                    if (deltaPin > maxHeight) {
                        onPinned()
                        shouldCancel = true
                    }
                }
            } else {
                if (deltaDelete <= dragWidth) {
                    change.consume()
                    offsetX.value = summed.x.coerceAtMost(0f)
                    offsetY.value = 0f
                    if (deltaDelete > maxWidth) {
                        onCancelled()
                        shouldCancel = true
                    }
                }
            }
        }
    }
    offsetX.value = 0f
    offsetY.value = 0f
    if (!shouldCancel) onCompleted()
}

@Preview
@Composable
private fun Preview() {
    InputToolbar(
        modifier = Modifier,
        openKeyboard = false,
        config = InputToolbarConfig(
            isSendEnabled = true,
            actionsEnabled = true,
            addTooltip = null,
            cameraResult = null,
            onSendMessage = { DO_NOTHING },
            onTextChanged = { DO_NOTHING },
            onRecordingState = { DO_NOTHING },
            onClearCameraResult = { DO_NOTHING }
        )
    )
}
