package co.theworldlab.luzia.foundation.design.system.tokens

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

enum class Spacing(val dp: Dp) {
    X1(1.dp),
    X2(2.dp),
    X4(4.dp),
    X6(6.dp),
    X8(8.dp),
    X10(10.dp),
    X12(12.dp),
    X14(14.dp),
    X16(16.dp),
    X18(18.dp),
    X20(20.dp),
    X24(24.dp),
    X32(32.dp),
    X40(40.dp),
    X48(48.dp),
    X56(56.dp),
    X60(60.dp),
    X64(64.dp),
    X80(80.dp),
    X100(100.dp),
    X128(128.dp),
    X150(150.dp),
    X160(160.dp),
    X180(180.dp),
    X240(240.dp),
    X256(256.dp);

    @Composable
    fun Vertical() {
        Spacer(Modifier.height(this.dp))
    }

    @Composable
    fun Horizontal() {
        Spacer(Modifier.width(this.dp))
    }
}
