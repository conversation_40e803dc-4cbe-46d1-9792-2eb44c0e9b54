package co.theworldlab.luzia.foundation.design.system.components.badge

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import kotlin.math.sqrt

@Composable
fun Modifier.badge(
    show: <PERSON><PERSON><PERSON>,
    color: Color = LuziaTheme.palette.accents.red.error90,
    radiusRatio: Float = 0.3f,
    offset: Dp = 0.dp
): Modifier {
    return this.then(
        Modifier.drawWithContent {
            drawContent()
            if (show) {
                val radius = size.width / 2f
                val hypotenuse = radius + offset.toPx()
                val badgeX = radius + hypotenuse / sqrt(2f)
                val badgeY = radius - hypotenuse / sqrt(2f)
                val position = Offset(badgeX, badgeY)
                drawCircle(color = color, radius = radius * radiusRatio, center = position)
            }
        }
    )
}
