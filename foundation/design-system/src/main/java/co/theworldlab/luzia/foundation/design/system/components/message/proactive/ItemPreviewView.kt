package co.theworldlab.luzia.foundation.design.system.components.message.proactive

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR

private val cornerShape = RoundedCornerShape(Corners.X4.dp)

@Composable
fun ItemPreviewView(
    modifier: Modifier = Modifier,
    title: String,
    description: String,
    @DrawableRes icon: Int
) {
    Box(
        modifier = modifier
            .clip(shape = cornerShape)
            .background(color = LuziaTheme.palette.surface.background)
            .border(
                width = 1.dp,
                color = LuziaTheme.palette.border.primary,
                shape = cornerShape
            )
    ) {
        Row(
            modifier = Modifier.padding(Spacing.X8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                modifier = Modifier
                    .size(IconSizes.X48.dp)
                    .clip(CircleShape),
                painter = painterResource(id = icon),
                contentDescription = null,
                tint = Color.Unspecified
            )
            Spacer(Modifier.width(Spacing.X8.dp))
            Column(
                modifier = Modifier,
                horizontalAlignment = Alignment.Start
            ) {
                LuziaText(
                    text = title,
                    modifier = Modifier.fillMaxWidth(),
                    style = LuziaTheme.typography.body.semiBold.small,
                    color = LuziaTheme.palette.text.primary
                )
                LuziaText(
                    text = description,
                    modifier = Modifier.fillMaxWidth(),
                    style = LuziaTheme.typography.body.regular.footnote,
                    color = LuziaTheme.palette.text.secondary
                )
            }
        }
    }
}

@Composable
@PreviewLightDark
private fun Preview() {
    LuziaTheme {
        ItemPreviewView(
            title = "Lorem Ipsum is simply",
            description = "Lorem Ipsum is simply dummy text of the printing and typesetting industry",
            icon = designR.drawable.luzia_with_background
        )
    }
}
