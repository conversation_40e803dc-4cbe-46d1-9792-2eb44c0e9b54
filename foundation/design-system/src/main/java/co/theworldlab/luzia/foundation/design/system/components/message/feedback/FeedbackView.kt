package co.theworldlab.luzia.foundation.design.system.components.message.feedback

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import co.thewordlab.luzia.foundation.design.system.R
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.localization.R as localizationR

private val roundedCornerShape = RoundedCornerShape(Corners.X2.dp)

@Composable
fun FeedbackView(modifier: Modifier = Modifier, model: FeedbackUiModel) {
    Box(
        modifier = modifier
            .background(color = LuziaTheme.palette.interactive.brandLight, shape = roundedCornerShape)
            .padding(Spacing.X8.dp),
        contentAlignment = Alignment.TopEnd
    ) {
        IconButton(
            modifier = Modifier
                .size(IconSizes.X16.dp)
                .clip(CircleShape),
            onClick = model.onDismiss,
            colors = IconButtonDefaults.iconButtonColors(
                contentColor = Color.White
            )
        ) {
            Icon(
                modifier = Modifier.size(size = IconSizes.X12.dp),
                painter = painterResource(R.drawable.ic_close),
                contentDescription = null,
                tint = LuziaTheme.palette.text.brand
            )
        }
        Column(modifier = Modifier.fillMaxWidth()) {
            LuziaText(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(id = model.title),
                style = LuziaTheme.typography.body.semiBold.small,
                color = LuziaTheme.palette.text.brand,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(Spacing.X8.dp))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center
            ) {
                ThumbButton(R.drawable.ic_thumb_up_24, model.onLiked)
                Spacer(modifier = Modifier.width(Spacing.X8.dp))
                ThumbButton(R.drawable.ic_thumb_down_24, model.onDisliked)
            }
        }
    }
}

@Composable
private fun ThumbButton(@DrawableRes icon: Int, action: () -> Unit) {
    IconButton(
        modifier = Modifier
            .size(IconSizes.X48.dp)
            .clip(CircleShape),
        onClick = action,
        colors = IconButtonDefaults.iconButtonColors(containerColor = Color.White)
    ) {
        Icon(
            modifier = Modifier.size(size = IconSizes.X24.dp),
            painter = painterResource(icon),
            contentDescription = null,
            tint = LuziaTheme.palette.interactive.brand
        )
    }
}

@Preview
@Composable
private fun FeedbackViewPreviewDark() {
    LuziaTheme(darkTheme = true) {
        FeedbackView(
            modifier = Modifier,
            model = FeedbackUiModel(
                title = localizationR.string.generic_error,
                onDismiss = { },
                onLiked = { },
                onDisliked = { }
            )
        )
    }
}

@Preview
@Composable
private fun FeedbackViewPreviewLight() {
    LuziaTheme(darkTheme = false) {
        FeedbackView(
            modifier = Modifier,
            model = FeedbackUiModel(
                title = localizationR.string.generic_error,
                onDismiss = { },
                onLiked = { },
                onDisliked = { }
            )
        )
    }
}
