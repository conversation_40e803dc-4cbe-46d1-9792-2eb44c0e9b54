package co.theworldlab.luzia.foundation.design.system.components.lds.textinput

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.tooling.preview.PreviewLightDark
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.helpers.click
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR

private const val NOT_SET = -1

@Composable
fun LuziaTextInput(
    modifier: Modifier = Modifier,
    value: String = "",
    hint: String = "",
    errorText: String = "",
    maxLines: Int = 1,
    maxLength: Int = NOT_SET,
    state: TextInputState = TextInputState.ENABLED,
    testTag: String? = null,
    onTextChanged: (String) -> Unit
) {
    val isError = state == TextInputState.ERROR
    val containerColor = LuziaTheme.palette.surface.content
    val textColor = LuziaTheme.palette.text.primary

    OutlinedTextField(
        modifier = modifier.testTag(testTag ?: "inputField"),
        value = value,
        onValueChange = {
            if (maxLength != NOT_SET && it.length <= maxLength || maxLength == NOT_SET) {
                onTextChanged(it)
            }
        },
        enabled = state != TextInputState.DISABLED,
        textStyle = LuziaTheme.typography.body.regular.small,
        singleLine = maxLines == 1,
        maxLines = maxLines,
        placeholder = { PlacerHolderContent(hint) },
        supportingText = supportiveTextContent(isError, errorText, maxLength, value.length),
        trailingIcon = {
            TrailingIconContent(value, state != TextInputState.DISABLED) {
                onTextChanged("")
            }
        },
        isError = isError,
        shape = RoundedCornerShape(Corners.X4.dp),
        colors = OutlinedTextFieldDefaults.colors(
            cursorColor = textColor,
            focusedTextColor = textColor,
            focusedContainerColor = containerColor,
            focusedBorderColor = LuziaTheme.palette.border.brand,
            unfocusedTextColor = textColor,
            unfocusedContainerColor = containerColor,
            unfocusedBorderColor = LuziaTheme.palette.border.primary,
            disabledTextColor = LuziaTheme.palette.text.helper,
            disabledContainerColor = containerColor,
            disabledBorderColor = LuziaTheme.palette.border.primary,
            errorTextColor = textColor,
            errorContainerColor = LuziaTheme.palette.accents.red.error10,
            errorBorderColor = LuziaTheme.palette.accents.red.error50,
        )
    )
}

@Composable
private fun PlacerHolderContent(text: String) {
    Text(
        text = text,
        style = LuziaTheme.typography.body.regular.small,
        color = LuziaTheme.palette.text.secondary
    )
}

@Composable
private fun supportiveTextContent(
    isError: Boolean,
    text: String,
    maxLength: Int,
    currentLength: Int
): @Composable (() -> Unit)? = if (isError || maxLength != NOT_SET) {
    {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.End
        ) {
            if (isError) {
                Row(
                    modifier = Modifier.weight(1f),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(Spacing.X4.dp)
                ) {
                    Icon(
                        modifier = Modifier.size(IconSizes.X20.dp),
                        painter = painterResource(designR.drawable.ic_info),
                        contentDescription = null,
                        tint = LuziaTheme.palette.accents.red.error50
                    )
                    Text(
                        modifier = Modifier.clearAndSetSemantics {},
                        text = text,
                        style = LuziaTheme.typography.body.regular.small,
                        color = LuziaTheme.palette.accents.red.error50
                    )
                }
            }
            if (maxLength != NOT_SET) {
                Text(
                    text = "$currentLength/$maxLength",
                    style = LuziaTheme.typography.body.regular.footnote,
                    color = if (isError) {
                        LuziaTheme.palette.accents.red.error50
                    } else {
                        LuziaTheme.palette.text.helper
                    }
                )
            }
        }
    }
} else {
    null
}

@Composable
private fun TrailingIconContent(text: String, isNotDisabled: Boolean, clearText: () -> Unit) {
    if (text.isNotEmpty() && isNotDisabled) {
        Icon(
            painter = painterResource(id = designR.drawable.ic_close),
            contentDescription = null,
            tint = LuziaTheme.palette.interactive.primary,
            modifier = Modifier
                .size(IconSizes.X20.dp)
                .click(action = clearText)
        )
    }
}

@Composable
@PreviewLightDark
private fun Preview() {
    LuziaTheme {
        Column(
            modifier = Modifier
                .background(LuziaTheme.palette.surface.background)
                .padding(Spacing.X16.dp),
            verticalArrangement = Arrangement.spacedBy(Spacing.X16.dp)
        ) {
            Text(
                text = "Text Input",
                style = LuziaTheme.typography.headlines.h4,
                color = LuziaTheme.palette.text.primary
            )
            LuziaTextInput(
                value = "",
                hint = "Hint",
                onTextChanged = { DO_NOTHING },
                state = TextInputState.ENABLED
            )
            LuziaTextInput(
                value = "Input",
                hint = "Hint",
                onTextChanged = { DO_NOTHING },
                state = TextInputState.DISABLED
            )
            LuziaTextInput(
                value = "Input",
                hint = "Hint",
                onTextChanged = { DO_NOTHING },
                state = TextInputState.FILLED
            )
            LuziaTextInput(
                value = "Input",
                hint = "Hint",
                errorText = "Error Message",
                onTextChanged = { DO_NOTHING },
                state = TextInputState.ERROR,
                maxLength = 130
            )
            LuziaTextInput(
                value = "Input",
                hint = "Hint",
                onTextChanged = { DO_NOTHING },
                state = TextInputState.FILLED,
                maxLength = 130
            )
        }
    }
}
