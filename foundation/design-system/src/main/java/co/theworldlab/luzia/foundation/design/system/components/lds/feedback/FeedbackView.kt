package co.theworldlab.luzia.foundation.design.system.components.lds.feedback

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import co.theworldlab.luzia.foundation.design.system.components.message.model.FeedbackState
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing
import co.thewordlab.luzia.foundation.design.system.R as designR

@Composable
fun FeedbackView(
    modifier: Modifier = Modifier,
    state: FeedbackState?,
    isEnabled: Boolean,
    onFeedbackClicked: (FeedbackState) -> Unit,
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(Spacing.X4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        FeedbackButton(
            iconRes = designR.drawable.ic_dislike_24,
            isSelected = state == FeedbackState.Negative,
            isEnabled = isEnabled,
            onClick = { onFeedbackClicked(FeedbackState.Negative) },
        )

        FeedbackButton(
            iconRes = designR.drawable.ic_like_24,
            isSelected = state == FeedbackState.Positive,
            isEnabled = isEnabled,
            onClick = { onFeedbackClicked(FeedbackState.Positive) },
        )
    }
}

@Composable
private fun FeedbackButton(
    modifier: Modifier = Modifier,
    iconRes: Int,
    isSelected: Boolean,
    isEnabled: Boolean,
    onClick: () -> Unit,
    contentDescription: String? = null
) {
    val containerColor = if (isSelected) {
        LuziaTheme.palette.accents.blue.blue10
    } else {
        LuziaTheme.palette.interactive.contrast
    }

    val iconColor = if (isSelected) {
        LuziaTheme.palette.text.primary
    } else {
        LuziaTheme.palette.text.secondary
    }

    IconButton(
        onClick = onClick,
        modifier = modifier.size(IconSizes.X32.dp),
        enabled = isEnabled,
        colors = IconButtonDefaults.iconButtonColors(
            containerColor = containerColor,
            contentColor = iconColor,
            disabledContainerColor = LuziaTheme.palette.interactive.disabled,
            disabledContentColor = LuziaTheme.palette.text.contrast
        ),
    ) {
        Icon(
            painter = painterResource(iconRes),
            contentDescription = contentDescription,
            modifier = Modifier.size(IconSizes.X20.dp)
        )
    }
}

@PreviewLightDark
@Composable
private fun FeedbackViewPreview(
    @PreviewParameter(PreviewData::class) data: FeedbackState
) {
    LuziaTheme {
        Surface(color = LuziaTheme.palette.surface.background) {
            FeedbackView(
                state = data,
                isEnabled = true,
                onFeedbackClicked = {}
            )
        }
    }
}

private class PreviewData : PreviewParameterProvider<FeedbackState?> {
    override val values: Sequence<FeedbackState?>
        get() = sequenceOf(
            null,
            FeedbackState.Positive,
            FeedbackState.Negative
        )
}
