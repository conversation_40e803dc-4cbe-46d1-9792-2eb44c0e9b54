package co.theworldlab.luzia.foundation.design.system.components.message

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.style.TextAlign
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.IconSizes

@Composable
fun UnreadMessageView(modifier: Modifier = Modifier) {
    LuziaText(
        text = "1",
        color = LuziaTheme.palette.surface.content,
        style = LuziaTheme.typography.body.regular.footnote,
        textAlign = TextAlign.Center,
        modifier = modifier
            .size(IconSizes.X16.dp)
            .clip(CircleShape)
            .background(LuziaTheme.palette.interactive.brand)
    )
}
