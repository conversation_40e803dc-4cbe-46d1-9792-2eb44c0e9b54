package co.theworldlab.luzia.foundation.design.system.components.latex

import android.annotation.SuppressLint
import android.graphics.Color
import android.view.MotionEvent
import android.webkit.CookieManager
import android.webkit.WebView
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import com.google.accompanist.web.WebContent
import com.google.accompanist.web.WebView
import com.google.accompanist.web.WebViewState

@Suppress("LongMethod")
@SuppressLint("SetJavaScriptEnabled", "DEPRECATION", "ClickableViewAccessibility")
@Composable
fun LatexInlineView(
    modifier: Modifier = Modifier,
    message: String,
    query: String,
    onLongClick: (Int, Int) -> Unit
) {
    var webView by remember { mutableStateOf<WebView?>(null) }
    val textColor = LuziaTheme.palette.text.primary
    val highlightColor = LuziaTheme.palette.accents.yellow.yellow30
    val webContent = LatexHtmlGenerator.generateHTML(message, textColor, highlightColor)
    val finalMessage = enrichWithHighlight(webContent, query)
    val webUrl = "https://cdn.jsdelivr.net"
    val state = remember(finalMessage, textColor) {
        WebViewState(WebContent.Data(finalMessage, webUrl))
    }

    // Track the last touch position
    var lastTouchX by remember { mutableFloatStateOf(0f) }
    var lastTouchY by remember { mutableFloatStateOf(0f) }

    Box {
        WebView(
            state = state,
            modifier = modifier,
            onCreated = {
                it.isVerticalScrollBarEnabled = false
                it.isHorizontalScrollBarEnabled = false
                it.settings.javaScriptEnabled = true
                it.settings.domStorageEnabled = true
                it.settings.allowContentAccess = true
                it.settings.allowFileAccess = true
                it.isHapticFeedbackEnabled = false

                // Track touch positions for more accurate coordinates
                it.setOnTouchListener { _, event ->
                    when (event.action) {
                        MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> {
                            lastTouchX = event.x
                            lastTouchY = event.y
                        }
                    }
                    false // Don't consume the event
                }

                it.setOnLongClickListener { view ->
                    // Use the last touch position for more accurate coordinates
                    onLongClick(lastTouchX.toInt(), lastTouchY.toInt())
                    true
                }

                it.setBackgroundColor(Color.argb(1, 0, 0, 0))
                CookieManager.getInstance().setAcceptThirdPartyCookies(it, true)
                webView = it
            }
        )
        LaunchedEffect(finalMessage, webView) {
            webView?.loadDataWithBaseURL(
                webUrl,
                finalMessage,
                "text/html",
                "UTF-8",
                null
            )
        }
    }
}

private fun enrichWithHighlight(text: String, query: String): String {
    if (query.isEmpty()) return text
    return text.replace(query, "<mark>$query</mark>", true)
}
