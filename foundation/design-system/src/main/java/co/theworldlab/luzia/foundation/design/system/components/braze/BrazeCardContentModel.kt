package co.theworldlab.luzia.foundation.design.system.components.braze

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import co.thewordlab.luzia.foundation.common.extensions.DO_NOTHING
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import com.braze.models.cards.CaptionedImageCard
import com.braze.models.cards.Card
import com.braze.models.cards.ImageOnlyCard
import com.braze.models.cards.ShortNewsCard
import com.braze.models.cards.TextAnnouncementCard

const val EXTRA_LOCATION = "location"
private const val EXTRA_CARD_TYPE = "type"
private const val EXTRA_BACKGROUND_COLOR = "background_color"

data class BrazeCardContentModel(
    val title: String = "",
    val description: String = "",
    val imageUrl: String = "",
    val link: String = "",
    val backgroundColorName: String = "",
    val type: BrazeCardType = BrazeCardType.EMPTY,
    val location: BrazeCardLocation = BrazeCardLocation.EMPTY,
    val markAs: (action: BrazeCardViewedAction) -> Unit = { DO_NOTHING }
)

enum class BrazeCardLocation(val value: String) {
    EMPTY(""), TOP("top"), MIDDLE("middle"), BOTTOM("bottom"), FOOTER("footer");

    companion object {
        fun fromString(value: String): BrazeCardLocation =
            entries.firstOrNull { it.value == value } ?: EMPTY
    }
}

enum class BrazeCardType(val value: String) {
    EMPTY(""), BASIC("basic_card"), IMAGE_ONLY("image_only_card");

    companion object {
        fun fromString(value: String): BrazeCardType =
            entries.firstOrNull { it.value == value } ?: EMPTY
    }
}

enum class BrazeCardViewedAction {
    ON_CLICK, ON_CLOSE
}

internal fun Card.buildCardModel(): BrazeCardContentModel {
    val basicCard = extractBasicInfo()
    isDismissibleByUser = false
    return basicCard.copy(
        location = BrazeCardLocation.fromString(extras[EXTRA_LOCATION].orEmpty()),
        type = BrazeCardType.fromString(extras[EXTRA_CARD_TYPE].orEmpty()),
        backgroundColorName = extras[EXTRA_BACKGROUND_COLOR].orEmpty(),
        markAs = { action ->
            when (action) {
                BrazeCardViewedAction.ON_CLICK -> logClick()
                BrazeCardViewedAction.ON_CLOSE -> isDismissed = true
            }
        }
    )
}

internal fun Card.isValid(location: BrazeCardLocation) =
    !extras[EXTRA_LOCATION].isNullOrEmpty() &&
        extras[EXTRA_LOCATION] == location.value &&
        !extras[EXTRA_CARD_TYPE].isNullOrEmpty() &&
        !extras[EXTRA_BACKGROUND_COLOR].isNullOrEmpty()

@Composable
fun String.extractColor(): Color =
    when (this) {
        "blue_bright" -> LuziaTheme.palette.accents.blue.blue30
        "brand_dark" -> LuziaTheme.palette.primitives.brand.brand10
        "background" -> LuziaTheme.palette.surface.background
        "content" -> LuziaTheme.palette.surface.content
        "secondary" -> LuziaTheme.palette.surface.alternative
        "low" -> LuziaTheme.palette.interactive.brandLight
        "brand" -> LuziaTheme.palette.interactive.brand
        "accent" -> LuziaTheme.palette.surface.alternative
        "contrast" -> LuziaTheme.palette.text.contrast
        "bubble" -> LuziaTheme.palette.accents.green.green90
        else -> LuziaTheme.palette.surface.background
    }

private fun Card.extractBasicInfo(): BrazeCardContentModel =
    when (this) {
        is TextAnnouncementCard -> BrazeCardContentModel(
            title = title.orEmpty(),
            description = description,
            link = url.orEmpty()
        )

        is ShortNewsCard -> BrazeCardContentModel(
            title = title.orEmpty(),
            description = description,
            imageUrl = imageUrl,
            link = url.orEmpty()
        )

        is ImageOnlyCard -> BrazeCardContentModel(
            type = BrazeCardType.IMAGE_ONLY,
            imageUrl = imageUrl,
            link = url.orEmpty()
        )

        is CaptionedImageCard -> BrazeCardContentModel(
            title = title,
            description = description,
            imageUrl = imageUrl,
            link = url.orEmpty()
        )

        else -> BrazeCardContentModel()
    }
