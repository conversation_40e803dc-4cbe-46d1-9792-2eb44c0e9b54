package co.theworldlab.luzia.foundation.design.system.helpers

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Corners
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun Modifier.addShadow(
    corners: Corners = Corners.X4,
    elevation: Dp = Spacing.X20.dp,
    color: Color = LuziaTheme.palette.shadow
): Modifier = this then Modifier.shadow(
    elevation = elevation,
    clip = true,
    ambientColor = color,
    spotColor = color,
    shape = RoundedCornerShape(corners.dp)
)
