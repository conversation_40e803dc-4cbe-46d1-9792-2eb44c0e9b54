package co.theworldlab.luzia.foundation.design.system.components.segmented

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.SegmentedButton
import androidx.compose.material3.SegmentedButtonDefaults
import androidx.compose.material3.SingleChoiceSegmentedButtonRow
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import co.theworldlab.luzia.foundation.design.system.components.text.LuziaText
import co.theworldlab.luzia.foundation.design.system.theme.LuziaTheme
import co.theworldlab.luzia.foundation.design.system.tokens.Spacing

@Composable
fun SegmentedPickerView(
    modifier: Modifier = Modifier,
    tabs: List<String>,
    selectedTab: Int,
    onSelected: (Int) -> Unit
) {
    Box(
        modifier = modifier.fillMaxWidth()
            .clip(CircleShape)
            .background(LuziaTheme.palette.interactive.brandLight)
            .border(1.dp, LuziaTheme.palette.interactive.brand, CircleShape)
            .padding(horizontal = Spacing.X4.dp),
    ) {
        SingleChoiceSegmentedButtonRow(modifier = Modifier.fillMaxWidth()) {
            tabs.forEachIndexed { index, tab ->
                SegmentedButton(
                    modifier = Modifier.fillMaxWidth(),
                    selected = index == selectedTab,
                    onClick = { onSelected(index) },
                    icon = {},
                    shape = CircleShape,
                    colors = SegmentedButtonDefaults.colors(
                        activeContainerColor = LuziaTheme.palette.interactive.brand,
                        activeContentColor = Color.White,
                        activeBorderColor = Color.Transparent,
                        inactiveContainerColor = Color.Transparent,
                        inactiveBorderColor = Color.Transparent,
                        inactiveContentColor = LuziaTheme.palette.text.brand
                    ),
                    label = {
                        LuziaText(tab, style = LuziaTheme.typography.body.semiBold.small)
                    }
                )
            }
        }
    }
}

@Preview
@Composable
private fun Preview() {
    LuziaTheme {
        SegmentedPickerView(
            modifier = Modifier.fillMaxWidth(),
            tabs = listOf("Black", "White"),
            selectedTab = 0,
            onSelected = {}
        )
    }
}
