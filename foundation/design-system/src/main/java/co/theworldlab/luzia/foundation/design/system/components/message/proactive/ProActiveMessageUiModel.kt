package co.theworldlab.luzia.foundation.design.system.components.message.proactive

import androidx.annotation.DrawableRes

sealed class ProActiveMessageUiModel(val message: String) {

    data class Text(val title: String, val onLinkClicked: (String) -> Unit) :
        ProActiveMessageUiModel(title)

    data class Buttons(
        val title: String,
        val onLinkClicked: (String) -> Unit,
        val buttons: List<ButtonsUiModel>,
    ) : ProActiveMessageUiModel(title)

    data class Link(
        val title: String,
        val onLinkClicked: (String) -> Unit,
        val buttonText: String,
        val buttonAction: () -> Unit,
    ) : ProActiveMessageUiModel(title)

    data class LinkWithPreview(
        val title: String,
        val onLinkClicked: (String) -> Unit,
        val buttonText: String,
        val buttonAction: () -> Unit,
        val previewTitle: String,
        val previewDesc: String,
        @DrawableRes val previewIcon: Int,
    ) : ProActiveMessageUiModel(title)

    data class ExternalLink(val title: String, val link: String) : ProActiveMessageUiModel(title)

    data class Sticker(val stickerUrl: String) : ProActiveMessageUiModel("")

    data object None : ProActiveMessageUiModel("")
}

data class ButtonsUiModel(
    val text: String,
    val action: () -> Unit,
)
