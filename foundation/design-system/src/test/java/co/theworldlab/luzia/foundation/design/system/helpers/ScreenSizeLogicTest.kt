package co.theworldlab.luzia.foundation.design.system.helpers

import androidx.compose.ui.unit.dp
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Test

class ScreenSizeLogicTest {

    @Test
    fun `given maxHeight less than 640dp when determineContentVersion then returns Small`() = runTest {
        val result = determineContentVersion(maxHeight = 600.dp, maxWidth = 400.dp)
        assertEquals(ScreenContentVersion.Small, result)
    }

    @Test
    fun `given maxWidth less than 360dp when determineContentVersion then returns Small`() = runTest {
        val result = determineContentVersion(maxHeight = 700.dp, maxWidth = 300.dp)
        assertEquals(ScreenContentVersion.Small, result)
    }

    @Test
    fun `given both maxHeight and maxWidth are small when determineContentVersion then returns Small`() = runTest {
        val result = determineContentVersion(maxHeight = 600.dp, maxWidth = 300.dp)
        assertEquals(ScreenContentVersion.Small, result)
    }

    @Test
    fun `given maxHeight and maxWidth are large enough when determineContentVersion then returns Default`() = runTest {
        val result = determineContentVersion(maxHeight = 700.dp, maxWidth = 400.dp)
        assertEquals(ScreenContentVersion.Default, result)
    }

    @Test
    fun `given maxHeight at boundary 640dp when determineContentVersion then returns Small`() = runTest {
        val result = determineContentVersion(maxHeight = 640.dp, maxWidth = 400.dp)
        assertEquals(ScreenContentVersion.Small, result)
    }

    @Test
    fun `given maxWidth at boundary 360dp when determineContentVersion then returns Small`() = runTest {
        val result = determineContentVersion(maxHeight = 700.dp, maxWidth = 320.dp)
        assertEquals(ScreenContentVersion.Small, result)
    }
}
