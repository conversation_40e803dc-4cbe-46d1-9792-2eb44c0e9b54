package co.theworldlab.luzia.foundation.design.system.components.camera

import androidx.camera.core.CameraSelector
import org.junit.Assert.assertEquals
import org.junit.Test

class OrientationTest {

    @Test
    fun `test degrees with CameraLensModel BACK`() {
        assertEquals(0f, Orientation.PORTRAIT.degrees(CameraLensModel.BACK))
        assertEquals(270f, Orientation.LANDSCAPE.degrees(CameraLensModel.BACK))
        assertEquals(180f, Orientation.PORTRAIT_REVERSED.degrees(CameraLensModel.BACK))
        assertEquals(90f, Orientation.LANDSCAPE_REVERSED.degrees(CameraLensModel.BACK))
    }

    @Test
    fun `test degrees with CameraLensModel FRONT`() {
        assertEquals(0f, Orientation.PORTRAIT.degrees(CameraLensModel.FRONT))
        assertEquals(90f, Orientation.LANDSCAPE.degrees(CameraLensModel.FRONT))
        assertEquals(180f, Orientation.PORTRAIT_REVERSED.degrees(CameraLensModel.FRONT))
        assertEquals(270f, Orientation.LANDSCAPE_REVERSED.degrees(CameraLensModel.FRONT))
    }

    @Test
    fun `test degrees with CameraSelector DEFAULT_BACK_CAMERA`() {
        assertEquals(0f, Orientation.PORTRAIT.degrees(CameraSelector.DEFAULT_BACK_CAMERA))
        assertEquals(270f, Orientation.LANDSCAPE.degrees(CameraSelector.DEFAULT_BACK_CAMERA))
        assertEquals(180f, Orientation.PORTRAIT_REVERSED.degrees(CameraSelector.DEFAULT_BACK_CAMERA))
        assertEquals(90f, Orientation.LANDSCAPE_REVERSED.degrees(CameraSelector.DEFAULT_BACK_CAMERA))
    }

    @Test
    fun `test degrees with CameraSelector DEFAULT_FRONT_CAMERA`() {
        assertEquals(0f, Orientation.PORTRAIT.degrees(CameraSelector.DEFAULT_FRONT_CAMERA))
        assertEquals(90f, Orientation.LANDSCAPE.degrees(CameraSelector.DEFAULT_FRONT_CAMERA))
        assertEquals(180f, Orientation.PORTRAIT_REVERSED.degrees(CameraSelector.DEFAULT_FRONT_CAMERA))
        assertEquals(270f, Orientation.LANDSCAPE_REVERSED.degrees(CameraSelector.DEFAULT_FRONT_CAMERA))
    }

    @Test
    fun `test degrees with other CameraSelector`() {
        val customSelector = CameraSelector.Builder().build()
        assertEquals(0f, Orientation.PORTRAIT.degrees(customSelector))
        assertEquals(270f, Orientation.LANDSCAPE.degrees(customSelector))
        assertEquals(180f, Orientation.PORTRAIT_REVERSED.degrees(customSelector))
        assertEquals(90f, Orientation.LANDSCAPE_REVERSED.degrees(customSelector))
    }
}
