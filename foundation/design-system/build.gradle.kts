plugins {
	alias(libs.plugins.luzia.android.library)
	alias(libs.plugins.luzia.android.library.compose)
	alias(libs.plugins.luzia.android.library.jacoco)
	alias(libs.plugins.luzia.android.hilt)
	alias(libs.plugins.kotlin.serialization)
}

android {
	namespace = "co.thewordlab.luzia.foundation.design.system"
	compileOptions {
		isCoreLibraryDesugaringEnabled = true
	}
}

dependencies {
	implementation(projects.foundation.common)
	implementation(projects.foundation.config)
	implementation(projects.foundation.localization)
	implementation(libs.androidx.activity.compose)
	implementation(libs.androidx.camera.core)
	implementation(libs.androidx.camera.camera2)
	implementation(libs.androidx.camera.view)
	implementation(libs.androidx.camera.extensions)
	implementation(libs.androidx.camera.lifecycle)
	implementation(libs.accompanist.webview)
	implementation(libs.braze.compose)
	implementation(libs.google.guava)
	implementation(libs.kotlinx.serialization.json)
	implementation(libs.guava)
	implementation(libs.androidx.media3.exoplayer)
	implementation(libs.androidx.media3.ui)

	coreLibraryDesugaring(libs.android.desugarJdkLibs)

	testImplementation(projects.foundation.testing)
}
