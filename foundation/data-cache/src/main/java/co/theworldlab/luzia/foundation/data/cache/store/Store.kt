package co.theworldlab.luzia.foundation.data.cache.store

import kotlinx.coroutines.flow.Flow

/**
 * Generic interface for managing cached data with fetch and update capabilities.
 *
 * @param T The type of data being stored
 */
interface Store<T> {

    /**
     * Provides a Flow of the stored data.
     * The flow will emit:
     * 1. Current value (null if empty)
     * 2. Fetched value
     * 3. All subsequent updates
     *
     * @return Flow that emits nullable T values
     */
    fun flow(): Flow<T?>

    /**
     * Retrieves the current value or fetches a new one if none exists.
     *
     * @return The stored value of type T
     */
    suspend fun get(): T

    /**
     * Forces a new fetch of the data, regardless of cache state.
     *
     * @return The newly fetched value of type T
     */
    suspend fun fetch(): T

    /**
     * Updates the stored value directly.
     *
     * @param value The new value to store, can be null
     */
    suspend fun update(value: T?)

    /**
     * Updates the stored value using a transformation function.
     *
     * @param function A function that takes the current value and returns a new value
     */
    suspend fun update(function: (T?) -> T?)
}
