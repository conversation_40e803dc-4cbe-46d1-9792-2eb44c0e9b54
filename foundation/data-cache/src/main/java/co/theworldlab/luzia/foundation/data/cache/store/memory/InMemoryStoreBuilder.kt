package co.theworldlab.luzia.foundation.data.cache.store.memory

import co.theworldlab.luzia.foundation.data.cache.store.Store
import co.theworldlab.luzia.foundation.data.cache.store.StoreConfig
import co.theworldlab.luzia.foundation.data.cache.store.disk.DiskCache
import kotlin.time.Duration

class InMemoryStoreBuilder<T>(
    private val fetcher: suspend () -> T,
    private val diskCache: DiskCache<T>? = null
) {

    private var config: StoreConfig = StoreConfig()

    fun withExpiration(expiration: Duration) = apply {
        config = config.copy(expiration = expiration)
    }

    fun build(): Store<T> {
        return InMemoryStore(config, fetcher, diskCache)
    }
}

fun <T> inMemoryStore(
    fetcher: suspend () -> T,
    diskCache: DiskCache<T>? = null
): InMemoryStoreBuilder<T> {
    return InMemoryStoreBuilder(fetcher, diskCache)
}
