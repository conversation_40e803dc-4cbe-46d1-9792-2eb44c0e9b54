package co.theworldlab.luzia.foundation.data.cache.store.memory

import co.theworldlab.luzia.foundation.data.cache.store.Store
import co.theworldlab.luzia.foundation.data.cache.store.StoreConfig
import co.theworldlab.luzia.foundation.data.cache.store.disk.DiskCache
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.merge
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.concurrent.atomic.AtomicLong

/**
 * In-memory implementation of [Store] with thread-safe caching and expiration capabilities.
 *
 * @param T The type of data being stored
 * @property config Configuration for store behavior including expiration time
 * @property fetcher Suspending function that retrieves fresh data
 * @property currentTimeProvider Function to get current time in milliseconds (useful for testing)
 * @property logger Function to log debug information
 */
internal class InMemoryStore<T>(
    private val config: StoreConfig,
    private val fetcher: suspend () -> T,
    private val diskCache: DiskCache<T>? = null,
    private val currentTimeProvider: () -> Long = { System.currentTimeMillis() },
    private val logger: (String) -> Unit = {}
) : Store<T> {

    /** Holds the current value and notifies observers of changes */
    private val mutableState = MutableStateFlow(null as T?)

    /** Tracks when the data was last fetched for expiration checking */
    private val lastFetchedAt = AtomicLong(0L)

    /** Ensures thread-safe access to fetch operations */
    private val fetchMutex = Mutex()

    /**
     * Emits the current value, fetches if needed, and continues to emit updates.
     * The flow sequence is:
     * 1. Current value (null if empty)
     * 2. Result of [get] (fetched if needed)
     * 3. All subsequent updates
     */
    override fun flow(): Flow<T?> = flow {
        emit(mutableState.value)
        emit(get())
        val sources = listOfNotNull(mutableState, diskCache?.getFromCache())
        emitAll(sources.merge())
    }

    /**
     * Returns the current value or fetches new data if none exists.
     * Thread-safe through [fetchMutex].
     */
    override suspend fun get(): T {
        return if (isExpired()) {
            fetch()
        } else {
            val diskValue = diskCache?.getFromCache()?.firstOrNull()
            diskValue?.let { mutableState.update { it } }
            return diskValue ?: fetch()
        }
    }

    /**
     * Forces a new data fetch if the cache is expired or empty.
     * Thread-safe through [fetchMutex].
     *
     * @return The fetched or cached value
     */
    override suspend fun fetch(): T = fetchMutex.withLock {
        logger("Acquired lock")
        mutableState.value?.let { cachedValue ->
            val isExpired = isExpired()
            logger("Cached value: $cachedValue and isExpired: $isExpired")
            if (!isExpired) {
                logger("Returning cached value")
                return cachedValue
            }
        }
        val fetchedValue = fetcher()
        mutableState.value = fetchedValue
        diskCache?.saveIntoCache(fetchedValue)
        lastFetchedAt.set(currentTimeProvider())
        return fetchedValue
    }

    /**
     * Updates the stored value directly.
     *
     * @param value New value to store
     */
    override suspend fun update(value: T?) {
        if (value != null) diskCache?.saveIntoCache(value)
        mutableState.update { value }
    }

    /**
     * Updates the stored value using a transformation function.
     *
     * @param function Transformation to apply to current value
     */
    override suspend fun update(function: (T?) -> T?) {
        mutableState.update {
            val newValue = function(it)
            if (newValue != null) diskCache?.saveIntoCache(newValue)
            newValue
        }
    }

    /**
     * Checks if the cached data has expired based on [config.expiration].
     *
     * @return true if the data has expired, false otherwise
     */
    private fun isExpired(): Boolean {
        val currentTime = currentTimeProvider()
        val lastFetchedAt = lastFetchedAt.get()
        val expireMillis = config.expiration.inWholeMilliseconds
        logger("Current: $currentTime, last fetched: $lastFetchedAt, expireMillis: $expireMillis")
        return currentTime - lastFetchedAt > expireMillis
    }
}
