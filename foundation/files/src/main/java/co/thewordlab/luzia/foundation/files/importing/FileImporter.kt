package co.thewordlab.luzia.foundation.files.importing

import android.content.Context
import android.net.Uri
import co.thewordlab.luzia.foundation.analytics.Analytics
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FileImporter
@Inject
constructor(
    @ApplicationContext private val context: Context,
    private val analytics: Analytics,
    private val dispatcher: CoroutineDispatcher
) {
    private var cache: MutableStateFlow<ImportedFile?> = MutableStateFlow(null)
    private val supportedFiles = listOf("audio/")

    suspend fun importFile(uri: Uri, shortcutId: String?) {
        val file = resolveFile(uri, "Voice_", "")
        val imported = file?.let { ImportedFile(it, shortcutId ?: LUZIA_ID_PERSONALITY) }
        cache.tryEmit(imported)
    }

    fun getStream(personalityId: String): Flow<ImportedFile> {
        return cache
            .asStateFlow()
            .filterNotNull()
            .filter { it.personalityId == personalityId }
            .onEach { cache.update { null } }
    }

    suspend fun resolveFile(uri: Uri, prefix: String, extension: String): File? =
        withContext(dispatcher) {
            when (uri.scheme) {
                "file" -> {
                    val file = uri.path?.let { File(it) }
                    if (file?.exists() == true) file else null
                }

                "content" -> resolveContentFile(prefix, extension, uri)

                else -> {
                    val message = "Unsupported URI scheme: ${uri.scheme}"
                    analytics.reportException(message, Exception(message))
                    null
                }
            }
        }

    private fun resolveContentFile(
        prefix: String,
        extension: String,
        uri: Uri
    ) = try {
        val contentResolver = context.contentResolver
        val file = File.createTempFile(prefix, extension, context.cacheDir)
        contentResolver.openInputStream(uri)?.use { input ->
            FileOutputStream(file).use { output ->
                input.copyTo(output)
            }
        }
        file
    } catch (e: FileNotFoundException) {
        analytics.reportException("file cannot be found for content uri: ${uri.scheme}", e)
        null
    }

    fun supports(intentType: String): Boolean =
        supportedFiles.any { fileType -> intentType.startsWith(fileType) }

    private companion object {
        const val LUZIA_ID_PERSONALITY = "LuzIA"
    }
}
