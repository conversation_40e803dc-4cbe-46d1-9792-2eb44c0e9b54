package co.thewordlab.luzia.foundation.files.importing

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TextImporter @Inject constructor() {
    
    private var cache: MutableStateFlow<ImportedText?> = MutableStateFlow(null)

    fun importText(text: String, personalityId: String = LUZIA_ID_PERSONALITY) {
        val imported = ImportedText(text, personalityId)
        cache.tryEmit(imported)
    }

    fun getStream(personalityId: String): Flow<ImportedText> {
        return cache
            .asStateFlow()
            .filterNotNull()
            .filter { it.personalityId == personalityId }
            .onEach { cache.update { null } }
    }

    private companion object {
        const val LUZIA_ID_PERSONALITY = "LuzIA"
    }
}
