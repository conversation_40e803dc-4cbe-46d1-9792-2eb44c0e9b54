package co.thewordlab.luzia.foundation.files.importing

import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.util.Log
import androidx.core.graphics.drawable.toBitmap
import java.io.ByteArrayOutputStream

private const val QUALITY = 100

fun Drawable.convertToByteArray(): ByteArray? {
    var bitmap: Bitmap? = toBitmap()
    val byteArray: ByteArray? = try {
        val stream = ByteArrayOutputStream()
        bitmap?.compress(Bitmap.CompressFormat.PNG, QUALITY, stream)
        stream.toByteArray()
    } catch (expected: Exception) {
        Log.d("LuziaApp", "Error converting drawable to byte array: ${expected.message}")
        null
    } finally {
        bitmap = null
    }

    return byteArray
}
