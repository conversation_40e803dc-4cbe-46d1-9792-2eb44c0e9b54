package co.thewordlab.fouundation.persistence.datastore

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import co.thewordlab.fouundation.persistence.LuziaDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class LuziaDataStoreImpl @Inject constructor(
    private val store: DataStore<Preferences>
) : LuziaDataStore {

    override suspend fun <T : Any> delete(key: Preferences.Key<T>) {
        store.edit { it.remove(key) }
    }

    override suspend fun <T : Any> saveData(key: Preferences.Key<T>, value: T) {
        store.edit { it[key] = value }
    }

    override suspend fun <T : Any> getData(key: Preferences.Key<T>): T? = runCatching {
        store.data.map { it[key] }.first()
    }.getOrNull()

    override fun <T : Any> getDataFlow(key: Preferences.Key<T>): Flow<T?> = runCatching {
        store.data.map { it[key] }
    }.getOrElse { flowOf(null) }
}
