package co.thewordlab.fouundation.persistence.datastore

import androidx.datastore.preferences.core.booleanPreferencesKey
import co.thewordlab.fouundation.persistence.LuziaDataStore
import javax.inject.Inject

class DataStoreRepository @Inject constructor(
    private val dataSource: LuziaDataStore
) {

    suspend fun shouldShowCharacterWhitelist() =
        dataSource.getData(characterWhitelistPreference)?.let { !it } ?: true

    suspend fun markAsShownCharacterWhitelist() {
        dataSource.saveData(characterWhitelistPreference, true)
    }

    private companion object {
        const val CHARACTER_WHITELIST_SHOWN = "character_whitelist_shown"
        val characterWhitelistPreference = booleanPreferencesKey(CHARACTER_WHITELIST_SHOWN)
    }
}
