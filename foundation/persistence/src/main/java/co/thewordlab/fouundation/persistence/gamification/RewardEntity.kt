package co.thewordlab.fouundation.persistence.gamification

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "reward")
data class RewardEntity(
    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = "id")
    val id: String = "",
    @ColumnInfo(name = "level")
    val level: Int = 0,
    @ColumnInfo(name = "points")
    val points: Int = 0,
    @ColumnInfo(name = "image")
    val image: String = "",
    @ColumnInfo(name = "imageCelebration")
    val imageCelebration: String = "",
    @ColumnInfo(name = "message")
    val message: String = "",
    @ColumnInfo(name = "messageCelebration")
    val messageCelebration: String = "",
    @ColumnInfo(name = "title")
    val title: String = "",
    @ColumnInfo(name = "titleCelebration")
    val titleCelebration: String = "",
    @ColumnInfo(name = "type")
    val type: String = "",
    @ColumnInfo(name = "state")
    val state: RewardState = RewardState.Unclaimed,
    @ColumnInfo(name = "buttonAction")
    val buttonAction: String = "",
    @ColumnInfo(name = "buttonClose")
    val buttonClose: String = "",
    @ColumnInfo(name = "deeplink")
    val deeplink: String = "",
)

enum class RewardState {
    Claimed,
    Locked,
    Unclaimed
}
