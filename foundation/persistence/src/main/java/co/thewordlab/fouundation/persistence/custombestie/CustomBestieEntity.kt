package co.thewordlab.fouundation.persistence.custombestie

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "custombesties")
data class CustomBestieEntity(
    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = "customBestieId") val customBestieId: String = "",
    @ColumnInfo(name = "name") val name: String = "",
    @ColumnInfo(name = "description") val description: String = "",
    @ColumnInfo(name = "imageURL") val imageURL: String? = null,
    @ColumnInfo(name = "pronouns") val pronouns: String? = null,
    @ColumnInfo(name = "backgroundURL") val backgroundURL: String? = null,
    @ColumnInfo(name = "responseStyle") val responseStyle: String? = null,
    @ColumnInfo(name = "humour") val humour: String? = null,
    @ColumnInfo(name = "vibe") val vibe: String? = null,
    @ColumnInfo(name = "freeText") val freeText: String? = "",
    @ColumnInfo(name = "order") val order: Int = 0,
    @ColumnInfo(name = "show") val show: Boolean = true,
)
