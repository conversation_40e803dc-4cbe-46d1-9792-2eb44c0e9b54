package co.thewordlab.fouundation.persistence.chat

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import javax.annotation.concurrent.Immutable

@Immutable
@Entity(tableName = "messages")
data class MessageEntity(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "messageId") val messageId: Long,
    @ColumnInfo(name = "personalityId") val personalityId: String,
    @ColumnInfo(name = "text") val text: String,
    @ColumnInfo(name = "fileName") val fileName: String?,
    @ColumnInfo(name = "isAi") val isAi: Boolean,
    @ColumnInfo(name = "isRead") val isRead: Boolean,
    @ColumnInfo(name = "isLoading") val isLoading: Boolean,
    @ColumnInfo(name = "messageType") val messageType: MessageType,
    @ColumnInfo(name = "error") val error: String?,
    @ColumnInfo(name = "timeStamp") val timeStamp: Long,
    @ColumnInfo(name = "feedbackId") val feedbackId: String?,
    @ColumnInfo(name = "proactivePayload") val proactivePayload: String?,
    @ColumnInfo(name = "requestId") val requestId: String?,
    @ColumnInfo(name = "deleted") val deleted: Boolean,
    @ColumnInfo(name = "isFavorite") val isFavorite: Boolean,
    @ColumnInfo(name = "maxPromptLength") val maxPromptLength: Int?,
    @ColumnInfo(name = "contentInjection") val contentInjection: String?,
    @ColumnInfo(name = "webSearchData") val webSearchData: String?,
    @ColumnInfo(name = "webViewData") val webViewData: String?
)

enum class MessageType(val type: String, val property: String) {
    Text("Text", "text"),
    AudioRecord("AudioImport", "audio"),
    AudioImport("AudioImport", "audio_transcription"),
    Image("Image", "image"),
    File("File", "file"),
    Math("Math", "text"),
    Proactive("Proactive", "proactive"),
    Ad("Ad", "ad")
}
