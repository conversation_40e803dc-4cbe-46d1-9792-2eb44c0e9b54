package co.thewordlab.fouundation.persistence.di

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.core.handlers.ReplaceFileCorruptionHandler
import androidx.datastore.preferences.core.PreferenceDataStoreFactory
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.emptyPreferences
import androidx.datastore.preferences.preferencesDataStoreFile
import androidx.room.Room
import co.thewordlab.fouundation.persistence.LuziaDataStore
import co.thewordlab.fouundation.persistence.LuziaDatabase
import co.thewordlab.fouundation.persistence.MIGRATION_10_11
import co.thewordlab.fouundation.persistence.MIGRATION_11_12
import co.thewordlab.fouundation.persistence.MIGRATION_12_13
import co.thewordlab.fouundation.persistence.MIGRATION_13_14
import co.thewordlab.fouundation.persistence.MIGRATION_14_15
import co.thewordlab.fouundation.persistence.MIGRATION_15_16
import co.thewordlab.fouundation.persistence.MIGRATION_16_17
import co.thewordlab.fouundation.persistence.MIGRATION_17_18
import co.thewordlab.fouundation.persistence.MIGRATION_19_20
import co.thewordlab.fouundation.persistence.MIGRATION_1_2
import co.thewordlab.fouundation.persistence.MIGRATION_20_21
import co.thewordlab.fouundation.persistence.MIGRATION_21_22
import co.thewordlab.fouundation.persistence.MIGRATION_22_23
import co.thewordlab.fouundation.persistence.MIGRATION_23_24
import co.thewordlab.fouundation.persistence.MIGRATION_24_25
import co.thewordlab.fouundation.persistence.MIGRATION_25_26
import co.thewordlab.fouundation.persistence.MIGRATION_26_27
import co.thewordlab.fouundation.persistence.MIGRATION_27_28
import co.thewordlab.fouundation.persistence.MIGRATION_28_29
import co.thewordlab.fouundation.persistence.MIGRATION_29_30
import co.thewordlab.fouundation.persistence.MIGRATION_2_3
import co.thewordlab.fouundation.persistence.MIGRATION_3_4
import co.thewordlab.fouundation.persistence.MIGRATION_4_5
import co.thewordlab.fouundation.persistence.MIGRATION_5_6
import co.thewordlab.fouundation.persistence.MIGRATION_6_7
import co.thewordlab.fouundation.persistence.MIGRATION_7_8
import co.thewordlab.fouundation.persistence.MIGRATION_8_9
import co.thewordlab.fouundation.persistence.MIGRATION_9_10
import co.thewordlab.fouundation.persistence.datastore.LuziaDataStoreImpl
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@InstallIn(SingletonComponent::class)
@Module
object PersistenceModule {
    @Provides
    @Singleton
    fun provideAppDataBase(@ApplicationContext context: Context) = Room
        .databaseBuilder(context, LuziaDatabase::class.java, "luzia.db")
        .fallbackToDestructiveMigration()
        .addMigrations(MIGRATION_1_2)
        .addMigrations(MIGRATION_2_3)
        .addMigrations(MIGRATION_3_4)
        .addMigrations(MIGRATION_4_5)
        .addMigrations(MIGRATION_5_6)
        .addMigrations(MIGRATION_6_7)
        .addMigrations(MIGRATION_7_8)
        .addMigrations(MIGRATION_8_9)
        .addMigrations(MIGRATION_9_10)
        .addMigrations(MIGRATION_10_11)
        .addMigrations(MIGRATION_11_12)
        .addMigrations(MIGRATION_12_13)
        .addMigrations(MIGRATION_13_14)
        .addMigrations(MIGRATION_14_15)
        .addMigrations(MIGRATION_15_16)
        .addMigrations(MIGRATION_16_17)
        .addMigrations(MIGRATION_17_18)
        .addMigrations(MIGRATION_19_20)
        .addMigrations(MIGRATION_20_21)
        .addMigrations(MIGRATION_21_22)
        .addMigrations(MIGRATION_22_23)
        .addMigrations(MIGRATION_23_24)
        .addMigrations(MIGRATION_24_25)
        .addMigrations(MIGRATION_25_26)
        .addMigrations(MIGRATION_26_27)
        .addMigrations(MIGRATION_27_28)
        .addMigrations(MIGRATION_28_29)
        .addMigrations(MIGRATION_29_30)
        .build()

    @Provides
    fun provideChatDao(db: LuziaDatabase) = db.chatDao()

    @Provides
    fun providePersonalityDao(db: LuziaDatabase) = db.personalityDao()

    @Provides
    fun provideToolDao(db: LuziaDatabase) = db.toolDao()

    @Provides
    fun provideProfileDao(db: LuziaDatabase) = db.profileDao()

    @Provides
    fun provideCustomBestieDao(db: LuziaDatabase) = db.customBestiesDao()

    @Provides
    fun provideDraftsDao(db: LuziaDatabase) = db.draftsDaa()

    @Provides
    fun provideGamificationDao(db: LuziaDatabase) = db.gamificationDao()

    @Singleton
    @Provides
    fun providePreferencesDataStore(@ApplicationContext appContext: Context): DataStore<Preferences> {
        return PreferenceDataStoreFactory.create(
            corruptionHandler = ReplaceFileCorruptionHandler(
                produceNewData = { emptyPreferences() }
            ),
            produceFile = { appContext.preferencesDataStoreFile("LuziaDataStore") }
        )
    }

    @Module
    @InstallIn(SingletonComponent::class)
    interface Bindings {
        @Binds
        fun bindLuziaDataStore(impl: LuziaDataStoreImpl): LuziaDataStore
    }
}
