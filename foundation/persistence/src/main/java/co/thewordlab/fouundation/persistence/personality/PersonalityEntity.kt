package co.thewordlab.fouundation.persistence.personality

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "personalities")
data class PersonalityEntity(
    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = "personalityId")
    val personalityId: String = "",
    @ColumnInfo(name = "name")
    val name: String = "",
    @ColumnInfo(name = "description")
    val description: String = "",
    @ColumnInfo(name = "thumbnail")
    val thumbnail: String = "",
    @ColumnInfo(name = "tag")
    val tag: String = "",
    @ColumnInfo(name = "tagDescription")
    val tagDescription: String = "",
    @ColumnInfo(name = "welcomeMessage")
    val welcomeMessage: String? = null,
    @ColumnInfo(name = "order")
    val order: Int = 0,
    @ColumnInfo(name = "show")
    val show: Boolean = true,
    @ColumnInfo(name = "iceBreakers")
    val iceBreakers: List<String> = emptyList(),
    @ColumnInfo(name = "pronouns")
    val pronouns: String? = null,
    @ColumnInfo(name = "backgroundURL")
    val backgroundURL: String? = null,
    @ColumnInfo(name = "responseStyles")
    val responseStyles: String? = null
)
