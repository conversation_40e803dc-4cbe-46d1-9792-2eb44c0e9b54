package co.thewordlab.fouundation.persistence.profile

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "profile")
data class ProfileEntity(
    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = "masterUserId")
    val masterUserId: String = "",
    @ColumnInfo(name = "username")
    val username: String? = null,
    @ColumnInfo(name = "nickname")
    val nickname: String? = null,
    @ColumnInfo(name = "avatarURL")
    val avatarURL: String? = null,
    @ColumnInfo(name = "backgroundURL")
    val backgroundURL: String? = null,
    @ColumnInfo(name = "phoneNumber")
    val phoneNumber: String? = null,
    @ColumnInfo(name = "email")
    val email: String? = null,
    @ColumnInfo(name = "utcOffset")
    val utcOffset: Int? = null,
    @ColumnInfo(name = "birthdate")
    val birthdate: String? = null,
    @ColumnInfo(name = "pronouns")
    val pronouns: Pronouns? = null,
    @ColumnInfo(name = "privacy")
    val privacy: ProfilePrivacy? = null,
    @ColumnInfo(name = "schoolName")
    val schoolName: String? = null,
    @ColumnInfo(name = "schoolMates")
    val schoolMates: Int? = null,
    @ColumnInfo(name = "isStudent")
    val isStudent: Boolean? = null,
    @ColumnInfo(name = "userType")
    val userType: ProfileType? = null,
    @ColumnInfo(name = "bestiePoints")
    val bestiePoints: Int? = null,
    @ColumnInfo(name = "country")
    val country: String? = null,
    @ColumnInfo(name = "streamUserId")
    val streamUserId: String? = null,
    @ColumnInfo(name = "streamToken")
    val streamToken: String? = null,
    @ColumnInfo(name = "referralURL")
    val referralURL: String? = null,
    @ColumnInfo(name = "referralCode")
    val referralCode: String? = null,
    @ColumnInfo(name = "preferredLanguage")
    val preferredLanguage: String? = null,
    @ColumnInfo(name = "onboardingIntentId")
    val onboardingIntentId: String? = null,
    @ColumnInfo(name = "selfDescription")
    val selfDescription: String? = null
)

enum class Pronouns {
    HE_HIM,
    SHE_HER,
    THEY_THEM,
    OTHER
}

enum class ProfilePrivacy {
    PUBLIC,
    PRIVATE
}

enum class ProfileType {
    GUEST,
    GUEST_PLUS,
    FULL_USER,
    VIP
}
