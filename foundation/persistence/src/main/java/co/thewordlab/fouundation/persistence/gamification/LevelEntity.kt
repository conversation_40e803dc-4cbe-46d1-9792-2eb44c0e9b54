package co.thewordlab.fouundation.persistence.gamification

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "level")
data class LevelEntity(
    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = "id")
    val id: String = "levelId",
    @ColumnInfo(name = "currentLevel")
    val currentLevel: Int = 0,
    @ColumnInfo(name = "currentPoints")
    val currentPoints: Int = 0,
    @ColumnInfo(name = "totalPointsForCurrentLevel")
    val totalPointsForCurrentLevel: Int = 0,
    @ColumnInfo(name = "totalPointsForNextLevel")
    val totalPointsForNextLevel: Int = 0,
    @ColumnInfo(name = "streak")
    val streak: Int = 0
)
