package co.thewordlab.fouundation.persistence.gamification

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "pendingReward")
data class PendingRewardEntity(
    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = "id")
    val id: String = "",
    @ColumnInfo(name = "dismissed")
    val dismissed: Boolean = false,
    @ColumnInfo(name = "isMajor")
    val isMajor: Boolean = false,
    @ColumnInfo(name = "earnedPoints")
    val earnedPoints: Int = 0,
    @ColumnInfo(name = "level")
    val level: Int = 0,
    @ColumnInfo(name = "isDelayed")
    val isDelayed: Boolean = false,
    @ColumnInfo(name = "isClaimed")
    val isClaimed: Boolean = false
)
