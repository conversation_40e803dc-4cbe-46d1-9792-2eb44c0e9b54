{"formatVersion": 1, "database": {"version": 8, "identityHash": "8fef0c3fbe283270e8df4c04f03085d3", "entities": [{"tableName": "messages", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`messageId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `personalityId` TEXT NOT NULL, `text` TEXT NOT NULL, `fileName` TEXT, `isAi` INTEGER NOT NULL, `isRead` INTEGER NOT NULL, `isLoading` INTEGER NOT NULL, `messageType` TEXT NOT NULL, `error` TEXT, `timeStamp` INTEGER NOT NULL, `feedbackId` TEXT, `proactivePayload` TEXT, `requestId` TEXT, `deleted` INTEGER NOT NULL)", "fields": [{"fieldPath": "messageId", "columnName": "messageId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "personalityId", "columnName": "personalityId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "text", "columnName": "text", "affinity": "TEXT", "notNull": true}, {"fieldPath": "fileName", "columnName": "fileName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isAi", "columnName": "isAi", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isRead", "columnName": "isRead", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isLoading", "columnName": "isLoading", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "messageType", "columnName": "messageType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "error", "columnName": "error", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeStamp", "columnName": "timeStamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "feedbackId", "columnName": "feedbackId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "proactivePayload", "columnName": "proactivePayload", "affinity": "TEXT", "notNull": false}, {"fieldPath": "requestId", "columnName": "requestId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["messageId"]}, "indices": [], "foreignKeys": []}, {"tableName": "personalities", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`personalityId` TEXT NOT NULL, `name` TEXT NOT NULL, `description` TEXT NOT NULL, `thumbnail` TEXT NOT NULL, `tag` TEXT NOT NULL, `tagDescription` TEXT NOT NULL, `welcomeMessage` TEXT, `order` INTEGER NOT NULL, `show` INTEGER NOT NULL, `iceBreakers` TEXT NOT NULL, PRIMARY KEY(`personalityId`))", "fields": [{"fieldPath": "personalityId", "columnName": "personalityId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tag", "columnName": "tag", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tagDescription", "columnName": "tagDescription", "affinity": "TEXT", "notNull": true}, {"fieldPath": "welcomeMessage", "columnName": "welcomeMessage", "affinity": "TEXT", "notNull": false}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "show", "columnName": "show", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iceBreakers", "columnName": "iceBreakers", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["personalityId"]}, "indices": [], "foreignKeys": []}, {"tableName": "tools", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `isFeatured` INTEGER NOT NULL, `isPopular` INTEGER NOT NULL, `order` INTEGER NOT NULL, `description` TEXT NOT NULL, `icon` TEXT NOT NULL, `iconBackgroundColor` TEXT NOT NULL, `image` TEXT, `upcoming_name` TEXT, `upcoming_description` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isFeatured", "columnName": "isFeatured", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isPopular", "columnName": "isPopular", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": true}, {"fieldPath": "iconBackgroundColor", "columnName": "iconBackgroundColor", "affinity": "TEXT", "notNull": true}, {"fieldPath": "image", "columnName": "image", "affinity": "TEXT", "notNull": false}, {"fieldPath": "upcoming.name", "columnName": "upcoming_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "upcoming.description", "columnName": "upcoming_description", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '8fef0c3fbe283270e8df4c04f03085d3')"]}}