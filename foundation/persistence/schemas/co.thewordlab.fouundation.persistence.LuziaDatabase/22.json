{"formatVersion": 1, "database": {"version": 22, "identityHash": "1cd054f88a7f21e681cec27ede45d2f8", "entities": [{"tableName": "messages", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`messageId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `personalityId` TEXT NOT NULL, `text` TEXT NOT NULL, `fileName` TEXT, `isAi` INTEGER NOT NULL, `isRead` INTEGER NOT NULL, `isLoading` INTEGER NOT NULL, `messageType` TEXT NOT NULL, `error` TEXT, `timeStamp` INTEGER NOT NULL, `feedbackId` TEXT, `proactivePayload` TEXT, `requestId` TEXT, `deleted` INTEGER NOT NULL, `isFavorite` INTEGER NOT NULL, `maxPromptLength` INTEGER, `contentInjection` TEXT)", "fields": [{"fieldPath": "messageId", "columnName": "messageId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "personalityId", "columnName": "personalityId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "text", "columnName": "text", "affinity": "TEXT", "notNull": true}, {"fieldPath": "fileName", "columnName": "fileName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isAi", "columnName": "isAi", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isRead", "columnName": "isRead", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isLoading", "columnName": "isLoading", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "messageType", "columnName": "messageType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "error", "columnName": "error", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeStamp", "columnName": "timeStamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "feedbackId", "columnName": "feedbackId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "proactivePayload", "columnName": "proactivePayload", "affinity": "TEXT", "notNull": false}, {"fieldPath": "requestId", "columnName": "requestId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isFavorite", "columnName": "isFavorite", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "contentInjection", "columnName": "contentInjection", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["messageId"]}, "indices": [], "foreignKeys": []}, {"tableName": "personalities", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`personalityId` TEXT NOT NULL, `name` TEXT NOT NULL, `description` TEXT NOT NULL, `thumbnail` TEXT NOT NULL, `tag` TEXT NOT NULL, `tagDescription` TEXT NOT NULL, `welcomeMessage` TEXT, `order` INTEGER NOT NULL, `show` INTEGER NOT NULL, `iceBreakers` TEXT NOT NULL, `pronouns` TEXT, `backgroundURL` TEXT, `responseStyles` TEXT, PRIMARY KEY(`personalityId`))", "fields": [{"fieldPath": "personalityId", "columnName": "personalityId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tag", "columnName": "tag", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tagDescription", "columnName": "tagDescription", "affinity": "TEXT", "notNull": true}, {"fieldPath": "welcomeMessage", "columnName": "welcomeMessage", "affinity": "TEXT", "notNull": false}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "show", "columnName": "show", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iceBreakers", "columnName": "iceBreakers", "affinity": "TEXT", "notNull": true}, {"fieldPath": "pronouns", "columnName": "pronouns", "affinity": "TEXT", "notNull": false}, {"fieldPath": "backgroundURL", "columnName": "backgroundURL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "responseStyles", "columnName": "responseStyles", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["personalityId"]}, "indices": [], "foreignKeys": []}, {"tableName": "tools", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `isFeatured` INTEGER NOT NULL, `isPopular` INTEGER NOT NULL, `order` INTEGER NOT NULL, `description` TEXT NOT NULL, `icon` TEXT NOT NULL, `iconBackgroundColor` TEXT NOT NULL, `image` TEXT, `isDynamic` INTEGER NOT NULL, `upcoming_name` TEXT, `upcoming_description` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isFeatured", "columnName": "isFeatured", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isPopular", "columnName": "isPopular", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": true}, {"fieldPath": "iconBackgroundColor", "columnName": "iconBackgroundColor", "affinity": "TEXT", "notNull": true}, {"fieldPath": "image", "columnName": "image", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isDynamic", "columnName": "isDynamic", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "upcoming.name", "columnName": "upcoming_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "upcoming.description", "columnName": "upcoming_description", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "profile", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`masterUserId` TEXT NOT NULL, `username` TEXT, `nickname` TEXT, `avatarURL` TEXT, `backgroundURL` TEXT, `phoneNumber` TEXT, `email` TEXT, `utcOffset` INTEGER, `birthdate` TEXT, `pronouns` TEXT, `privacy` TEXT, `schoolName` TEXT, `schoolMates` INTEGER, `isStudent` INTEGER, `userType` TEXT, `bestiePoints` INTEGER, `country` TEXT, `streamUserId` TEXT, `streamToken` TEXT, `referralURL` TEXT, `referralCode` TEXT, `preferredLanguage` TEXT, PRIMARY KEY(`masterUserId`))", "fields": [{"fieldPath": "masterUserId", "columnName": "masterUserId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": false}, {"fieldPath": "nickname", "columnName": "nickname", "affinity": "TEXT", "notNull": false}, {"fieldPath": "avatarURL", "columnName": "avatarURL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "backgroundURL", "columnName": "backgroundURL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "phoneNumber", "columnName": "phoneNumber", "affinity": "TEXT", "notNull": false}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": false}, {"fieldPath": "utcOffset", "columnName": "utcOffset", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "birthdate", "columnName": "birthdate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "pronouns", "columnName": "pronouns", "affinity": "TEXT", "notNull": false}, {"fieldPath": "privacy", "columnName": "privacy", "affinity": "TEXT", "notNull": false}, {"fieldPath": "schoolName", "columnName": "schoolName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "schoolMates", "columnName": "schoolMates", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isStudent", "columnName": "isStudent", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "userType", "columnName": "userType", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bestiePoints", "columnName": "bestiePoints", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "country", "columnName": "country", "affinity": "TEXT", "notNull": false}, {"fieldPath": "streamUserId", "columnName": "streamUserId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "streamToken", "columnName": "streamToken", "affinity": "TEXT", "notNull": false}, {"fieldPath": "referralURL", "columnName": "referralURL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "referralCode", "columnName": "referralCode", "affinity": "TEXT", "notNull": false}, {"fieldPath": "preferredLanguage", "columnName": "preferredLanguage", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["masterUserId"]}, "indices": [], "foreignKeys": []}, {"tableName": "custombesties", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`customBestieId` TEXT NOT NULL, `name` TEXT NOT NULL, `description` TEXT NOT NULL, `imageURL` TEXT, `pronouns` TEXT, `backgroundURL` TEXT, `responseStyle` TEXT, `humour` TEXT, `vibe` TEXT, `freeText` TEXT, `order` INTEGER NOT NULL, `show` INTEGER NOT NULL, PRIMARY KEY(`customBestieId`))", "fields": [{"fieldPath": "customBestieId", "columnName": "customBestieId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "imageURL", "columnName": "imageURL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "pronouns", "columnName": "pronouns", "affinity": "TEXT", "notNull": false}, {"fieldPath": "backgroundURL", "columnName": "backgroundURL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "responseStyle", "columnName": "responseStyle", "affinity": "TEXT", "notNull": false}, {"fieldPath": "humour", "columnName": "humour", "affinity": "TEXT", "notNull": false}, {"fieldPath": "vibe", "columnName": "vibe", "affinity": "TEXT", "notNull": false}, {"fieldPath": "freeText", "columnName": "freeText", "affinity": "TEXT", "notNull": false}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "show", "columnName": "show", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["customBestieId"]}, "indices": [], "foreignKeys": []}, {"tableName": "drafts", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `text` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "text", "columnName": "text", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "level", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `currentLevel` INTEGER NOT NULL, `currentPoints` INTEGER NOT NULL, `totalPointsForCurrentLevel` INTEGER NOT NULL, `totalPointsForNextLevel` INTEGER NOT NULL, `streak` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "currentLevel", "columnName": "currentLevel", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "currentPoints", "columnName": "currentPoints", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalPointsForCurrentLevel", "columnName": "totalPointsForCurrentLevel", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalPointsForNextLevel", "columnName": "totalPointsForNextLevel", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "streak", "columnName": "streak", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "reward", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `level` INTEGER NOT NULL, `points` INTEGER NOT NULL, `image` TEXT NOT NULL, `imageCelebration` TEXT NOT NULL, `message` TEXT NOT NULL, `messageCelebration` TEXT NOT NULL, `title` TEXT NOT NULL, `titleCelebration` TEXT NOT NULL, `type` TEXT NOT NULL, `state` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "level", "columnName": "level", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "points", "columnName": "points", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "image", "columnName": "image", "affinity": "TEXT", "notNull": true}, {"fieldPath": "imageCelebration", "columnName": "imageCelebration", "affinity": "TEXT", "notNull": true}, {"fieldPath": "message", "columnName": "message", "affinity": "TEXT", "notNull": true}, {"fieldPath": "messageCelebration", "columnName": "messageCelebration", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "titleCelebration", "columnName": "titleCelebration", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "state", "columnName": "state", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '1cd054f88a7f21e681cec27ede45d2f8')"]}}