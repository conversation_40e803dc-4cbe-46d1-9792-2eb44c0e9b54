package co.thewordlab.luzia.foundation.securelib

enum class Secure<PERSON><PERSON>(val key: String) {
    KEY_GOOGLE_WEB_CLIENT_ID("google_web_client_id"),
    KEY_AMPLITUDE("amplitude_api_key"),
    K<PERSON>Y_AMPLITUDE_EXPERIMENT("amplitude_experiment_api_key"),
    KEY_GOOGLE_CLIENT_PREFIX("google_client_prefix"),
    KEY_GOOGLE_CLIENT_PREFIX_DEBUG("google_client_prefix_debug"),
    KEY_GET_STREAM_API("key_get_stream_api"),
    KEY_GET_STREAM_NOTIFICATION("android_push"),
    KEY_DATADOG("key_data_dog"),
    KEY_DATADOG_RUM("key_data_dog_rum"),
    APPSFLYER_KEY("appsflyer_key"),
    APPSFLYER_LINK_ID("appsflyer_link_id"),
}
