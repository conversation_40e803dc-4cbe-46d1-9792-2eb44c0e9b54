package co.thewordlab.luzia.foundation.analytics

@Suppress("TooManyFunctions")
interface Analytics {

    fun trackScreen(screen: AnalyticsScreens)

    fun trackAction(action: AnalyticsActions)

    fun trackEvent(event: AnalyticsEvents)

    fun setUserProperties(properties: Map<Parameter, Any> = emptyMap())

    fun setUserIdentification(identification: UserIdentification)

    fun reportException(message: String, ex: Exception? = null)

    @Deprecated("Use trackScreen instead")
    fun logScreenView(screenView: ScreenView, params: Map<Parameter, Any> = emptyMap())

    @Deprecated("Use trackEvent instead")
    fun logEvent(event: Event, params: Map<Parameter, Any> = emptyMap(), trackOn: TrackOn = TrackOn.Base)

    @Deprecated("Use trackEvent instead")
    fun logEventWithProps(event: Event, params: Map<String, Any> = emptyMap())

    @Deprecated("Use trackEvent instead")
    fun logFailedEvent(failedEvent: Event, params: Map<Parameter, Any> = emptyMap())

    @Deprecated("Use trackScreen instead")
    fun logScreen(screen: AnalyticsScreens, params: Map<Parameter, Any> = emptyMap())

    @Deprecated("Use trackScreen instead")
    fun logScreenWithProps(action: AnalyticsScreens, params: Map<String, Any> = emptyMap())

    @Deprecated("Use trackAction instead")
    fun logAction(action: AnalyticsActions, params: Map<Parameter, Any> = emptyMap())

    @Deprecated("Use trackAction instead")
    fun logActionWithProps(action: AnalyticsActions, params: Map<String, Any> = emptyMap())

    @Deprecated("Use trackEvent instead")
    fun logEvent(event: AnalyticsEvents, params: Map<Parameter, Any> = emptyMap(), trackOn: TrackOn = TrackOn.Base)
}
