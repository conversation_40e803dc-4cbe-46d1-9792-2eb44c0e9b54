package co.thewordlab.luzia.foundation.analytics.providers

import co.thewordlab.luzia.foundation.analytics.AnalyticsActions
import co.thewordlab.luzia.foundation.analytics.AnalyticsEvents
import co.thewordlab.luzia.foundation.analytics.AnalyticsScreens
import co.thewordlab.luzia.foundation.analytics.DO_NOTHING
import co.thewordlab.luzia.foundation.analytics.Event
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.analytics.ScreenView
import co.thewordlab.luzia.foundation.analytics.Trackers
import co.thewordlab.luzia.foundation.analytics.UserIdentification

sealed interface AnalyticsProvider {
    fun setUserIdentification(identification: UserIdentification) { DO_NOTHING }
}

@Suppress("TooManyFunctions")
interface CustomerBehaviour : AnalyticsProvider {

    val id: Trackers

    fun trackScreen(screen: AnalyticsScreens) { DO_NOTHING }

    fun trackAction(action: AnalyticsActions) { DO_NOTHING }

    fun trackEvent(event: AnalyticsEvents) { DO_NOTHING }

    fun setUserProperties(properties: Map<Parameter, Any> = emptyMap()) { DO_NOTHING }

    @Deprecated("Use trackScreen instead")
    fun logScreenView(screenView: ScreenView, params: Map<Parameter, Any> = emptyMap())

    @Deprecated("Use trackEvent instead")
    fun logEvent(event: Event, params: Map<Parameter, Any> = emptyMap())

    @Deprecated("Use trackEvent instead")
    fun logEventWithProps(event: Event, params: Map<String, Any> = emptyMap())

    @Deprecated("Use trackEvent instead")
    fun logFailedEvent(failedEvent: Event, params: Map<Parameter, Any> = emptyMap())

    @Deprecated("Use trackScreen instead")
    fun logScreen(screen: AnalyticsScreens, params: Map<Parameter, Any> = emptyMap())

    @Deprecated("Use trackScreen instead")
    fun logScreenWithProps(screen: AnalyticsScreens, params: Map<String, Any> = emptyMap())

    @Deprecated("Use trackAction instead")
    fun logAction(action: AnalyticsActions, params: Map<Parameter, Any> = emptyMap())

    @Deprecated("Use trackAction instead")
    fun logActionWithProps(action: AnalyticsActions, params: Map<String, Any> = emptyMap())

    @Deprecated("Use trackEvent instead")
    fun logEvent(event: AnalyticsEvents, params: Map<Parameter, Any> = emptyMap())
}

interface ErrorTracking : AnalyticsProvider {
    fun reportException(message: String, ex: Exception?)
}
