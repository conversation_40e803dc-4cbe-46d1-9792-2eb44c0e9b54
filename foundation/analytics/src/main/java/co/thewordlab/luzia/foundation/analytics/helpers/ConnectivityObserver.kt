package co.thewordlab.luzia.foundation.analytics.helpers

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkCapabilities.TRANSPORT_CELLULAR
import android.net.NetworkCapabilities.TRANSPORT_WIFI
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ConnectivityObserver @Inject constructor(
    @ApplicationContext private val context: Context,
    coroutineScope: CoroutineScope
) {
    private val connectivityManager =
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

    private val publishTriggerEvent: Channel<Unit> = Channel(Channel.UNLIMITED)

    private val publishTrigger: Flow<Unit> = publishTriggerEvent.receiveAsFlow()

    private val connectivityCallback = callbackFlow {
        val checkConnection: () -> Unit = {
            coroutineScope.launch {
                trySend(currentConnectionState())
            }
        }
        val callback = object : ConnectivityManager.NetworkCallback() {
            override fun onUnavailable() {
                checkConnection()
            }

            override fun onAvailable(network: Network) {
                checkConnection()
            }

            override fun onLosing(network: Network, maxMsToLive: Int) {
                checkConnection()
            }

            override fun onLost(network: Network) {
                checkConnection()
            }
        }
        trySend(currentConnectionState())
        connectivityManager.registerDefaultNetworkCallback(callback)
        launch {
            publishTrigger.collectLatest {
                val state = currentConnectionState()
                trySend(state)
            }
        }
        awaitClose {
            connectivityManager.unregisterNetworkCallback(callback)
        }
    }.stateIn(coroutineScope, SharingStarted.Lazily, ConnectionState.Available)

    fun observe(): Flow<ConnectionState> = connectivityCallback

    fun refreshState() {
        publishTriggerEvent.trySend(Unit)
    }

    private fun currentConnectionState(): ConnectionState {
        val network = connectivityManager.activeNetwork
        val capabilities = connectivityManager.getNetworkCapabilities(network)
        val isWifi = capabilities?.hasTransport(TRANSPORT_WIFI) == true
        val isCellular = capabilities?.hasTransport(TRANSPORT_CELLULAR) == true
        val isConnected = capabilities != null && (isWifi || isCellular)
        return when {
            capabilities.isUsingVpn() -> ConnectionState.Vpn
            isConnected -> ConnectionState.Available
            else -> ConnectionState.Unavailable
        }
    }

    private fun NetworkCapabilities?.isUsingVpn(): Boolean {
        return this?.hasTransport(NetworkCapabilities.TRANSPORT_VPN) == true
    }

    private companion object {
        const val REPLAY_COUNT = 1
    }
}
