package co.thewordlab.luzia.foundation.analytics.providers

import android.content.Context
import android.content.Intent
import android.util.Log
import co.thewordlab.luzia.foundation.analytics.AnalyticsActions
import co.thewordlab.luzia.foundation.analytics.AnalyticsEvents
import co.thewordlab.luzia.foundation.analytics.AnalyticsScreens
import co.thewordlab.luzia.foundation.analytics.BuildConfig
import co.thewordlab.luzia.foundation.analytics.DO_NOTHING
import co.thewordlab.luzia.foundation.analytics.EVENT_PREFIX
import co.thewordlab.luzia.foundation.analytics.Event
import co.thewordlab.luzia.foundation.analytics.Parameter
import co.thewordlab.luzia.foundation.analytics.ScreenView
import co.thewordlab.luzia.foundation.analytics.Trackers
import co.thewordlab.luzia.foundation.analytics.UserIdentification
import co.thewordlab.luzia.foundation.analytics.domain.AppsFlyerDeepLinks
import co.thewordlab.luzia.foundation.analytics.getEventPrefix
import co.thewordlab.luzia.foundation.securelib.SecureKey
import co.thewordlab.luzia.foundation.securelib.SecureStorage
import com.appsflyer.AppsFlyerLib
import com.appsflyer.deeplink.DeepLinkResult
import com.appsflyer.share.LinkGenerator
import com.appsflyer.share.ShareInviteHelper
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

@Singleton
class AppsFlyerAnalyticsProvider @Inject constructor(
    private val secureStorage: SecureStorage,
    @ApplicationContext private val context: Context,
    private val dispatcher: CoroutineDispatcher
) : CustomerBehaviour {

    override val id: Trackers = Trackers.APPSFLYER

    suspend fun initialize(onDeeplinkReceived: (AppsFlyerDeepLinks?) -> Unit) = withContext(dispatcher) {
        AppsFlyerLib.getInstance().run {
            setDebugLog(BuildConfig.DEBUG)
            init(secureStorage.get(SecureKey.APPSFLYER_KEY), null, context)
            setAppInviteOneLink(secureStorage.get(SecureKey.APPSFLYER_LINK_ID))
            subscribeDeepLinks(onDeeplinkReceived)
            waitForCustomerUserId(false)
            start(context)
        }
    }

    private fun AppsFlyerLib.subscribeDeepLinks(onDeeplinkReceived: (AppsFlyerDeepLinks?) -> Unit) {
        subscribeForDeepLink { result ->
            when (result.status) {
                DeepLinkResult.Status.NOT_FOUND -> Log.i("AppsFlyer", "Deeplink not found")
                DeepLinkResult.Status.ERROR -> Log.e("AppsFlyer", "Error: ${result.error}")
                DeepLinkResult.Status.FOUND -> {
                    result.deepLink?.deepLinkValue?.let { link ->
                        if (link.contains(INVITE_DEEP_LINK_DETECTION)) {
                            val invite = result.deepLink.run {
                                AppsFlyerDeepLinks.Invite(
                                    cid = getStringValue(INVITE_PARAM_CID).orEmpty(),
                                    name = getStringValue(INVITE_PARAM_NAME).orEmpty(),
                                    memberCount = getStringValue(INVITE_PARAM_MEMBER_COUNT).orEmpty(),
                                    image = getStringValue(INVITE_PARAM_MEMBER_IMAGE)
                                )
                            }
                            onDeeplinkReceived(invite)
                        } else {
                            onDeeplinkReceived(AppsFlyerDeepLinks.Deeplink(link))
                        }
                    }
                }
            }
        }
    }

    override fun trackEvent(event: AnalyticsEvents) {
        AppsFlyerLib.getInstance().logEvent(
            context,
            "${getEventPrefix(event.options.isError)}${event.value}",
            event.properties.map { it.key to it.value }.toMap()
        )
    }

    override fun setUserIdentification(identification: UserIdentification) {
        AppsFlyerLib.getInstance().run {
            identification.masterUserId?.let {
                setCustomerUserId(it)
            }
            identification.externalDeviceId?.let {
                setAdditionalData(mapOf(PROPERTY_EXTERNAL_ID to it))
            }
            identification.userAddId?.let {
                setAdditionalData(mapOf(PROPERTY_AD_ID to it))
            }
            identification.userSessionId?.let {
                setAdditionalData(mapOf(PROPERTY_USER_SESSION_ID to it))
            }
        }
    }

    override fun setUserProperties(properties: Map<Parameter, Any>) {
        val props = properties.map { it.key.value to it.value }.toMap()
        AppsFlyerLib.getInstance().setAdditionalData(props)
    }

    @Deprecated("Use trackScreen instead")
    override fun logScreenView(screenView: ScreenView, params: Map<Parameter, Any>) {
        DO_NOTHING
    }

    @Deprecated("Use trackEvent instead")
    override fun logEvent(event: Event, params: Map<Parameter, Any>) {
        AppsFlyerLib.getInstance().logEvent(
            context,
            "${EVENT_PREFIX}${event.value}",
            params.map { it.key.value to it.value }.toMap()
        )
    }

    @Deprecated("Use trackEvent instead")
    override fun logEvent(event: AnalyticsEvents, params: Map<Parameter, Any>) {
        AppsFlyerLib.getInstance().logEvent(
            context,
            "${EVENT_PREFIX}${event.value}",
            params.map { it.key.value to it.value }.toMap()
        )
    }

    @Deprecated("Use trackEvent instead")
    override fun logEventWithProps(event: Event, params: Map<String, Any>) {
        DO_NOTHING
    }

    @Deprecated("Use trackEvent instead")
    override fun logFailedEvent(failedEvent: Event, params: Map<Parameter, Any>) {
        DO_NOTHING
    }

    @Deprecated("Use trackScreen instead")
    override fun logScreen(screen: AnalyticsScreens, params: Map<Parameter, Any>) {
        DO_NOTHING
    }

    @Deprecated("Use trackScreen instead")
    override fun logScreenWithProps(screen: AnalyticsScreens, params: Map<String, Any>) {
        DO_NOTHING
    }

    @Deprecated("Use trackAction instead")
    override fun logAction(action: AnalyticsActions, params: Map<Parameter, Any>) {
        DO_NOTHING
    }

    @Deprecated("Use trackAction instead")
    override fun logActionWithProps(action: AnalyticsActions, params: Map<String, Any>) {
        DO_NOTHING
    }

    fun provideDeeplink(context: Context, intent: Intent) {
        AppsFlyerLib.getInstance().performOnDeepLinking(intent, context)
    }

    suspend fun generateLink(cid: String, name: String, memberCount: Int, image: String?): String {
        return suspendCancellableCoroutine { continuation ->
            val generator = ShareInviteHelper.generateInviteUrl(context).apply {
                addParameter(INVITE_DEEP_LINK_KEY, INVITE_DEEP_LINK)
                addParameter(INVITE_PARAM_CID, cid)
                addParameter(INVITE_TYPE, INVITE_TYPE_GROUP)
                addParameter(INVITE_PARAM_NAME, name)
                addParameter(INVITE_PARAM_MEMBER_COUNT, "$memberCount")
                addParameter(INVITE_PARAM_MEMBER_IMAGE, image)
                campaign = INVITE_CAMPAIGN
                channel = INVITE_CHANNEL
            }
            generator.generateLink(
                context,
                object : LinkGenerator.ResponseListener {
                    override fun onResponse(link: String?) {
                        if (continuation.isActive && link != null) continuation.resume(link)
                    }

                    override fun onResponseError(message: String?) {
                        continuation.resumeWithException(Throwable(message))
                    }
                }
            )
        }
    }

    private companion object {
        const val PROPERTY_AD_ID = "ad_id"
        const val PROPERTY_EXTERNAL_ID = "external_id"
        const val PROPERTY_USER_SESSION_ID = "user_session_id"
        const val INVITE_DEEP_LINK_KEY = "deep_link_value"
        const val INVITE_PARAM_CID = "deep_link_sub1"
        const val INVITE_TYPE = "deep_link_sub2"
        const val INVITE_PARAM_NAME = "deep_link_sub3"
        const val INVITE_PARAM_MEMBER_COUNT = "deep_link_sub4"
        const val INVITE_PARAM_MEMBER_IMAGE = "deep_link_sub5"
        const val INVITE_TYPE_GROUP = "group"
        const val INVITE_CAMPAIGN = "group-invites"
        const val INVITE_CHANNEL = "app-group-invite"
        const val INVITE_DEEP_LINK = "luzia://groups-invite"
        const val INVITE_DEEP_LINK_DETECTION = "groups-invite"
    }
}
