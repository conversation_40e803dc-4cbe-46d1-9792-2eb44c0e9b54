package co.thewordlab.luzia.foundation.analytics.helpers

import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import java.net.InetSocketAddress
import java.net.Socket
import javax.inject.Inject

class InternetAvailability<PERSON>hecker @Inject constructor(
    private val dispatcher: CoroutineDispatcher
) {

    suspend fun hasInternetAccess(): <PERSON><PERSON><PERSON> {
        return withContext(dispatcher) {
            runCatching {
                withTimeoutOrNull(SOCKET_TIMEOUT_MS) {
                    Socket().use { socket ->
                        socket.connect(
                            InetSocketAddress(GOOGLE_DNS_HOST, GOOGLE_DNS_PORT),
                            SOCKET_TIMEOUT_MS.toInt()
                        )
                        true
                    }
                }
            }.getOrNull() == true
        }
    }

    private companion object {
        const val GOOGLE_DNS_HOST = "*******"
        const val GOOGLE_DNS_PORT = 53
        const val SOCKET_TIMEOUT_MS = 1500L
    }
}
