package co.thewordlab.luzia.foundation.analytics

open class AnalyticsEvents(
    val value: String,
    val properties: Map<String, Any> = emptyMap(),
    val options: EventOptions = EventOptions.Builder().build()
)

interface EventOptions {

    val trackOn: Set<Trackers>
    val isError: Boolean

    class Builder {
        private var trackOn: Set<Trackers> = setOf(Trackers.ALL)
        private var isError: Boolean = false

        fun trackOn(vararg trackers: Trackers) = apply {
            this.trackOn = trackers.toSet()
        }

        fun isError() = apply {
            this.isError = true
        }

        fun build(): EventOptions {
            return object : EventOptions {
                override val trackOn: Set<Trackers>
                    get() = <EMAIL>
                override val isError: Boolean
                    get() = <EMAIL>
            }
        }
    }
}
