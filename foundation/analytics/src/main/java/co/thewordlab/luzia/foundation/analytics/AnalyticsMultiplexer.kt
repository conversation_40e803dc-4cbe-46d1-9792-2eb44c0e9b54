package co.thewordlab.luzia.foundation.analytics

import android.util.Log
import co.thewordlab.luzia.foundation.analytics.providers.AnalyticsProvider
import co.thewordlab.luzia.foundation.analytics.providers.CustomerBehaviour
import co.thewordlab.luzia.foundation.analytics.providers.ErrorTracking
import javax.inject.Inject

class AnalyticsMultiplexer @Inject constructor(
    private val providers: List<AnalyticsProvider>
) : Analytics {

    override fun trackScreen(screen: AnalyticsScreens) {
        providers.filterIsInstance<CustomerBehaviour>()
            .forEach { it.trackScreen(screen) }
    }

    override fun trackAction(action: AnalyticsActions) {
        providers.filterIsInstance<CustomerBehaviour>()
            .forEach { it.trackAction(action) }
    }

    override fun trackEvent(event: AnalyticsEvents) {
        providers.filterIsInstance<CustomerBehaviour>().forEach {
            if (event.options.trackOn.contains(Trackers.ALL) ||
                event.options.trackOn.contains(it.id)
            ) {
                it.trackEvent(event)
            }
        }
    }

    override fun setUserProperties(properties: Map<Parameter, Any>) {
        providers.filterIsInstance<CustomerBehaviour>().forEach { it.setUserProperties(properties) }
    }

    override fun setUserIdentification(identification: UserIdentification) {
        providers.forEach { it.setUserIdentification(identification) }
    }

    override fun reportException(message: String, ex: Exception?) {
        if (BuildConfig.DEBUG) Log.e("ErrorLog", message)
        providers.filterIsInstance<ErrorTracking>().forEach { it.reportException(message, ex) }
    }

    @Deprecated("Use trackScreen instead")
    override fun logScreenView(screenView: ScreenView, params: Map<Parameter, Any>) {
        providers.filterIsInstance<CustomerBehaviour>()
            .forEach { it.logScreenView(screenView, params) }
    }

    @Deprecated("Use trackEvent instead")
    override fun logEvent(event: Event, params: Map<Parameter, Any>, trackOn: TrackOn) {
        providers.filterIsInstance<CustomerBehaviour>().forEach {
            if (it.id.isSpecifiedTracker(trackOn)) {
                it.logEvent(event, params)
            }
        }
    }

    @Deprecated("Use trackEvent instead")
    override fun logEventWithProps(event: Event, params: Map<String, Any>) {
        providers.filterIsInstance<CustomerBehaviour>()
            .forEach { it.logEventWithProps(event, params) }
    }

    @Deprecated("Use trackEvent instead")
    override fun logFailedEvent(failedEvent: Event, params: Map<Parameter, Any>) {
        providers.filterIsInstance<CustomerBehaviour>()
            .forEach { it.logFailedEvent(failedEvent, params) }
    }

    @Deprecated("Use trackScreen instead")
    override fun logScreen(screen: AnalyticsScreens, params: Map<Parameter, Any>) {
        providers.filterIsInstance<CustomerBehaviour>().forEach { it.logScreen(screen, params) }
    }

    @Deprecated("Use trackScreen instead")
    override fun logScreenWithProps(screen: AnalyticsScreens, params: Map<String, Any>) {
        providers.filterIsInstance<CustomerBehaviour>().forEach { it.logScreenWithProps(screen, params) }
    }

    @Deprecated("Use trackAction instead")
    override fun logAction(action: AnalyticsActions, params: Map<Parameter, Any>) {
        providers.filterIsInstance<CustomerBehaviour>().forEach { it.logAction(action, params) }
    }

    @Deprecated("Use trackAction instead")
    override fun logActionWithProps(action: AnalyticsActions, params: Map<String, Any>) {
        providers.filterIsInstance<CustomerBehaviour>().forEach { it.logActionWithProps(action, params) }
    }

    @Deprecated("Use trackEvent instead")
    override fun logEvent(event: AnalyticsEvents, params: Map<Parameter, Any>, trackOn: TrackOn) {
        providers.filterIsInstance<CustomerBehaviour>().forEach {
            if (it.id.isSpecifiedTracker(trackOn)) {
                it.logEvent(event, params)
            }
        }
    }

    private fun Trackers.isSpecifiedTracker(trackOn: TrackOn): Boolean =
        when (trackOn) {
            TrackOn.Base -> isBaseTracker()
            is TrackOn.Extras -> trackOn.trackers.contains(this) || isBaseTracker()
        }

    private fun Trackers.isBaseTracker() =
        this == Trackers.FIREBASE || this == Trackers.AMPLITUDE || this == Trackers.BRAZE
}
