package co.thewordlab.luzia.foundation.analytics.providers

import android.content.Context
import android.util.Log
import co.thewordlab.luzia.foundation.analytics.BuildConfig
import co.thewordlab.luzia.foundation.analytics.UserIdentification
import co.thewordlab.luzia.foundation.analytics.providers.config.DataDogConfig
import co.thewordlab.luzia.foundation.securelib.SecureKey
import co.thewordlab.luzia.foundation.securelib.SecureStorage
import com.datadog.android.Datadog
import com.datadog.android.DatadogSite
import com.datadog.android.core.configuration.Configuration
import com.datadog.android.log.Logger
import com.datadog.android.log.Logs
import com.datadog.android.log.LogsConfiguration
import com.datadog.android.privacy.TrackingConsent
import com.datadog.android.rum.Rum
import com.datadog.android.rum.RumConfiguration
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DataDogAnalyticsProvider @Inject constructor(
    @ApplicationContext private val context: Context,
    private val config: DataDogConfig,
    secureStorage: SecureStorage
) : ErrorTracking {

    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val configuration by lazy {
        Configuration
            .Builder(
                clientToken = secureStorage.get(SecureKey.KEY_DATADOG),
                env = ENVIRONMENT_NAME,
                variant = APP_VARIANT_NAME
            )
            .setCrashReportsEnabled(true)
            .useSite(DatadogSite.EU1)
            .build()
    }

    private val logConfiguration = LogsConfiguration.Builder().build()

    private val rumConfiguration by lazy {
        RumConfiguration
            .Builder(secureStorage.get(SecureKey.KEY_DATADOG_RUM))
            .setSessionSampleRate(config.sessionSampleRate().toFloat())
            .trackUserInteractions()
            .trackLongTasks(LONG_TASK_THRESHOLD_MS)
            .build()
    }

    private val logger = Logger.Builder()
        .setLogcatLogsEnabled(BuildConfig.DEBUG)
        .setBundleWithRumEnabled(config.isRumEnabled())
        .setNetworkInfoEnabled(true)
        .setService(SERVICE_NAME)
        .setName(LOGGER_NAME)
        .build()

    init {
        coroutineScope.launch { initialize() }
    }

    private fun initialize() {
        Datadog.initialize(context, configuration, TrackingConsent.GRANTED)
        if (BuildConfig.DEBUG) {
            Datadog.setVerbosity(Log.VERBOSE)
        }
        if (Datadog.isInitialized()) {
            Logs.enable(logConfiguration)
            if (config.isRumEnabled()) {
                Rum.enable(rumConfiguration)
            }
        }
    }

    override fun reportException(message: String, ex: Exception?) {
        logger.e(message, ex)
    }

    override fun setUserIdentification(identification: UserIdentification) {
        val instance = Datadog.getInstance()
        instance.setUserInfo(
            identification.masterUserId,
            null,
            identification.email,
            mapOf(
                EXTERNAL_ID to identification.externalDeviceId,
                MASTER_USER_ID to identification.masterUserId,
                USER_SESSION_ID to identification.userSessionId,
                AD_ID to identification.userAddId,
                BROWSER to identification.defaultBrowser
            )
        )
    }

    private companion object {
        const val LONG_TASK_THRESHOLD_MS = 1000L
        const val EXTERNAL_ID = "External ID"
        const val MASTER_USER_ID = "Master UserId"
        const val USER_SESSION_ID = "Session UserId"
        const val AD_ID = "ADId"
        const val BROWSER = "browser"
        const val LOGGER_NAME = "default"
        const val SERVICE_NAME = "co.thewordlab.luzia"
        const val ENVIRONMENT_NAME = BuildConfig.FLAVOR
        const val APP_VARIANT_NAME = BuildConfig.BUILD_TYPE
    }
}
