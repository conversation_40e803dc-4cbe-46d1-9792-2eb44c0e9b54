package co.thewordlab.luzia.foundation.analytics.providers.config

import co.thewordlab.luzia.foundation.analytics.providers.DataDogAnalyticsProvider
import dagger.Lazy
import javax.inject.Inject

class DataDogAnalyticsProviderFactory @Inject constructor(
    private val dataDogConfig: DataDogConfig,
    private val dataDogAnalyticsProvider: Lazy<DataDogAnalyticsProvider>
) {

    fun create(): DataDogAnalyticsProvider? {
        return if (dataDogConfig.isEnabled()) dataDogAnalyticsProvider.get() else null
    }
}
